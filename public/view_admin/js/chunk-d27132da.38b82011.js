(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-d27132da"],{"0394":function(t,e,a){},"057a":function(t,e,a){"use strict";var i=a("7a9f");a.n(i).a},"07c8":function(t,e,a){"use strict";var i=a("b3f2");a.n(i).a},"0fc4":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"b",(function(){return s}));var i=a("b6bd");function r(){return Object(i.a)({url:"erp/config",method:"get"})}function o(t){return Object(i.a)({url:"store/erp/shop",method:"get",params:t})}function s(t){return Object(i.a)({url:"product/import_erp_product",method:"post",data:t})}},"28a7":function(t,e,a){t.exports=a.p+"view_admin/img/card_cover_image.5ea79bee.png"},"2e12":function(t,e,a){"use strict";var i=a("cad2");a.n(i).a},3123:function(t,e,a){},"31f9":function(t,e,a){"use strict";var i=a("438c");a.n(i).a},4269:function(t,e,a){"use strict";var i=a("741b");a.n(i).a},"438c":function(t,e,a){},4929:function(t,e,a){"use strict";var i=a("4b70");a.n(i).a},"4b70":function(t,e,a){},"5b8b":function(t,e,a){"use strict";var i=a("918e");a.n(i).a},"5c4f":function(t,e,a){"use strict";var i=a("0394");a.n(i).a},"5d4b":function(t,e,a){"use strict";var i=a("a34a"),r=a.n(i),o=a("c4c8");function s(t,e,a,i,r,o,s){try{var n=t[o](s),l=n.value}catch(t){return void a(t)}n.done?e(l):Promise.resolve(l).then(i,r)}var n={name:"menusFrom",props:{formValidate:{type:Object,default:null},fromName:{type:Number,default:0}},data:function(){return{ruleValidate:{brand_name:[{required:!0,message:"请输入品牌名称",trigger:"blur"}]},type:1,modals:!1,FromData:[],titleFrom:"",grid:{xl:24,lg:24,md:12,sm:24,xs:24}}},mounted:function(){this.getAddFrom()},methods:{getAddFrom:function(){var t=this;Object(o.e)().then(function(){var e,a=(e=r.a.mark((function e(a){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=a.data;case 1:case"end":return e.stop()}}),e)})),function(){var t=this,a=arguments;return new Promise((function(i,r){var o=e.apply(t,a);function n(t){s(o,i,r,n,l,"next",t)}function l(t){s(o,i,r,n,l,"throw",t)}n(void 0)}))});return function(t){return a.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},handleReset:function(){this.modals=!1,this.$parent.getData()},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){t?2==e.type?Object(o.G)(e.formValidate.id,e.formValidate).then((function(t){e.$Message.success(t.msg),e.getAddFrom(),e.$parent.getData(),e.modals=!1})).catch((function(t){e.$Message.error(t.msg)})):Object(o.D)(e.formValidate).then((function(t){e.$Message.success(t.msg),e.getAddFrom(),e.fromName?e.$parent.getBrandList():e.$parent.getData(),e.modals=!1})).catch((function(t){e.$Message.error(t.msg)})):e.$Message.error("请输入品牌名称")}))},cancle:function(){this.modals=!1}}},l=(a("057a"),a("2877")),c=Object(l.a)(n,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Modal",{attrs:{width:"700",scrollable:"","footer-hide":"",closable:"",title:t.titleFrom,"z-index":1,"class-name":"vertical-center-modal"},on:{"on-cancel":t.handleReset},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":110,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"上级品牌："}},[a("Cascader",{attrs:{data:t.FromData,placeholder:"请选择上级品牌","change-on-select":""},model:{value:t.formValidate.fid,callback:function(e){t.$set(t.formValidate,"fid",e)},expression:"formValidate.fid"}})],1)],1),a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"品牌名称：",prop:"brand_name"}},[a("Input",{attrs:{maxlength:"10","show-word-limit":"",placeholder:"请输入品牌名称",prop:""},model:{value:t.formValidate.brand_name,callback:function(e){t.$set(t.formValidate,"brand_name",e)},expression:"formValidate.brand_name"}})],1)],1),a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"品牌排序："}},[a("InputNumber",{staticStyle:{width:"100%"},attrs:{step:1,placeholder:"请输入品牌排序"},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"是否显示："}},[a("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.formValidate.is_show,callback:function(e){t.$set(t.formValidate,"is_show",e)},expression:"formValidate.is_show"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1),a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"style-add"},[a("Button",{staticClass:"mr14",attrs:{type:"default"},on:{click:t.cancle}},[t._v("取消")]),a("Button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("确认")])],1)])],1)],1)],1)}),[],!1,null,"42e33cb4",null);e.a=c.exports},"6fd3":function(t,e,a){"use strict";var i=a("2f62"),r=a("c4c8");function o(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function s(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var n={name:"addAttr",data:function(){return{spinShow:!1,modal_loading:!1,grid:{xl:3,lg:3,md:12,sm:24,xs:24},modal:!1,index:1,rules:{rule_name:[{required:!0,message:"请输入分类名称",trigger:"blur"}]},formDynamic:{rule_name:"",spec:[]},attrsName:"",attrsVal:"",formDynamicNameData:[],isBtn:!1,formDynamicName:[],results:[],result:[],ids:0,title:"添加商品规格"}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?o(a,!0).forEach((function(e){s(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):o(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(i.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:110},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:{onCancel:function(){this.clear()},addBtn:function(){if(""==this.formDynamic.rule_name.trim())return this.$Message.error("请输入分类名称");this.isBtn=!0},getIofo:function(t){var e=this;this.spinShow=!0,this.ids=t.id,this.title="编辑商品规格",Object(r.sb)(t.id).then((function(t){e.formDynamic=t.data.info,e.spinShow=!1})).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;if(0===e.formDynamic.spec.length)return e.$Message.warning("请至少添加一条商品规格！");for(var a=0;a<e.formDynamic.spec.length;a++){if(!e.formDynamic.spec[a].value.trim())return e.$Message.warning("请添加规格名称！");if(!e.formDynamic.spec[a].detail.length)return e.$Message.warning("请添加规格值！")}e.modal_loading=!0,setTimeout((function(){Object(r.rb)(e.formDynamic,e.ids).then((function(t){e.$Message.success(t.msg),setTimeout((function(){e.modal=!1,e.modal_loading=!1}),500),setTimeout((function(){e.$emit("getList"),e.clear()}),600)})).catch((function(t){e.modal_loading=!1,e.$Message.error(t.msg)}))}),1200)}))},clear:function(){this.$refs.formDynamic.resetFields(),this.formDynamic.spec=[],this.isBtn=!1,this.ids=0,this.title="添加商品规格",this.attrsName="",this.attrsVal=""},offAttrName:function(){this.isBtn=!1},cancle:function(){this.modal=!1,this.clear()},handleRemove:function(t){this.formDynamic.spec.splice(t,1)},handleRemove2:function(t,e){t.splice(e,1)},createAttrName:function(){if(this.attrsName&&this.attrsVal){var t={value:this.attrsName,detail:[this.attrsVal]};this.formDynamic.spec.push(t);var e={};this.formDynamic.spec=this.formDynamic.spec.reduce((function(t,a){return!e[a.value]&&(e[a.value]=t.push(a)),t}),[]),this.attrsName="",this.attrsVal="",this.isBtn=!1}else this.$Message.warning("请添加规格名称或规格值")},createAttr:function(t,e){if(t){this.formDynamic.spec[e].detail.push(t);var a={};this.formDynamic.spec[e].detail=this.formDynamic.spec[e].detail.reduce((function(t,e){return!a[e]&&(a[e]=t.push(e)),t}),[])}else this.$Message.warning("请添加属性")}}},l=(a("b76f"),a("2877")),c=Object(l.a)(n,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Modal",{attrs:{scrollable:"",title:t.title,"class-name":"vertical-center-modal",width:"700"},on:{"on-cancel":t.onCancel},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("Form",{ref:"formDynamic",staticClass:"attrFrom",attrs:{model:t.formDynamic,rules:t.rules,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{gutter:24}},[a("Col",{attrs:{span:"24"}},[a("Col",{staticClass:"mb15",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"分类名称：",prop:"rule_name"}},[a("Input",{attrs:{placeholder:"请输入分类名称",maxlength:20},model:{value:t.formDynamic.rule_name,callback:function(e){t.$set(t.formDynamic,"rule_name",e)},expression:"formDynamic.rule_name"}})],1)],1)],1),t._l(t.formDynamic.spec,(function(e,i){return a("Col",{key:i,staticClass:"noForm",attrs:{span:"23"}},[a("FormItem",[a("div",{staticClass:"acea-row row-middle"},[a("span",{staticClass:"mr5"},[t._v(t._s(e.value))]),a("Icon",{attrs:{type:"ios-close-circle"},on:{click:function(e){return t.handleRemove(i)}}})],1),a("div",{staticClass:"rulesBox"},[t._l(e.detail,(function(i,r){return a("Tag",{key:r,attrs:{type:"dot",closable:"",color:"primary",name:i},on:{"on-close":function(a){return t.handleRemove2(e.detail,r)}}},[t._v(t._s(i))])})),a("Input",{staticClass:"width20",attrs:{maxlength:"30","show-word-limit":"",search:"","enter-button":"添加",placeholder:"请输入属性名称"},on:{"on-search":function(a){return t.createAttr(e.detail.attrsVal,i)}},model:{value:e.detail.attrsVal,callback:function(a){t.$set(e.detail,"attrsVal",a)},expression:"item.detail.attrsVal"}})],2)])],1)})),t.isBtn?a("Col",{staticClass:"mt10",attrs:{span:"24"}},[a("Col",{staticClass:"mt10 mr15",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"规格名称："}},[a("Input",{attrs:{placeholder:"请输入规格名称",maxlength:"30","show-word-limit":""},model:{value:t.attrsName,callback:function(e){t.attrsName=e},expression:"attrsName"}})],1)],1),a("Col",{staticClass:"mt10 mr20",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"规格值："}},[a("Input",{attrs:{maxlength:"30","show-word-limit":"",placeholder:"请输入规格值"},model:{value:t.attrsVal,callback:function(e){t.attrsVal=e},expression:"attrsVal"}})],1)],1),a("Col",{staticClass:"mr20",attrs:{span:"10"}},[a("div",{staticClass:"sub"},[a("Button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),a("Button",{on:{click:t.offAttrName}},[t._v("取消")])],1)])],1):t._e(),t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e()],2),t.isBtn?t._e():a("Button",{staticClass:"ml110 mt10",attrs:{type:"primary"},on:{click:t.addBtn}},[t._v("添加新规格")])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{on:{click:t.cancle}},[t._v("取消")]),a("Button",{attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formDynamic")}}},[t._v("确定")])],1)],1)}),[],!1,null,"48ec42b7",null);e.a=c.exports},7362:function(t,e,a){"use strict";var i=a("d766");a.n(i).a},"741b":function(t,e,a){},"7a9f":function(t,e,a){},"830a":function(t,e,a){"use strict";var i;function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}a.d(e,"f",(function(){return o})),a.d(e,"a",(function(){return s})),a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return l})),a.d(e,"c",(function(){return c})),a.d(e,"b",(function(){return m}));var o=(r(i={is_presale_product:0,is_limit:0,limit_type:1,limit_num:1,is_vip_product:0,is_support_refund:0,disk_info:"",presale_day:1,presale_time:[],auto_on_time:"",auto_off_time:"",video_open:!1,store_name:"",freight:1,postage:0,custom_form:[],cate_id:[],label_id:[],ensure_id:[],keyword:"",unit_name:"",specs_id:0,store_info:"",bar_code:"",code:"",image:"",recommend_image:"",slider_image:[],description:"",ficti:0,give_integral:0,sort:0,is_show:1,is_hot:0,is_benefit:0,is_best:0,is_new:0,is_good:0,is_postage:0,is_sub:[],id:0,spec_type:0,video_link:"",temp_id:"",attr:{pic:"",price:0,settle_price:0,cost:0,ot_price:0,stock:0,bar_code:"",code:"",weight:0,volume:0,brokerage:0,brokerage_two:0,vip_price:0,virtual_list:[],write_times:0,write_valid:1,days:1,reservation_time_data:[]},attrs:[],items:[{pic:"",price:0,cost:0,ot_price:0,stock:0,bar_code:"",code:""}],coupons:[],couponName:[],header:[],selectRule:"",coupon_ids:[],command_word:"",delivery_type:["1"],specs:[],recommend_list:[],brand_id:[],product_type:0,store_label_id:[],off_show:0},"header",[]),r(i,"share_content",""),r(i,"presale_status",1),r(i,"applicable_type",1),r(i,"reservation_time_type",1),r(i,"reservation_times",[]),r(i,"reservation_time_interval",30),r(i,"customize_time_period",[[]]),r(i,"reservation_type",1),r(i,"reservation_timing_type",1),r(i,"is_show_stock",1),r(i,"sale_time_type",1),r(i,"sale_time_week",[]),r(i,"sale_time_data",[]),r(i,"show_reservation_days_type",1),r(i,"show_reservation_days",1),r(i,"is_advance",0),r(i,"advance_time",1),r(i,"is_cancel_reservation",0),r(i,"cancel_reservation_time",1),r(i,"card_cover",1),r(i,"card_cover_image",""),r(i,"card_cover_color",""),r(i,"related",[]),r(i,"type",0),r(i,"supplier_id",0),r(i,"is_sync_stock",1),r(i,"is_sync_show",1),i),s=[{title:"图片",slot:"pic",align:"center",minWidth:"80px"},{title:"售价",slot:"price",align:"center",minWidth:"120px"},{title:"成本价",slot:"cost",align:"center",minWidth:"120px"},{title:"划线价",slot:"ot_price",align:"center",minWidth:"120px"},{title:"库存",slot:"stock",align:"center",minWidth:"120px"},{title:"商品编号",slot:"code",align:"center",minWidth:"120px"},{title:"商品条形码",slot:"bar_code",align:"center",minWidth:"120px"},{title:"重量（KG）",slot:"weight",align:"center",minWidth:"100px"},{title:"体积(m³)",slot:"volume",align:"center",minWidth:"100px"},{title:"默认选中规格",slot:"selected_spec",fixed:"right",align:"center",minWidth:"100px"},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:"120px"}],n=[{title:"图片",slot:"pic",align:"center",minWidth:80},{title:"售价",slot:"price",align:"center",minWidth:120},{title:"成本价",slot:"cost",align:"center",minWidth:120},{title:"划线价",slot:"ot_price",align:"center",minWidth:120},{title:"库存",slot:"stock",align:"center",minWidth:120},{title:"商品条形码",slot:"bar_code",align:"center",minWidth:"120px"},{title:"产品编号",slot:"code",align:"center",minWidth:120},{title:"默认选中规格",slot:"selected_spec",fixed:"right",align:"center",minWidth:90},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:120}],l=[{title:"图片",slot:"pic",align:"center",minWidth:80},{title:"售价",slot:"price",align:"center",minWidth:120},{title:"成本价",slot:"cost",align:"center",minWidth:120},{title:"划线价",slot:"ot_price",align:"center",minWidth:120},{title:"库存",slot:"stock",align:"center",minWidth:120},{title:"商品条形码",slot:"bar_code",align:"center",minWidth:"120px"},{title:"产品编号",slot:"code",align:"center",minWidth:120},{title:"添加卡密/网盘",slot:"fictitious",align:"center",minWidth:120},{title:"默认选中规格",slot:"selected_spec",fixed:"right",align:"center",minWidth:90},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:120}],c=[{title:"ID",key:"id",width:60},{title:"门店图片",slot:"image",minWidth:80,className:"store-image-column"},{title:"门店分类",key:"cate_name",minWidth:80},{title:"门店名称",key:"name",minWidth:80},{title:"联系电话",key:"phone",minWidth:90},{title:"门店地址",key:"address",ellipsis:!0,minWidth:150},{title:"营业时间",key:"day_time",minWidth:120},{title:"营业状态",key:"status_name",minWidth:80},{title:"操作",slot:"action",width:100}],m=[{title:"图片",slot:"pic",align:"center",minWidth:"80px"},{title:"售价",slot:"price",align:"center",minWidth:"120px"},{title:"成本价",slot:"cost",align:"center",minWidth:"120px"},{title:"划线价",slot:"ot_price",align:"center",minWidth:"120px"},{title:"预约数量",slot:"stock",align:"center",minWidth:"120px"},{title:"默认选中规格",slot:"selected_spec",fixed:"right",align:"center",minWidth:"100px"},{title:"操作",slot:"action",fixed:"right",align:"center",minWidth:"120px"}]},"918e":function(t,e,a){},a6b9:function(t,e,a){"use strict";a.d(e,"F",(function(){return r})),a.d(e,"k",(function(){return o})),a.d(e,"a",(function(){return s})),a.d(e,"v",(function(){return n})),a.d(e,"w",(function(){return l})),a.d(e,"l",(function(){return c})),a.d(e,"h",(function(){return m})),a.d(e,"b",(function(){return u})),a.d(e,"c",(function(){return d})),a.d(e,"t",(function(){return p})),a.d(e,"o",(function(){return f})),a.d(e,"p",(function(){return h})),a.d(e,"r",(function(){return v})),a.d(e,"u",(function(){return _})),a.d(e,"G",(function(){return b})),a.d(e,"q",(function(){return g})),a.d(e,"j",(function(){return y})),a.d(e,"n",(function(){return w})),a.d(e,"x",(function(){return x})),a.d(e,"g",(function(){return k})),a.d(e,"f",(function(){return C})),a.d(e,"m",(function(){return D})),a.d(e,"i",(function(){return I})),a.d(e,"B",(function(){return V})),a.d(e,"C",(function(){return $})),a.d(e,"E",(function(){return F})),a.d(e,"d",(function(){return A})),a.d(e,"D",(function(){return T})),a.d(e,"e",(function(){return S})),a.d(e,"y",(function(){return O})),a.d(e,"A",(function(){return B})),a.d(e,"H",(function(){return N})),a.d(e,"z",(function(){return P})),a.d(e,"s",(function(){return j}));var i=a("b6bd");function r(t){return Object(i.a)({url:"supplier/supplier",method:"get",params:t})}function o(t){return Object(i.a)({url:"/supplier/supplier/".concat(t),method:"get"})}function s(t){return Object(i.a)({url:"supplier/supplier",method:"post",data:t})}function n(t,e){return Object(i.a)({url:"supplier/supplier/".concat(t),method:"put",data:e})}function l(t,e){return Object(i.a)({url:"/supplier/supplier/set_status/".concat(t,"/").concat(e),method:"put"})}function c(t){return Object(i.a)({url:"/supplier/list",method:"get",params:t})}function m(t){return Object(i.a)({url:"/supplier/order/list",method:"get",params:t})}function u(t){return Object(i.a)({url:"/supplier/order/deliver_remind/".concat(t.supplier_id,"/").concat(t.id),method:"put"})}function d(t){return Object(i.a)({url:"/supplier/order/distribution_info",method:"get",params:{ids:t}})}function p(t){return Object(i.a)({url:"/supplier/refund/list",method:"get",params:t})}function f(t){return Object(i.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function h(t){return Object(i.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function v(t){return Object(i.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function _(t){return Object(i.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function b(t){return Object(i.a)({url:"/supplier/supplier/login/".concat(t),method:"get"})}function g(t){return Object(i.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function y(t){return Object(i.a)({url:"/supplier/refund/detail/".concat(t),method:"get"})}function w(t){return Object(i.a)({url:"/supplier/order/no_refund/".concat(t),method:"get"})}function x(t){return Object(i.a)({url:"/supplier/order/refund_integral/".concat(t),method:"get"})}function k(t){return Object(i.a)({url:"/supplier/order/distribution/".concat(t),method:"get"})}function C(t){return Object(i.a)({url:"/supplier/apply/list",method:"get",params:t})}function D(t){return Object(i.a)({url:"/supplier/apply/verify/form/".concat(t),method:"get"})}function I(t){return Object(i.a)({url:"/supplier/apply/mark/form/".concat(t),method:"get"})}function V(t){return Object(i.a)({url:"/supplier/flowing_water/fund_record_info",method:"get",params:t})}function $(t){return Object(i.a)({url:"/supplier/flowing_water/list",method:"get",params:t})}function F(t,e){return Object(i.a)({url:"/supplier/flowing_water/mark/".concat(t),method:"put",params:e})}function A(t){return Object(i.a)({url:"/export/supplierWaterExport",method:"get",params:t})}function T(t){return Object(i.a)({url:"/supplier/flowing_water/fund_record",method:"get",params:t})}function S(t){return Object(i.a)({url:"/export/supplierWaterRecord",method:"get",params:t})}function O(t){return Object(i.a)({url:"/supplier/extract/list",method:"get",params:t})}function B(t,e){return Object(i.a)({url:"/supplier/extract/verify/".concat(t),method:"post",data:e})}function N(t){return Object(i.a)({url:"/supplier/extract/transfer/".concat(t),method:"get"})}function P(t,e){return Object(i.a)({url:"/supplier/extract/mark/".concat(t),method:"post",data:e})}function j(t){return Object(i.a)({url:"supplier/order/chart",method:"get",params:t})}},ac02:function(t,e,a){"use strict";var i=a("ee87");a.n(i).a},b3f2:function(t,e,a){},b500:function(t,e){t.exports="data:image/png;base64,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"},b562:function(t,e,a){"use strict";a.d(e,"l",(function(){return r})),a.d(e,"n",(function(){return o})),a.d(e,"D",(function(){return s})),a.d(e,"h",(function(){return n})),a.d(e,"j",(function(){return l})),a.d(e,"m",(function(){return c})),a.d(e,"y",(function(){return m})),a.d(e,"a",(function(){return u})),a.d(e,"x",(function(){return d})),a.d(e,"s",(function(){return p})),a.d(e,"t",(function(){return f})),a.d(e,"C",(function(){return h})),a.d(e,"g",(function(){return v})),a.d(e,"i",(function(){return _})),a.d(e,"k",(function(){return b})),a.d(e,"d",(function(){return g})),a.d(e,"e",(function(){return y})),a.d(e,"f",(function(){return w})),a.d(e,"z",(function(){return x})),a.d(e,"B",(function(){return k})),a.d(e,"A",(function(){return C})),a.d(e,"H",(function(){return D})),a.d(e,"o",(function(){return I})),a.d(e,"c",(function(){return V})),a.d(e,"G",(function(){return $})),a.d(e,"E",(function(){return F})),a.d(e,"F",(function(){return A})),a.d(e,"w",(function(){return T})),a.d(e,"u",(function(){return S})),a.d(e,"v",(function(){return O})),a.d(e,"p",(function(){return B})),a.d(e,"b",(function(){return N})),a.d(e,"r",(function(){return P})),a.d(e,"q",(function(){return j}));var i=a("b6bd");function r(t){return Object(i.a)({url:"app/routine",method:"get",params:t})}function o(){return Object(i.a)({url:"app/routine/syncSubscribe",method:"GET"})}function s(){return Object(i.a)({url:"app/wechat/syncSubscribe",method:"GET"})}function n(){return Object(i.a)({url:"app/routine/create",method:"get"})}function l(t){return Object(i.a)({url:"app/routine/".concat(t,"/edit"),method:"get"})}function c(t){return Object(i.a)({url:"app/routine/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function m(t){return Object(i.a)({url:"app/wechat/menu",method:"get"})}function u(t){return Object(i.a)({url:"app/wechat/menu",method:"post",data:t})}function d(t){return Object(i.a)({url:"app/wechat/template",method:"get",params:t})}function p(){return Object(i.a)({url:"app/wechat/template/create",method:"get"})}function f(t){return Object(i.a)({url:"app/wechat/template/".concat(t,"/edit"),method:"get"})}function h(t){return Object(i.a)({url:"app/wechat/template/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function v(t){return Object(i.a)({url:t.url,method:"post",data:t.key})}function _(t){return Object(i.a)({url:"app/routine/download",method:"post",data:t})}function b(){return Object(i.a)({url:"app/routine/info",method:"get"})}function g(t){return Object(i.a)({url:"app/wechat/keyword",method:"get",params:t})}function y(t){return Object(i.a)({url:"app/wechat/keyword/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function w(t,e){return Object(i.a)({url:t,method:"get",params:e.key})}function x(t){return Object(i.a)({url:"/app/wechat/news",method:"POST",data:t})}function k(t){return Object(i.a)({url:"app/wechat/news",method:"GET",params:t})}function C(t){return Object(i.a)({url:"app/wechat/news/".concat(t),method:"GET"})}function D(t){return Object(i.a)({url:"app/wechat/user",method:"GET",params:t})}function I(){return Object(i.a)({url:"app/wechat/user/tag_group",method:"GET"})}function V(t){return Object(i.a)({url:t,method:"GET"})}function $(){return Object(i.a)({url:"app/wechat/tag",method:"GET"})}function F(){return Object(i.a)({url:"app/wechat/tag/create",method:"GET"})}function A(t){return Object(i.a)({url:"app/wechat/tag/".concat(t,"/edit"),method:"GET"})}function T(){return Object(i.a)({url:"app/wechat/group",method:"GET"})}function S(){return Object(i.a)({url:"app/wechat/group/create",method:"GET"})}function O(t){return Object(i.a)({url:"app/wechat/group/".concat(t,"/edit"),method:"GET"})}function B(t){return Object(i.a)({url:"app/wechat/action",method:"GET",params:t})}function N(t){return Object(i.a)({url:"app/wechat/code_reply/".concat(t),method:"GET"})}function P(){return Object(i.a)({url:"/app/wechat/card",method:"GET"})}function j(t){return Object(i.a)({url:"/app/wechat/card",method:"post",data:t})}},b76f:function(t,e,a){"use strict";var i=a("3123");a.n(i).a},c7e8:function(t,e,a){"use strict";a.r(e);var i=a("a34a"),r=a.n(i),o=a("2f62"),s=a("c4c8"),n=a("ed08"),l=a("0fc4"),c=a("830a"),m=a("5334"),u={name:"changePrice",props:{timeData:{type:Array,default:function(){return[]}}},data:function(){var t=this;return{stockModals:!1,tooltipVisible:!1,timeInputNumberValue:0,timeColumns:[{title:"时段",slot:"time",align:"left",width:186},{title:"库存",slot:"stock",align:"left",minWidth:180,renderHeader:function(e,a){return e("div",[e("Poptip",{props:{transfer:!0,placement:"top",trigger:"click",value:t.tooltipVisible},on:{"on-popper-hide":function(){t.tooltipVisible=!1}},scopedSlots:{default:function(){return e("span",{on:{click:function(e){e.stopPropagation(),t.tooltipVisible=!0,t.timeInputNumberValue=0}}},[e("span","库存"),e("span",{class:["iconfont iconbianji11"],style:{marginLeft:"6px",color:"#AAAAAA",fontSize:"12px"}})])},content:function(){return e("div",[e("div",{class:["fs-12 text-wlll-515A6E mb-12"]},"批量修改"),e("InputNumber",{props:{min:0,max:99999999,precision:0,value:t.timeInputNumberValue},class:["w-85"],on:{"on-change":function(e){t.timeInputNumberValue=e}}}),e("Button",{props:{size:"small"},class:["ml-8"],on:{click:function(){t.tooltipVisible=!1}}},"取消"),e("Button",{props:{type:"primary",size:"small"},class:["ml-8"],on:{click:function(){t.tooltipVisible=!1,t.handleButtonClick()}}},"确定")])}}})])}}]}},computed:{},mounted:function(){},methods:{handleButtonClick:function(){var t=this;this.timeData.forEach((function(e){e.stock=t.timeInputNumberValue}))},cancel:function(){this.stockModals=!1},submit:function(){this.stockModals=!1,this.$emit("modalStockSet",this.timeData)}}},d=(a("31f9"),a("2877")),p=Object(d.a)(u,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Modal",{attrs:{scrollable:"",title:"库存设置",width:"526",closable:!1},model:{value:t.stockModals,callback:function(e){t.stockModals=e},expression:"stockModals"}},[a("Table",{staticClass:"timeTable",attrs:{columns:t.timeColumns,data:t.timeData,border:"","no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果","max-height":"380",width:"100%"},scopedSlots:t._u([{key:"time",fn:function(e){var i=e.row;return e.index,[a("div",{staticClass:"ml-34"},[t._v(t._s(i.start)+"-"+t._s(i.end))])]}},{key:"stock",fn:function(e){e.row;var i=e.index;return[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:129,expression:"129"}],staticClass:"ml-45",attrs:{min:0,max:99999999,precision:0},model:{value:t.timeData[i].stock,callback:function(e){t.$set(t.timeData[i],"stock",e)},expression:"timeData[index].stock"}})]}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{on:{click:t.cancel}},[t._v("取消")]),a("Button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确认")])],1)],1)}),[],!1,null,"5f4d4e7e",null).exports,f={name:"reservationSet",props:{baseInfo:{type:Object,default:function(){return{}}}},data:function(){return{weekList:[{id:1,name:"周一",selected:!0},{id:2,name:"周二",selected:!0},{id:3,name:"周三",selected:!0},{id:4,name:"周四",selected:!0},{id:5,name:"周五",selected:!0},{id:6,name:"周六",selected:!1},{id:0,name:"周天",selected:!1}]}},watch:{"baseInfo.sale_time_week":{handler:function(t){t.length&&this.weekList.forEach((function(e){-1!=t.indexOf(e.id)?e.selected=!0:e.selected=!1}))},immediate:!0,deep:!0}},methods:{weekTap:function(t){t.selected=!t.selected,this.$emit("weekData",this.weekList)},onchangeData:function(t){this.baseInfo.sale_time_start=t}}},h=(a("7362"),Object(d.a)(f,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("FormItem",{attrs:{label:"预约模式："}},[a("RadioGroup",{model:{value:t.baseInfo.reservation_type,callback:function(e){t.$set(t.baseInfo,"reservation_type",e)},expression:"baseInfo.reservation_type"}},[a("Radio",{attrs:{label:1}},[t._v("到店服务+上门服务")]),a("Radio",{attrs:{label:2}},[t._v("到店服务")]),a("Radio",{attrs:{label:3}},[t._v("上门服务")])],1)],1),a("FormItem",{attrs:{label:"预约时机："}},[a("RadioGroup",{model:{value:t.baseInfo.reservation_timing_type,callback:function(e){t.$set(t.baseInfo,"reservation_timing_type",e)},expression:"baseInfo.reservation_timing_type"}},[a("Radio",{attrs:{label:1}},[t._v("购买时预约+先买后约")]),a("Radio",{attrs:{label:2}},[t._v("购买时预约")]),a("Radio",{attrs:{label:3}},[t._v("先买后约")])],1),a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t\t购买时预约：用户需选择预约时间后再提交预约订单；先买后约：支持用户先购买预约服务，再选择合适的预约时间。\n\t\t ")])],1),a("FormItem",{attrs:{label:"预约数量展示："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.baseInfo.is_show_stock,callback:function(e){t.$set(t.baseInfo,"is_show_stock",e)},expression:"baseInfo.is_show_stock"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("显示")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("隐藏")])]),a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t   关闭后，用户无法查看各时段的剩余预约数量\n\t\t ")])],1),a("FormItem",{attrs:{label:"可售日期："}},[a("RadioGroup",{model:{value:t.baseInfo.sale_time_type,callback:function(e){t.$set(t.baseInfo,"sale_time_type",e)},expression:"baseInfo.sale_time_type"}},[a("Radio",{attrs:{label:1}},[t._v("每天")]),a("Radio",{attrs:{label:2}},[t._v("每周")]),a("Radio",{attrs:{label:3}},[t._v("自定义时间")])],1),2==t.baseInfo.sale_time_type?a("div",{staticClass:"acea-row row-middle"},t._l(t.weekList,(function(e,i){return a("div",{staticClass:"week w-60 h-32 fs-14 rd-5px acea-row row-center-wrapper mr-10 mt10 cup",class:e.selected?"on":"",on:{click:function(a){return t.weekTap(e)}}},[t._v(t._s(e.name))])})),0):3==t.baseInfo.sale_time_type?a("div",{staticClass:"mt10"},[a("DatePicker",{staticClass:"w-250",attrs:{type:"daterange",placeholder:"选择日期",format:"yyyy-MM-dd"},on:{"on-change":t.onchangeData},model:{value:t.baseInfo.sale_time_data,callback:function(e){t.$set(t.baseInfo,"sale_time_data",e)},expression:"baseInfo.sale_time_data"}})],1):t._e(),a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t     设置预约服务的可预约日期。（自定义时间：出售中的商品超出自定义日期后自动下架）\n\t\t   ")])],1),a("FormItem",{attrs:{label:"显示日期："}},[a("RadioGroup",{model:{value:t.baseInfo.show_reservation_days_type,callback:function(e){t.$set(t.baseInfo,"show_reservation_days_type",e)},expression:"baseInfo.show_reservation_days_type"}},[a("Radio",{attrs:{label:1}},[t._v("全部展示")]),a("Radio",{attrs:{label:2}},[t._v("自定义展示时间")])],1),2==t.baseInfo.show_reservation_days_type?a("div",{staticClass:"mt10"},[t._v("\n\t\t\t   对用户展示\n\t\t       "),a("InputNumber",{attrs:{min:0,max:99999999,precision:0},model:{value:t.baseInfo.show_reservation_days,callback:function(e){t.$set(t.baseInfo,"show_reservation_days",e)},expression:"baseInfo.show_reservation_days"}}),t._v("\n\t\t\t  天内的可预约日期\n\t\t   ")],1):t._e(),a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t     用户端可以看到的可预约日期。示例：设置1天，则用户最多可以选择的预约日期为第二天。\n\t\t   ")])],1),a("FormItem",{attrs:{label:"提前预约："}},[a("RadioGroup",{attrs:{vertical:""},model:{value:t.baseInfo.is_advance,callback:function(e){t.$set(t.baseInfo,"is_advance",e)},expression:"baseInfo.is_advance"}},[a("Radio",{attrs:{label:0}},[t._v("无需提前")]),a("Radio",{attrs:{label:1}},[t._v("\n\t\t\t\t提前\n\t\t\t\t"),a("InputNumber",{attrs:{min:0,max:99999999,precision:0},model:{value:t.baseInfo.advance_time,callback:function(e){t.$set(t.baseInfo,"advance_time",e)},expression:"baseInfo.advance_time"}}),t._v("\n\t\t\t\t小时预约\n\t\t\t ")],1)],1),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("\n\t\t     用户只能预约间隔时间后的时段。示例：当前10:00,设置2h，则用户只可预约12:00往后的时段\n\t\t   ")])],1),a("FormItem",{attrs:{label:"取消预约："}},[a("RadioGroup",{attrs:{vertical:""},model:{value:t.baseInfo.is_cancel_reservation,callback:function(e){t.$set(t.baseInfo,"is_cancel_reservation",e)},expression:"baseInfo.is_cancel_reservation"}},[a("Radio",{attrs:{label:0}},[t._v("不允许取消")]),a("Radio",{attrs:{label:1}},[t._v("\n\t\t\t   服务开始\n\t\t\t   "),a("InputNumber",{attrs:{min:0,max:99999999,precision:0},model:{value:t.baseInfo.cancel_reservation_time,callback:function(e){t.$set(t.baseInfo,"cancel_reservation_time",e)},expression:"baseInfo.cancel_reservation_time"}}),t._v("\n\t\t\t   小时之前，允许取消并自动退款\n\t\t   ")],1)],1),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("\n\t\t   设置用户最晚可以取消预约的时间。示例：设置2h，用户预约12:00-14:00，则当天10:00之前允许用户取消预约\n\t\t ")])],1)],1)}),[],!1,null,"1bdb7661",null).exports),v=a("a6b9"),_=a("f1c7"),b=a("5d4b"),g=a("310e"),y=a.n(g),w=a("d8ad");function x(t,e,a,i,r,o,s){try{var n=t[o](s),l=n.value}catch(t){return void a(t)}n.done?e(l):Promise.resolve(l).then(i,r)}function k(t){return function(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var C={name:"productBaseSet",props:{baseInfo:{type:Object,default:function(){return{}}},successData:{type:Boolean,default:!1}},data:function(){return{formValidate:{id:0,brand_id:[],code:"",store_name:"",cate_id:[],store_label_id:[],unit_name:"",video_link:"",video_open:!1,is_show:0,auto_on_time:"",auto_off_time:"",off_show:0,supplier_id:0},props:{emitPath:!1,multiple:!0,checkStrictly:!0},treeSelect:[],brandData:[],unitNameList:[],storeLabelShow:!1,formBrand:{},ruleValidate:{store_name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cate_id:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array"}],unit_name:[{required:!0,message:"请输入单位",trigger:"change"}],slider_image:[{required:!0,message:"请上传商品轮播图",type:"array"}]},goodsSource:1,supplierList:[]}},components:{storeLabelList:_.a,menusFrom:b.a,draggable:y.a},computed:{startPickOptions:function(){var t=this;return{disabledDate:function(e){return t.formValidate.auto_off_time?e.getTime()>new Date(t.formValidate.auto_off_time).getTime():""}}},endPickOptions:function(){var t=this;return{disabledDate:function(e){return"1"==t.formValidate.is_show?e.getTime()<Date.now():t.formValidate.auto_on_time?e.getTime()<new Date(t.formValidate.auto_on_time).getTime():""}}}},watch:{successData:{handler:function(t){var e=this;t&&Object.keys(this.formValidate).map((function(t){e.formValidate[t]=e.baseInfo[t]}))},immediate:!0,deep:!0},"baseInfo.slider_image":{handler:function(t){},deep:!0},"baseInfo.video_link":function(t){this.formValidate.video_link=t},"baseInfo.supplier_id":function(t){t&&(this.goodsSource=2)}},created:function(){this.goodsCategory(),this.getBrandList(),this.getAllUnit(),this.getSupplierList()},methods:{getBrandList:function(){var t=this;Object(s.f)().then((function(e){t.initBran(e.data),t.brandData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},initBran:function(t){var e=this;t.map((function(t){t.value=t.value.toString(),t.children&&t.children.length&&e.initBran(t.children)}))},addBrand:function(){this.$refs.menusFrom.modals=!0,this.$refs.menusFrom.titleFrom="添加品牌分类",this.formBrand={sort:0,is_show:1},this.formBrand.fid=[0],this.$refs.menusFrom.type=1},goodsCategory:function(){var t=this;Object(s.h)(1).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},addClass:function(){var t=this;this.$modalForm(Object(s.K)()).then((function(){return t.goodsCategory()}))},getAllUnit:function(){var t=this;Object(s.B)().then((function(e){t.unitNameList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},addUnit:function(){var t=this;this.$modalForm(Object(s.mb)()).then((function(){return t.getAllUnit()}))},addStoreLabel:function(){this.$modalForm(Object(s.U)()).then((function(){}))},activeStoreData:function(t){this.storeLabelShow=!1,this.formValidate.store_label_id=t},openStoreLabel:function(t){this.storeLabelShow=!0,this.$refs.storeLabel.storeLabel(JSON.parse(JSON.stringify(this.formValidate.store_label_id)))},storeLabelClose:function(){this.storeLabelShow=!1},goodsOn:function(t){0!=t&&1!=t||(this.formValidate.auto_on_time="")},goodsOff:function(t){t||(this.formValidate.auto_off_time="")},onchangeShow:function(t){this.formValidate.auto_on_time=t},onchangeOff:function(t){this.formValidate.auto_off_time=t},delVideo:function(){this.$set(this.formValidate,"video_link","")},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var a=k(this.baseInfo.slider_image),i=a.indexOf(this.dragging),r=a.indexOf(e);a.splice.apply(a,[r,0].concat(k(a.splice(i,1)))),this.baseInfo.slider_image=a}},handleRemove:function(t){this.baseInfo.slider_image.splice(t,1)},modalPicTap:function(t){this.$emit("modalPicTap","duo")},addVideo:function(){this.$emit("modalPicTap","dan","video")},unitChange:function(t){w.a.$emit("unitChanged",t)},getSupplierList:function(){var t=this;Object(v.l)().then(function(){var e,a=(e=r.a.mark((function e(a){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.supplierList=a.data;case 1:case"end":return e.stop()}}),e)})),function(){var t=this,a=arguments;return new Promise((function(i,r){var o=e.apply(t,a);function s(t){x(o,i,r,s,n,"next",t)}function n(t){x(o,i,r,s,n,"throw",t)}s(void 0)}))});return function(t){return a.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},sourceChange:function(t){this.formValidate.supplier_id=0,this.$emit("sourceChange",t)}}},D=(a("2e12"),Object(d.a)(C,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("FormItem",{attrs:{label:"商品名称：",required:"",rules:t.ruleValidate.store_name}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请输入商品名称"},model:{value:t.formValidate.store_name,callback:function(e){t.$set(t.formValidate,"store_name",e)},expression:"formValidate.store_name"}})],1),a("FormItem",{attrs:{label:"商品分类：",required:"",rules:t.ruleValidate.cate_id}},[a("el-cascader",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请选择商品分类",size:"mini",options:t.treeSelect,props:t.props,filterable:"",clearable:""},model:{value:t.formValidate.cate_id,callback:function(e){t.$set(t.formValidate,"cate_id",e)},expression:"formValidate.cate_id"}}),a("span",{staticClass:"addClass",on:{click:t.addClass}},[t._v("新增分类")])],1),a("FormItem",{attrs:{label:"商品品牌：",prop:""}},[a("div",{staticClass:"flex"},[a("Cascader",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{data:t.brandData,placeholder:"请选择商品品牌","change-on-select":"",filterable:""},model:{value:t.formValidate.brand_id,callback:function(e){t.$set(t.formValidate,"brand_id",e)},expression:"formValidate.brand_id"}}),a("span",{staticClass:"addClass",on:{click:t.addBrand}},[t._v("新增品牌")])],1)]),a("FormItem",{attrs:{label:"单位：",required:"",rules:t.ruleValidate.unit_name}},[a("Select",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{clearable:"",filterable:"",placeholder:"请输入单位"},on:{"on-change":t.unitChange},model:{value:t.formValidate.unit_name,callback:function(e){t.$set(t.formValidate,"unit_name",e)},expression:"formValidate.unit_name"}},t._l(t.unitNameList,(function(e,i){return a("Option",{key:i,attrs:{value:e.name}},[t._v(t._s(e.name))])})),1),a("span",{staticClass:"addClass",on:{click:t.addUnit}},[t._v("新增单位")])],1),6!=t.baseInfo.product_type&&5!=t.baseInfo.product_type?a("FormItem",{attrs:{label:"商品编码：",prop:""}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请输入商品编码"},model:{value:t.formValidate.code,callback:function(e){t.$set(t.formValidate,"code",e)},expression:"formValidate.code"}})],1):t._e(),a("FormItem",{attrs:{label:"商品轮播图：",required:"",rules:t.ruleValidate.slider_image}},[a("div",{staticClass:"acea-row"},[t._l(t.baseInfo.slider_image,(function(e,i){return a("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e,expression:"item"}]}),a("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){return t.handleRemove(i)}}})],1)})),t.baseInfo.slider_image.length<10?a("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap("duo")}}},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1):t._e(),a("Input",{staticClass:"input-display",model:{value:t.baseInfo.slider_image[0],callback:function(e){t.$set(t.baseInfo.slider_image,0,e)},expression:"baseInfo.slider_image[0]"}})],2),a("div",{staticClass:"tips"},[t._v("\n      建议尺寸：800 *\n      800px，可拖拽改变图片顺序，默认首张图为主图，最多上传10张\n    ")])]),a("FormItem",{staticClass:"labelClass",attrs:{label:"商品标签："}},[a("div",{staticClass:"acea-row row-middle"},[a("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openStoreLabel}},[a("div",{staticStyle:{width:"90%"}},[t.formValidate.store_label_id.length?a("div",t._l(t.formValidate.store_label_id,(function(e,i){return a("Tag",{key:i,attrs:{closable:""},on:{"on-close":function(a){return t.closeStoreLabel(e)}}},[t._v(t._s(e.label_name))])})),1):a("span",{staticClass:"span"},[t._v("选择商品标签")])]),a("div",{staticClass:"iconfont iconxiayi"})]),a("span",{staticClass:"addClass",on:{click:t.addStoreLabel}},[t._v("新增标签")])])]),a("FormItem",{attrs:{label:"添加视频："}},[a("i-switch",{attrs:{size:"large"},model:{value:t.formValidate.video_open,callback:function(e){t.$set(t.formValidate,"video_open",e)},expression:"formValidate.video_open"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),t.formValidate.video_open?a("FormItem",{attrs:{label:"上传视频：",prop:"video_link"}},[a("div",{staticClass:"flex"},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],model:{value:t.formValidate.video_link,callback:function(e){t.$set(t.formValidate,"video_link",e)},expression:"formValidate.video_link"}}),a("span",{staticClass:"addClass",on:{click:t.addVideo}},[t._v("选择视频")])],1),t.formValidate.video_link?a("div",{staticClass:"iview-video-style"},[a("video",{staticClass:"video-style",attrs:{src:t.formValidate.video_link,controls:"controls"}}),a("div",{staticClass:"mark"}),a("Icon",{staticClass:"iconv",attrs:{type:"ios-trash-outline"},on:{click:t.delVideo}})],1):t._e()]):t._e(),a("FormItem",{attrs:{label:"上架时间："}},[a("RadioGroup",{on:{"on-change":t.goodsOn},model:{value:t.formValidate.is_show,callback:function(e){t.$set(t.formValidate,"is_show",e)},expression:"formValidate.is_show"}},[a("Radio",{attrs:{label:1}},[a("Icon",{attrs:{type:"social-apple"}}),a("span",[t._v("立即上架")])],1),a("Radio",{attrs:{label:2}},[a("Icon",{attrs:{type:"social-android"}}),a("span",[t._v("定时上架")])],1),a("Radio",{attrs:{label:0}},[a("Icon",{attrs:{type:"social-windows"}}),a("span",[t._v("放入仓库")])],1)],1)],1),2==t.formValidate.is_show?a("FormItem",{attrs:{label:""}},[a("DatePicker",{staticStyle:{width:"250px"},attrs:{type:"datetime",options:t.startPickOptions,value:t.formValidate.auto_on_time,placeholder:"请选择上架时间",format:"yyyy-MM-dd HH:mm"},on:{"on-change":t.onchangeShow},model:{value:t.formValidate.auto_on_time,callback:function(e){t.$set(t.formValidate,"auto_on_time",e)},expression:"formValidate.auto_on_time"}})],1):t._e(),a("FormItem",{attrs:{label:"定时下架："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},on:{"on-change":t.goodsOff},model:{value:t.formValidate.off_show,callback:function(e){t.$set(t.formValidate,"off_show",e)},expression:"formValidate.off_show"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),1==t.formValidate.off_show?a("FormItem",{attrs:{label:""}},[a("DatePicker",{staticStyle:{width:"260px"},attrs:{type:"datetime",options:t.endPickOptions,value:t.formValidate.auto_off_time,placeholder:"请选择下架时间",format:"yyyy-MM-dd HH:mm"},on:{"on-change":t.onchangeOff},model:{value:t.formValidate.auto_off_time,callback:function(e){t.$set(t.formValidate,"auto_off_time",e)},expression:"formValidate.auto_off_time"}}),a("div",{staticClass:"tips"},[t._v("\n      开启定时下架后，系统会在设置时间下架该商品。下架时间需晚于开售时间，商品才能定时开售。\n    ")])],1):t._e(),1!=t.baseInfo.type&&0==t.baseInfo.product_type?a("FormItem",{attrs:{label:"商品来源："}},[a("RadioGroup",{on:{"on-change":t.sourceChange},model:{value:t.goodsSource,callback:function(e){t.goodsSource=e},expression:"goodsSource"}},[a("Radio",{attrs:{label:1}},[a("span",[t._v("平台自采")])]),a("Radio",{attrs:{label:2}},[a("span",[t._v("供应商")])])],1)],1):t._e(),1!=t.baseInfo.type&&0==t.baseInfo.product_type&&2==t.goodsSource?a("FormItem",{attrs:{label:"供应商："}},[a("Select",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{clearable:""},model:{value:t.formValidate.supplier_id,callback:function(e){t.$set(t.formValidate,"supplier_id",e)},expression:"formValidate.supplier_id"}},t._l(t.supplierList,(function(e){return a("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.supplier_name))])})),1)],1):t._e(),a("Modal",{attrs:{scrollable:"",title:"选择商品标签",closable:!0,width:"540","footer-hide":!0,"mask-closable":!1},model:{value:t.storeLabelShow,callback:function(e){t.storeLabelShow=e},expression:"storeLabelShow"}},[a("storeLabelList",{ref:"storeLabel",on:{activeData:t.activeStoreData,close:t.storeLabelClose}})],1),a("menus-from",{ref:"menusFrom",attrs:{formValidate:t.formBrand,fromName:1}})],1)}),[],!1,null,"1c2acf70",null).exports),I=a("d708"),V=a("e449"),$=a("0f0e"),F=a("c4ad"),A={name:"marketingSet",props:{baseInfo:{type:Object,default:function(){return{}}},successData:{type:Boolean,default:!1}},data:function(){return{baseURL:I.a.apiBaseURL.replace(/adminapi/,""),formValidate:{ficti:0,sort:0,give_integral:0,couponName:[],coupon_ids:[],store_label_id:[],is_presale_product:0,is_limit:0,limit_type:1,limit_num:1,presale_time:[],presale_day:1,is_good:0,is_vip_product:0,label_id:[],ensure_id:[],unit_name:"",presale_status:1,recommend_list:[]},ensureData:[],labelShow:!1,goodsModals:!1,datePickerOptions:{disabledDate:function(t){return t&&t.valueOf()<Date.now()-864e5}}}},components:{couponList:V.a,userLabel:$.a,goodsList:F.default},watch:{successData:{handler:function(t){var e=this;t&&Object.keys(this.formValidate).map((function(t){e.formValidate[t]=e.baseInfo[t]}))},immediate:!0,deep:!0}},created:function(){var t=this;this.getProductAllEnsure(),w.a.$on("unitChanged",(function(e){t.formValidate.unit_name=e}))},methods:{addCoupon:function(){this.$refs.couponTemplates.isTemplate=!0,this.$refs.couponTemplates.tableList()},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},nameId:function(t,e){this.formValidate.coupon_ids=t,this.formValidate.couponName=this.unique(e)},handleClose:function(t){var e=this.formValidate.couponName.indexOf(t);this.formValidate.couponName.splice(e,1),this.formValidate.coupon_ids.splice(e,1)},openLabel:function(t){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.formValidate.label_id)))},activeData:function(t){this.labelShow=!1,this.formValidate.label_id=t},labelClose:function(){this.labelShow=!1},onchangeTime:function(t){this.formValidate.presale_time=t},goodCancel:function(){this.goodsModals=!1},goodsTap:function(){this.goodsModals=!0,this.$refs.goodslist.handleSelectAll()},bindDelete:function(t){this.formValidate.recommend_list.splice(t,1)},getProductId:function(t){this.goodsModals=!1;var e=this.formValidate.recommend_list.concat(t).filter((function(t,e,a){return a.findIndex((function(e){return e.product_id==t.product_id}))==e}));this.formValidate.recommend_list=e.slice(0,12)},getProductAllEnsure:function(){var t=this;Object(s.z)().then((function(e){t.ensureData=e.data})).catch((function(e){t.$Message.error(e.msg)}))}}},T=(a("5c4f"),Object(d.a)(A,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("FormItem",{attrs:{label:"已售数量："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:999999,placeholder:"请输入已售数量"},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1),a("FormItem",{attrs:{label:"排序："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:999999,placeholder:"请输入排序"},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1),a("FormItem",{attrs:{label:"赠送积分：",prop:"give_integral"}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:999999,placeholder:"请输入积分"},model:{value:t.formValidate.give_integral,callback:function(e){t.$set(t.formValidate,"give_integral",e)},expression:"formValidate.give_integral"}})],1),a("FormItem",{attrs:{label:"赠送优惠券："}},[t.formValidate.couponName.length?a("div",{staticClass:"mb20"},t._l(t.formValidate.couponName,(function(e,i){return a("Tag",{key:i,attrs:{type:"border",closable:""},on:{"on-close":function(a){return t.handleClose(e)}}},[t._v(t._s(e.title))])})),1):t._e(),a("Button",{attrs:{type:"primary"},on:{click:t.addCoupon}},[t._v("添加优惠券")])],1),a("FormItem",{staticClass:"labelClass",attrs:{label:"关联用户标签："}},[a("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openLabel}},[a("div",{staticStyle:{width:"90%"}},[t.formValidate.label_id.length?a("div",t._l(t.formValidate.label_id,(function(e,i){return a("Tag",{key:i,attrs:{closable:""},on:{"on-close":function(a){return t.closeLabel(e)}}},[t._v(t._s(e.label_name))])})),1):a("span",{staticClass:"span"},[t._v("选择用户关联标签")])]),a("div",{staticClass:"iconfont iconxiayi"})]),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("\n      用户购买该商品后，自动为用户关联所选标签\n    ")])]),a("FormItem",{attrs:{label:"服务保障："}},[a("CheckboxGroup",{staticClass:"checkAlls",model:{value:t.formValidate.ensure_id,callback:function(e){t.$set(t.formValidate,"ensure_id",e)},expression:"formValidate.ensure_id"}},t._l(t.ensureData,(function(e,i){return a("Checkbox",{key:i,attrs:{label:e.id}},[t._v(t._s(e.name))])})),1),a("div",{staticClass:"tips"},[a("Poptip",{attrs:{placement:"bottom",trigger:"hover",width:"256",transfer:"",padding:"8px"}},[a("a",[t._v("查看示例")]),a("div",{staticClass:"exampleImg",attrs:{slot:"content"},slot:"content"},[a("img",{attrs:{src:t.baseURL+"/statics/system/productEnsure.png",alt:""}})])])],1)],1),a("FormItem",{attrs:{label:"仅会员可见："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_vip_product,callback:function(e){t.$set(t.formValidate,"is_vip_product",e)},expression:"formValidate.is_vip_product"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n      开启后仅付费会员可以看见并购买此商品\n    ")])],1),a("FormItem",{attrs:{label:"是否限购："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_limit,callback:function(e){t.$set(t.formValidate,"is_limit",e)},expression:"formValidate.is_limit"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),t.formValidate.is_limit?a("FormItem",{attrs:{label:"限购类型："}},[a("RadioGroup",{model:{value:t.formValidate.limit_type,callback:function(e){t.$set(t.formValidate,"limit_type",e)},expression:"formValidate.limit_type"}},[a("Radio",{attrs:{label:1}},[t._v("单次限购")]),a("Radio",{attrs:{label:2}},[t._v("单人限购")])],1),a("div",{staticClass:"tips"},[t._v("\n      单次限购是限制每次下单最多购买的数量，长期限购是限制一个用户总共可以购买的数量\n    ")])],1):t._e(),t.formValidate.is_limit?a("FormItem",{attrs:{label:"限购数量："}},[a("InputNumber",{staticClass:"perW20 maxW",attrs:{min:1,placeholder:"请输入限购数量"},model:{value:t.formValidate.limit_num,callback:function(e){t.$set(t.formValidate,"limit_num",e)},expression:"formValidate.limit_num"}})],1):t._e(),0==t.baseInfo.product_type?a("FormItem",{attrs:{label:"预售商品："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_presale_product,callback:function(e){t.$set(t.formValidate,"is_presale_product",e)},expression:"formValidate.is_presale_product"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1):t._e(),0==t.baseInfo.product_type&&t.formValidate.is_presale_product?a("div",[a("FormItem",{attrs:{label:"预售活动时间：",prop:"presale_time"}},[a("div",{staticClass:"acea-row row-middle"},[a("DatePicker",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{editable:!1,options:t.datePickerOptions,type:"datetimerange",format:"yyyy-MM-dd HH:mm",placeholder:"请选择活动时间",value:t.formValidate.presale_time},on:{"on-change":t.onchangeTime},model:{value:t.formValidate.presale_time,callback:function(e){t.$set(t.formValidate,"presale_time",e)},expression:"formValidate.presale_time"}})],1),a("div",{staticClass:"tips"},[t._v("\n        设置活动开启结束时间，用户可以在设置时间内发起参与预售\n      ")])]),a("FormItem",{attrs:{label:"预计发货时间："}},[a("div",{staticClass:"acea-row row-middle"},[a("span",{staticClass:"mr10"},[t._v("预售活动结束后")]),a("InputNumber",{staticStyle:{width:"100px"},attrs:{placeholder:"请输入发货时间",precision:0,min:1},model:{value:t.formValidate.presale_day,callback:function(e){t.$set(t.formValidate,"presale_day",e)},expression:"formValidate.presale_day"}}),a("span",{staticClass:"ml10"},[t._v("天之内")])],1)]),a("FormItem",{attrs:{label:"活动结束后状态："}},[a("RadioGroup",{model:{value:t.formValidate.presale_status,callback:function(e){t.$set(t.formValidate,"presale_status",e)},expression:"formValidate.presale_status"}},[a("Radio",{attrs:{label:1}},[t._v("上架")]),a("Radio",{attrs:{label:0}},[t._v("下架")])],1),a("div",{staticClass:"tips"},[t._v("\n        选择上架时，预售活动结束后该商品作为普通商品继续售卖\n      ")])],1)],1):t._e(),a("FormItem",{attrs:{label:"优品推荐："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_good,callback:function(e){t.$set(t.formValidate,"is_good",e)},expression:"formValidate.is_good"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),a("FormItem",{attrs:{label:"选择优品推荐商品："}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.recommend_list,(function(e,i){return a("div",{key:i,staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"item.image"}]}),a("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){return t.bindDelete(i)}}})],1)})),t.formValidate.recommend_list.length<12?a("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:t.goodsTap}},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1):t._e()],2)]),a("coupon-list",{ref:"couponTemplates",attrs:{couponids:t.formValidate.coupon_ids,updateIds:t.formValidate.coupon_ids,updateName:t.formValidate.couponName},on:{nameId:t.nameId}}),a("Modal",{attrs:{scrollable:"",title:"选择用户标签",closable:!0,width:"540","footer-hide":!0,"mask-closable":!1},model:{value:t.labelShow,callback:function(e){t.labelShow=e},expression:"labelShow"}},[a("userLabel",{ref:"userLabel",on:{activeData:t.activeData,close:t.labelClose}})],1),a("Modal",{attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.goodCancel},model:{value:t.goodsModals,callback:function(e){t.goodsModals=e},expression:"goodsModals"}},[t.goodsModals?a("goods-list",{ref:"goodslist",attrs:{ischeckbox:!0},on:{getProductId:t.getProductId}}):t._e()],1)],1)}),[],!1,null,"2f3a1950",null).exports),S={name:"otherSet",props:{baseInfo:{type:Object,default:function(){return{}}},successData:{type:Boolean,default:!1}},data:function(){var t=this;return{baseURL:I.a.apiBaseURL.replace(/adminapi/,""),formValidate:{keyword:"",store_info:"",command_word:"",recommend_image:"",specs_id:"",is_support_refund:0,system_form_id:"",specs:[],product_type:""},specsData:[],customBtn:!1,specsColumns:[{title:"参数名称",key:"name",align:"center",width:150,render:function(e,a){return e("div",[e("Input",{props:{value:a.row.name,placeholder:"请输入参数名称"},on:{"on-change":function(e){a.row.name=e.target.value,t.formValidate.specs[a.index].name=e.target.value}}})])}},{title:"参数值",key:"value",align:"center",width:300,render:function(e,a){return e("div",[e("Input",{props:{value:a.row.value,placeholder:"请输入参数值"},on:{"on-change":function(e){a.row.value=e.target.value,t.formValidate.specs[a.index].value=e.target.value}}})])}},{title:"排序",key:"sort",align:"center",width:100,render:function(e,a){return e("div",[e("InputNumber",{props:{value:parseInt(a.row.sort)||0,placeholder:"排序",precision:0},on:{"on-change":function(e){a.row.sort=e,t.formValidate.specs[a.index].sort=e}}})])}},{title:"操作",slot:"action",align:"center",minWidth:120}]}},watch:{successData:{handler:function(t){var e=this;t&&(Object.keys(this.formValidate).map((function(t){e.formValidate[t]=e.baseInfo[t]})),this.customBtn=!!this.formValidate.system_form_id)},immediate:!0,deep:!0},"baseInfo.recommend_image":function(t){this.formValidate.recommend_image=t},"baseInfo.product_type":function(t){this.formValidate.product_type=t}},created:function(){this.getProductAllSpecs(),this.allFormList()},methods:{specsInfo:function(t){var e=this.specsData.find((function(e){return e.id==t}));this.formValidate.specs=e?e.specs:[]},delSpecs:function(t){this.formValidate.specs.splice(t,1),this.formValidate.specs.length||(this.formValidate.specs_id="")},addSpecs:function(){this.formValidate.specs.push({name:"",value:"",sort:0})},customMessBtn:function(t){t||(this.formValidate.system_form_id=0)},getProductAllSpecs:function(){var t=this;Object(s.A)().then((function(e){t.specsData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},modalPicTap:function(){this.$emit("modalPicTap","dan","recommend_image")},allFormList:function(){var t=this;Object(s.c)().then((function(e){t.formList=e.data})).catch((function(e){t.$Message.error(e.msg)}))}}},O=(a("4269"),Object(d.a)(S,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("FormItem",{attrs:{label:"商品关键字："}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请输入商品关键字"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}}),a("div",{staticClass:"tips"},[t._v("通过命中关键字搜索对应商品，方便用户查找")])],1),a("FormItem",{attrs:{label:"商品简介：",prop:""}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{type:"textarea",rows:3,placeholder:"请输入商品简介"},model:{value:t.formValidate.store_info,callback:function(e){t.$set(t.formValidate,"store_info",e)},expression:"formValidate.store_info"}}),a("div",{staticClass:"tips"},[t._v("\n        微信分享商品时，分享的卡片上会显示商品简介\n        "),a("Poptip",{attrs:{placement:"bottom",trigger:"hover",width:"256",transfer:"",padding:"8px"}},[a("a",[t._v("查看示例")]),a("div",{staticClass:"exampleImg",attrs:{slot:"content"},slot:"content"},[a("img",{attrs:{src:t.baseURL+"/statics/system/productDesc.png",alt:""}})])])],1)],1),a("FormItem",{attrs:{label:"商品口令："}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{type:"textarea",rows:3,placeholder:"请输入商品口令"},model:{value:t.formValidate.command_word,callback:function(e){t.$set(t.formValidate,"command_word",e)},expression:"formValidate.command_word"}}),a("div",{staticClass:"tips"},[t._v("\n        可将淘宝、抖音等平台的口令复制在此处，用户点击商城商品详情后会自动复制口令，随后打开淘宝等平台会自动弹出口令弹窗\n        "),a("Poptip",{attrs:{placement:"bottom",trigger:"hover",width:"256",transfer:"",padding:"8px"}},[a("a",[t._v("查看示例")]),a("div",{staticClass:"exampleImg",attrs:{slot:"content"},slot:"content"},[a("div",{staticStyle:{"margin-bottom":"10px","font-size":"12px","line-height":"18px",color:"#666666"}},[t._v("\n              商品口令需在淘宝、天猫、京东、苏"),a("br"),t._v("宁、1688上有同款商品，复制口令后"),a("br"),t._v("可在相关应用中打开(下图为淘宝展示)\n            ")]),a("img",{attrs:{src:t.baseURL+"/statics/system/productCommandWord.png",alt:""}})])])],1)],1),a("FormItem",{attrs:{label:"商品推荐图："}},[a("div",{staticClass:"acea-row"},[t.formValidate.recommend_image?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.recommend_image,expression:"formValidate.recommend_image"}]}),a("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){t.formValidate.recommend_image=""}}})],1):a("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap()}}},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)]),a("div",{staticClass:"tips"},[t._v("\n        在特殊的商品分类样式中显示(建议图片比例5:2)\n        "),a("Poptip",{attrs:{placement:"bottom",trigger:"hover",width:"256",transfer:"",padding:"8px"}},[a("a",[t._v("查看示例")]),a("div",{staticClass:"exampleImg",attrs:{slot:"content"},slot:"content"},[a("img",{attrs:{src:t.baseURL+"/statics/system/productRecommendImage.png",alt:""}})])])],1)]),a("FormItem",{attrs:{label:"商品参数：",prop:""}},[a("Select",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{clearable:"",filterable:"",placeholder:"请输入商品参数"},on:{"on-change":t.specsInfo},model:{value:t.formValidate.specs_id,callback:function(e){t.$set(t.formValidate,"specs_id",e)},expression:"formValidate.specs_id"}},t._l(t.specsData,(function(e,i){return a("Option",{key:i,attrs:{value:e.id}},[t._v(t._s(e.name))])})),1)],1),a("FormItem",[a("Table",{ref:"table",staticClass:"specsList",attrs:{border:"",columns:t.specsColumns,data:t.formValidate.specs,width:"700"},scopedSlots:t._u([{key:"action",fn:function(e){e.row;var i=e.index;return[a("a",{on:{click:function(e){return t.delSpecs(i)}}},[t._v("删除")])]}}])}),a("Button",{staticClass:"mt20",on:{click:t.addSpecs}},[t._v("添加参数")])],1),t.formValidate.product_type?a("FormItem",{attrs:{label:"支持退款："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_support_refund,callback:function(e){t.$set(t.formValidate,"is_support_refund",e)},expression:"formValidate.is_support_refund"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1):t._e(),a("FormItem",{attrs:{label:"自定义留言："}},[a("i-switch",{attrs:{size:"large"},on:{"on-change":t.customMessBtn},model:{value:t.customBtn,callback:function(e){t.customBtn=e},expression:"customBtn"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),a("div",{staticClass:"tips"},[t._v("请先与店铺 > 系统表单中维护表单模板")]),t.customBtn?a("div",{staticClass:"mt10"},[a("Select",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{filterable:"",placeholder:"请选择"},model:{value:t.formValidate.system_form_id,callback:function(e){t.$set(t.formValidate,"system_form_id",e)},expression:"formValidate.system_form_id"}},t._l(t.formList,(function(e,i){return a("Option",{key:i,attrs:{value:e.id}},[t._v(t._s(e.name))])})),1)],1):t._e()],1)],1)}),[],!1,null,"3419cfd1",null).exports),B=[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"img_box flex-y-center"},[e("img",{staticClass:"img",attrs:{src:a("b500")}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"flex-1"},[e("div",[this._v("商品名称展示")]),e("div",{staticClass:"mt-8 fs-10"},[this._v("有效期：XXX")])])}],N=a("b562"),P={props:{baseInfo:{type:Object,default:function(){return{}}}},data:function(){return{formData:{card_cover:1,card_cover_image:"",card_cover_color:""},colorList:[]}},watch:{"baseInfo.card_cover":function(t){this.formData.card_cover=t},"baseInfo.card_cover_image":function(t){this.formData.card_cover_image=t},"baseInfo.card_cover_color":function(t){this.formData.card_cover_color=t}},mounted:function(){this.wechatCardListApi()},methods:{wechatCardListApi:function(){var t=this;Object(N.r)().then((function(e){t.colorList=e.data.color}))},dancolor:function(t){this.formData.card_cover_color=t.value},modalPicTap:function(){this.$emit("modalPicTap","dan","card_cover_image")},getFormData:function(){var t=JSON.parse(JSON.stringify(this.formData));return 1==t.card_cover?t.card_cover_color="":t.card_cover_image="",t},cardCoverChange:function(t){2==t&&(this.formData.card_cover_color||(this.formData.card_cover_color=this.colorList[0]?this.colorList[0].value:""))},handleRemove:function(){this.$emit("handleRemove")}}},j=(a("4929"),Object(d.a)(P,(function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"flex"},[i("div",{staticClass:"card_box"},[t._m(0),i("div",{staticClass:"card_wrap",style:{backgroundColor:2==t.formData.card_cover?t.formData.card_cover_color:"",backgroundImage:1==t.formData.card_cover?"url("+(t.formData.card_cover_image||a("28a7"))+")":""}},[i("div",{staticClass:"card flex-y-center pl-14 pr-10 text-wlll-fff",class:{cover:1==t.formData.card_cover}},[i("div"),t._m(1)])])]),i("FormItem",{staticClass:"flex-1",attrs:{label:"卡片封面："}},[i("RadioGroup",{on:{"on-change":t.cardCoverChange},model:{value:t.formData.card_cover,callback:function(e){t.$set(t.formData,"card_cover",e)},expression:"formData.card_cover"}},[i("Radio",{attrs:{label:1}},[i("span",[t._v("图片")])]),i("Radio",{attrs:{label:2}},[i("span",[t._v("颜色")])])],1),i("div",{staticClass:"selst"},[1==t.formData.card_cover?i("div",[i("div",{staticClass:"acea-row row-middle"},[t.formData.card_cover_image?i("div",{staticClass:"pictrue"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formData.card_cover_image,expression:"formData.card_cover_image"}]}),i("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){return t.handleRemove(e)}}})],1):i("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap("dan",0)}}},[i("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1),i("div",{staticClass:"tips ml-16"},[t._v("建议尺寸：700 * 360px")])])]):t._e(),2==t.formData.card_cover&&t.colorList.length?i("div",[i("Poptip",{attrs:{placement:"bottom"}},[i("div",{staticClass:"color_input flex-between-center pl-8 pr-8 rd-4px"},[i("div",{staticClass:"input_color w-18 h-18 rd-2px",style:{backgroundColor:""+t.formData.card_cover_color||""+t.colorList[0].value}}),i("Icon",{attrs:{type:"ios-arrow-down",size:"14",color:"#808695"}})],1),i("div",{staticClass:"color_box",attrs:{slot:"content"},slot:"content"},t._l(t.colorList,(function(e,a){return i("div",{key:a,staticClass:"color_link",style:{backgroundColor:""+e.value},on:{click:function(a){return a.stopPropagation(),t.dancolor(e)}}})})),0)])],1):t._e()])],1)],1)}),B,!1,null,"2d20205f",null).exports),M=a("6fd3"),R=a("a069"),L=a("c276"),z=a("2e83");function E(t,e,a,i,r,o,s){try{var n=t[o](s),l=n.value}catch(t){return void a(t)}n.done?e(l):Promise.resolve(l).then(i,r)}var G={name:"addCarMy",props:{virtualList:{type:Array,default:function(){return[]}}},data:function(){return{cartMyType:1,fixedCar:{disk_info:"",stock:0},cardUrl:I.a.apiBaseURL+"/file/upload/1",header:{}}},created:function(){this.getToken()},mounted:function(){},methods:{getCarMyList:function(){var t,e=(t=r.a.mark((function t(){var e,a,i,o,s;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=[],a=[],i=[],o="",t.next=3,this.getExcelData();case 3:s=t.sent,o||(o=s.filename),a.length||(a=s.filekey),e.length||(e=s.header),i=s.export,Object(z.a)(e,a,o,i);case 9:case"end":return t.stop()}}),t,this)})),function(){var e=this,a=arguments;return new Promise((function(i,r){var o=t.apply(e,a);function s(t){E(o,i,r,s,n,"next",t)}function n(t){E(o,i,r,s,n,"throw",t)}s(void 0)}))});return function(){return e.apply(this,arguments)}}(),getExcelData:function(){return new Promise((function(t,e){Object(s.o)().then((function(e){return t(e.data)}))}))},removeVirtual:function(t){this.virtualList.splice(t,1)},upFile:function(t){var e=this;Object(s.v)({file:t.data.src}).then((function(t){e.$emit("changeVirtual",JSON.parse(JSON.stringify(t.data)))})).catch((function(t){return e.$Message.error(t.msg)}))},handleFormatError:function(t){return this.$Message.error("必须上传xlsx格式文件")},getToken:function(){this.header["Authori-zation"]="Bearer "+L.a.cookies.get("token")},cancel:function(){this.$emit("closeCarMy")},handleAdd:function(){this.virtualList.push({key:"",value:""})},beforeUpload:function(){var t=this;return new Promise((function(e){t.$nextTick((function(){e(!0)}))}))},subBtn:function(){if(1==this.cartMyType){if(this.fixedCar.cartMyType=1,""==this.fixedCar.disk_info)return this.$Message.error("请填写卡密信息");if(!this.fixedCar.stock)return this.$Message.error("请填写库存数量");this.$emit("fixdBtn",JSON.parse(JSON.stringify(this.fixedCar)))}else{for(var t={cartMyType:2,virtualList:this.virtualList},e=0;e<this.virtualList.length;e++)if(!this.virtualList[e].value)return this.$Message.error("请输入所有卡密");this.$emit("fixdBtn",JSON.parse(JSON.stringify(t)))}}}},W=(a("5b8b"),Object(d.a)(G,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"carMywrapper"},[a("div",{staticClass:"type-radio"},[a("Form",{attrs:{"label-width":80}},[a("FormItem",{attrs:{label:"卡密类型："}},[a("RadioGroup",{attrs:{size:"large"},model:{value:t.cartMyType,callback:function(e){t.cartMyType=e},expression:"cartMyType"}},[a("Radio",{attrs:{label:1}},[t._v("固定卡密")]),a("Radio",{attrs:{label:2}},[t._v("一次性卡密")])],1),1==t.cartMyType?a("div",[a("div",{staticClass:"stock-disk"},[a("Input",{attrs:{size:"large",type:"textarea",rows:4,placeholder:"填写卡密信息"},model:{value:t.fixedCar.disk_info,callback:function(e){t.$set(t.fixedCar,"disk_info",e)},expression:"fixedCar.disk_info"}})],1),a("div",{staticClass:"stock-input"},[a("Input",{attrs:{type:"number",size:"large",placeholder:"填写库存数量"},model:{value:t.fixedCar.stock,callback:function(e){t.$set(t.fixedCar,"stock",e)},expression:"fixedCar.stock"}},[a("span",{attrs:{slot:"append"},slot:"append"},[t._v("件")])])],1)]):t._e(),2==t.cartMyType?a("div",{staticClass:"scroll-virtual"},t._l(t.virtualList,(function(e,i){return a("div",{key:i,staticClass:"acea-row row-middle mb10"},[a("span",{staticClass:"mr10 virtual-title"},[t._v("卡号"+t._s(i+1)+"：")]),a("Input",{staticClass:"mr10 width15",attrs:{type:"text",placeholder:"请输入卡号(非必填)"},model:{value:e.key,callback:function(a){t.$set(e,"key","string"==typeof a?a.trim():a)},expression:"item.key"}}),a("span",{staticClass:"mr10 virtual-title"},[t._v("卡密"+t._s(i+1)+"：")]),a("Input",{staticClass:"mr10 width15",attrs:{type:"text",placeholder:"请输入卡密"},model:{value:e.value,callback:function(a){t.$set(e,"value","string"==typeof a?a.trim():a)},expression:"item.value"}}),a("span",{staticClass:"deteal-btn",on:{click:function(e){return t.removeVirtual(i)}}},[t._v("删除")])],1)})),0):t._e(),2==t.cartMyType?a("div",{staticClass:"add-more"},[a("Button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("添加卡密")]),a("Upload",{ref:"upload",staticClass:"ml10",attrs:{action:t.cardUrl,"before-upload":t.beforeUpload,headers:t.header,"on-success":t.upFile,format:["xlsx"],"on-format-error":t.handleFormatError}},[a("Button",{attrs:{type:"success"}},[t._v("导入卡密")])],1),a("Button",{staticClass:"download",attrs:{type:"default",icon:"ios-download-outline"},on:{click:t.getCarMyList}},[t._v("下载卡密模板")])],1):t._e()],1)],1)],1),a("div",{staticClass:"footer"},[a("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:t.cancel}},[t._v("取消")]),a("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确定")])],1)])}),[],!1,null,"51b45423",null).exports),q={name:"taoBao",data:function(){return{roterPre:I.a.roterPre,soure_link:"",spinShow:!1,grid:{xl:8,lg:8,md:12,sm:24,xs:24},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},copyConfig:{copy_type:"",copy_num:0},artFrom:{type:"taobao",url:""}}},computed:{},created:function(){},mounted:function(){this.getCopyConfig()},methods:{mealPay:function(t){this.$router.push({path:this.roterPre+"/setting/sms/sms_pay/index",query:{type:t}})},getCopyConfig:function(){var t=this;Object(s.k)().then((function(e){t.copyConfig.copy_type=e.data.copy_type,t.copyConfig.copy_num=e.data.copy_num}))},add:function(){var t=this;return this.soure_link?/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(this.soure_link)?(this.spinShow=!0,this.artFrom.url=this.soure_link,void Object(s.l)(this.artFrom).then((function(e){var a=e.data.productInfo;t.$emit("on-close",a),t.spinShow=!1})).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))):this.$Message.warning("请输入以http开头的地址！"):this.$Message.warning("请输入链接地址！")}}},H=(a("07c8"),Object(d.a)(q,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"Box"},[a("Card",[a("div",[t._v("生成的商品默认是没有上架的，请手动上架商品！\n            "),2==t.copyConfig.copy_type?a("a",{attrs:{href:"http://help.crmeb.net/crmeb-v4/1863579",target:"_blank"}},[t._v("如何配置密钥")]):a("span",[t._v("您当前剩余"+t._s(t.copyConfig.copy_num)+"条采集次数，"),a("a",{attrs:{href:"#"},on:{click:function(e){return t.mealPay("copy")}}},[t._v("增加采集次数")])])]),a("div",[t._v("商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置")])]),a("Form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{"label-width":120,"label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{gutter:24,type:"flex"}},[a("Col",{attrs:{span:"18"}},[a("FormItem",{attrs:{label:"链接地址："}},[a("Input",{staticClass:"numPut",attrs:{search:"","enter-button":"确定",placeholder:"请输入链接地址"},on:{"on-search":t.add},model:{value:t.soure_link,callback:function(e){t.soure_link=e},expression:"soure_link"}})],1)],1)],1)],1),t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e()],1)}),[],!1,null,"a65dd548",null).exports),U=a("5671"),Y=a("b0e7"),J=a("cf55");function Q(t,e,a,i,r,o,s){try{var n=t[o](s),l=n.value}catch(t){return void a(t)}n.done?e(l):Promise.resolve(l).then(i,r)}function K(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var o=t.apply(e,a);function s(t){Q(o,i,r,s,n,"next",t)}function n(t){Q(o,i,r,s,n,"throw",t)}s(void 0)}))}}function X(t){return function(t){if(Array.isArray(t))return t}(t)||tt(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function Z(t){return function(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}(t)||tt(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function tt(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function et(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function at(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?et(a,!0).forEach((function(e){it(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):et(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function it(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var rt={name:"productAdd",data:function(){var t=this;return{currentTab:"1",spinShow:!1,openSubimit:!1,ruleList:[],attrs:[],formData:Object.assign({},JSON.parse(JSON.stringify(c.f))),oneFormBatch:[{bar_code:"",code:"",cost:null,detail:{},settle_price:null,ot_price:null,pic:"",price:null,stock:null,weight:null,volume:null,virtual_list:[]}],openErp:!1,currentIndex:0,merchantType:0,columnsInstalM:[],manyFormValidate:[],oldVal:[],disabledSpecType:!1,createBnt:!0,formDynamic:{attrsName:"",attrsVal:""},carMyShow:!1,virtualList:[],tabIndex:0,tabName:"",success:!1,changeAttrValue:"",canSel:!0,templateList:[],template:!1,templateName:"",roterPre:I.a.roterPre,StoreTableHead:c.c,storesList:[],storeModals:!1,modalPic:!1,isChoice:"",picTit:"",tableIndex:"",modals:!1,type:0,timeoutId:null,reservationTime:[],timeCheckAll:!0,timeCheckAllGroup:[],timeCheckAllClone:[],timeDataClone:[],timeInputNumberValue:0,tooltipVisible:!1,timeColumns:[{title:"时段",slot:"time",align:"left",width:142},{title:"预约数量",slot:"stock",align:"left",minWidth:180,renderHeader:function(e,a){return e("div",[e("Poptip",{props:{transfer:!0,placement:"top",trigger:"click",value:t.tooltipVisible},on:{"on-popper-hide":function(){t.tooltipVisible=!1}},scopedSlots:{default:function(){return e("span",{on:{click:function(e){e.stopPropagation(),t.tooltipVisible=!0,t.timeInputNumberValue=0}}},[e("span","预约数量"),e("span",{class:["iconfont iconbianji11"],style:{marginLeft:"6px",color:"#AAAAAA",fontSize:"12px"}})])},content:function(){return e("div",[e("div",{class:["fs-12 text-wlll-515A6E mb-12"]},"批量修改"),e("InputNumber",{props:{min:0,max:99999999,precision:0,value:t.timeInputNumberValue},class:["w-85"],on:{"on-change":function(e){t.timeInputNumberValue=e}}}),e("Button",{props:{size:"small"},class:["ml-8"],on:{click:function(){t.tooltipVisible=!1}}},"取消"),e("Button",{props:{type:"primary",size:"small"},class:["ml-8"],on:{click:function(){t.tooltipVisible=!1,t.handleButtonClick()}}},"确定")])}}})])}}],customizeTimeData:[],specTimeData:[],specIndex:0,goodsModal:!1,cardColumns:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"product",width:210},{title:"商品规格",key:"suk"},{title:"商品类型",render:function(t,e){return t("div",e.row.product_type?"预约商品":"普通商品")}},{title:"售价",key:"price"},{slot:"write_times",renderHeader:function(e,a){return e("div",[e("span","可核销次数"),e("Poptip",{props:{transfer:!0,value:t.poptipVisible},on:{"on-popper-show":function(){t.poptipVisible=!0},"on-popper-hide":function(){t.poptipVisible=!1}},scopedSlots:{content:function(){return e("div",[e("div",{class:{"mb-12":!0,"fs-12":!0}},"批量操作"),e("div",[e("InputNumber",{props:{min:1,precision:0},on:{"on-change":function(e){t.batchWriteTimes=e}}}),e("Button",{props:{size:"small"},class:{"ml-10":!0},on:{click:function(){t.poptipVisible=!1}}},"取消"),e("Button",{props:{type:"primary",size:"small"},class:{"ml-10":!0},on:{click:function(){t.poptipVisible=!1,t.handleBatch()}}},"确认")])])}}},[e("Icon",{props:{type:"ios-create-outline"},class:{"ml-3":!0,cup:!0,"fs-14":!0},style:{display:t.cardData.length?"inline-block":"none"}})])])}},{title:"操作",slot:"action"}],cardData:[],poptipVisible:!1,batchWriteTimes:1,description:"",section_time:[],cardDataSelection:[],goodsSource:1,isVip:1,levelType:1,attrVipPrice:[]}},components:{stockSet:p,freightTemplate:m.a,productBaseSet:D,reservationSet:h,marketingSet:T,otherSet:O,cardFaceSet:j,addAttr:M.a,wangeditor:R.a,addCarMy:W,taoBao:H,draggable:y.a,storeList:U.a,uploadPictures:Y.a,goodsAttr:J.a},computed:at({},Object(o.e)("admin/layout",["isMobile","menuCollapse"]),{filterHeadTab:function(){var t=this,e=[];return 1==this.formData.product_type||3==this.formData.product_type?(e=[{title:"基础信息",name:"1"},{title:"规格库存",name:"2"},{title:"商品详情",name:"3"},{title:"营销设置",name:"5"},{title:"其他设置",name:"6"}],this.formData.postage=0):e=4==this.formData.product_type?[{title:"基础信息",name:"1"},{title:"规格库存",name:"2"},{title:"商品详情",name:"3"},{title:"适用门店",name:"7"},{title:"营销设置",name:"5"},{title:"其他设置",name:"6"}]:5==this.formData.product_type?[{title:"基础信息",name:"1"},{title:"卡项信息",name:"2"},{title:"卡面设置",name:"9"},{title:"商品详情",name:"3"},{title:"适用门店",name:"7"},{title:"营销设置",name:"5"},{title:"其他设置",name:"6"}]:6==this.formData.product_type?[{title:"基础信息",name:"1"},{title:"预约服务",name:"2"},{title:"预约设置",name:"8"},{title:"商品详情",name:"3"},{title:"适用门店",name:"7"},{title:"营销设置",name:"5"},{title:"其他设置",name:"6"}]:[{title:"基础信息",name:"1"},{title:"规格库存",name:"2"},{title:"商品详情",name:"3"},{title:"适用门店",name:"7"},{title:"物流设置",name:"4"},{title:"营销设置",name:"5"},{title:"其他设置",name:"6"}],e.filter((function(e){return 7!=e.name||7==e.name&&1!=t.merchantType}))}}),destroyed:function(){this.setCopyrightShow({value:!0})},mounted:function(){this.productGetRule(),this.productGetTemplate(),this.getErpConfig(),(this.$route.params.id||this.$route.query.copy)&&(this.changeSpec(),this.getInfo(),this.getProductBrokerage()),this.$route.query.productType&&(this.formData.product_type=Number(this.$route.query.productType)),this.$route.query.type&&-1==this.$route.query.type&&(this.modals=!0,this.type=-1)},methods:at({},Object(o.d)("admin/layout",["setCopyrightShow"]),{weekData:function(t){this.weekList=t},modalStockSet:function(t){this.manyFormValidate[this.specIndex].reservation_time_data=t},setTimeStock:function(t){this.specIndex=t;var e=this.manyFormValidate[t].reservation_time_data;e||this.$Message.error("请点击时段划分中的设置按钮"),this.specTimeData=JSON.parse(JSON.stringify(e)),this.$refs.stockSet.stockModals=!0,this.$refs.stockSet.timeInputNumberValue=0},timeDivide:function(t){this.formData.attr.reservation_time_data=[],this.formData.attr.reservation_time_data=1==t?this.timeDataClone:this.customizeTimeData,this.attrs.length&&this.generateAttr(this.attrs)},closeTime:function(t){this.formData.customize_time_period.splice(t,1)},intersection:function(t){var e=this.$hasIntersection(t),a=this.$isTimeRangesIncreasing(t);return e||!a},addTime:function(){for(var t=this.formData.customize_time_period,e=0;e<t.length;e++)if(!t[e][0])return this.$Message.error("请选择时段");if(this.intersection(t))return this.$Message.error("时段必须递增无交集");t.push([])},customizeTime:function(t){var e=this.formData.customize_time_period;e[e.length-1]=t},setCustomizeTime:function(){for(var t=this.formData.customize_time_period,e=0;e<t.length;e++)if(!t[e][0])return this.$Message.error("请选择时段");if(this.intersection(t))return this.$Message.error("时段必须递增无交集");var a=[];if(t.forEach((function(t){var e={start:t[0],end:t[1],stock:0};a.push(e)})),this.customizeTimeData=a,this.formData.attr.reservation_time_data=a,1==this.formData.spec_type&&!this.attrs.length)return this.$Message.error("请设置商品规格");this.attrs.length&&(this.$Message.success("设置成功"),this.generateAttr(this.attrs))},handleButtonClick:function(){var t=this;this.formData.attr.reservation_time_data.forEach((function(e){e.stock=t.timeInputNumberValue}))},setTime:function(){var t=[],e=this,a=this.formData.reservation_times,i=this.formData.reservation_time_interval;if(!a[0]||i<=0)return this.$Message.error("请输入起止时间或时间跨度");this.reservationTime=this.$splitTimeRange(a,i),this.reservationTime.forEach((function(e,a){t.push(e.start)})),setTimeout((function(){e.timeCheckAllGroup=t}),100),this.timeCheckAllClone=t,this.timeCheckAll=!0,this.formData.attr.reservation_time_data=this.reservationTime,this.timeDataClone=this.reservationTime,this.attrs.length&&this.generateAttr(this.attrs)},handleCheckAll:function(t){var e=[];t?(this.timeCheckAllGroup=this.timeCheckAllClone,e=this.reservationTime):(this.timeCheckAllGroup=[],e=[]),this.formData.attr.reservation_time_data=e,this.timeDataClone=e,this.attrs.length&&this.generateAttr(this.attrs)},checkAllGroupChange:function(t){t.length==this.timeCheckAllClone.length?this.timeCheckAll=!0:this.timeCheckAll=!1;var e=this.reservationTime.filter((function(e){return t.includes(e.start)}));this.formData.attr.reservation_time_data=e,this.timeDataClone=e,this.attrs.length&&this.generateAttr(this.attrs)},handleKeyPress:function(t){"."===t.key&&t.preventDefault()},handleInputChange:function(t){var e=t.target.value,a=this;(e=Number(e))<10?(clearTimeout(a.timeoutId),a.timeoutId=setTimeout((function(){a.formData.reservation_time_interval=10}),1200)):(clearTimeout(a.timeoutId),a.timeoutId=null,e>1440&&setTimeout((function(){a.formData.reservation_time_interval=1440})))},changeSpec:function(){var t=this,e=this.$route.params.id;e&&Object(s.j)(e).catch((function(e){t.disabledSpecType=!0}))},addPic:function(t,e){var a=this;t?(this.attrs.map((function(t,i){i!==e&&a.$set(t,"add_pic",0)})),this.canSel=!1):this.canSel=!0},onMoveSpec:function(){this.generateAttr(this.attrs)},changeCurrentIndex:function(t){this.currentIndex=t},generateAttr:function(t){var e=this;this.generateHeader(t);var a=this.generateCombinations(t).map((function(a){var i={attr_arr:a,detail:{},title:"",key:"",price:0,pic:"",ot_price:0,cost:0,stock:0,is_show:1,is_default_select:0,unique:"",weight:0,brokerage:0,brokerage_two:0,vip_price:0,vip_proportion:0,code:"",bar_code:"",volume:0};1==e.formData.product_type?(e.$set(i,"virtual_list",[]),e.$set(i,"disk_info","")):6==e.formData.product_type&&e.$set(i,"reservation_time_data",e.formData.attr.reservation_time_data);for(var r=0;r<a.length;r++){var o=a[r];e.$set(i,t[r].value,o),e.$set(i,"title",t[r].value),e.$set(i,"key",t[r].value),e.$set(i.detail,t[r].value,o);for(var s=0;s<e.manyFormValidate.length;s++){var l=e.manyFormValidate[s],c=Object.values(l.detail);s>0&&c&&Object(n.b)(c,a)&&(Object.assign(i,{price:l.price,cost:l.cost,ot_price:l.ot_price,stock:l.stock,pic:l.pic,unique:l.unique||"",weight:l.weight||0,is_show:l.is_show||1,is_default_select:l.is_default_select||0,volume:l.volume||0,code:l.code||"",bar_code:l.bar_code||"",is_virtual:l.is_virtual,brokerage:l.brokerage,brokerage_two:l.brokerage_two,vip_price:l.vip_price,vip_proportion:l.vip_proportion}),1==e.formData.product_type?(i.virtual_list=l.virtual_list,i.disk_info=l.disk_info):6==e.formData.product_type&&(i.reservation_time_data=e.formData.attr.reservation_time_data))}}return i}));this.$nextTick((function(){e.manyFormValidate=[].concat(Z(e.oneFormBatch),Z(a))}))},handleRemoveRole:function(t){this.attrs.splice(t,1),this.manyFormValidate.splice(t,1),this.attrs.length?this.generateAttr(this.attrs):(this.formData.header=[],this.manyFormValidate=[])},delAttrTable:function(t){for(var e=0;e<this.manyFormValidate.length;e++){var a=this.manyFormValidate[e];Object.values(a.detail)&&Object.values(a.detail).includes(t)&&(this.manyFormValidate.splice(e,1),e--)}},handleRemove2:function(t,e,a){t.splice(e,1),this.delAttrTable(a)},attrChangeValue:function(t,e){var a=this;e.trim().length&&this.attrs[t].detail.length?(this.generateHeader(this.attrs),this.manyFormValidate.length&&(this.manyFormValidate.map((function(t,i){i>0&&Object.keys(t.detail).includes(a.changeAttrValue)&&(t.detail[e]=t.detail[a.changeAttrValue],t[e]=t[a.changeAttrValue],delete t.detail[a.changeAttrValue],delete t[a.changeAttrValue])})),this.changeAttrValue=e)):this.generateAttr(this.attrs)},attrDetailChangeValue:function(t,e){var a=this;if(this.manyFormValidate.length){var i=this.attrs[e].value;this.manyFormValidate.map((function(e,r){r>0&&Object.keys(e.detail).includes(i)&&e.detail[i]===a.changeAttrValue&&(e.detail[i]=t)})),this.changeAttrValue=t}else this.generateAttr(this.attrs,1)},handleShowPop:function(t){this.$refs["inputRef_"+t][0].focus()},handleAddRole:function(){var t={value:this.formDynamic.attrsName,add_pic:0,detail:[]};this.attrs.push(t)},addOneAttr:function(){this.generateAttr(this.attrs)},changeSpecImg:function(t,e){var a=this;this.$Modal.confirm({title:"提示",content:"可以同步修改商品属性中该规格图片，确定使用吗？",onOk:function(){var i=!0,r=!1,o=void 0;try{for(var s,n=a.manyFormValidate[Symbol.iterator]();!(i=(s=n.next()).done);i=!0){var l=s.value;a.isSubset(Object.values(l.detail),t)&&a.$set(l,"pic",e)}}catch(t){r=!0,o=t}finally{try{i||null==n.return||n.return()}finally{if(r)throw o}}}})},handleFocus:function(t){this.changeAttrValue=t},handleBlur:function(){this.changeAttrValue=""},handleSelImg:function(t,e,a){this.modalPicTap("dan","attrs",[a,e])},handleRemoveImg:function(t){t.pic=""},generateCombinations:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(0===t.length)return[a];var i=X(t),r=i[0],o=i.slice(1);return r.detail.flatMap((function(t){return e.generateCombinations(o,[].concat(Z(a),[t.value]))}))},createAttr:function(t,e){var a=this;if(t){if(this.attrs[e].detail.some((function(e){return e.value===t})))return void this.$Message.error("规格值已存在");this.attrs[e].detail.push({value:t,pic:""}),this.manyFormValidate.length?this.addOneAttr(this.attrs[e].value,t):this.generateAttr(this.attrs),this.$refs["popoverRef_"+e][0].doClose(),this.formDynamic.attrsName="",this.formDynamic.attrsVal="",setTimeout((function(){a.$refs["popoverRef_"+e]&&a.$refs["popoverRef_"+e][0].doShow()}),20)}else this.$refs["popoverRef_"+e][0].doClose()},productGetRule:function(){var t=this;Object(s.P)().then((function(e){t.ruleList=e.data}))},changeDefaultSelect:function(t){this.manyFormValidate.map((function(e,a){a!==t&&(e.is_default_select=0)}))},changeDefaultShow:function(t){1===this.manyFormValidate[t].is_default_select&&(this.manyFormValidate[t].is_show=1,this.$message.error("默认规格不可隐藏"))},tableCellClassName:function(t){var e=t.row,a=t.column,i=t.rowIndex,r=t.columnIndex;e.index=i||"",a.index=r},objectSpanMethod:function(t){t.row;var e=t.column,a=t.rowIndex;if(0===t.columnIndex&&a>0){var i=e.label,r=this.manyFormValidate[a].detail[i],o=this.manyFormValidate.findIndex((function(t,e){if(e>0)return t.detail[i]==r}));if(a==o){for(var s=1,n=o+1;n<this.manyFormValidate.length&&this.manyFormValidate[n].detail[i]===r;n++)s++;return{rowspan:s,colspan:1}}return{rowspan:0,colspan:0}}},headerRowClassName:function(){return"custom-header-class"},batchDel:function(){this.oneFormBatch=[{bar_code:"",code:"",cost:null,detail:{},ot_price:null,settle_price:null,pic:"",price:null,stock:null,weight:null,volume:null}]},isSubset:function(t,e){var a=new Set(t),i=new Set(e),r=!0,o=!1,s=void 0;try{for(var n,l=i[Symbol.iterator]();!(r=(n=l.next()).done);r=!0){var c=n.value;if(!a.has(c))return!1}}catch(t){o=!0,s=t}finally{try{r||null==l.return||l.return()}finally{if(o)throw s}}return!0},batchAdd:function(){var t=[],e=!0,a=!1,i=void 0;try{for(var r,o=this.attrs[Symbol.iterator]();!(e=(r=o.next()).done);e=!0){var s=r.value;this.oneFormBatch[0][s.value]&&t.push(this.oneFormBatch[0][s.value])}}catch(t){a=!0,i=t}finally{try{e||null==o.return||o.return()}finally{if(a)throw i}}var n=!0,l=!1,c=void 0;try{for(var m,u=this.manyFormValidate[Symbol.iterator]();!(n=(m=u.next()).done);n=!0){var d=m.value;t.length?this.isSubset(d.attr_arr,t)&&(this.oneFormBatch[0].pic&&this.$set(d,"pic",this.oneFormBatch[0].pic),this.$set(d,"price",this.oneFormBatch[0].price),this.oneFormBatch[0].price,null!==this.oneFormBatch[0].settle_price&&this.$set(d,"settle_price",this.oneFormBatch[0].settle_price),null!==this.oneFormBatch[0].cost&&this.$set(d,"cost",this.oneFormBatch[0].cost),null!==this.oneFormBatch[0].ot_price&&this.$set(d,"ot_price",this.oneFormBatch[0].ot_price),null!==this.oneFormBatch[0].stock&&this.$set(d,"stock",this.oneFormBatch[0].stock),this.$set(d,"bar_code",this.oneFormBatch[0].bar_code),this.$set(d,"code",this.oneFormBatch[0].code),null!==this.oneFormBatch[0].weight&&this.$set(d,"weight",this.oneFormBatch[0].weight),null!==this.oneFormBatch[0].volume&&this.$set(d,"volume",this.oneFormBatch[0].volume)):(this.oneFormBatch[0].pic&&this.$set(d,"pic",this.oneFormBatch[0].pic),null!==this.oneFormBatch[0].price&&this.$set(d,"price",this.oneFormBatch[0].price),null!==this.oneFormBatch[0].settle_price&&this.$set(d,"settle_price",this.oneFormBatch[0].settle_price),null!==this.oneFormBatch[0].cost&&this.$set(d,"cost",this.oneFormBatch[0].cost),null!==this.oneFormBatch[0].ot_price&&this.$set(d,"ot_price",this.oneFormBatch[0].ot_price),null!==this.oneFormBatch[0].stock&&this.$set(d,"stock",this.oneFormBatch[0].stock),null!==this.oneFormBatch[0].weight&&this.$set(d,"weight",this.oneFormBatch[0].weight),null!==this.oneFormBatch[0].volume&&this.$set(d,"volume",this.oneFormBatch[0].volume),this.$set(d,"bar_code",this.oneFormBatch[0].bar_code),this.$set(d,"code",this.oneFormBatch[0].code))}}catch(t){l=!0,c=t}finally{try{n||null==u.return||u.return()}finally{if(l)throw c}}},confirm:function(t){var e=this;if(this.createBnt=!0,this.formData.selectRule=t,this.formData.selectRule.trim().length<=0)return this.$Message.error("请选择属性");this.ruleList.forEach((function(t,a){t.rule_name===e.formData.selectRule&&(e.attrs=6==e.formData.product_type?t.rule_value.slice(0,1):t.rule_value,e.attrs.map((function(t){e.$set(t,"add_pic",0)})))})),this.canSel=!0,this.generateAttr(this.attrs)},addRule:function(){this.$refs.addattr.modal=!0},getEditorContent:function(t){this.formData.description=t},addVirtual:function(t,e){this.tabIndex=t,this.tabName=e,this.virtualListClear(),this.$refs.addCarMy.fixedCar={disk_info:"",stock:0},this.$refs.addCarMy.cartMyType=1,this.carMyShow=!0},seeVirtual:function(t,e,a){this.tabName=e,this.tabIndex=a,this.virtualListClear(),this.$refs.addCarMy.fixedCar={disk_info:"",stock:0},t.virtual_list&&t.virtual_list.length?(this.$refs.addCarMy.cartMyType=2,this.virtualList=t.virtual_list):t.disk_info&&(this.$refs.addCarMy.cartMyType=1,this.$refs.addCarMy.fixedCar.disk_info=t.disk_info,this.$refs.addCarMy.fixedCar.stock=t.stock),this.carMyShow=!0},closeCarMy:function(){this.carMyShow=!1},fixdBtn:function(t){1==t.cartMyType?"attr"==this.tabName?(this.formData.attr.disk_info=t.disk_info,this.formData.attr.stock=t.stock,this.formData.attr.virtual_list=[]):(this.$set(this[this.tabName][this.tabIndex],"disk_info",t.disk_info),this.$set(this[this.tabName][this.tabIndex],"stock",Number(t.stock)),this[this.tabName][this.tabIndex].virtual_list=[]):"attr"==this.tabName?(this.formData.attr.virtual_list=t.virtualList,this.formData.attr.stock=t.virtualList.length,this.formData.attr.disk_info=""):(this.$set(this[this.tabName][this.tabIndex],"virtual_list",t.virtualList),this.$set(this[this.tabName][this.tabIndex],"stock",t.virtualList.length),this[this.tabName][this.tabIndex].disk_info=""),this.carMyShow=!1},changeVirtual:function(t){this.virtualList=t},virtualListClear:function(){this.virtualList=[{key:"",value:""}]},getInfo:function(){var t=this;this.spinShow=!0,Object(s.S)(this.$route.params.id||this.$route.query.copy).then(function(){var e=K(r.a.mark((function e(a){var i;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=a.data.productInfo,t.infoData(i),t.spinShow=!1,t.success=!0;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))},infoData:function(t){var e=this;if(this.storesList=t.stores||[],this.merchantType=parseInt(t.type),this.formData.attr.reservation_time_data=[],Object.keys(this.formData).forEach((function(a){void 0!==t[a]&&(e.formData[a]=t[a])})),4!=t.product_type&&5!=t.product_type||(this.section_time=t.attr.section_time),this.description=t.description,this.attrs=t.items||[],this.attrs.map((function(t){t.add_pic&&(e.canSel=!1)})),this.formData.off_show=t.auto_off_time?1:0,this.formData.coupon_ids=this.formData.coupons.map((function(t){return t.id})),this.formData.couponName=t.coupons,this.formData.brand_id=this.formData.brand_id.map(String),this.formData.is_limit=this.formData.is_limit?1:0,this.formData.limit_type=parseInt(t.limit_type),this.formData.system_form_id=t.system_form_id,Array.isArray(t.sale_time_week)&&this.$refs.reservationSet.weekList.forEach((function(e){t.sale_time_week.forEach((function(t){e.id==t?e.selected=!0:e.selected=!1}))})),this.generateHeader(this.attrs),1==t.spec_type&&t.attrs.map((function(t){e.$set(t,"price",Number(t.price)),2==e.merchantType?e.$set(t,"cost",Number(t.settle_price)):e.$set(t,"cost",Number(t.cost)),e.$set(t,"ot_price",Number(t.ot_price)),e.$set(t,"weight",Number(t.weight)),e.$set(t,"volume",Number(t.volume)),e.$set(t,"stock",Number(t.stock))})),this.manyFormValidate=[].concat(Z(this.oneFormBatch),Z(t.attrs)),5==t.product_type){var a=t.related.map((function(t){return at({},t.productInfo.attrInfo,{store_name:t.productInfo.store_name,write_times:t.write_times})}));this.cardData=a}if(6==t.product_type){t.is_advance||(t.advance_time=1),t.is_cancel_reservation||(t.cancel_reservation_time=1),this.reservationTime=this.$splitTimeRange(t.reservation_times,t.reservation_time_interval),(t.spec_type?t.attrs[0].reservation_time_data:t.attr.reservation_time_data).forEach((function(t){e.timeCheckAllGroup.push(t.start)})),this.reservationTime.forEach((function(t){e.timeCheckAllClone.push(t.start)})),this.reservationTime.length==this.timeCheckAllGroup.length?this.timeCheckAll=!0:this.timeCheckAll=!1,0==t.customize_time_period.length&&t.customize_time_period.push([]);var i=t.attrs.length?JSON.parse(JSON.stringify(t.attrs[0].reservation_time_data)):[];i.forEach((function(t){t.stock=0})),t.spec_type?1==t.reservation_time_type?this.timeDataClone=i:this.customizeTimeData=i:1==t.reservation_time_type?this.timeDataClone=t.attr.reservation_time_data:this.customizeTimeData=t.attr.reservation_time_data}},generateHeader:function(t){var e=t.map((function(t){return{title:t.value,key:t.value,minWidth:140,fixed:"left"}}));if(0==this.formData.product_type){var a=[].concat(Z(e),Z(c.a)),i=a.findIndex((function(t){return"售价"===t.title}));-1===i||2!=this.merchantType&&2!=this.goodsSource||a.splice(i+1,0,{title:"结算价",slot:"settle_price",align:"center",minWidth:"120px"}),this.formData.header=a}else 3==this.formData.product_type?this.formData.header=[].concat(Z(e),Z(c.d)):1==this.formData.product_type?this.formData.header=[].concat(Z(e),Z(c.e)):6==this.formData.product_type&&(this.formData.header=[].concat(Z(e),Z(c.b)));this.columnsInstalM=this.formData.header},upTab:function(){var t=this,e=this.filterHeadTab.findIndex((function(e){return e.name==t.currentTab}));this.currentTab=this.filterHeadTab[e-1].name},downTab:function(t){var e=this;this.$refs[t].validate((function(t){if(t){if(2==e.formData.is_show&&!e.formData.auto_on_time)return e.$Message.warning("请填写定时上架时间");if(1==e.off_show&&!e.formData.auto_off_time)return e.$Message.warning("请填写定时下架时间");if(4==e.currentTab&&!e.formData.delivery_type.length)return e.$Message.warning("请选择配送方式");var a=e.filterHeadTab.findIndex((function(t){return t.name==e.currentTab}));e.currentTab=e.filterHeadTab[a+1].name}else e.$Message.warning("请完善数据")}))},attrPicTap:function(){this.modalPicTap("dan","attr")},setAllPic:function(){this.modalPicTap("dan","oneFormBatch")},setAttrPic:function(t){this.modalPicTap("dan","manyFormValidate",t)},getErpConfig:function(){var t=this;Object(l.a)().then((function(e){t.openErp=e.data.open_erp})).catch((function(e){t.$Message.error(e.msg)}))},productGetTemplate:function(){var t=this;Object(s.R)({id:this.formData.id}).then((function(e){t.templateList=e.data}))},addTemp:function(){this.$refs.templates.isTemplate=!0},editTemp:function(){this.$refs.templates.isTemplate=!0,this.$refs.templates.editFrom(this.formData.temp_id)},changeTemplate:function(t){this.template=t},validatePrice:function(t){var e=this;return new Promise((function(a,i){var r=new Map;e.attrVipPrice.forEach((function(t){var e=t.suk,a=t.level_price,i=t.vip_price;r.set(e,{levelPrice:a.map((function(t){var e=t.price;return Number(e)})),vipPrice:Number(i)})})),t.some((function(t){var a=t.attr_arr.join();if(!r.has(a))return!1;var i=r.get(a),o=i.levelPrice,s=i.vipPrice;return e.isVip&&t.price<s||2==e.levelType&&o.some((function(e){return t.price<e}))}))?e.$Modal.confirm({title:"提示",content:"当前设置的商品售价低于".concat(e.formData.spec_type?"部分":"","会员价，是否继续保存？"),okText:"保存",onOk:function(){return a()},onCancel:function(){return i(new Error("取消"))}}):a()}))},handleSubmit:function(){var t=K(r.a.mark((function t(){var e,a,i,o,n,l=this;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(""!=(e=this.summarizeData()).store_name){t.next=5;break}return this.currentTab="1",this.$Message.error("请添加商品名称"),t.abrupt("return");case 5:if(e.cate_id.length){t.next=9;break}return this.currentTab="1",this.$Message.error("请选择商品分类"),t.abrupt("return");case 9:if(""!=e.unit_name){t.next=13;break}return this.currentTab="1",this.$Message.error("请添加商品单位"),t.abrupt("return");case 13:if(a=[],this.storesList.forEach((function(t){a.push(t.id)})),2!=e.applicable_type||a.length){t.next=17;break}return t.abrupt("return",this.$Message.warning("适用门店-请选择适用门店"));case 17:e.applicable_store_id=a,e.type=this.type,i=[],this.$refs.reservationSet.weekList.forEach((function(t){t.selected&&i.push(t.id)})),e.sale_time_week=i,o=0;case 23:if(!(o<e.items.length)){t.next=35;break}if(!e.items[o].add_pic){t.next=32;break}n=0;case 26:if(!(n<e.items[o].detail.length)){t.next=32;break}if(e.items[o].detail[n].pic){t.next=29;break}return t.abrupt("return",this.$Message.warning("请上传规格图"));case 29:n++,t.next=26;break;case 32:o++,t.next=23;break;case 35:if(!this.isVip&&2!=this.levelType){t.next=38;break}return t.next=38,this.validatePrice(e.spec_type?e.attrs:[at({},e.attr,{attr_arr:["默认"]})]);case 38:Object(s.y)(e).then(function(){var t=K(r.a.mark((function t(e){return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:l.openSubimit=!0,l.$Message.success(e.msg),"0"===l.$route.params.id&&cacheDelete().catch((function(t){l.$Message.error(t.msg)})),setTimeout((function(){l.$router.push({path:"".concat(l.roterPre,"/product/product_list")})}),500);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){l.openSubimit=!1,l.$Message.error(t.msg)}));case 39:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),summarizeData:function(){var t=this.$refs.productBaseSet.formValidate,e=this.$refs.marketingSet.formValidate,a=at({},t,{},e,{},this.$refs.otherSet.formValidate,{},this.$refs.cardFaceSet.getFormData());return this.$route.query.copy&&this.$set(a,"id",0),this.$set(a,"slider_image",this.formData.slider_image),this.$set(a,"type",this.type),this.$set(a,"product_type",this.formData.product_type),this.$set(a,"spec_type",this.formData.spec_type),this.$set(a,"items",this.attrs),this.$set(a,"attr",this.formData.attr),this.$set(a,"attrs",this.manyFormValidate.slice(1)),this.$set(a,"description",this.formData.description),this.$set(a,"delivery_type",this.formData.delivery_type),this.$set(a,"freight",this.formData.freight),this.$set(a,"postage",this.formData.postage),this.$set(a,"temp_id",this.formData.temp_id),this.$set(a,"applicable_type",this.formData.applicable_type),this.$set(a,"reservation_time_type",this.formData.reservation_time_type),this.$set(a,"reservation_times",this.formData.reservation_times),this.$set(a,"reservation_time_interval",this.formData.reservation_time_interval),this.$set(a,"customize_time_period",this.formData.customize_time_period),this.$set(a,"reservation_type",this.formData.reservation_type),this.$set(a,"reservation_timing_type",this.formData.reservation_timing_type),this.$set(a,"is_show_stock",this.formData.is_show_stock),this.$set(a,"sale_time_type",this.formData.sale_time_type),this.$set(a,"sale_time_week",this.formData.sale_time_week),this.$set(a,"sale_time_data",this.formData.sale_time_data),this.$set(a,"show_reservation_days_type",this.formData.show_reservation_days_type),this.$set(a,"show_reservation_days",this.formData.show_reservation_days),this.$set(a,"is_advance",this.formData.is_advance),this.$set(a,"advance_time",this.formData.advance_time),this.$set(a,"is_cancel_reservation",this.formData.is_cancel_reservation),this.$set(a,"cancel_reservation_time",this.formData.cancel_reservation_time),this.$set(a,"related",this.cardData),this.$set(a,"label_id",e.label_id.map((function(t){return t.id}))),this.$set(a,"store_label_id",t.store_label_id.map((function(t){return t.id}))),this.$set(a,"recommend_list",e.recommend_list.map((function(t){return t.product_id}))),this.$set(a,"is_sync_show",this.formData.is_sync_show),this.$set(a,"is_sync_stock",this.formData.is_sync_stock),a},handleSaveAsTemplate:function(){var t=this,e=this;e.$Modal.confirm({title:"另存为模板",render:function(t){return t("div",[t("Input",{props:{placeholder:"请输入模板名称",value:""},style:{marginTop:"20px"},on:{input:function(t){e.templateName=t}}})])},onOk:function(){var a=t.attrs.map((function(t){return{value:t.value,detail:t.detail.map((function(t){return t.value}))}})),i={rule_name:e.templateName,spec:a};Object(s.rb)(i,0).then((function(t){e.$Message.success(t.msg),e.productGetRule()})).catch((function(e){t.$message.error(e.msg)}))}})},addStore:function(){this.storeModals=!0},getStoreId:function(t){this.storeModals=!1;var e=this.storesList.concat(t),a=this.unique(e);this.storesList=a},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},delte:function(t){this.storesList.splice(t,1)},modalPicTap:function(t,e,a){this.modalPic=!0,this.isChoice="dan"===t?"单选":"多选",this.picTit=e,this.tableIndex=a},getPic:function(t){switch(this.picTit){case"danFrom":this.formValidate.image=t.att_dir,this.$route.params.id||(0===this.formValidate.spec_type?this.oneFormValidate[0].pic=t.att_dir:(this.manyFormValidate.map((function(e){e.pic=t.att_dir})),this.oneFormBatch[0].pic=t.att_dir));break;case"danTable":this.oneFormValidate[this.tableIndex].pic=t.att_dir;break;case"duopi":this.oneFormBatch[this.tableIndex].pic=t.att_dir;break;case"recommend_image":this.formData.recommend_image=t.att_dir;break;case"video":this.formData.video_link=t.att_dir;break;case"manyFormValidate":this.manyFormValidate[this.tableIndex].pic=t.att_dir;break;case"oneFormBatch":this.oneFormBatch[0].pic=t.att_dir;break;case"attr":this.formData.attr.pic=t.att_dir;break;case"attrs":this.attrs[this.tableIndex[0]].detail[this.tableIndex[1]].pic=t.att_dir,this.changeSpecImg([this.attrs[this.tableIndex[0]].detail[this.tableIndex[1]].value],t.att_dir);break;case"card_cover_image":this.formData.card_cover_image=t.att_dir;break;default:this.manyFormValidate[this.tableIndex].pic=t.att_dir}this.modalPic=!1},getPicD:function(t){var e=t.map((function(t){return t.att_dir}));this.formData.slider_image=[].concat(Z(this.formData.slider_image),Z(e)),this.modalPic=!1},cancel:function(){this.$router.push({path:"".concat(this.roterPre,"/product/product_list")})},onClose:function(t){this.modals=!1,this.infoData(t),this.success=!0},getAtterId:function(t){this.goodsModal=!1;var e=new Set(this.cardData.map((function(t){return t.unique}))),a=t.filter((function(t){return!e.has(t.unique)}));a.forEach((function(t){t.write_times=1})),this.cardData=[].concat(Z(this.cardData),Z(a))},handleBatch:function(){var t=this;this.cardData.forEach((function(e){e.write_times=t.batchWriteTimes}))},cardDataRowDelete:function(t){this.cardData.splice(t,1)},handleRemoveCardCoverImage:function(){this.formData.card_cover_image=""},onchangeTime:function(t){this.formData.attr.section_time=t},cardDataChange:function(t){this.cardDataSelection=t},cardDataDelete:function(){if(this.cardDataSelection.length){var t=this.cardDataSelection.map((function(t){return t.id}));this.cardData=this.cardData.filter((function(e){return!t.includes(e.id)})),this.cardDataSelection=[]}},sourceChange:function(t){this.goodsSource=t,this.generateHeader(this.attrs)},getProductBrokerage:function(){var t=this;Object(s.H)(this.$route.params.id||this.$route.query.copy,2).then((function(e){var a=e.data,i=a.storeInfo,r=a.attrValue;t.isVip=i.is_vip,t.levelType=i.level_type,t.attrVipPrice=Object.values(r)})).catch((function(e){t.$Message.error(e.msg)}))}})},ot=(a("ac02"),Object(d.a)(rt,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"i-layout-page-header"},[a("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[a("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[a("router-link",{attrs:{to:{path:t.roterPre+"/product/product_list"}}},[a("div",{staticClass:"font-sm after-line"},[a("span",{staticClass:"iconfont iconfanhui"}),a("span",{staticClass:"pl10"},[t._v("返回")])])]),a("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑商品":"添加商品")}})],1)])],1),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"new_tab"},[a("Tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.filterHeadTab,(function(t,e){return a("TabPane",{key:e,attrs:{label:t.title,name:t.name}})})),1)],1),a("Form",{ref:"formData",staticClass:"formData mt20",attrs:{model:t.formData,"label-width":170,"label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab,expression:"currentTab === '1'"}]},[a("FormItem",{attrs:{label:"商品类型："}},[a("Select",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{value:t.formData.product_type,disabled:""}},[a("Option",{attrs:{value:0}},[t._v("普通商品")]),a("Option",{attrs:{value:1}},[t._v("卡密/网盘")]),a("Option",{attrs:{value:3}},[t._v("虚拟商品")]),a("Option",{attrs:{value:4}},[t._v("次卡商品")]),a("Option",{attrs:{value:5}},[t._v("卡项商品")]),a("Option",{attrs:{value:6}},[t._v("预约商品")])],1)],1),a("productBaseSet",{ref:"productBaseSet",attrs:{successData:t.success,baseInfo:t.formData},on:{modalPicTap:t.modalPicTap,sourceChange:t.sourceChange}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}]},[4!==t.formData.product_type&&5!=t.formData.product_type?a("FormItem",{attrs:{label:"商品规格："}},[a("div",{staticClass:"flex-y-center"},[a("RadioGroup",{model:{value:t.formData.spec_type,callback:function(e){t.$set(t.formData,"spec_type",e)},expression:"formData.spec_type"}},[a("Radio",{staticClass:"radio",attrs:{disabled:t.disabledSpecType,label:0}},[t._v("单规格")]),a("Radio",{attrs:{disabled:t.disabledSpecType,label:1}},[t._v("多规格")])],1),1==t.formData.spec_type&&t.ruleList.length?a("Dropdown",{on:{"on-click":t.confirm},scopedSlots:t._u([{key:"list",fn:function(){return[a("DropdownMenu",t._l(t.ruleList,(function(e,i){return a("DropdownItem",{key:i,attrs:{name:e.rule_name}},[t._v(t._s(e.rule_name))])})),1)]},proxy:!0}],null,!1,1627096704)},[a("span",{staticClass:"pl-14 text-blue pointer"},[t._v("\n                  选择规格模板\n                  "),a("Icon",{attrs:{type:"ios-arrow-down"}})],1)]):t._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:t.disabledSpecType,expression:"disabledSpecType"}],staticClass:"tips"},[t._v("\n              商品有活动开启，无法切换商品规格\n            ")])]):t._e(),1==t.formData.spec_type?a("div",[a("FormItem",{attrs:{label:"商品规格："}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.attrs.length,expression:"attrs.length"}],staticClass:"specifications"},[a("draggable",{attrs:{group:"specifications",list:t.attrs,handle:".move-icon",animation:"300"},on:{end:t.onMoveSpec}},t._l(t.attrs,(function(e,i){return a("div",{key:i,staticClass:"specifications-item pointer active",on:{click:function(e){return t.changeCurrentIndex(i)}}},[a("div",{staticClass:"move-icon"},[a("span",{staticClass:"iconfont icondrag2"})]),a("i",{staticClass:"del ivu-icon ivu-icon-md-close-circle",on:{click:function(e){return t.handleRemoveRole(i)}}}),a("div",{staticClass:"specifications-item-box"},[a("div",{staticClass:"lineBox"}),a("div",{staticClass:"specifications-item-name mb18"},[a("Input",{staticClass:"specifications-item-name-input",attrs:{placeholder:"规格名称",maxlength:30,"show-word-limit":""},on:{"on-change":function(a){return t.attrChangeValue(i,e.value)},"on-focus":function(a){return t.handleFocus(e.value)}},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}}),a("Checkbox",{staticClass:"ml20",attrs:{disabled:!e.add_pic&&!t.canSel,"true-value":1,"false-value":0},on:{"on-change":function(e){return t.addPic(e,i)}},model:{value:e.add_pic,callback:function(a){t.$set(e,"add_pic",a)},expression:"item.add_pic"}},[t._v("添加规格图")]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"添加规格图片, 仅支持打开一个(建议尺寸:800*800)",placement:"right"}},[a("Icon",{attrs:{type:"md-information-circle"}})],1)],1),a("div",{staticClass:"rulesBox ml30"},[a("draggable",{staticClass:"item",attrs:{list:e.detail,handle:".drag"},on:{end:t.onMoveSpec}},[t._l(e.detail,(function(r,o){return a("div",{key:o,staticClass:"mr10 spec drag relative"},[a("i",{staticClass:"del2 ivu-icon ivu-icon-md-close-circle",on:{click:function(a){return t.handleRemove2(e.detail,o,r.value)}}}),a("Input",{attrs:{placeholder:"规格值",maxlength:30,"show-word-limit":""},on:{"on-change":function(e){return t.attrDetailChangeValue(r.value,i)},"on-focus":function(e){return t.handleFocus(r.value)},"on-blur":function(e){return t.handleBlur()}},model:{value:r.value,callback:function(e){t.$set(r,"value",e)},expression:"j.value"}},[a("template",{slot:"prefix"},[a("span",{staticClass:"iconfont icondrag2"})])],2),e.add_pic?a("div",{staticClass:"img-popover"},[a("div",{staticClass:"popper-arrow"}),a("div",{staticClass:"popper",on:{click:function(e){return t.handleSelImg(r,o,i)}}},[r.pic?a("img",{staticClass:"img",attrs:{src:r.pic}}):a("i",{staticClass:"el-icon-plus"})]),r.pic?a("i",{staticClass:"img-del el-icon-error",on:{click:function(e){return t.handleRemoveImg(r)}}}):t._e()]):t._e()],1)})),a("el-popover",{ref:"popoverRef_"+i,refInFor:!0,staticStyle:{"z-index":"9"},style:{"min-height":1==e.add_pic&&e.detail.length?"121px":""},attrs:{placement:"",width:"210",trigger:"click"},on:{"after-enter":function(e){return t.handleShowPop(i)}}},[a("Input",{ref:"inputRef_"+i,refInFor:!0,attrs:{placeholder:"请输入规格值",maxlength:30,"show-word-limit":""},on:{"on-blur":function(e){return t.createAttr(t.formDynamic.attrsVal,i)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.createAttr(t.formDynamic.attrsVal,i)}},model:{value:t.formDynamic.attrsVal,callback:function(e){t.$set(t.formDynamic,"attrsVal",e)},expression:"formDynamic.attrsVal"}}),a("a",{staticClass:"addfont",attrs:{slot:"reference"},slot:"reference"},[t._v("添加规格值")])],1)],2)],1)])])})),0)],1),t.attrs.length<(6==t.formData.product_type?1:4)?a("Button",{on:{click:function(e){return t.handleAddRole()}}},[t._v("添加新规格")]):t._e(),t.attrs.length?a("Button",{staticClass:"save-btn text-wlll-2d8cf0",on:{click:function(e){return t.handleSaveAsTemplate()}}},[t._v("另存为模板")]):t._e()],1),6==t.formData.product_type?a("FormItem",{attrs:{label:"时段划分：",required:""}},[a("RadioGroup",{on:{"on-change":t.timeDivide},model:{value:t.formData.reservation_time_type,callback:function(e){t.$set(t.formData,"reservation_time_type",e)},expression:"formData.reservation_time_type"}},[a("Radio",{attrs:{label:1}},[a("Icon",{attrs:{type:"social-apple"}}),a("span",[t._v("自动划分")])],1),a("Radio",{attrs:{label:2}},[a("Icon",{attrs:{type:"social-android"}}),a("span",[t._v("自定义划分")])],1)],1),2==t.formData.reservation_time_type?a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t\t    请依照时间的先后顺序添加时段，并且时段的开始时间不得早于上一个时段的结束时间。\n\t\t\t  ")]):t._e(),1==t.formData.reservation_time_type?a("div",{staticClass:"w-full pt-24 pb-24 pl-20 pr-20 bg-w111-F9F9F9 mt-10 rd-4px"},[a("span",[t._v("起止时间：")]),a("TimePicker",{staticClass:"w-160",attrs:{format:"HH:mm",type:"timerange",placement:"bottom-end",placeholder:"请选择时间"},model:{value:t.formData.reservation_times,callback:function(e){t.$set(t.formData,"reservation_times",e)},expression:"formData.reservation_times"}}),a("span",{staticClass:"ml20"},[t._v("时间跨度：")]),a("Input",{staticClass:"w-160",attrs:{type:"number"},on:{"on-change":t.handleInputChange,"on-keypress":t.handleKeyPress},scopedSlots:t._u([{key:"suffix",fn:function(){return[a("i",{staticClass:"fs-12 text-wlll-909399 fs-normal"},[t._v("分钟")])]},proxy:!0}],null,!1,1906402206),model:{value:t.formData.reservation_time_interval,callback:function(e){t.$set(t.formData,"reservation_time_interval",e)},expression:"formData.reservation_time_interval"}}),a("span",{staticClass:"ml-20px"},[t._v("支持设置10-1440分钟")]),a("Button",{staticClass:"ml-20px",on:{click:t.setTime}},[t._v("设置")]),t.reservationTime.length?a("div",{staticClass:"mt-14 acea-row"},[a("Checkbox",{attrs:{size:"small"},on:{"on-change":t.handleCheckAll},model:{value:t.timeCheckAll,callback:function(e){t.timeCheckAll=e},expression:"timeCheckAll"}},[t._v("全选")]),a("CheckboxGroup",{staticClass:"flex-1",attrs:{size:"small"},on:{"on-change":t.checkAllGroupChange},model:{value:t.timeCheckAllGroup,callback:function(e){t.timeCheckAllGroup=e},expression:"timeCheckAllGroup"}},t._l(t.reservationTime,(function(e,i){return a("Checkbox",{staticClass:"ml-20px",attrs:{label:e.start}},[t._v("\n\t\t\t\t\t\t\t"+t._s(e.start)+"-"+t._s(e.end)+"\n\t\t\t\t\t\t")])})),1)],1):t._e()],1):a("div",{staticClass:"w-full pt-24 pb-4 pl-20 pr-20 bg-w111-F9F9F9 mt-10 rd-4px acea-row row-middle"},[t._l(t.formData.customize_time_period,(function(e,i){return a("div",{key:i,staticClass:"customize-time relative w-160 mr-20 mb-20"},[a("TimePicker",{staticClass:"w-160",attrs:{clearable:!1,format:"HH:mm",type:"timerange",placement:"bottom-end",placeholder:"请选择时间"},on:{"on-change":t.customizeTime},model:{value:t.formData.customize_time_period[i],callback:function(e){t.$set(t.formData.customize_time_period,i,e)},expression:"formData.customize_time_period[index]"}}),t.formData.customize_time_period.length>1?a("div",{staticClass:"hidden w-14 lh-14px bg--w111-ccc rd-7px absolute text-center t-f5 r-f5 z-1",on:{click:function(e){return e.stopPropagation(),t.closeTime(i)}}},[a("span",{staticClass:"iconfont iconguanbi fs-12 text--w111-fff"})]):t._e()],1)})),t.formData.customize_time_period.length<24?a("span",{staticClass:"ml-10px fs-12 text-wlll-2d8cf0 cup mb-20",on:{click:t.addTime}},[t._v("添加时段（"+t._s(t.formData.customize_time_period.length)+"/24）")]):t._e(),a("Button",{staticClass:"ml-20px mb-20",on:{click:t.setCustomizeTime}},[t._v("设置")])],2)],1):t._e(),a("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.manyFormValidate.length,expression:"manyFormValidate.length"}],attrs:{label:"商品属性：",prop:""}},[a("el-table",{staticStyle:{width:"100%"},attrs:{size:"small",data:t.manyFormValidate,"cell-class-name":t.tableCellClassName,"span-method":t.objectSpanMethod,"header-row-class-name":t.headerRowClassName,border:""}},t._l(t.formData.header,(function(e,i){return a("el-table-column",{key:i,attrs:{label:e.title,"min-width":e.minWidth||"100",fixed:e.fixed},scopedSlots:t._u([{key:"default",fn:function(i){return[0==i.$index?[e.key?[t.attrs.length&&t.attrs[i.column.index]&&t.manyFormValidate.length?a("div",[a("el-select",{attrs:{size:"small",placeholder:"请选择"+e.title,clearable:""},model:{value:t.oneFormBatch[0][e.title],callback:function(a){t.$set(t.oneFormBatch[0],e.title,a)},expression:"oneFormBatch[0][item.title]"}},t._l(t.attrs[i.column.index].detail,(function(t){return a("el-option",{key:t.value,attrs:{label:t.value,value:t.value}})})),1)],1):t._e()]:"pic"===e.slot?[a("div",{staticClass:"pictrueBox small flex-center",on:{click:function(e){return t.setAllPic()}}},[t.oneFormBatch[0].pic?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.oneFormBatch[0].pic,expression:"oneFormBatch[0].pic"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])]:"price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999,clearable:""},model:{value:t.oneFormBatch[0].price,callback:function(e){t.$set(t.oneFormBatch[0],"price",e)},expression:"oneFormBatch[0].price"}})]:t._e(),"settle_price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.oneFormBatch[0].settle_price,callback:function(e){t.$set(t.oneFormBatch[0],"settle_price",e)},expression:"oneFormBatch[0].settle_price"}})]:"cost"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999,clearable:""},model:{value:t.oneFormBatch[0].cost,callback:function(e){t.$set(t.oneFormBatch[0],"cost",e)},expression:"oneFormBatch[0].cost"}})]:"ot_price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,clearable:""},model:{value:t.oneFormBatch[0].ot_price,callback:function(e){t.$set(t.oneFormBatch[0],"ot_price",e)},expression:"oneFormBatch[0].ot_price"}})]:"stock"===e.slot?[6==t.formData.product_type?a("div",[t._v("--")]):a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,disabled:1==t.formData.virtual_type,min:0,max:9999999999,clearable:""},model:{value:t.oneFormBatch[0].stock,callback:function(e){t.$set(t.oneFormBatch[0],"stock",e)},expression:"oneFormBatch[0].stock"}})]:"fictitious"===e.slot?[t._v("\n                        --\n                      ")]:"code"===e.slot?[a("Input",{model:{value:t.oneFormBatch[0].code,callback:function(e){t.$set(t.oneFormBatch[0],"code",e)},expression:"oneFormBatch[0].code"}})]:"bar_code"===e.slot?[a("Input",{model:{value:t.oneFormBatch[0].bar_code,callback:function(e){t.$set(t.oneFormBatch[0],"bar_code",e)},expression:"oneFormBatch[0].bar_code"}})]:"weight"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,step:.1,min:0,max:9999999999,clearable:""},model:{value:t.oneFormBatch[0].weight,callback:function(e){t.$set(t.oneFormBatch[0],"weight",e)},expression:"oneFormBatch[0].weight"}})]:"volume"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,step:.1,min:0,max:9999999999,clearable:""},model:{value:t.oneFormBatch[0].volume,callback:function(e){t.$set(t.oneFormBatch[0],"volume",e)},expression:"oneFormBatch[0].volume"}})]:"selected_spec"===e.slot?[t._v("\n                        --\n                      ")]:"action"===e.slot?[a("a",{on:{click:t.batchAdd}},[t._v("批量修改")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:t.batchDel}},[t._v("清空")])]:t._e()]:[e.key?[a("div",[a("span",[t._v(t._s(i.row.detail[e.key]))])])]:t._e(),"pic"===e.slot?[a("div",{staticClass:"pictrueBox small flex-center",on:{click:function(e){return t.setAttrPic(i.$index)}}},[t.manyFormValidate[i.$index].pic?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.manyFormValidate[i.$index].pic,expression:"manyFormValidate[scope.$index].pic"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])]:t._e(),"price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].price,callback:function(e){t.$set(t.manyFormValidate[i.$index],"price",e)},expression:"manyFormValidate[scope.$index].price"}})]:t._e(),"settle_price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].settle_price,callback:function(e){t.$set(t.manyFormValidate[i.$index],"settle_price",e)},expression:"manyFormValidate[scope.$index].settle_price"}})]:"cost"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].cost,callback:function(e){t.$set(t.manyFormValidate[i.$index],"cost",e)},expression:"manyFormValidate[scope.$index].cost"}})]:"ot_price"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].ot_price,callback:function(e){t.$set(t.manyFormValidate[i.$index],"ot_price",e)},expression:"manyFormValidate[scope.$index].ot_price"}})]:"stock"===e.slot?[6==t.formData.product_type?a("div",{staticClass:"text-wlll-2d8cf0 cup",on:{click:function(e){return t.setTimeStock(i.$index)}}},[t._v("设置预约数量")]):a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,disabled:1==t.formData.product_type,min:0,max:9999999999,precision:0},model:{value:t.manyFormValidate[i.$index].stock,callback:function(e){t.$set(t.manyFormValidate[i.$index],"stock",e)},expression:"manyFormValidate[scope.$index].stock"}})]:"code"===e.slot?[a("Input",{model:{value:t.manyFormValidate[i.$index].code,callback:function(e){t.$set(t.manyFormValidate[i.$index],"code",e)},expression:"manyFormValidate[scope.$index].code"}})]:"bar_code"===e.slot?[a("Input",{model:{value:t.manyFormValidate[i.$index].bar_code,callback:function(e){t.$set(t.manyFormValidate[i.$index],"bar_code",e)},expression:"\n                            manyFormValidate[scope.$index].bar_code\n                          "}})]:"weight"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].weight,callback:function(e){t.$set(t.manyFormValidate[i.$index],"weight",e)},expression:"manyFormValidate[scope.$index].weight"}})]:"volume"===e.slot?[a("InputNumber",{staticClass:"priceBox",attrs:{controls:!1,min:0,max:9999999999},model:{value:t.manyFormValidate[i.$index].volume,callback:function(e){t.$set(t.manyFormValidate[i.$index],"volume",e)},expression:"manyFormValidate[scope.$index].volume"}})]:"fictitious"===e.slot?[i.row.virtual_list&&i.row.virtual_list.length||i.row.stock?a("span",{staticClass:"seeCatMy",on:{click:function(e){return t.seeVirtual(t.manyFormValidate[i.$index],"manyFormValidate",i.$index)}}},[t._v("已设置")]):a("Button",{on:{click:function(e){return t.addVirtual(i.$index,"manyFormValidate")}}},[t._v("添加卡密")])]:"selected_spec"===e.slot?[a("i-switch",{attrs:{"true-value":1,"false-value":0},on:{"on-change":function(e){return t.changeDefaultSelect(i.$index)}},model:{value:t.manyFormValidate[i.$index].is_default_select,callback:function(e){t.$set(t.manyFormValidate[i.$index],"is_default_select",e)},expression:"\n                            manyFormValidate[scope.$index].is_default_select\n                          "}})]:"action"===e.slot?[a("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},on:{"on-change":function(e){return t.changeDefaultShow(i.$index)}},scopedSlots:t._u([{key:"open",fn:function(){return[a("span",[t._v("显示")])]},proxy:!0},{key:"close",fn:function(){return[a("span",[t._v("隐藏")])]},proxy:!0}],null,!0),model:{value:t.manyFormValidate[i.$index].is_show,callback:function(e){t.$set(t.manyFormValidate[i.$index],"is_show",e)},expression:"manyFormValidate[scope.$index].is_show"}})]:t._e()]]}}],null,!0)})})),1)],1)],1):t._e(),0===t.formData.spec_type&&[0,1,3].includes(t.formData.product_type)?a("div",[a("FormItem",{attrs:{label:"图片：",required:""}},[a("div",{staticClass:"pictrueBox inline-block",on:{click:function(e){return t.attrPicTap()}}},[t.formData.attr.pic?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formData.attr.pic,expression:"formData.attr.pic"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])]),a("FormItem",{attrs:{label:"售价：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.price,callback:function(e){t.$set(t.formData.attr,"price",e)},expression:"formData.attr.price"}})],1),2==t.merchantType||2==t.goodsSource?a("FormItem",{attrs:{label:"结算价：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.settle_price,callback:function(e){t.$set(t.formData.attr,"settle_price",e)},expression:"formData.attr.settle_price"}})],1):t._e(),a("FormItem",{attrs:{label:"成本价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.cost,callback:function(e){t.$set(t.formData.attr,"cost",e)},expression:"formData.attr.cost"}})],1),a("FormItem",{attrs:{label:"划线价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.ot_price,callback:function(e){t.$set(t.formData.attr,"ot_price",e)},expression:"formData.attr.ot_price"}})],1),a("FormItem",{attrs:{label:"库存：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999,disabled:1==t.formData.product_type||t.openErp,precision:0},model:{value:t.formData.attr.stock,callback:function(e){t.$set(t.formData.attr,"stock",e)},expression:"formData.attr.stock"}})],1),a("FormItem",{attrs:{label:"商品编号："}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请输入商品编码"},model:{value:t.formData.attr.code,callback:function(e){t.$set(t.formData.attr,"code","string"==typeof e?e.trim():e)},expression:"formData.attr.code"}})],1),a("FormItem",{attrs:{label:"商品条形码："}},[a("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{placeholder:"请输入商品条形码"},model:{value:t.formData.attr.bar_code,callback:function(e){t.$set(t.formData.attr,"bar_code","string"==typeof e?e.trim():e)},expression:"formData.attr.bar_code"}})],1),0==t.formData.product_type?a("FormItem",{attrs:{label:"重量（KG）："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.weight,callback:function(e){t.$set(t.formData.attr,"weight",e)},expression:"formData.attr.weight"}})],1):t._e(),0==t.formData.product_type?a("FormItem",{attrs:{label:"体积(m³)："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.volume,callback:function(e){t.$set(t.formData.attr,"volume",e)},expression:"formData.attr.volume"}})],1):t._e(),1==t.formData.product_type?[a("FormItem",{attrs:{label:"卡密设置："}},[t.formData.attr.virtual_list.length||t.formData.attr.stock?a("span",{staticClass:"seeCatMy",on:{click:function(e){return t.seeVirtual(t.formData.attr,"attr")}}},[t._v("已设置")]):a("Button",{on:{click:function(e){return t.addVirtual(0,"attr")}}},[t._v("添加卡密")])],1)]:t._e()],2):t._e(),5==t.formData.product_type||4==t.formData.product_type&&0==t.formData.spec_type?a("div",[5==t.formData.product_type?a("FormItem",{attrs:{label:"图片：",required:""}},[a("div",{staticClass:"pictrueBox inline-block",on:{click:function(e){return t.attrPicTap()}}},[t.formData.attr.pic?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formData.attr.pic,expression:"formData.attr.pic"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])]):t._e(),5!=t.formData.product_type?a("FormItem",{attrs:{label:"核销次数："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:1,max:99999999,precision:0,placeholder:"请输入核销次数"},model:{value:t.formData.attr.write_times,callback:function(e){t.$set(t.formData.attr,"write_times",e)},expression:"formData.attr.write_times"}})],1):t._e(),a("FormItem",{attrs:{required:"",label:"核销时效："}},[a("RadioGroup",{model:{value:t.formData.attr.write_valid,callback:function(e){t.$set(t.formData.attr,"write_valid",e)},expression:"formData.attr.write_valid"}},[a("Radio",{attrs:{label:1}},[t._v("永久有效")]),a("Radio",{attrs:{label:2}},[t._v("购买后几天有效")]),a("Radio",{attrs:{label:3}},[t._v("固定有效期")])],1),3==t.formData.attr.write_valid?a("div",{staticClass:"tips"},[t._v("超过有效期后，商品会自动下架放入仓库")]):t._e()],1),2==t.formData.attr.write_valid?a("FormItem",{attrs:{label:"",prop:"freight"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:1,precision:0,placeholder:"请输入有效天数"},model:{value:t.formData.attr.days,callback:function(e){t.$set(t.formData.attr,"days",e)},expression:"formData.attr.days"}}),a("span",{staticClass:"ml10"},[t._v("天")])],1)]):t._e(),3==t.formData.attr.write_valid?a("FormItem",{attrs:{label:"",prop:"freight"}},[a("div",{staticClass:"acea-row row-middle"},[a("DatePicker",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{editable:!1,type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",placeholder:"请选择固定有效期"},on:{"on-change":t.onchangeTime},model:{value:t.section_time,callback:function(e){t.section_time=e},expression:"section_time"}})],1)]):t._e(),a("FormItem",{attrs:{label:"售价：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.price,callback:function(e){t.$set(t.formData.attr,"price",e)},expression:"formData.attr.price"}})],1),a("FormItem",{attrs:{label:"成本价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.cost,callback:function(e){t.$set(t.formData.attr,"cost",e)},expression:"formData.attr.cost"}})],1),a("FormItem",{attrs:{label:"划线价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.ot_price,callback:function(e){t.$set(t.formData.attr,"ot_price",e)},expression:"formData.attr.ot_price"}})],1),a("FormItem",{attrs:{label:"库存：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999,disabled:1==t.formData.product_type||t.openErp,precision:0},model:{value:t.formData.attr.stock,callback:function(e){t.$set(t.formData.attr,"stock",e)},expression:"formData.attr.stock"}})],1),5==t.formData.product_type?a("FormItem",{attrs:{label:"卡项商品：",required:""}},[a("Button",{attrs:{type:"primary"},on:{click:function(e){t.goodsModal=!0}}},[t._v("添加商品")]),a("Button",{staticClass:"ml-10",attrs:{type:"primary",disabled:!t.cardDataSelection.length},on:{click:t.cardDataDelete}},[t._v("批量删除")]),a("Table",{staticClass:"mt-20",attrs:{columns:t.cardColumns,data:t.cardData},on:{"on-selection-change":t.cardDataChange},scopedSlots:t._u([{key:"product",fn:function(e){var i=e.row;return[a("div",{staticClass:"flex-y-center"},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}]},[a("img",{staticClass:"block w-36 h-36",attrs:{src:i.image}})]),a("div",{staticClass:"line1 ml-10"},[t._v(t._s(i.store_names))])])]}},{key:"write_times",fn:function(e){var i=e.row,r=e.index;return[a("InputNumber",{attrs:{max:i.stock,min:1,readonly:i.stock<=1,precision:0},model:{value:t.cardData[r].write_times,callback:function(e){t.$set(t.cardData[r],"write_times",e)},expression:"cardData[index].write_times"}})]}},{key:"action",fn:function(e){e.row;var i=e.index;return[a("a",{on:{click:function(e){return t.cardDataRowDelete(i)}}},[t._v("删除")])]}}],null,!1,1571010609)})],1):t._e()],1):t._e(),0===t.formData.spec_type&&6==t.formData.product_type?a("div",[a("FormItem",{attrs:{label:"图片：",required:""}},[a("div",{staticClass:"pictrueBox inline-block",on:{click:function(e){return t.attrPicTap()}}},[t.formData.attr.pic?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formData.attr.pic,expression:"formData.attr.pic"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])]),a("FormItem",{attrs:{label:"售价：",required:""}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.price,callback:function(e){t.$set(t.formData.attr,"price",e)},expression:"formData.attr.price"}})],1),a("FormItem",{attrs:{label:"成本价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.cost,callback:function(e){t.$set(t.formData.attr,"cost",e)},expression:"formData.attr.cost"}})],1),a("FormItem",{attrs:{label:"划线价："}},[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formData.attr.ot_price,callback:function(e){t.$set(t.formData.attr,"ot_price",e)},expression:"formData.attr.ot_price"}})],1),a("FormItem",{attrs:{label:"时段划分：",required:""}},[a("RadioGroup",{on:{"on-change":t.timeDivide},model:{value:t.formData.reservation_time_type,callback:function(e){t.$set(t.formData,"reservation_time_type",e)},expression:"formData.reservation_time_type"}},[a("Radio",{attrs:{label:1}},[a("Icon",{attrs:{type:"social-apple"}}),a("span",[t._v("自动划分")])],1),a("Radio",{attrs:{label:2}},[a("Icon",{attrs:{type:"social-android"}}),a("span",[t._v("自定义划分")])],1)],1),2==t.formData.reservation_time_type?a("div",{staticClass:"fs-12 text--w111-999"},[t._v("\n\t\t\t    请依照时间的先后顺序添加时段，并且时段的开始时间不得早于上一个时段的结束时间。\n\t\t\t  ")]):t._e(),1==t.formData.reservation_time_type?a("div",{staticClass:"w-full pt-24 pb-24 pl-20 pr-20 bg-w111-F9F9F9 mt-10 rd-4px"},[a("span",[t._v("起止时间：")]),a("TimePicker",{staticClass:"w-160",attrs:{format:"HH:mm",type:"timerange",placement:"bottom-end",placeholder:"请选择时间"},model:{value:t.formData.reservation_times,callback:function(e){t.$set(t.formData,"reservation_times",e)},expression:"formData.reservation_times"}}),a("span",{staticClass:"ml20"},[t._v("时间跨度：")]),a("Input",{staticClass:"w-160",attrs:{type:"number"},on:{"on-change":t.handleInputChange,"on-keypress":t.handleKeyPress},scopedSlots:t._u([{key:"suffix",fn:function(){return[a("i",{staticClass:"fs-12 text-wlll-909399 fs-normal"},[t._v("分钟")])]},proxy:!0}],null,!1,1906402206),model:{value:t.formData.reservation_time_interval,callback:function(e){t.$set(t.formData,"reservation_time_interval",e)},expression:"formData.reservation_time_interval"}}),a("span",{staticClass:"ml-20px"},[t._v("支持设置10-1440分钟")]),a("Button",{staticClass:"ml-20px",on:{click:t.setTime}},[t._v("设置")]),t.reservationTime.length?a("div",{staticClass:"mt-14 acea-row"},[a("Checkbox",{attrs:{size:"small"},on:{"on-change":t.handleCheckAll},model:{value:t.timeCheckAll,callback:function(e){t.timeCheckAll=e},expression:"timeCheckAll"}},[t._v("全选")]),a("CheckboxGroup",{staticClass:"flex-1",attrs:{size:"small"},on:{"on-change":t.checkAllGroupChange},model:{value:t.timeCheckAllGroup,callback:function(e){t.timeCheckAllGroup=e},expression:"timeCheckAllGroup"}},t._l(t.reservationTime,(function(e,i){return a("Checkbox",{staticClass:"ml-20px",attrs:{label:e.start}},[t._v("\n\t\t\t\t\t\t\t"+t._s(e.start)+"-"+t._s(e.end)+"\n\t\t\t\t\t\t")])})),1)],1):t._e()],1):a("div",{staticClass:"w-full pt-24 pb-4 pl-20 pr-20 bg-w111-F9F9F9 mt-10 rd-4px acea-row row-middle"},[t._l(t.formData.customize_time_period,(function(e,i){return a("div",{key:i,staticClass:"customize-time relative w-160 mr-20 mb-20"},[a("TimePicker",{staticClass:"w-160",attrs:{clearable:!1,format:"HH:mm",type:"timerange",placement:"bottom-end",placeholder:"请选择时间"},on:{"on-change":t.customizeTime},model:{value:t.formData.customize_time_period[i],callback:function(e){t.$set(t.formData.customize_time_period,i,e)},expression:"formData.customize_time_period[index]"}}),t.formData.customize_time_period.length>1?a("div",{staticClass:"hidden w-14 lh-14px bg--w111-ccc rd-7px absolute text-center t-f5 r-f5 z-1",on:{click:function(e){return e.stopPropagation(),t.closeTime(i)}}},[a("span",{staticClass:"iconfont iconguanbi fs-12 text--w111-fff"})]):t._e()],1)})),t.formData.customize_time_period.length<24?a("span",{staticClass:"ml-10px fs-12 text-wlll-2d8cf0 cup mb-20 mr-20",on:{click:t.addTime}},[t._v("添加时段（"+t._s(t.formData.customize_time_period.length)+"/24）")]):t._e(),a("Button",{staticClass:"mb-20",on:{click:t.setCustomizeTime}},[t._v("设置")])],2)],1),a("FormItem",{attrs:{label:"库存：",required:""}},[a("Table",{staticClass:"timeTable",attrs:{columns:t.timeColumns,data:t.formData.attr.reservation_time_data,border:"","no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果","max-height":"710",width:"343"},scopedSlots:t._u([{key:"time",fn:function(e){var i=e.row;return e.index,[a("div",{staticClass:"ml-19"},[t._v(t._s(i.start)+"-"+t._s(i.end))])]}},{key:"stock",fn:function(e){e.row;var i=e.index;return[a("InputNumber",{directives:[{name:"width",rawName:"v-width",value:129,expression:"129"}],staticClass:"ml-30",attrs:{min:0,max:99999999,precision:0},model:{value:t.formData.attr.reservation_time_data[i].stock,callback:function(e){t.$set(t.formData.attr.reservation_time_data[i],"stock",e)},expression:"formData.attr.reservation_time_data[index].stock"}})]}}],null,!1,3792728964)})],1)],1):t._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"8"===t.currentTab,expression:"currentTab === '8'"}]},[a("reservationSet",{ref:"reservationSet",attrs:{baseInfo:t.formData},on:{weekData:t.weekData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.currentTab,expression:"currentTab === '3'"}]},[a("Row",{staticClass:"mb10"},[a("Col",{attrs:{span:"16"}},[a("wangeditor",{staticStyle:{width:"100%"},attrs:{content:t.description},on:{editorContent:t.getEditorContent}})],1),a("Col",{staticStyle:{width:"33%"},attrs:{span:"6"}},[a("div",{staticClass:"ifam"},[a("div",{staticClass:"content",domProps:{innerHTML:t._s(t.formData.description)}})])])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===t.currentTab,expression:"currentTab === '4'"}]},[a("FormItem",{attrs:{label:"配送方式：",prop:"",required:""}},[a("CheckboxGroup",{model:{value:t.formData.delivery_type,callback:function(e){t.$set(t.formData,"delivery_type",e)},expression:"formData.delivery_type"}},[1!=t.merchantType?a("Checkbox",{attrs:{label:"1"}},[t._v("快递")]):t._e(),a("Checkbox",{attrs:{label:"3"}},[t._v("门店配送")]),a("Checkbox",{attrs:{label:"2"}},[t._v("到店核销")])],1)],1),a("FormItem",{attrs:{label:"运费设置："}},[a("RadioGroup",{model:{value:t.formData.freight,callback:function(e){t.$set(t.formData,"freight",e)},expression:"formData.freight"}},[a("Radio",{attrs:{label:1}},[t._v("包邮")]),a("Radio",{attrs:{label:2}},[t._v("固定邮费")]),a("Radio",{attrs:{label:3}},[t._v("运费模板")])],1)],1),2==t.formData.freight?a("FormItem",{attrs:{label:"",prop:"freight"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"perW20 maxW",attrs:{min:0,placeholder:"请输入金额"},model:{value:t.formData.postage,callback:function(e){t.$set(t.formData,"postage",e)},expression:"formData.postage"}}),a("span",{staticClass:"ml10"},[t._v("元")])],1)]):t._e(),3==t.formData.freight?a("FormItem",{attrs:{label:""}},[a("div",{staticClass:"acea-row"},[a("Select",{staticClass:"perW20 maxW",attrs:{clearable:""},model:{value:t.formData.temp_id,callback:function(e){t.$set(t.formData,"temp_id",e)},expression:"formData.temp_id"}},t._l(t.templateList,(function(e,i){return a("Option",{key:i,attrs:{value:e.id}},[t._v(t._s(e.name))])})),1),t.formData.temp_id?a("Button",{staticClass:"ml15",on:{click:t.editTemp}},[t._v("查看运费模板")]):a("Button",{staticClass:"ml15",on:{click:t.addTemp}},[t._v("添加运费模板")])],1)]):t._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===t.currentTab,expression:"currentTab === '5'"}]},[a("marketingSet",{ref:"marketingSet",attrs:{successData:t.success,baseInfo:t.formData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===t.currentTab,expression:"currentTab === '6'"}]},[a("otherSet",{ref:"otherSet",attrs:{successData:t.success,baseInfo:t.formData},on:{modalPicTap:t.modalPicTap}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"7"===t.currentTab,expression:"currentTab === '7'"}]},[a("Row",[a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"适用门店：","label-width":100}},[a("RadioGroup",{staticClass:"radioGroup",model:{value:t.formData.applicable_type,callback:function(e){t.$set(t.formData,"applicable_type",e)},expression:"formData.applicable_type"}},[a("Radio",{attrs:{label:1}},[t._v("全部门店")]),a("Radio",{attrs:{label:2}},[t._v("部分门店")]),4!=t.formData.product_type&&5!=t.formData.product_type&&6!=t.formData.product_type&&-1!=t.formData.delivery_type.indexOf("1")?a("Radio",{attrs:{label:0}},[t._v("仅平台适用")]):t._e()],1),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("\n                  可选择将商品同步到哪些门店使用，"+t._s(4!=t.formData.product_type?"选择“仅平台适用“则商品不同步任何门店":"")+"\n                ")])],1),a("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.formData.applicable_type&&(!t.formData.id||t.$route.query.copy),expression:"formData.applicable_type && (!formData.id || $route.query.copy)"}],attrs:{label:"库存同步：","label-width":100}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formData.is_sync_stock,callback:function(e){t.$set(t.formData,"is_sync_stock",e)},expression:"formData.is_sync_stock"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("开启：商品创建时，您填写的库存数量将同步至所有关联门店；关闭：商品创建时，门店库存将自动设为0（需门店手动修改）。")])],1),a("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.formData.applicable_type&&(!t.formData.id||t.$route.query.copy),expression:"formData.applicable_type && (!formData.id || $route.query.copy)"}],attrs:{label:"状态同步：","label-width":100}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formData.is_sync_show,callback:function(e){t.$set(t.formData,"is_sync_show",e)},expression:"formData.is_sync_show"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),a("div",{staticClass:"fs-12 text--w111-999 mt10"},[t._v("开启：商品保存时的当前状态（上架/下架）将实时同步至所有关联门店；关闭：商品在门店的初始状态将强制设为「下架」，需手动在门店管理后台上架。")])],1)],1),2==t.formData.applicable_type?a("Col",{staticClass:"ml10",attrs:{span:"24"}},[a("Button",{attrs:{type:"primary"},on:{click:t.addStore}},[t._v("添加门店")])],1):t._e(),a("Col",{staticClass:"ml10",attrs:{span:"24"}},[2==t.formData.applicable_type?a("div",{staticClass:"storeTable"},[a("Table",{ref:"table",staticClass:"ivu-mt",attrs:{columns:t.StoreTableHead,data:t.storesList,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"image",fn:function(t){var e=t.row;return[a("img",{attrs:{src:e.image}})]}},{key:"action",fn:function(e){e.row;var i=e.index;return[a("a",{on:{click:function(e){return t.delte(i)}}},[t._v("删除")])]}}],null,!1,1468069516)})],1):t._e()])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"9"===t.currentTab,expression:"currentTab === '9'"}]},[a("cardFaceSet",{ref:"cardFaceSet",attrs:{successData:t.success,baseInfo:t.formData},on:{modalPicTap:t.modalPicTap,handleRemove:t.handleRemoveCardCoverImage}})],1)])],1),a("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"80px":"200px"},attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"flex-center"},["1"!==t.currentTab?a("Button",{on:{click:t.upTab}},[t._v("上一步")]):t._e(),"6"!==t.currentTab?a("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.downTab("formData")}}},[t._v("下一步")]):t._e(),t.$route.params.id||"6"===t.currentTab?a("Button",{staticClass:"submission",attrs:{type:"primary",disabled:t.openSubimit},on:{click:function(e){return t.handleSubmit()}}},[t._v("保存")]):t._e()],1)]),a("add-attr",{ref:"addattr",on:{getList:t.productGetRule}}),a("Modal",{attrs:{scrollable:"",title:"添加卡密",closable:"",width:"700","footer-hide":!0,"mask-closable":!1},model:{value:t.carMyShow,callback:function(e){t.carMyShow=e},expression:"carMyShow"}},[a("add-carMy",{ref:"addCarMy",attrs:{virtualList:t.virtualList},on:{changeVirtual:t.changeVirtual,fixdBtn:t.fixdBtn,closeCarMy:t.closeCarMy}})],1),a("freightTemplate",{ref:"templates",attrs:{template:t.template,merchantType:t.merchantType},on:{changeTemplate:t.changeTemplate}}),a("Modal",{attrs:{"mask-closable":!1,title:"门店列表",width:"900",closable:"",scrollable:"","footer-hide":""},model:{value:t.storeModals,callback:function(e){t.storeModals=e},expression:"storeModals"}},[a("store-list",{on:{getStoreId:t.getStoreId}})],1),a("Modal",{attrs:{title:"video"==t.picTit?"上传视频":"上传商品图","mask-closable":!1,"z-index":8,width:"960",closable:"",scrollable:"","footer-hide":""},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?a("uploadPictures",{attrs:{isType:"video"==t.picTit?2:1,isChoice:t.isChoice},on:{getPic:t.getPic,getPicD:t.getPicD}}):t._e()],1),a("Modal",{staticClass:"Box",attrs:{"class-name":"vertical-center-modal",scrollable:"","footer-hide":"",closable:"",title:"复制淘宝、天猫、京东、苏宁、1688","mask-closable":!1,width:"800",height:"500"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?a("tao-bao",{ref:"taobaos",on:{"on-close":t.onClose}}):t._e()],1),a("stockSet",{ref:"stockSet",attrs:{timeData:t.specTimeData},on:{modalStockSet:t.modalStockSet}}),a("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},model:{value:t.goodsModal,callback:function(e){t.goodsModal=e},expression:"goodsModal"}},[t.goodsModal?a("goods-attr",{ref:"goodSattr",attrs:{chooseType:90},on:{getProductId:t.getAtterId}}):t._e()],1)],1)}),[],!1,null,"4067cad4",null));e.default=ot.exports},cad2:function(t,e,a){},d766:function(t,e,a){},ed08:function(t,e,a){"use strict";function i(t,e){if(t.length!==e.length)return!1;for(var a=t.slice().sort(),i=e.slice().sort(),r=0;r<a.length;r++)if(a[r]!==i[r])return!1;return!0}a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return r}));var r=function(t,e){var a,i=e||500;return function(){var e=this,r=arguments;a&&clearTimeout(a),a=setTimeout((function(){a=null,t.apply(e,r)}),i)}}},ee87:function(t,e,a){}}]);