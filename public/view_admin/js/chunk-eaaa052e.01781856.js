(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-eaaa052e"],{4420:function(t,e,a){},"49a1":function(t,e,a){"use strict";var i=a("4420");a.n(i).a},5671:function(t,e,a){"use strict";var i=a("2f62"),r=a("73f5");function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var s={name:"index",props:{region:{type:Number,default:0}},data:function(){return{selectEquips:[],selectEquipsIds:[],modal_loading:!1,treeSelect:[],formValidate:{page:1,limit:10,cate_id:[],name:""},total:0,loading:!1,grid:{xl:10,lg:10,md:12,sm:24,xs:24},tableList:[],columns:[{type:"selection",width:60,align:"center"},{title:"ID",key:"id",width:60},{title:"门店图片",slot:"image",minWidth:80},{title:"门店名称",key:"name",minWidth:80},{title:"门店分类",key:"cate_name",minWidth:80},{title:"联系电话",key:"phone",minWidth:90},{title:"门店地址",key:"address",ellipsis:!0,minWidth:150},{title:"营业时间",key:"day_time",minWidth:120},{title:"营业状态",key:"status_name",minWidth:80}]}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(a,!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(i.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.region>0&&(this.columns.shift(),this.columns.push({title:"操作",slot:"action",width:60}))},mounted:function(){this.getList(),this.goodsCategory()},methods:{delte:function(t,e,a){var i=this,r={store_id:t.id},n={title:e,num:a,url:"store/region/del_store/".concat(this.region),method:"DELETE",ids:r};this.$modalSure(n).then((function(t){i.$Message.success(t.msg),i.tableList.splice(a,1),i.tableList.length||(i.formValidate.page=1==i.formValidate.page?1:i.formValidate.page-1),i.getList()})).catch((function(t){i.$Message.error(t.msg)}))},sortData:function(){var t=this;this.selectEquipsIds.length&&this.tableList.forEach((function(e){t.selectEquipsIds.includes(e.id)&&(e._checked=!0)}))},TableSelectRow:function(t,e){this.selectEquipsIds.includes(e.id)||(this.selectEquipsIds.push(e.id),this.selectEquips.push(e))},TableSelectCancelRow:function(t,e){var a=this.selectEquipsIds.indexOf(e.id);-1!=a&&(this.selectEquipsIds.splice(a,1),this.selectEquips.splice(a,1))},selectAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectRow(null,this.tableList[t])},cancelAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectCancelRow(null,this.tableList[t])},handleSelectAll:function(){this.$refs.table.selectAll(!1)},pageChange:function(t){this.formValidate.page=t,this.getList()},goodsCategory:function(){var t=this;Object(r.a)(1).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getList:function(){var t=this;this.loading=!0,-1==this.region&&(this.formValidate.is_region=0),this.region>0&&(this.formValidate.region_id=this.region),Object(r.K)(this.formValidate).then((function(e){t.tableList=e.data.list,t.total=e.data.count,t.sortData(),t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},ok:function(){var t=[];this.selectEquips.forEach((function(e){var a={image:e.image,product_id:e.id,store_name:e.store_name,temp_id:e.temp_id};t.push(a)})),t.length>0?this.$emit("getStoreId",this.selectEquips):this.$Message.warning("请先选择商品")},userSearchs:function(){this.formValidate.page=1,this.getList()}}},l=(a("c76c"),a("2877")),c=Object(l.a)(s,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"goodList"},[t.region<1?a("Form",{ref:"formValidate",staticClass:"tabform",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition,inline:""}},[a("FormItem",{attrs:{label:"门店分类：","label-for":"pid"}},[a("Cascader",{staticClass:"input-add",attrs:{data:t.treeSelect,placeholder:"请选择门店分类","change-on-select":"",filterable:""},model:{value:t.formValidate.cate_id,callback:function(e){t.$set(t.formValidate,"cate_id",e)},expression:"formValidate.cate_id"}})],1),a("FormItem",{attrs:{label:"门店搜索：","label-for":"name"}},[a("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入门店名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}}),a("Button",{attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("搜索")])],1)],1):t._e(),a("Table",{ref:"table",staticClass:"mr-20",class:t.region<1?"store":"",attrs:{"no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果",columns:t.columns,data:t.tableList,loading:t.loading,height:"350"},on:{"on-select-all":t.selectAll,"on-select-all-cancel":t.cancelAll,"on-select":t.TableSelectRow,"on-select-cancel":t.TableSelectCancelRow},scopedSlots:t._u([{key:"image",fn:function(t){var e=t.row;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])]}},{key:"action",fn:function(e){var i=e.row,r=e.index;return[a("a",{on:{click:function(e){return t.delte(i,"删除区域门店",r)}}},[t._v("删除")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1),t.region<1?a("div",{staticClass:"footer",attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary",size:"large",loading:t.modal_loading,long:""},on:{click:t.ok}},[t._v("提交")])],1):t._e()],1)}),[],!1,null,"1069bd13",null);e.a=c.exports},"73f5":function(t,e,a){"use strict";a.d(e,"z",(function(){return r})),a.d(e,"t",(function(){return n})),a.d(e,"r",(function(){return o})),a.d(e,"u",(function(){return s})),a.d(e,"n",(function(){return l})),a.d(e,"x",(function(){return c})),a.d(e,"H",(function(){return u})),a.d(e,"I",(function(){return d})),a.d(e,"F",(function(){return f})),a.d(e,"G",(function(){return m})),a.d(e,"g",(function(){return h})),a.d(e,"C",(function(){return p})),a.d(e,"D",(function(){return b})),a.d(e,"E",(function(){return g})),a.d(e,"P",(function(){return v})),a.d(e,"K",(function(){return _})),a.d(e,"J",(function(){return w})),a.d(e,"e",(function(){return y})),a.d(e,"O",(function(){return k})),a.d(e,"p",(function(){return x})),a.d(e,"L",(function(){return C})),a.d(e,"M",(function(){return O})),a.d(e,"N",(function(){return V})),a.d(e,"o",(function(){return P})),a.d(e,"s",(function(){return j})),a.d(e,"A",(function(){return T})),a.d(e,"q",(function(){return S})),a.d(e,"y",(function(){return D})),a.d(e,"f",(function(){return L})),a.d(e,"B",(function(){return $})),a.d(e,"b",(function(){return B})),a.d(e,"d",(function(){return I})),a.d(e,"c",(function(){return M})),a.d(e,"a",(function(){return E})),a.d(e,"i",(function(){return q})),a.d(e,"k",(function(){return A})),a.d(e,"w",(function(){return F})),a.d(e,"j",(function(){return z})),a.d(e,"v",(function(){return N})),a.d(e,"h",(function(){return R})),a.d(e,"l",(function(){return W})),a.d(e,"m",(function(){return G}));var i=a("b6bd");function r(t){return Object(i.a)({url:"merchant/store_list",method:"get",params:t})}function n(t){return Object(i.a)({url:"store/order/list",method:"get",params:t})}function o(t){return Object(i.a)({url:"store/order/chart",method:"get",params:t})}function s(t){return Object(i.a)({url:"store/refund/list",method:"get",params:t})}function l(t){return Object(i.a)({url:"/order/no_refund/".concat(t),method:"get"})}function c(t){return Object(i.a)({url:"/order/refund_integral/".concat(t),method:"get"})}function u(t){return Object(i.a)({url:"store/finance_flow/list",method:"get",params:t})}function d(t,e){return Object(i.a)({url:"store/finance_flow/mark/".concat(t),method:"put",params:e})}function f(t){return Object(i.a)({url:"store/finance_flow/fund_record",method:"get",params:t})}function m(t){return Object(i.a)({url:"store/finance_flow/fund_record_info",method:"get",params:t})}function h(t){return Object(i.a)({url:"/export/storeFinanceRecord",method:"get",params:t})}function p(t){return Object(i.a)({url:"/store/extract/list",method:"get",params:t})}function b(t,e){return Object(i.a)({url:"store/extract/mark/".concat(t),method:"post",data:e})}function g(t,e){return Object(i.a)({url:"store/extract/verify/".concat(t),method:"post",data:e})}function v(t){return Object(i.a)({url:"store/extract/transfer/".concat(t),method:"get"})}function _(t){return Object(i.a)({url:"store/store",method:"get",params:t})}function w(t){return Object(i.a)({url:"store/store/get_info/".concat(t),method:"get"})}function y(t){return Object(i.a)({url:"city",method:"get",params:t})}function k(t,e){return Object(i.a)({url:"store/store/".concat(t),method:"post",data:e})}function x(){return Object(i.a)({url:"store/store/address",method:"get"})}function C(t){return Object(i.a)({url:"store/store/login/".concat(t),method:"get"})}function O(t,e){return Object(i.a)({url:"store/store/set_show/".concat(t,"/").concat(e),method:"put"})}function V(t){return Object(i.a)({url:"store/share/order",method:"post",params:t})}function P(t){return Object(i.a)({url:"store/home/<USER>",method:"get",params:t})}function j(t){return Object(i.a)({url:"store/home/<USER>",method:"get",params:t})}function T(t){return Object(i.a)({url:"store/home/<USER>",method:"get",params:t})}function S(t){return Object(i.a)({url:"store/home/<USER>",method:"get",params:t})}function D(t){return Object(i.a)({url:"store/store/reset_admin/".concat(t),method:"get"})}function L(t,e,a){return Object(i.a)({url:"export/storeFlowExport?store_id=".concat(t,"&keyword=").concat(e,"&data=").concat(a),method:"get"})}function $(t){return Object(i.a)({url:"/store/category",params:t,method:"get"})}function B(t){return Object(i.a)({url:"/store/category/create/".concat(t),method:"get"})}function I(t){return Object(i.a)({url:"/store/category/tree/".concat(t),method:"get"})}function M(t){return Object(i.a)({url:"/store/category/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function E(t){return Object(i.a)({url:"store/category/cascader_list/".concat(t),method:"get"})}function q(t){return Object(i.a)({url:"/store/refund/detail/".concat(t),method:"get"})}function A(t){return Object(i.a)({url:"store/region",method:"get",params:t})}function F(t,e){return Object(i.a)({url:"store/region/set_alone/".concat(t,"/").concat(e),method:"put"})}function z(t){return Object(i.a)({url:"store/region/info/".concat(t),method:"get"})}function N(t,e){return Object(i.a)({url:"store/region/".concat(e),method:"post",data:t})}function R(t){return Object(i.a)({url:"store/all_region",method:"get",params:t})}function W(t){return Object(i.a)({url:"resolve/city",method:"get",params:t})}function G(t){return Object(i.a)({url:"store/region/city",method:"get",params:t})}},"939d":function(t,e,a){},"93a9":function(t,e,a){"use strict";a.r(e);var i=a("2f62"),r=a("ed08"),n=a("b0e7"),o=a("c4ad"),s=a("d708"),l=a("5671"),c=a("b7be");function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(a,!0).forEach((function(e){f(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function f(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var m={name:"storeCreate",components:{uploadPictures:n.a,goodsList:o.default,storeList:l.a},data:function(){return{roterPre:s.a.roterPre,copy:0,keyword:"",storesList:[],storeModals:!1,modalsSet:!1,checkPidList:[],isAllChecked:!1,modals:!1,searchTableData:[],tableData:[],modalPic:!1,currentTab:"1",headTab:[{name:"基础设置",type:"1"},{name:"添加商品",type:"2"},{name:"适用门店",type:"3"}],timeList:[],id:0,formValidate:{name:"",section_data:[],time_id:[],num:1,once_num:1,image:"",status:1,seckill_ids:[],applicable_type:1},ruleValidate:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],section_data:[{required:!0,type:"array",message:"请选择活动日期",trigger:"change"}],time_id:[{required:!0,type:"array",message:"请选择秒杀场次",trigger:"change"}],num:[{required:!0,type:"number",message:"请输入购买数量限制",trigger:"blur"}],once_num:[{required:!0,type:"number",message:"请输入单次购买数量限制",trigger:"blur"}]},formBatch:{price:"",quota:""},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12}}},computed:d({},Object(i.e)("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:110},labelPosition:function(){return this.isMobile?"top":"right"},labelBottom:function(){return this.isMobile?void 0:15}}),mounted:function(){this.setCopyrightShow({value:!1}),this.seckillTimeList(),this.$route.params.id&&(this.id=this.$route.params.id,this.copy=this.$route.params.copy,this.getInfo())},destroyed:function(){this.setCopyrightShow({value:!0})},methods:d({},Object(i.d)("admin/layout",["setCopyrightShow"]),{searchWord:function(){var t=this,e=[];this.tableData.forEach((function(a){-1!=a.store_name.indexOf(t.keyword)&&e.push(a)})),this.keyword?this.searchTableData=e:this.searchTableData=[]},getInfo:function(){var t=this;Object(c.yb)(this.$route.params.id).then((function(e){e.data.info.seckill_ids=[],t.formValidate=e.data.info,t.storesList=e.data.info.stores||[];var a=e.data.info.productList||[];a.forEach((function(t){t.parent=1,t.isAllChecked=t.attrValue.some((function(t){return 1==t.status}))})),t.tableData=a})).catch((function(e){t.$Message.error(e.msg)}))},delte:function(t){var e=this;this.storesList.forEach((function(a,i){t.id==a.id&&e.storesList.splice(i,1)}))},addStore:function(){this.storeModals=!0},cancelStore:function(){this.storeModals=!1},getStoreId:function(t){this.storeModals=!1;var e=this.storesList.concat(t),a=this.unique(e);this.storesList=a},batchVisibleChange:function(){this.formBatch.price="",this.formBatch.quota=""},onchangeIsShow:function(t){this.tableData.forEach((function(e){t.product_id==e.id&&(e.status=e.attrValue.some((function(t){return 1==t.status})))}))},okBatch:function(){var t=this;if(""==this.formBatch.price&&""==this.formBatch.quota)return this.$Message.error("请输入秒杀价或限量");if(!this.isAllChecked||this.tableData.length!=this.searchTableData.length&&this.searchTableData.length)for(var e=0;e<this.tableData.length;e++)for(var a=0;a<this.checkPidList.length;a++)this.tableData[e].id==this.checkPidList[a]&&this.tableData[e].attrValue.forEach((function(e){""!=t.formBatch.price&&(e.price=t.formBatch.price),""!=t.formBatch.quota&&(e.quota=t.formBatch.quota)}));else this.tableData.forEach((function(e){e.attrValue.forEach((function(e){""!=t.formBatch.price&&(e.price=t.formBatch.price),""!=t.formBatch.quota&&(e.quota=t.formBatch.quota)}))}));this.modalsSet=!1},cancelBatch:function(){this.modalsSet=!1},batchSet:function(){this.modalsSet=!0},delAll:function(){var t=this;!this.isAllChecked||this.tableData.length!=this.searchTableData.length&&this.searchTableData.length?this.tableData=this.tableData.filter((function(e){return!t.checkPidList.some((function(t){return t===e.id}))})):this.tableData=[],this.checkPidList=[],this.isAllChecked=!1},del:function(t){var e=this;if(this.searchTableData.length?(this.searchTableData.forEach((function(a,i){t.id==a.id&&e.searchTableData.splice(i,1)})),this.tableData.forEach((function(a,i){if(t.id==a.id)return e.tableData.splice(i,1)}))):this.tableData.forEach((function(a,i){if(t.id==a.id)return e.tableData.splice(i,1)})),this.isAllChecked&&!this.tableData.length)this.isAllChecked=!1,this.checkPidList=[];else{var a=this.checkPidList.indexOf(t.id);this.checkPidList.splice(a,1)}},checkboxAll:function(){this.isAllChecked=this.$refs.xTree.isAllCheckboxChecked(),this.isAllChecked||(this.checkPidList=[])},checkboxItem:function(t){var e=parseInt(t.rowid);if(t.row.product_id){var a=this.checkPidList.indexOf(t.row.product_id);-1===a||t.checked||(this.checkPidList=this.checkPidList.filter((function(e){return e!==t.row.product_id}))),-1===a&&t.checked&&this.checkPidList.push(t.row.product_id)}else{var i=this.checkPidList.indexOf(e);-1===i||t.checked||(this.checkPidList=this.checkPidList.filter((function(t){return t!==e}))),-1===i&&t.checked&&this.checkPidList.push(e)}this.isAllChecked=this.$refs.xTree.isAllCheckboxChecked()},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},getProductId:function(t){this.modals=!1;var e=JSON.parse(JSON.stringify(t));e.forEach((function(t){t.parent=1,t.status=1,t.attrValue.forEach((function(e){e.cate_name=t.cate_name,e.store_label=t.store_label,e.status=1}))}));var a=this.tableData.concat(e),i=this.unique(a);this.tableData=i},addGoods:function(){this.modals=!0},cancel:function(){this.modals=!1},modalPicTap:function(){this.modalPic=!0},getPic:function(t){this.modalPic=!1,this.formValidate.image=t.att_dir,this.$refs.formValidate.validateField("image")},delImage:function(){this.formValidate.image="",this.$refs.formValidate.validateField("image")},seckillTimeList:function(){var t=this;Object(c.Gb)().then((function(e){t.timeList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},onchangeTime:function(t){this.formValidate.section_data=t},upTab:function(){this.currentTab=(Number(this.currentTab)-1).toString()},downTab:function(t){var e=this;this.$refs[t].validate((function(t){if(t){if(""!=!e.formValidate.section_data[0])return e.$Message.warning("请选择活动日期");if(2==e.currentTab&&!e.tableData.length)return e.$Message.warning("请添加商品");e.currentTab=(Number(e.currentTab)+1).toString()}else e.$Message.warning("请完善基础信息")}))},handleSubmit:Object(r.a)((function(t){var e=this;this.$refs[t].validate((function(t){if(t){if(1==e.copy?e.formValidate.copy=1:e.formValidate.copy=0,""!=!e.formValidate.section_data[0])return e.$Message.warning("请选择活动日期");e.tableData.length||e.$Message.warning("请添加商品");var a=[];e.tableData.forEach((function(t){t.attrValue.every((function(t){return 0==t.status}))||a.push({id:t.id,attrValue:t.attrValue,status:t.status})})),e.formValidate.seckill_ids=a;var i=[];if(e.storesList.forEach((function(t){i.push(t.id)})),2==e.formValidate.applicable_type&&!i.length)return e.$Message.warning("请添加适用门店");e.formValidate.applicable_store_id=i,Object(c.xb)(e.formValidate,1==e.copy?0:e.id).then((function(t){e.$Message.success(t.msg),setTimeout((function(){e.$router.push({path:e.roterPre+"/marketing/store_seckill/list"})}),500)})).catch((function(t){e.$Message.error(t.msg)}))}else e.$Message.warning("请完善基础信息")}))})),onchangeIsAllShow:function(t){}})},h=(a("49a1"),a("2877")),p=Object(h.a)(m,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"form-submit"},[a("div",{staticClass:"i-layout-page-header"},[a("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[a("div",{attrs:{slot:"title"},slot:"title"},[a("router-link",{attrs:{to:{path:t.roterPre+"/marketing/store_seckill/list"}}},[a("div",{staticClass:"font-sm after-line"},[a("span",{staticClass:"iconfont iconfanhui"}),a("span",{staticClass:"pl10"},[t._v("返回")])])]),a("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑秒杀活动":"添加秒杀活动")}})],1)])],1),a("Card",{staticClass:"ivu-mt mb79 mh",attrs:{bordered:!1,"dis-hover":""}},[a("Tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.headTab,(function(t,e){return a("TabPane",{key:e,attrs:{label:t.name,name:t.type}})})),1),a("Form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab,expression:"currentTab === '1'"}],attrs:{gutter:24,type:"flex"}},[a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动名称：",prop:"name"}},[a("Input",{staticClass:"w_input",attrs:{placeholder:"请输入活动名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动日期：",prop:"section_data"}},[a("div",{staticClass:"acea-row row-middle"},[a("DatePicker",{staticClass:"w_input",attrs:{editable:!1,type:"daterange",format:"yyyy-MM-dd",placeholder:"请选择活动日期",value:t.formValidate.section_data},on:{"on-change":t.onchangeTime},model:{value:t.formValidate.section_data,callback:function(e){t.$set(t.formValidate,"section_data",e)},expression:"formValidate.section_data"}})],1),a("div",{staticClass:"tips"},[t._v("设置活动开启结束日期，用户可以在有效日期内参与秒杀")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"秒杀场次：",prop:"time_id"}},[a("div",{staticClass:"acea-row row-middle"},[a("Select",{staticClass:"w_input",attrs:{multiple:""},model:{value:t.formValidate.time_id,callback:function(e){t.$set(t.formValidate,"time_id",e)},expression:"formValidate.time_id"}},t._l(t.timeList,(function(e){return a("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.start_time)+" - "+t._s(e.end_time))])})),1)],1),a("div",{staticClass:"tips"},[t._v("选择产品开始时间段，该时间段内用户可参与购买；其它时间段会显示活动未开始或已结束。如活动超过一天，则活动期内，每天都会定时开启")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动限购：",prop:"num"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"w_input",attrs:{min:1,placeholder:"请输入数量限制","element-id":"num",precision:0},model:{value:t.formValidate.num,callback:function(e){t.$set(t.formValidate,"num",e)},expression:"formValidate.num"}}),a("span",{staticClass:"ml10"},[t._v("个")])],1),a("div",{staticClass:"tips"},[t._v("活动有效期内每个用户可购买该商品总数限制。例如设置为4，表示本次活动有效期内，每个用户最多可购买总数4个")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"单次限购：",prop:"once_num"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"w_input",attrs:{min:1,placeholder:"请输入单次购买数量限制","element-id":"once_num",precision:0},model:{value:t.formValidate.once_num,callback:function(e){t.$set(t.formValidate,"once_num",e)},expression:"formValidate.once_num"}}),a("span",{staticClass:"ml10"},[t._v("个")])],1),a("div",{staticClass:"tips"},[t._v("用户参与秒杀时，一次购买最大数量限制。例如设置为2，表示参与秒杀时，用户一次购买数量最大可选择2个")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动氛围框："}},[a("Row",[a("Col",{attrs:{span:"24"}},[t.formValidate.image?a("div",{staticClass:"upload-list"},[a("div",{staticClass:"upload-item"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.image,expression:"formValidate.image"}]}),a("Button",{attrs:{shape:"circle",icon:"ios-close"},on:{click:t.delImage}})],1)]):a("Button",{staticClass:"upload-select",attrs:{type:"dashed",icon:"ios-add"},on:{click:function(e){return t.modalPicTap("dan","image",1)}}})],1)],1)],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"是否开启：",props:"status","label-for":"status"}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1)],1),a("Row",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}],attrs:{gutter:24,type:"flex"}},[a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"acea-row row-between-wrapper"},[a("div",[a("Button",{attrs:{type:"primary"},on:{click:t.addGoods}},[t._v("添加商品")]),a("Button",{staticClass:"ml20",attrs:{disabled:!t.isAllChecked&&!t.checkPidList.length},on:{click:t.batchSet}},[t._v("批量设置")]),a("Button",{staticClass:"ml20",attrs:{disabled:!t.isAllChecked&&!t.checkPidList.length},on:{click:t.delAll}},[t._v("批量删除")])],1),a("div",{staticClass:"goodsWord"},[a("FormItem",{attrs:{label:"商品搜索："}},[a("Input",{staticClass:"w_input240",attrs:{placeholder:"请输入商品关键词"},on:{"on-change":t.searchWord},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)],1)])]),a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"vxeTable"},[a("vxe-table",{ref:"xTree",attrs:{border:"inner","column-config":{resizable:!0},"row-id":"id","tree-config":{children:"attrValue",reserve:!0},data:t.searchTableData.length||t.keyword?t.searchTableData:t.tableData},on:{"checkbox-all":t.checkboxAll,"checkbox-change":t.checkboxItem}},[a("vxe-column",{attrs:{type:"checkbox",title:"多选",width:"90","tree-node":""}}),a("vxe-column",{attrs:{field:"info",title:"商品信息","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("div",{staticClass:"imgPic acea-row row-middle"},[a("viewer",[a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:i.image,expression:"row.image"}]})])]),a("div",{staticClass:"info"},[a("Tooltip",{attrs:{"max-width":"200",placement:"bottom",transfer:""}},[a("span",{staticClass:"line2"},[t._v(t._s(i.store_name)+t._s(i.suk))]),a("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(i.store_name)+t._s(i.suk))])])],1)],1)]}}])}),a("vxe-column",{attrs:{field:"price",title:"秒杀价","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[1==i.parent?a("div",[t._v("——")]):a("vxe-input",{attrs:{min:"0",placeholder:"请输入秒杀价",type:"float",digits:"2",step:"1"},model:{value:i.price,callback:function(e){t.$set(i,"price",e)},expression:"row.price"}})]}}])}),a("vxe-column",{attrs:{field:"cost",title:"成本价","min-width":"80"}}),a("vxe-column",{attrs:{field:"ot_price",title:"划线价","min-width":"80"}}),a("vxe-column",{attrs:{field:"stock",title:"库存","min-width":"90"}}),a("vxe-column",{attrs:{field:"quota",title:"限量","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[1==i.parent?a("div",[t._v("——")]):a("vxe-input",{attrs:{min:"0",placeholder:"请输入限量",type:"integer"},model:{value:i.quota,callback:function(e){t.$set(i,"quota",e)},expression:"row.quota"}})]}}])}),a("vxe-column",{attrs:{field:"status",title:"是否开启","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[1==i.parent?a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:i.status,callback:function(e){t.$set(i,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("上架")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("下架")])]):a("i-switch",{attrs:{value:i.status,"true-value":1,"false-value":0,size:"large"},model:{value:i.status,callback:function(e){t.$set(i,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("上架")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("下架")])])]}}])}),a("vxe-column",{attrs:{field:"date",title:"操作","min-width":"100",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[1==i.parent?a("a",{on:{click:function(e){return t.del(i)}}},[t._v("删除")]):t._e()]}}])})],1)],1)])],1),a("Row",{directives:[{name:"show",rawName:"v-show",value:"3"===t.currentTab,expression:"currentTab === '3'"}],attrs:{gutter:24,type:"flex"}},[a("Col",{attrs:{span:"24"}},[a("RadioGroup",{model:{value:t.formValidate.applicable_type,callback:function(e){t.$set(t.formValidate,"applicable_type",e)},expression:"formValidate.applicable_type"}},[a("Radio",{attrs:{label:1}},[t._v("全部门店")]),a("Radio",{attrs:{label:2}},[t._v("部分门店")]),a("Radio",{attrs:{label:0}},[t._v("仅平台适用")])],1),a("div",{staticClass:"tips"},[t._v("可选择活动在哪些门店使用，选择“仅平台适用“则不支持门店购买")])],1),2==t.formValidate.applicable_type?a("Col",{staticClass:"mt20",attrs:{span:"24"}},[a("Button",{attrs:{type:"primary"},on:{click:t.addStore}},[t._v("添加门店")])],1):t._e(),a("Col",{attrs:{span:"24"}},[2==t.formValidate.applicable_type?a("div",{staticClass:"vxeTable"},[a("vxe-table",{ref:"xTree",attrs:{border:"inner","row-id":"id",data:t.storesList}},[a("vxe-column",{attrs:{field:"id",title:"ID","min-width":"60"}}),a("vxe-column",{attrs:{field:"info",title:"门店图片","tree-node":"","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;return[a("div",{staticClass:"imgPic acea-row row-middle"},[a("viewer",[a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])],1)]}}],null,!1,3598873617)}),a("vxe-column",{attrs:{field:"cate_name",title:"门店分类","min-width":"110"}}),a("vxe-column",{attrs:{field:"name",title:"门店名称","min-width":"110"}}),a("vxe-column",{attrs:{field:"phone",title:"联系电话","min-width":"110"}}),a("vxe-column",{attrs:{field:"address",title:"门店地址","min-width":"150"}}),a("vxe-column",{attrs:{field:"day_time",title:"营业时间","min-width":"140"}}),a("vxe-column",{attrs:{field:"status_name",title:"营业状态","min-width":"80"}}),a("vxe-column",{attrs:{field:"date",title:"操作","min-width":"100",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("a",{on:{click:function(e){return t.delte(i)}}},[t._v("删除")])]}}],null,!1,3111734551)})],1)],1):t._e()])],1)],1)],1),a("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[a("Form",[a("FormItem",["1"!==t.currentTab?a("Button",{staticStyle:{"margin-right":"10px"},on:{click:t.upTab}},[t._v("上一步")]):t._e(),"3"!==t.currentTab?a("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.downTab("formValidate")}}},[t._v("下一步")]):a("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),a("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"上传活动氛围框","mask-closable":!1,"z-index":500},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?a("uploadPictures",{attrs:{isChoice:"单选",gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic}}):t._e()],1),a("Modal",{attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?a("goods-list",{ref:"goodslist",attrs:{ischeckbox:!0,isdiy:!0,chooseType:1},on:{getProductId:t.getProductId}}):t._e()],1),a("Modal",{attrs:{title:"批量设置"},on:{"on-visible-change":t.batchVisibleChange},model:{value:t.modalsSet,callback:function(e){t.modalsSet=e},expression:"modalsSet"}},[a("Form",{ref:"formBatch",attrs:{model:t.formBatch,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"秒杀价：",prop:"price"}},[a("vxe-input",{staticClass:"w_input315",attrs:{min:"0",placeholder:"请输入秒杀价",type:"float",digits:"2",step:"1"},model:{value:t.formBatch.price,callback:function(e){t.$set(t.formBatch,"price",e)},expression:"formBatch.price"}})],1),a("FormItem",{attrs:{label:"限量：",prop:"quota"}},[a("vxe-input",{staticClass:"w_input315",attrs:{min:"0",placeholder:"请输入限量",type:"integer"},model:{value:t.formBatch.quota,callback:function(e){t.$set(t.formBatch,"quota",e)},expression:"formBatch.quota"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{on:{click:t.cancelBatch}},[t._v("取消")]),a("Button",{attrs:{type:"primary"},on:{click:t.okBatch}},[t._v("保存")])],1)],1),a("Modal",{attrs:{title:"门店列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.cancelStore},model:{value:t.storeModals,callback:function(e){t.storeModals=e},expression:"storeModals"}},[t.storeModals?a("store-list",{ref:"storelist",on:{getStoreId:t.getStoreId}}):t._e()],1)],1)}),[],!1,null,"703f0fa5",null);e.default=p.exports},c76c:function(t,e,a){"use strict";var i=a("939d");a.n(i).a},ed08:function(t,e,a){"use strict";function i(t,e){if(t.length!==e.length)return!1;for(var a=t.slice().sort(),i=e.slice().sort(),r=0;r<a.length;r++)if(a[r]!==i[r])return!1;return!0}a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return r}));var r=function(t,e){var a,i=e||500;return function(){var e=this,r=arguments;a&&clearTimeout(a),a=setTimeout((function(){a=null,t.apply(e,r)}),i)}}}}]);