(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-a71c02fc","chunk-0d5b3a92","chunk-2d0bd5d4"],{"0508":function(t,e,r){},"0866":function(t,e,r){"use strict";var a=r("0508");r.n(a).a},"0a1e":function(t,e,r){},"0fc4":function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"c",(function(){return s})),r.d(e,"b",(function(){return n}));var a=r("b6bd");function i(){return Object(a.a)({url:"erp/config",method:"get"})}function s(t){return Object(a.a)({url:"store/erp/shop",method:"get",params:t})}function n(t){return Object(a.a)({url:"product/import_erp_product",method:"post",data:t})}},"1a4a":function(t,e,r){"use strict";var a=r("7c76");r.n(a).a},"2c4a":function(t,e,r){"use strict";r.r(e);var a=r("a34a"),i=r.n(a),s=r("f8b7"),n=r("2f62"),o=r("2e83");function l(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function d(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u={data:function(){return{modal:!1,columns1:[{title:"ID",key:"id"},{title:"操作时间",key:"add_time"},{title:"发货单数",key:"total_num"},{title:"成功发货单数",key:"success_num"},{title:"发货类型",key:"title"},{title:"状态",key:"status_cn"},{title:"操作",slot:"action",width:150,align:"center"}],data1:[],page1:{total:0,pageNum:1,pageSize:10},formValidate:{type:"",status:"",data:""},options:{shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,r=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,r))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]},timeVal:[],typeList:[{label:"批量删除订单",value:"6"},{label:"批量手动发货",value:"7"},{label:"批量打印电子面单",value:"8"},{label:"批量配送",value:"9"},{label:"批量虚拟发货",value:"10"}],statusList:[{label:"未处理",value:"0"},{label:"处理中",value:"1"},{label:"已完成",value:"2"},{label:"处理失败",value:"3"}],columns2:[{title:"订单ID",key:"order_id"},{title:"物流公司",key:"delivery_name"},{title:"物流单号",key:"delivery_id"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns3:[{title:"订单ID",key:"order_id"},{title:"备注",key:"fictitious_content"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns5:[{title:"订单ID",key:"order_id"},{title:"配送员",key:"delivery_name"},{title:"配送员电话",key:"delivery_id"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns4:[],data2:[],page2:{total:0,pageNum:1,pageSize:12},modal1:!1,deliveryLog:null,deliveryLogId:0,deliveryLogType:"",loading:!1,loading2:!1}},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(r,!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(n.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getQueue()},methods:{getQueue:function(){var t=this,e={page:this.page1.pageNum,limit:this.page1.pageSize};this.formValidate.status&&(e.status=this.formValidate.status),this.formValidate.type&&(e.type=this.formValidate.type),this.formValidate.data&&(e.data=this.formValidate.data),this.loading=!0,Object(s.T)(e).then((function(e){t.loading=!1,t.data1=e.data.list,t.page1.total=e.data.count})).catch((function(e){t.loading=!1}))},pageChange:function(t){this.page1.pageNum=t,this.getQueue()},pageChange2:function(t){this.page2.pageNum=t,this.getDeliveryLog()},limitChange:function(t){this.page1.pageSize=t,this.getQueue()},limitChange2:function(t){this.page2.pageSize=t,this.getDeliveryLog()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",this.page1.pageNum=1,this.getQueue()},typeSearchs:function(){this.page1.pageNum=1,this.getQueue()},statusSearchs:function(){this.page1.pageNum=1,this.getQueue()},getDeliveryLog:function(){var t=this;this.loading2=!0,Object(s.c)(this.deliveryLogId,this.deliveryLogType,{page:this.page2.pageNum,limit:this.page2.pageSize}).then((function(e){t.loading2=!1,t.data2=e.data.list,t.page2.total=e.data.count})).catch((function(e){t.loading2=!1}))},deliveryLook:function(t){switch(this.modal1=!0,this.deliveryLogId=t.id,this.deliveryLogType=t.cache_type,this.deliveryLog=t,t.type){case 7:case 8:this.columns4=this.columns2;break;case 9:this.columns4=this.columns5;break;case 10:this.columns4=this.columns3}this.getDeliveryLog()},changeMenu:function(t,e){var r=this;switch(e){case"1":this.exports(t.id,t.type,t.cache_type);break;case"2":this.queueAgain(t.id,t.type);break;case"3":this.$Modal.confirm({title:"谨慎操作",content:"<p>确认停止该任务？</p>",onOk:function(){r.stopQueue(t.id)}});break;case"4":this.queueDel(t.id,t.type)}},exports:function(){var t,e=(t=i.a.mark((function t(e,r,a){var s,n,l,c,d;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=[],n=[],l=[],c="",t.next=3,this.getExcelData(e,r,a);case 3:d=t.sent,c||(c=d.filename),n.length||(n=d.filekey),s.length||(s=d.header),l=d.export,Object(o.a)(s,n,c,l);case 9:case"end":return t.stop()}}),t,this)})),function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function n(t){l(s,a,i,n,o,"next",t)}function o(t){l(s,a,i,n,o,"throw",t)}n(void 0)}))});return function(t,r,a){return e.apply(this,arguments)}}(),getExcelData:function(t,e,r){return new Promise((function(a,i){Object(s.a)(t,e,r).then((function(t){return a(t.data)}))}))},queueAgain:function(t,e){var r=this;Object(s.R)(t,e).then((function(t){r.$Message.success(t.msg),r.getQueue()})).catch((function(t){r.$Message.error(t.msg)}))},queueDel:function(t,e){var r=this;Object(s.S)(t,e).then((function(t){r.$Message.success(t.msg),r.getQueue()})).catch((function(t){r.$Message.error(t.msg)}))},stopQueue:function(t){var e=this;Object(s.bb)(t).then((function(t){e.$Message.success(t.msg),e.getQueue()})).catch((function(t){e.$Message.error(t.msg)}))}}},p=r("2877"),f=Object(p.a)(u,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{attrs:{title:"任务列表",width:"1000","footer-hide":"","class-name":"vertical-center-modal"},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[t.modal?r("Card",{attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"formValidate",staticClass:"tabform",attrs:{inline:"",model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"操作时间："}},[r("DatePicker",{staticClass:"width20",attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1),r("FormItem",{attrs:{label:"类型："}},[r("Select",{staticClass:"width20",attrs:{clearable:""},on:{"on-change":t.typeSearchs},model:{value:t.formValidate.type,callback:function(e){t.$set(t.formValidate,"type",e)},expression:"formValidate.type"}},t._l(t.typeList,(function(e){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1),r("FormItem",{attrs:{label:"状态："}},[r("Select",{staticClass:"width20",attrs:{clearable:""},on:{"on-change":t.statusSearchs},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},t._l(t.statusList,(function(e){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),r("Table",{staticClass:"mt25",attrs:{height:"500",columns:t.columns1,data:t.data1,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(e){var a=e.row;return e.index,[a.is_show_log?[r("a",{on:{click:function(e){return t.deliveryLook(a)}}},[t._v("查看")]),r("Divider",{attrs:{type:"vertical"}})]:t._e(),[r("Dropdown",{on:{"on-click":function(e){return t.changeMenu(a,e)}}},[r("a",[t._v("更多"),r("Icon",{attrs:{type:"ios-arrow-down"}})],1),r("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[[7,8,9,10].includes(a.type)?r("DropdownItem",{attrs:{name:"1"}},[t._v("下载")]):t._e(),1==a.status?r("DropdownItem",{attrs:{name:"2"}},[t._v("重新执行")]):t._e(),a.is_stop_button?r("DropdownItem",{attrs:{name:"3"}},[t._v("停止任务")]):t._e(),r("DropdownItem",{attrs:{name:"4"}},[t._v("清除任务")])],1)],1)]]}}],null,!1,2501981875)}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.page1.total,current:t.page1.pageNum,"show-elevator":"","show-total":"","page-size":t.page1.pageSize,"show-sizer":""},on:{"on-change":t.pageChange,"on-page-size-change":t.limitChange}})],1)],1):t._e(),r("Modal",{attrs:{width:"900","footer-hide":""},model:{value:t.modal1,callback:function(e){t.modal1=e},expression:"modal1"}},[r("Table",{staticClass:"mt25",attrs:{height:"500",columns:t.columns4,data:t.data2,loading:t.loading2}}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.page2.total,current:t.page2.pageNum,"show-elevator":"","show-total":"","page-size":t.page2.pageSize,"show-sizer":""},on:{"on-change":t.pageChange2,"on-page-size-change":t.limitChange2}})],1)],1)],1)}),[],!1,null,null,null);e.default=f.exports},"2e31":function(t,e,r){},"2f5e":function(t,e,r){},"3c96":function(t,e,r){"use strict";var a=r("a34a"),i=r.n(a),s=r("f8b7");function n(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}var o={name:"changePrice",data:function(){return{id:0,priceModals:!1,columns:[{title:"商品",slot:"name",align:"left",minWidth:150},{title:"单价",slot:"price",align:"left",minWidth:80},{title:"数量",key:"cart_num",align:"left",minWidth:70},{title:"应收金额",slot:"true_price",align:"left",minWidth:80},{title:"改价",slot:"change_price",align:"left",minWidth:200},{title:"改价后金额",slot:"result_price",align:"left",minWidth:80}],cartInfo:[],payPostage:0,payPrice:0,resultPayPrice:0,formValidate:{is_postage:0,gain_integral:0,cart_info:[]},timeoutId:null}},computed:{resultPayPostage:function(){return this.formValidate.is_postage?0:this.payPostage}},mounted:function(){},methods:{ordeUpdateInfo:function(t){var e=this;Object(s.l)(t).then((function(t){var r=t.data.orderInfo;e.formValidate.gain_integral=parseFloat(r.gain_integral),e.payPostage=parseFloat(r.pay_postage),e.payPrice=e.$computes.Sub(parseFloat(r.pay_price),e.payPostage);var a=0,i=[];t.data.cartInfo.forEach((function(t){t.is_gift||(t.priceType=1,t.truePrice=parseFloat(t.truePrice),t.changePrice=e.$computes.Mul(t.truePrice,t.cart_num),t.resultPrice=t.changePrice,a=e.$computes.Add(a,t.resultPrice),i.push(t))})),e.cartInfo=i,e.resultPayPrice=a})).catch((function(t){e.$Message.error(t.msg)}))},changeTap:function(t,e){var r=this;if(t.changePrice<0&&(clearTimeout(r.timeoutId),r.timeoutId=setTimeout((function(){t.changePrice=0}))),1==t.priceType){var a=this.$computes.Mul(t.changePrice,1);t.resultPrice=a>=0?a:0}else if(2==t.priceType){var i=r.$computes.Sub(r.$computes.Mul(t.truePrice,t.cart_num),t.changePrice);t.resultPrice=i>0?i:0}else t.changePrice>=0&&(clearTimeout(r.timeoutId),r.timeoutId=null,t.changePrice>=100&&setTimeout((function(){t.changePrice=100}))),setTimeout((function(){var e=r.$computes.Mul(r.$computes.Mul(t.truePrice,t.cart_num),r.$computes.Div(t.changePrice,100));t.resultPrice=e>=0?e:t.truePrice}));setTimeout((function(){r.cartInfo[e]=t;var a=0;r.cartInfo.forEach((function(t){a=r.$computes.Add(a,t.resultPrice)})),r.resultPayPrice=a}))},cancel:function(){this.priceModals=!1},submit:function(){var t=this,e=[];this.cartInfo.forEach((function(t){e.push({id:t.id,true_price:t.resultPrice})})),this.formValidate.cart_info=e,Object(s.M)(this.id,this.formValidate).then(function(){var e,r=(e=i.a.mark((function e(r){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$Message.success(r.msg),t.priceModals=!1,t.$emit("submitSuccess",r);case 3:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(a,i){var s=e.apply(t,r);function o(t){n(s,a,i,o,l,"next",t)}function l(t){n(s,a,i,o,l,"throw",t)}o(void 0)}))});return function(t){return r.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))}}},l=(r("86d6"),r("2877")),c=Object(l.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{attrs:{scrollable:"",title:"订单改价",width:"800",closable:!1},model:{value:t.priceModals,callback:function(e){t.priceModals=e},expression:"priceModals"}},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{"label-width":0}},[r("Table",{attrs:{columns:t.columns,data:t.cartInfo,border:"","no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果","max-height":"350"},scopedSlots:t._u([{key:"name",fn:function(e){var a=e.row;return e.index,[r("div",{staticClass:"line1"},[t._v(t._s(a.productInfo.store_name))])]}},{key:"price",fn:function(e){var a=e.row;return e.index,[r("div",[t._v("¥"+t._s(a.sum_price))])]}},{key:"true_price",fn:function(e){var a=e.row;return e.index,[r("div",[t._v("¥"+t._s(t.$computes.Mul(a.truePrice,a.cart_num)))])]}},{key:"change_price",fn:function(e){var a=e.row,i=e.index;return[r("div",[r("Input",{attrs:{type:"number"},on:{"on-change":function(e){return t.changeTap(a,i)}},scopedSlots:t._u([{key:"prepend",fn:function(){return[r("Select",{staticClass:"w-70",attrs:{transfer:""},on:{"on-change":function(e){return t.changeTap(a,i)}},model:{value:a.priceType,callback:function(e){t.$set(a,"priceType",e)},expression:"row.priceType"}},[r("Option",{attrs:{value:1}},[t._v("一口价")]),r("Option",{attrs:{value:2}},[t._v("减价")]),r("Option",{attrs:{value:3}},[t._v("折扣")])],1)]},proxy:!0},{key:"append",fn:function(){return[r("div",{staticClass:"fs-12 text-wlll-909399"},[t._v(t._s(3==a.priceType?"%":"元"))])]},proxy:!0}],null,!0),model:{value:a.changePrice,callback:function(e){t.$set(a,"changePrice",e)},expression:"row.changePrice"}})],1)]}},{key:"result_price",fn:function(e){var a=e.row;return e.index,[r("div",[t._v("¥"+t._s(a.resultPrice))])]}}])})],1),r("FormItem",{attrs:{label:"赠送积分："}},[r("div",{staticClass:"acea-row row-between"},[r("div",[r("InputNumber",{attrs:{precision:0,min:0,max:9999999999},model:{value:t.formValidate.gain_integral,callback:function(e){t.$set(t.formValidate,"gain_integral",e)},expression:"formValidate.gain_integral"}}),r("span",{staticClass:"ml-20"},[t._v("免邮：")]),r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_postage,callback:function(e){t.$set(t.formValidate,"is_postage",e)},expression:"formValidate.is_postage"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),r("div",[r("div",{staticClass:"text-right"},[r("span",[t._v("应付邮费：¥"+t._s(t.payPostage))]),r("span",{staticClass:"ml-23"},[t._v("实际支付邮费："),r("span",{staticClass:"text-wlll-f5222d"},[t._v("¥"+t._s(t.resultPayPostage))])])]),r("div",{staticClass:"text-right"},[r("span",[t._v("总价：¥"+t._s(t.$computes.Add(t.payPrice,t.resultPayPostage)))]),r("span",{staticClass:"ml-23"},[t._v("修改后总价："),r("span",{staticClass:"text-wlll-f5222d"},[t._v("¥"+t._s(t.$computes.Add(t.resultPayPrice,t.resultPayPostage)))])])])])])])],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancel}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确认")])],1)],1)}),[],!1,null,"285194d0",null);e.a=c.exports},"3ff8":function(t,e,r){"use strict";var a=r("a553");r.n(a).a},"417c":function(t,e,r){"use strict";var a=r("a34a"),i=r.n(a),s=r("f8b7");function n(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}var o={name:"orderRecord",data:function(){return{modals:!1,loading:!1,recordData:[],page:{page:1,limit:10},columns:[{title:"订单ID",key:"oid",align:"center",minWidth:40},{title:"操作记录",key:"change_message",align:"center",minWidth:280},{title:"操作时间",key:"change_time",align:"center",minWidth:100}]}},methods:{pageChange:function(t){this.page.pageNum=t,this.getList()},getList:function(t){var e=this,r={id:t,datas:this.page};this.loading=!0,Object(s.m)(r).then(function(){var t,r=(t=i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.recordData=r.data,e.loading=!1;case 2:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function o(t){n(s,a,i,o,l,"next",t)}function l(t){n(s,a,i,o,l,"throw",t)}o(void 0)}))});return function(t){return r.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))}}},l=(r("a39d"),r("2877")),c=Object(l.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单记录",width:"700","footer-hide":""},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Card",{attrs:{bordered:!1,"dis-hover":""}},[r("Table",{attrs:{columns:t.columns,border:"",data:t.recordData,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"}})],1)],1)}),[],!1,null,"72636b36",null);e.a=c.exports},"41e0":function(t,e,r){},"5c3ac":function(t,e,r){"use strict";r.r(e);var a=r("a584"),i=r("a34a"),s=r.n(i),n=r("2f62"),o=r("f8b7"),l=r("73f5"),c=r("a6b9");function d(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function n(t){d(s,a,i,n,o,"next",t)}function o(t){d(s,a,i,n,o,"throw",t)}n(void 0)}))}}var p={name:"orderSend",props:{isAll:{type:Number|String,default:0},ids:{type:Array,default:function(){return[]}},where:{type:Object,default:function(){return{}}},selectArr:{type:Array,default:function(){return[]}}},data:function(){return{formItem:{type:"1",express_record_type:"2",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0}},watch:{"formItem.express_temp_id":function(t){}},methods:{changeRadio:function(t){switch(this.$refs.formItem.resetFields(),t){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="2",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="",this.formItem.express_record_type="1";break;case"3":this.formItem.fictitious_content="",this.formItem.express_record_type="1"}},changeExpress:function(t){switch(t){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[];break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id=""}},reset:function(){this.formItem={type:"1",express_record_type:"2",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""}},getList:function(){var t=this;Object(o.j)(1).then(function(){var e=u(s.a.mark((function e(r){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.express=r.data,t.getSheetInfo();case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},putSend:function(t){var e=this,r=Object.assign(this.formItem);if(1==this.isAll?(r.all=1,r.ids=this.selectArr,r.where=this.where):0==this.isAll&&(r.all=0,r.ids=this.selectArr),"1"===this.formItem.type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("2"===this.formItem.type&&(this.formItem.express_temp_id&&(this.formItem.express_temp_id=""),""===this.formItem.sh_delivery))return this.$Message.error("送货人不能为空");Object(o.J)(r).then(function(){var t=u(s.a.mark((function t(r){return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.modals=!1,e.$Message.success(r.msg),e.reset();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.modals=!1,e.$Message.error(t.msg)}))},cancel:function(t){this.modals=!1,this.reset()},expressChange:function(t){var e=this,r=this.express.find((function(e){return e.value===t}));r&&(this.formItem.delivery_code=r.code,"1"===this.formItem.type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(o.z)({com:this.formItem.delivery_code}).then((function(t){e.expressTemp=t.data,t.data.length||e.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(t){e.$Message.error(t.msg)}))))},getDeliveryList:function(){var t=this;Object(o.x)().then((function(e){t.deliveryList=e.data.list})).catch((function(e){t.$Message.error(e.msg)}))},getSheetInfo:function(){var t=this;Object(o.I)().then((function(e){var r=e.data;for(var a in r)r.hasOwnProperty(a)&&"express_temp_id"!==a&&(t.formItem[a]=r[a]);t.export_open=void 0===r.export_open||r.export_open,t.export_open||(t.formItem.express_record_type="1"),t.formItem.to_addr=r.to_add})).catch((function(e){t.$Message.error(e.msg)}))},shDeliveryChange:function(t){var e=this.deliveryList.find((function(e){return e.id===t}));this.formItem.sh_delivery_name=e.wx_name,this.formItem.sh_delivery_id=e.phone,this.formItem.sh_delivery_uid=e.uid},expressTempChange:function(){var t=this;this.expressTemp.forEach((function(e){t.formItem.express_temp_id===e.temp_id&&(t.temp=e)}))},preview:function(){this.$refs.viewer.$viewer.show()}}},f=(r("3ff8"),r("2877")),m=Object(f.a)(p,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,"class-name":"vertical-center-modal"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Form",{ref:"formItem",attrs:{model:t.formItem,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"选择类型："}},[r("RadioGroup",{on:{"on-change":t.changeRadio},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[r("Radio",{attrs:{label:"1"}},[t._v("打印电子面单")]),r("Radio",{attrs:{label:"2"}},[t._v("送货")]),r("Radio",{attrs:{label:"3"}},[t._v("虚拟")])],1)],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.formItem.type,expression:"formItem.type === '1'"}]},[r("FormItem",{attrs:{label:"快递公司：",required:""}},[r("Select",{staticClass:"input-add",attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":t.expressChange},model:{value:t.formItem.delivery_name,callback:function(e){t.$set(t.formItem,"delivery_name",e)},expression:"formItem.delivery_name"}},t._l(t.express,(function(e,a){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1),"1"===t.formItem.type?[r("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单：",required:""}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择电子面单"},on:{"on-change":t.expressTempChange},model:{value:t.formItem.express_temp_id,callback:function(e){t.$set(t.formItem,"express_temp_id",e)},expression:"formItem.express_temp_id"}},t._l(t.expressTemp,(function(e,a){return r("Option",{key:a,attrs:{value:e.temp_id}},[t._v(t._s(e.title))])})),1),t.formItem.express_temp_id?r("Button",{attrs:{type:"text"},on:{click:t.preview}},[t._v("预览")]):t._e()],1),r("FormItem",{attrs:{label:"寄件人姓名：",required:""}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.to_name,callback:function(e){t.$set(t.formItem,"to_name",e)},expression:"formItem.to_name"}})],1),r("FormItem",{attrs:{label:"寄件人电话：",required:""}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.to_tel,callback:function(e){t.$set(t.formItem,"to_tel",e)},expression:"formItem.to_tel"}})],1),r("FormItem",{attrs:{label:"寄件人地址：",required:""}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.to_addr,callback:function(e){t.$set(t.formItem,"to_addr",e)},expression:"formItem.to_addr"}})],1)]:t._e()],2),r("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.formItem.type,expression:"formItem.type === '2'"}]},[r("FormItem",{attrs:{label:"送货人：",required:""}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择送货人"},on:{"on-change":t.shDeliveryChange},model:{value:t.formItem.sh_delivery,callback:function(e){t.$set(t.formItem,"sh_delivery",e)},expression:"formItem.sh_delivery"}},t._l(t.deliveryList,(function(e,a){return r("Option",{key:a,attrs:{value:e.id}},[t._v(t._s(e.wx_name)+"（"+t._s(e.phone)+"）")])})),1)],1)],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.formItem.type,expression:"formItem.type === '3'"}]},[r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.fictitious_content,callback:function(e){t.$set(t.formItem,"fictitious_content",e)},expression:"formItem.fictitious_content"}})],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancel}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.putSend}},[t._v("提交")])],1),r("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:t.temp,expression:"temp"}],ref:"viewer"},[r("img",{staticClass:"display-add ",attrs:{src:t.temp.pic}})])],1)}),[],!1,null,"3a750b4b",null).exports,h=r("2c4a"),v=r("d708"),_=r("c276"),g=r("0b65");function y(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(r,!0).forEach((function(e){I(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function I(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var x={name:"table_from",components:{autoSend:m,queueList:h.default},props:["formSelection","isAll","orderDataStatus"],data:function(){return{currentTab:"-1",grid:{xl:7,lg:12,md:24,sm:24,xs:24},orderData:{status:"",data:"",real_name:"",field_key:"all",pay_type:"",type:"",store_id:"",supplier_id:""},modalTitleSs:"",statusType:"",time:"",value2:[],isDelIdList:[],writeOffRules:{code:[{validator:function(t,e,r){if(!e)return r(new Error("请填写核销码"));Number.isInteger(e)&&/\b\d{12}\b/.test(e)?r():r(new Error("请填写12位数字"))},trigger:"blur",required:!0}]},writeOffFrom:{code:"",confirm:0},staffData:[],supplierName:[],modals2:!1,timeVal:[],options:g.a,payList:[{label:"全部",val:""},{label:"微信支付",val:"1"},{label:"支付宝支付",val:"4"},{label:"余额支付",val:"2"},{label:"线下支付",val:"3"}],manualModal:!1,uploadAction:"".concat(v.a.apiBaseURL,"/file/upload/1"),uploadHeaders:{},file:"",autoModal:!1,isShow:!1,recordModal:!1,sendOutValue:"",exportListOn:0,fileList:[]}},mounted:function(){},computed:w({},Object(n.e)("admin/layout",["isMobile"]),{},Object(n.e)("admin/order",["orderChartType","isDels","delIdList","orderType"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"},today:function(){var t=new Date,e=new Date;return[e.getFullYear()+"/"+(e.getMonth()+1)+"/"+e.getDate(),t.getFullYear()+"/"+(t.getMonth()+1)+"/"+t.getDate()]}}),watch:{$route:function(){"/order/list?status=1"===this.$route.fullPath&&this.getPath()},orderDataStatus:function(t){this.selectChange2(t)}},created:function(){this.staffList(),this.getSupplierList(),"/order/list?status=1"===this.$route.fullPath&&this.getPath(),this.$parent.$emit("add")},methods:w({},Object(n.d)("admin/order",["getOrderStatus","getOrderType","getOrderTime","getOrderNum","getfieldKey","getSupplier_id","getStore_id","getType_id"]),{getPath:function(){this.orderData.status=this.$route.query.status.toString(),this.getOrderStatus(this.orderData.status),this.$emit("getList",1),this.$emit("order-data",this.orderData)},clearTap:function(t){this.getOrderNum(t.target.value),this.$emit("order-data",this.orderData)},reset:function(){this.orderData={status:"",data:"",real_name:"",field_key:"all",pay_type:"",type:"",store_id:"",supplier_id:""},this.timeVal=[],this.getOrderNum(""),this.getType_id(""),this.getOrderType(""),this.getOrderTime(""),this.$emit("getList",1),this.$emit("order-data",this.orderData)},onchangeTime:function(t){"00:00:00"===t[1].slice(-8)?(t[1]=t[1].slice(0,-8)+"23:59:59",this.timeVal=t):this.timeVal=t,this.orderData.data=this.timeVal[0]?this.timeVal.join("-"):"",this.getOrderTime(this.orderData.data),this.$emit("getList",1),this.$emit("order-data",this.orderData)},selectChange:function(t){this.$store.dispatch("admin/order/getOrderTabs",{data:t}),this.orderData.data=t,this.getOrderTime(this.orderData.data),this.timeVal=[],this.$emit("getList"),this.$emit("order-data",this.orderData)},selectChange2:function(t){this.orderData.status=t,this.getOrderStatus(t),this.$emit("getList",1),this.$emit("order-data",this.orderData)},typeChange:function(t){this.getType_id(t),this.$emit("getList",1),this.$emit("order-data",this.orderData)},storeChange:function(t){this.getStore_id(t),this.$emit("getList",1),this.$emit("order-data",this.orderData)},supplierChange:function(t){this.getSupplier_id(t),this.$emit("getList",1)},userSearchs:function(t){this.getOrderType(t),this.$emit("getList",1)},timeChange:function(t){this.getOrderTime(t),this.$emit("getList")},staffList:function(){var t=this;Object(l.z)().then((function(e){t.staffData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getSupplierList:function(){var t=this;Object(c.l)().then(function(){var e,r=(e=s.a.mark((function e(r){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.supplierName=r.data;case 1:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(a,i){var s=e.apply(t,r);function n(t){y(s,a,i,n,o,"next",t)}function o(t){y(s,a,i,n,o,"throw",t)}n(void 0)}))});return function(t){return r.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},orderSearch:function(t){this.getOrderNum(t),this.getfieldKey(this.orderData.field_key),this.$emit("getList",1)},onClickTab:function(){this.$emit("onChangeType",this.currentTab)},delAll:function(){var t=this;if(0===this.delIdList.length)this.$Message.error("请先选择删除的订单！");else if(this.isDels){this.delIdList.filter((function(e){t.isDelIdList.push(e.id)}));var e={title:"删除订单",url:"/order/dels",method:"post",ids:{ids:this.isDelIdList,all:this.isAll,where:this.orderData}};this.$modalSure(e).then((function(e){t.$Message.success(e.msg),t.tabList()})).catch((function(e){t.$Message.error(e.msg)}))}else this.$Modal.error({title:"错误！",content:"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>"})},handleSubmit:function(){this.$emit("on-submit",this.data)},Refresh:function(){this.$emit("getList")},handleReset:function(){this.$refs.form.resetFields(),this.$emit("on-reset")},queuemModal:function(){this.$refs.queue.modal=!0}})},C=(r("edaf4"),Object(f.a)(x,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Form",{ref:"orderData",staticClass:"tabform",attrs:{inline:"",model:t.orderData,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"订单搜索：",prop:"real_name","label-for":"real_name"}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入","element-id":"name",clearable:"",maxlength:"20"},on:{"on-change":t.clearTap},model:{value:t.orderData.real_name,callback:function(e){t.$set(t.orderData,"real_name",e)},expression:"orderData.real_name"}},[r("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend","default-label":"全部"},slot:"prepend",model:{value:t.orderData.field_key,callback:function(e){t.$set(t.orderData,"field_key",e)},expression:"orderData.field_key"}},[r("Option",{attrs:{value:"all"}},[t._v("全部")]),r("Option",{attrs:{value:"order_id"}},[t._v("订单号")]),r("Option",{attrs:{value:"uid"}},[t._v("用户UID")]),r("Option",{attrs:{value:"real_name"}},[t._v("用户姓名")]),r("Option",{attrs:{value:"user_phone"}},[t._v("用户电话")]),r("Option",{attrs:{value:"title"}},[t._v("商品名称")]),r("Option",{attrs:{value:"total_num"}},[t._v("商品件数")])],1)],1)],1),r("FormItem",{attrs:{label:"订单类型："}},[r("Select",{staticClass:"input-add",attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.typeChange},model:{value:t.orderData.type,callback:function(e){t.$set(t.orderData,"type",e)},expression:"orderData.type"}},[r("Option",{attrs:{value:"0"}},[t._v("普通订单")]),r("Option",{attrs:{value:"1"}},[t._v("秒杀订单")]),r("Option",{attrs:{value:"2"}},[t._v("砍价订单")]),r("Option",{attrs:{value:"3"}},[t._v("拼团订单")]),r("Option",{attrs:{value:"4"}},[t._v("积分订单")]),r("Option",{attrs:{value:"5"}},[t._v("套餐订单")]),r("Option",{attrs:{value:"6"}},[t._v("预售订单")]),r("Option",{attrs:{value:"7"}},[t._v("新人订单")]),r("Option",{attrs:{value:"8"}},[t._v("抽奖订单")])],1)],1),r("FormItem",{attrs:{label:"支付方式："}},[r("Select",{staticClass:"input-add",attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.userSearchs},model:{value:t.orderData.pay_type,callback:function(e){t.$set(t.orderData,"pay_type",e)},expression:"orderData.pay_type"}},t._l(t.payList,(function(e){return r("Option",{key:e.id,attrs:{value:e.val}},[t._v(t._s(e.label))])})),1)],1),r("FormItem",{attrs:{label:"创建时间："}},[r("DatePicker",{staticClass:"input-add mr20",attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd HH:mm:ss",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}}),r("Button",{staticClass:"ml10",attrs:{type:"primary"},on:{click:function(e){return t.orderSearch(t.orderData.real_name)}}},[t._v("查询")]),r("Button",{staticClass:"ml-10",on:{click:function(e){return t.reset()}}},[t._v("重置")])],1)],1)],1)}),[],!1,null,"3ca6d130",null).exports),D={name:"distshow",data:function(){var t=this;return{id:0,currentid:0,modals:!1,loading:!1,orderList:[],total:0,formValidate:{page:1,limit:15,keywords:"",id:0},columns:[{title:"选择",width:60,align:"center",render:function(e,r){var a=r.row.id,i=!1;i=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=a,Object(l.N)({oid:t.id,store_id:r.row.id}).then((function(e){t.$Message.success(e.msg),t.modals=!1,s.currentid=0,t.$parent.getList(),t.$parent.closeDetail()})).catch((function(e){t.modals=!1,t.$Message.error(e.msg),s.currentid=0}))}}})])}},{title:"ID",key:"id",minWidth:50},{title:"门店图片",slot:"image",minWidth:80},{title:"门店名称",key:"name",minWidth:80},{title:"联系电话",key:"phone",minWidth:80},{title:"门店地址",key:"address",ellipsis:!0,minWidth:150},{title:"营业时间",key:"day_time",minWidth:120},{title:"营业状态",key:"status_name",minWidth:80}]}},props:{},mounted:function(){},methods:{userSearchs:function(){this.formValidate.page=1,this.getList(this.id)},getList:function(t){var e=this;this.id=t,this.formValidate.id=t,this.loading=!0,Object(l.K)(this.formValidate).then((function(t){e.orderList=t.data.list,e.total=t.data.count,e.loading=!1}))},cancel:function(t){this.modals=!1},putRemark:function(){},pageChange:function(t){this.formValidate.page=t,this.getList(this.id)}}},k=(r("e434"),Object(f.a)(D,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"请选择门店",width:"850"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("div",{staticClass:"table"},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"门店搜索："}},[r("Input",{staticStyle:{width:"300px"},attrs:{search:"","enter-button":"",placeholder:"请输入门店名称/电话/地址"},on:{"on-search":t.userSearchs},model:{value:t.formValidate.keywords,callback:function(e){t.$set(t.formValidate,"keywords",e)},expression:"formValidate.keywords"}})],1)],1),r("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"image",fn:function(t){var e=t.row;return t.index,[r("img",{attrs:{src:e.image}})]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"})])}),[],!1,null,"b84bc16e",null).exports),O=r("a464"),j=r("0fc4"),S=r("3c96"),T=r("31b4"),$=r("fc48"),M=r("61f8"),P=r("417c"),L=r("d616"),E=r("a716"),A=r("2e83"),F=r("6112"),N=r("add5"),R=r.n(N);function V(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function B(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function n(t){V(s,a,i,n,o,"next",t)}function o(t){V(s,a,i,n,o,"throw",t)}n(void 0)}))}}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function W(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(r,!0).forEach((function(e){U(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function U(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var q={name:"table_list",components:{expandRow:O.a,editFrom:T.a,detailsFrom:$.a,orderRemark:M.a,orderRecord:P.a,orderSend:L.a,userDetails:E.a,Distribution:k,autoSend:m,queueList:h.default,Template:F.default,changePrice:S.a},props:["where","currentTab"],data:function(){return{roterPre:v.a.roterPre,openErp:!1,distshow:!1,delfromData:{},modal:!1,orderList:[],pay_type:"",orderCards:[],loading:!1,orderId:0,page:{total:0,pageNum:1,pageSize:10},data:[],FromData:null,orderDatalist:null,modalTitleSs:"",isDelIdList:[],checkBox:!1,formSelection:[],display:"none",autoDisabled:!1,status:0,isAll:0,rowActive:{},tablists:{},selectArr:[],exportList:[{name:"1",label:"导出发货单"},{name:"0",label:"导出订单"}],exportListOn:0,manualModal:!1,uploadAction:"".concat(v.a.apiBaseURL,"/file/upload/1"),uploadHeaders:{},autoModal:!1,isShow:!1,recordModal:!1,sendOutValue:"",fileList:[],file:"",modals2:!1,writeOffRules:{code:[{validator:function(t,e,r){if(!e)return r(new Error("请填写核销码"));Number.isInteger(e)&&/\b\d{12}\b/.test(e)?r():r(new Error("请填写12位数字"))},trigger:"blur",required:!0}]},writeOffFrom:{code:"",confirm:0},orderConNum:0,orderConId:0,checkUidList:[],refundModal:!1,refundColumns:[{type:"selection",width:60,align:"center"},{title:"商品信息",width:210,slot:"product"},{title:"规格",render:function(t,e){return t("div",e.row.productInfo.attrInfo.suk)}},{title:"售价",render:function(t,e){return t("div",e.row.productInfo.attrInfo.price)}},{title:"优惠价",key:"refundPrice"},{title:"总数",key:"cart_num"},{title:"退款数量",slot:"action",width:160}],refundProduct:[],refundSelection:[],refundMoney:0,is_split_order:0,refund_explain:"",orderDataStatus:""}},computed:W({},Object(n.e)("admin/layout",["isMobile"]),{},Object(n.e)("admin/order",["orderPayType","orderStatus","orderTime","orderNum","fieldKey","orderType","orderChartType","supplier_id","store_id","type_id"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"},refundProductNum:function(){return this.refundProduct.reduce((function(t,e){return t+e.refundNum}),0)}}),mounted:function(){},created:function(){this.orderDataStatus=this.$route.query.status||"",this.getList(),this.getToken(),this.getErpConfig()},watch:{currentTab:function(){this.onClickTab()},orderType:function(){this.page.pageNum=1,this.getList()},orderList:{deep:!0,handler:function(t){var e=this;t.forEach((function(t){e.formSelection.forEach((function(e){e.id===t.id&&(t.checkBox=!0)}))}));var r=this.orderList.filter((function(t){return t.checkBox}));this.orderList.length?this.checkBox=this.orderList.length===r.length:this.checkBox=!1}},refundSelection:{handler:function(t){var e=this;this.refundMoney=t.reduce((function(t,r){var a=r.refundPrice,i=r.refundNum;return e.$computes.Add(t,e.$computes.Mul(a,i))}),0)},deep:!0},is_split_order:function(t){var e=this;this.$nextTick((function(){e.$refs.refundTable.selectAll(!!t)}))},refundMoney:function(t){var e=this;this.$nextTick((function(){"number"==typeof t&&parseFloat(t)!=parseInt(t)&&t.toString().length-(t.toString().indexOf(".")+1)>2&&(e.refundMoney=Number(t.toFixed(2)))}))}},methods:W({},Object(n.d)("admin/order",["getIsDel","getisDelIdListl","onChangeTabs","getStore_id","getSupplier_id"]),{visibleChange:function(t){this.is_split_order=0,t||(this.refundSelection=[])},cancelRefundModal:function(){this.refundModal=!1},putOpenRefund:function(){var t=this,e={id:this.orderId,refund_price:this.refundMoney,type:1,is_split_order:this.is_split_order,refund_explain:this.refund_explain};if(this.is_split_order){if(!this.refundSelection.length)return this.$Message.error("请选择需要退款的商品");e.cart_ids=this.refundSelection.map((function(t){return{cart_id:t.id,cart_num:t.refundNum}}))}Object(o.L)(e).then((function(e){t.$Message.success(e.msg),t.refundModal=!1,t.getList(),t.getData(t.orderDatalist.orderInfo.id)})).catch((function(e){t.$Message.error(e.msg)}))},refundSelectionChange:function(t){this.refundSelection=t},refundNumChange:function(t){var e=t.id,r=t.refundNum,a=this.refundSelection.find((function(t){return t.id===e}));a&&(a.refundNum=r)},checkboxItem:function(t){var e=parseInt(t.rowid);-1!==this.checkUidList.indexOf(e)?this.checkUidList=this.checkUidList.filter((function(t){return t!==e})):this.checkUidList.push(e)},checkboxAll:function(){var t=this.$refs.xTable.getCheckboxRecords(!0),e=this.$refs.xTable.getCheckboxReserveRecords(!0);0==this.isAll&&this.checkUidList.length<=e.length&&!this.isCheckBox&&(e=[]);var r=[];(e=e.concat(t)).forEach((function(t){r.push(parseInt(t.id))})),this.checkUidList=r,t.length||(this.isCheckBox=!1)},allPages:function(t){this.isAll=t,0==t?this.$refs.xTable.toggleAllCheckboxRow():(this.isCheckBox?(this.$refs.xTable.setAllCheckboxRow(!1),this.isCheckBox=!1,this.isAll=0):(this.$refs.xTable.setAllCheckboxRow(!0),this.isCheckBox=!0,this.isAll=1),this.checkUidList=[])},getErpConfig:function(){var t=this;Object(j.a)().then((function(e){t.openErp=e.data.open_erp})).catch((function(e){t.$Message.error(e.msg)}))},printOreder:function(){if(this.checkUidList.length>10||1==this.isAll&&this.page.total>10)return this.$Message.error("最多批量打印10个订单");var t=[];1==this.isAll&&this.page.total<=10&&this.orderList.forEach((function(e){t.push(parseInt(e.id))}));var e=this.$router.resolve({path:this.roterPre+"/supplier/order/distribution",query:{id:1==this.isAll?t.join(","):this.checkUidList.join(","),status:2}});window.open(e.href,"_blank")},delAll:function(){var t=this;if(0===this.checkUidList.length&&0==this.isAll)return this.$Message.error("请先选择删除的订单！");var e={title:"删除订单",url:"/order/dels",method:"post",ids:{all:this.isAll,ids:this.checkUidList}};this.$modalSure(e).then((function(e){t.$Message.success(e.msg),t.checkUidList=[],t.getList()})).catch((function(e){t.$Message.error(e.msg)}))},onAuto:function(){this.$refs.sends.modals=!0,this.$refs.sends.getList(),this.$refs.sends.getDeliveryList()},btnClick:function(t){var e=this,r={supplier_id:t.supplier_id,id:t.id};Object(o.W)(r).then(function(){var t=B(s.a.mark((function t(r){return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$Message.success(r.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},queuemModal:function(){this.$refs.queue.modal=!0},getExpressList:function(){var t=B(s.a.mark((function t(){var e,r,a,i,n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=[],r=[],a=[],i="",t.next=3,this.getExcelData();case 3:n=t.sent,i||(i=n.filename),r.length||(r=n.filekey),e.length||(e=n.header),a=n.export,Object(A.a)(e,r,i,a);case 9:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),getExcelData:function(){return new Promise((function(t,e){Object(o.e)().then((function(e){return t(e.data)}))}))},writeOff:function(){this.modals2=!0},search:function(t){var e=this;this.$refs[t].validate((function(t){t?(e.writeOffFrom.confirm=0,Object(o.Q)(e.writeOffFrom).then(function(){var t=B(s.a.mark((function t(r){return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:200===r.status?e.$Message.success(r.msg):e.$Message.error(r.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))):e.$Message.error("请填写正确的核销码")}))},ok:function(){var t=this;this.writeOffFrom.code?(this.writeOffFrom.confirm=1,Object(o.Q)(this.writeOffFrom).then(function(){var e=B(s.a.mark((function e(r){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:200===r.status?(t.$Message.success(r.msg),t.modals2=!1,t.$refs[name].resetFields(),t.$emit("getList")):t.$Message.error(r.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))):this.$Message.warning("请先验证订单！")},del:function(t){this.modals2=!1,this.writeOffFrom.confirm=0,this.$refs[t].resetFields()},getToken:function(){this.uploadHeaders["Authori-zation"]="Bearer "+_.a.cookies.get("token")},uploadSuccess:function(t,e,r){200===t.status?(this.$Message.success(t.msg),this.file=t.data.src,this.fileList=r):this.$Message.error(t.msg)},removeFile:function(t,e){this.file="",this.fileList=e},manualModalOk:function(){var t=this;this.$refs.upload.clearFiles(),Object(o.s)({file:this.file}).then((function(e){t.$Message.success(e.msg),t.fileList=[],t.manualModal=!1})).catch((function(e){t.$Message.error(e.msg),t.fileList=[]}))},manualModalCancel:function(){this.manualModal=!1,this.fileList=[],this.$refs.upload.clearFiles()},getTabs:function(){var t=this;this.spinShow=!0,this.$store.dispatch("admin/order/getOrderTabs",{status:this.orderStatus,pay_type:this.orderPayType,data:this.orderTime,real_name:this.orderNum,field_key:this.fieldKey,type:this.type_id,plat_type:this.currentTab,store_id:this.store_id,supplier_id:this.supplier_id}).then((function(e){t.tablists=e.data,t.spinShow=!1})).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))},onClickTab:function(){this.onChangeTabs(this.currentTab),this.isAll=0,this.isCheckBox=!1,this.$refs.xTable.setAllCheckboxRow(!1),this.checkUidList=[],1==this.currentTab&&this.getSupplier_id(""),2==this.currentTab&&this.getStore_id(""),this.getList(),this.$store.dispatch("admin/order/getOrderTabs",{type:this.currentTab})},closeDetail:function(){this.$refs.detailss.modals=!1},distribution:function(t){this.$refs.distshow.modals=!0,this.$refs.distshow.formValidate.keywords="",this.$refs.distshow.getList(t.id)},showUserInfo:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.activeName="info",this.$refs.userDetails.getDetails(t.uid)},printImg:function(t){R()({printable:t,type:"image",documentTitle:"快递信息",style:"img{\n\t      width: 100%;\n\t      height: 476px;\n\t    }"})},changeMenu:function(t,e,r){var a=this;switch(this.orderId=t.id,this.orderConId=t.pid>0?t.pid:t.id,this.orderConNum=r,e){case"1":this.delfromData={title:"确认收款",url:"/order/pay_offline/".concat(t.id),method:"post",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.$emit("changeGetTabs"),a.getData(t.id,1),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"2":this.rowActive=t,this.getData(t.id);break;case"3":this.$refs.record.modals=!0,this.$refs.record.getList(t.id);break;case"4":this.$refs.remarks.formValidate.remark=t.remark,this.$refs.remarks.modals=!0;break;case"5":this.getOnlyrefundData(t.id,t.refund_type,t);break;case"55":this.getrefundData(t.id,t.refund_type);break;case"6":this.getRefundIntegral(t.id);break;case"7":this.getNorefundData(t.id);break;case"8":if(t.refund.length)return this.$Message.error("该订单有售后处理中，请先处理售后申请");this.delfromData={title:"修改确认收货",url:"/order/take/".concat(t.id),method:"put",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.$emit("changeGetTabs"),a.getList(),r?a.$refs.detailss.getSplitOrder(t.pid):a.getData(t.id,1)})).catch((function(t){a.$Message.error(t.msg)}));break;case"10":this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.$emit("changeGetTabs"),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"11":this.delfromData={title:"立即打印电子面单",info:"您确认打印此电子面单吗?",url:"/order/order_dump/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"12":var i=this.$router.resolve({path:this.roterPre+"/supplier/order/distribution",query:{id:t.id,status:2}});window.open(i.href,"_blank");break;case"13":this.printImg(t.kuaidi_label);break;default:this.delfromData={title:"删除订单",url:"/order/del/".concat(t.id),method:"DELETE",ids:""},this.delOrder(t,this.delfromData)}},submitModel:function(){this.getList()},pageChange:function(t){this.page.pageNum=t,this.getList()},limitChange:function(t){this.page.pageSize=t,this.getList()},getList:function(t){var e=this;1==t&&(this.isAll=0,this.$refs.xTable.setAllCheckboxRow(!1),this.checkUidList=[]),this.page.pageNum=1===t?1:this.page.pageNum,this.loading=!0,Object(o.E)({page:this.page.pageNum,limit:this.page.pageSize,status:this.orderStatus,pay_type:this.orderPayType,data:this.orderTime,real_name:this.orderNum,field_key:this.fieldKey,type:this.type_id,plat_type:this.currentTab,store_id:this.store_id,supplier_id:this.supplier_id}).then(function(){var t=B(s.a.mark((function t(r){var a;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:(a=r.data).data.forEach((function(t){t.checkBox=1==e.isAll,t.id==e.orderId&&(e.rowActive=t)})),e.$set(e,"orderList",a.data),e.orderCards=a.stat,e.page.total=a.count,e.$emit("on-changeCards",a.stat),e.loading=!1,e.getTabs(),e.$nextTick((function(){if(1==this.isAll)this.isCheckBox?this.$refs.xTable.setAllCheckboxRow(!0):this.$refs.xTable.setAllCheckboxRow(!1);else{var t=this.$refs.xTable.getCheckboxReserveRecords(!0);(!this.checkUidList.length||this.checkUidList.length<=t.length)&&this.$refs.xTable.setAllCheckboxRow(!1)}}));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},edit:function(t){this.$refs.changePrice.id=t.id,this.$refs.changePrice.ordeUpdateInfo(t.id),this.$refs.changePrice.priceModals=!0},submitSuccess:function(t){if(!1===t.data.status)return this.$authLapse(t.data);this.$authLapse(t.data),this.FromData=t.data,this.$refs.edits.modals=!0},splitOrderDetail:function(t){this.$router.push({path:this.roterPre+"split_list",query:{id:t.id,orderChartType:this.orderStatus}})},delOrder:function(t,e){var r=this;1===t.is_del?this.$modalSure(e).then((function(t){r.$Message.success(t.msg),r.getList(),r.$refs.detailss.modals=!1,r.$emit("changeGetTabs")})).catch((function(t){r.$Message.error(t.msg)})):this.$Modal.error({title:"错误！",content:"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>"})},getData:function(t,e){var r=this;Object(o.g)(t).then(function(){var t=B(s.a.mark((function t(a){return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e||(r.$refs.detailss.modals=!0,r.$refs.detailss.isShow=0),r.orderDatalist=a.data,r.orderDatalist.orderInfo.refund_reason_wap_img)try{r.orderDatalist.orderInfo.refund_reason_wap_img=JSON.parse(r.orderDatalist.orderInfo.refund_reason_wap_img)}catch(t){r.orderDatalist.orderInfo.refund_reason_wap_img=[]}case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){r.$Message.error(t.msg)}))},submitFail:function(t){this.status=0,this.getList(),1!=this.orderConNum?this.getData(this.orderId,1):this.$refs.detailss.getSplitOrder(this.orderConId),t&&this.$emit("changeGetTabs")},getOnlyrefundData:function(t,e,r){var a=this,i=r._info,s=Object.keys(i).map((function(t){return i[t].cart_info})).filter((function(t){return!t.is_gift}));s.forEach((function(t){t.refundPrice=a.$computes.Div(t.refund_price,t.cart_num),t.refundNum=t.cart_num-t.refund_num,t._disabled=!t.refundNum})),this.refundProduct=s,1===this.refundProductNum&&(this.refundSelection=s),this.refundModal=!0},getrefundData:function(t,e){var r=this;this.delfromData={title:"是否立即退货退款",url:"/refund/agree/".concat(t),method:"get"},this.$modalSure(this.delfromData).then((function(t){r.$Message.success(t.msg),r.getList(),r.$emit("changeGetTabs")})).catch((function(t){r.$Message.error(t.msg)}))},getNorefundData:function(t){var e=this;this.$modalForm(Object(o.r)(t)).then((function(){e.getList(),e.$emit("changeGetTabs")}))},sendOrder:function(t,e){var r=this;this.orderConId=t.pid,this.orderConNum=e,this.$store.commit("admin/order/setSplitOrder",t.total_num),this.$refs.send.modals=!0,this.orderId=t.id,this.status=t._status,this.pay_type=t.pay_type,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(e){r.$refs.send.getCartInfo(t._status,t.id)}))},delivery:function(t,e){var r=this;Object(o.h)(t.id).then(function(){var a=B(s.a.mark((function a(i){return s.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:r.orderConNum=e,r.orderConId=t.pid,r.FromData=i.data,r.$refs.edits.modals=!0,1!=e&&r.getData(r.orderId,1);case 5:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()).catch((function(t){r.$Message.error(t.msg)}))},change:function(t){},exports:function(){var t=B(s.a.mark((function t(e){var r,a,i,n,o,l,c,d,u,p,f,m,h,v,_,g;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.exportListOn=this.exportList.findIndex((function(t){return t.name===e})),r=[],a=[],i=[],n="",o=W({},this.where,{page:1,export_type:e,ids:this.checkUidList.join(),plat_type:this.currentTab}),l=0;case 4:if(!(l<o.page)){t.next=18;break}return t.next=7,this.downOrderData(o);case 7:if((c=t.sent).export.length){t.next=10;break}return t.abrupt("break",18);case 10:n||(n=c.filename),a.length||(a=c.filekey),r.length||(r=c.header),i=i.concat(c.export),o.page++;case 15:l++,t.next=4;break;case 18:for(d=[],u=0;u<i.length;u++)for(p=i[u].attr?i[u].attr.split("\n"):[],f=i[u].goods_name?i[u].goods_name.split("\n"):[],m=i[u].product_id?i[u].product_id.split("\n"):[],h=i[u].cart_num?i[u].cart_num.split("\n"):[],v=0;v<f.length;v++){if((_=W({},i[u])).goods_name&&(_.goods_name=f[v]),_.attr&&(_.attr=p[v]),_.product_id&&(_.product_id=m[v]),_.cart_num&&(_.cart_num=h[v]),v)for(g in _)Object.hasOwnProperty.call(_,g)&&"goods_name"!==g&&"attr"!==g&&"product_id"!==g&&"cart_num"!==g&&(_[g]=null);d.push(_)}Object(A.a)(r,a,n,d);case 21:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),downOrderData:function(t){return new Promise((function(e,r){Object(o.db)(t).then((function(t){return e(t.data)}))}))},bindWrite:function(t){var e=this;this.$Modal.confirm({title:"提示",content:"确定要核销该订单吗？",cancelText:"取消",closable:!0,maskClosable:!0,onOk:function(){Object(o.eb)(t.order_id).then((function(t){e.$Message.success(t.msg),e.getList()})).catch((function(t){e.$Message.error(t.msg)}))},onCancel:function(){}})},selectChange2:function(){this.$emit("selectChange2",this.orderDataStatus)}})},H=(r("6f6b"),{name:"orderlistDetails",components:{tableForm:C,tableList:Object(f.a)(q,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"new_tab"},[r("Tabs",{on:{"on-click":t.selectChange2},model:{value:t.orderDataStatus,callback:function(e){t.orderDataStatus=e},expression:"orderDataStatus"}},[r("TabPane",{attrs:{label:"全部",name:" "}}),r("TabPane",{attrs:{label:"待支付("+(t.tablists.unpaid||0)+")",name:"0"}}),r("TabPane",{attrs:{label:"待发货("+(t.tablists.unshipped||0)+")",name:"1"}}),r("TabPane",{attrs:{label:"待收货",name:"2"}}),r("TabPane",{attrs:{label:"待评价",name:"3"}}),r("TabPane",{attrs:{label:"已完成",name:"4"}}),r("TabPane",{attrs:{label:"已退款",name:"-2"}})],1)],1),r("div",{staticClass:"acea-row row-between"},[r("div",[r("Tooltip",{directives:[{name:"auth",rawName:"v-auth",value:["order-batch-del_orders"],expression:"['order-batch-del_orders']"}],attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[r("Button",{staticClass:"mr10",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.delAll}},[t._v("批量删除订单")])],1),r("Button",{directives:[{name:"auth",rawName:"v-auth",value:["order-hand-batch_delivery"],expression:"['order-hand-batch_delivery']"}],staticClass:"mr10",attrs:{type:"primary"},on:{click:function(e){t.manualModal=!0}}},[t._v("手动批量发货")]),r("Tooltip",{directives:[{name:"auth",rawName:"v-auth",value:["order-other-batch_delivery"],expression:"['order-other-batch_delivery']"}],attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[r("Button",{staticClass:"mr10",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.onAuto}},[t._v("自动批量发货")])],1),r("Tooltip",{attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[r("Button",{staticClass:"mr10",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.printOreder}},[t._v("打印配货单")])],1),r("Dropdown",{directives:[{name:"auth",rawName:"v-auth",value:["export-storeOrder"],expression:"['export-storeOrder']"}],staticClass:"mr10",on:{"on-click":t.exports}},[r("Button",{staticStyle:{width:"110px"}},[t._v("\n            "+t._s(t.exportList[t.exportListOn].label)+"\n            "),r("Icon",{attrs:{type:"ios-arrow-down"}})],1),r("DropdownMenu",{attrs:{slot:"list"},slot:"list"},t._l(t.exportList,(function(e,a){return r("DropdownItem",{key:a,staticStyle:{"font-size":"12px !important"},attrs:{name:e.name}},[t._v(t._s(e.label))])})),1)],1)],1),r("div",{staticClass:"caozuo"},[r("Button",{directives:[{name:"auth",rawName:"v-auth",value:["queue-index"],expression:"['queue-index']"}],staticClass:"mr10",on:{click:t.queuemModal}},[t._v("批量发货记录")]),r("Button",{directives:[{name:"auth",rawName:"v-auth",value:["export-expressList"],expression:"['export-expressList']"}],staticClass:"mr10",on:{click:t.getExpressList}},[t._v("下载物流公司对照表")])],1)]),r("vxe-table",{ref:"xTable",staticClass:"mt25",attrs:{loading:t.loading,"row-id":"id","expand-config":{accordion:!0},"checkbox-config":{reserve:!0},data:t.orderList},on:{"checkbox-all":t.checkboxAll,"checkbox-change":t.checkboxItem}},[r("vxe-column",{attrs:{type:"",width:"0"}}),r("vxe-column",{attrs:{type:"expand",width:"35"},scopedSlots:t._u([{key:"content",fn:function(e){var a=e.row;return[r("div",{staticClass:"tdinfo"},[r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商品总价：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.total_price)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("下单时间：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.add_time)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("推广人：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.spread_nickname?a.spread_nickname:"无")}})])],1),r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("用户备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.mark?a.mark:"无")}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商家备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.remark?a.remark:"无")}})])],1)],1)]}}])}),r("vxe-column",{attrs:{type:"checkbox",width:"100"},scopedSlots:t._u([{key:"header",fn:function(){return[r("div",[r("Dropdown",{attrs:{transfer:""},on:{"on-click":t.allPages},scopedSlots:t._u([{key:"list",fn:function(){return[r("DropdownMenu",[r("DropdownItem",{attrs:{name:"0"}},[t._v("当前页")]),r("DropdownItem",{attrs:{name:"1"}},[t._v("所有页")])],1)]},proxy:!0}])},[r("a",{staticClass:"acea-row row-middle",attrs:{href:"javascript:void(0)"}},[r("span",[t._v("全选("+t._s(1==t.isAll?t.page.total-t.checkUidList.length:t.checkUidList.length)+")")]),r("Icon",{attrs:{type:"ios-arrow-down"}})],1)])],1)]},proxy:!0}])}),r("vxe-column",{attrs:{field:"order_id",title:"订单号","min-width":"175"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[1===a.is_del&&null==a.delete_time?r("Tooltip",{attrs:{transfer:!0,theme:"dark","max-width":"300",delay:600,content:"用户已删除"}},[r("span",{staticStyle:{color:"#ed4014",display:"block"}},[t._v(t._s(a.order_id))])]):r("span",{staticStyle:{color:"#2d8cf0",display:"block",cursor:"pointer"},on:{click:function(e){return t.changeMenu(a,"2")}}},[t._v(t._s(a.order_id))])]}}])}),r("vxe-column",{attrs:{field:"pink_name",title:"订单类型","min-width":"120"}}),r("vxe-column",{attrs:{field:"nickname",title:"用户信息","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("a",{on:{click:function(e){return t.showUserInfo(a)}}},[t._v(t._s(a.nickname))]),null!=a.delete_time?r("span",{staticStyle:{color:"#ed4014"}},[t._v("\n          (已注销)")]):t._e()]}}])}),r("vxe-column",{attrs:{field:"info",title:"商品信息","min-width":"330"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("Tooltip",{attrs:{transfer:!0,theme:"dark","max-width":"300",delay:600}},[t._l(a._info,(function(e,a){return r("div",{key:a,staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.image:e.cart_info.productInfo.image,expression:"val.cart_info.productInfo.attrInfo? val.cart_info.productInfo.attrInfo.image: val.cart_info.productInfo.image"}]})]),r("span",{staticClass:"tabBox_tit line1"},[e.cart_info.is_gift?r("span",{staticClass:"font-color-red"},[t._v("赠品")]):t._e(),t._v("\n\n              "+t._s(e.cart_info.productInfo.store_name+" | ")+"\n              "+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:"")+" ")])])})),r("div",{attrs:{slot:"content"},slot:"content"},t._l(a._info,(function(e,a){return r("div",{key:a},[e.cart_info.is_gift?r("p",{staticClass:"font-color-red"},[t._v("赠品")]):t._e(),r("p",[t._v(t._s(e.cart_info.productInfo.store_name))]),r("p",[t._v(" "+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:""))]),r("p",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.cart_info.sum_price+" x "+e.cart_info.cart_num)+" ")])])})),0)],2)]}}])}),r("vxe-column",{attrs:{field:"pay_price",title:"实际支付",align:"center","min-width":"70"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("span",[t._v(t._s(a.paid>0?a.pay_price:0))])]}}])}),r("vxe-column",{attrs:{field:"_pay_time",title:"支付时间","min-width":"150"}}),r("vxe-column",{attrs:{field:"pay_type_name",title:"支付类型","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("span",[t._v(t._s(a.pay_type_name))])]}}])}),r("vxe-column",{attrs:{field:"statusName",title:"订单状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("Tag",{directives:[{name:"show",rawName:"v-show",value:3==a.status,expression:"row.status == 3"}],attrs:{color:"default",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),r("Tag",{directives:[{name:"show",rawName:"v-show",value:4==a.status,expression:"row.status == 4"}],attrs:{color:"orange",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),r("Tag",{directives:[{name:"show",rawName:"v-show",value:1==a.status||2==a.status||5==a.status,expression:"row.status == 1 || row.status == 2 || row.status == 5"}],attrs:{color:"orange",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),r("Tag",{directives:[{name:"show",rawName:"v-show",value:0==a.status,expression:"row.status == 0"}],attrs:{color:"red",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),!a.is_all_refund&&a.refund.length?r("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("部分退款中")]):t._e(),a.is_all_refund&&a.refund.length&&6!=a.refund_type?r("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("退款中")]):t._e(),a.status_name.pics?r("div",{staticClass:"pictrue-box",attrs:{size:"medium"}},t._l(a.status_name.pics||[],(function(t,e){return r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:e},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"item"}],staticClass:"pictrue mr10",attrs:{src:t}})])})),0):t._e()]}}])}),r("vxe-column",{attrs:{field:"mark",title:"买家备注","min-width":"150"}}),r("vxe-column",{attrs:{field:"remark",title:"商家备注","min-width":"150"}}),r("vxe-column",{attrs:{field:"action",title:"操作",align:"center",width:"140",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[2!==a._status&&8!==a._status&&4!==a.status||1!==a.shipping_type||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time||0!==a.store_id||0!==a.supplier_id?t._e():r("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.sendOrder(a)}}},[t._v("发送货")]),0!==a.supplier_id&&"未发货"==a.status_name.status_name?r("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.btnClick(a)}}},[t._v("提醒发货")]):t._e(),0!==a.supplier_id&&"未发货"==a.status_name.status_name?r("Divider",{attrs:{type:"vertical"}}):t._e(),2!==a._status&&8!==a._status&&4!==a.status||1!==a.shipping_type||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time||0!==a.store_id||0!==a.supplier_id?t._e():r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.changeMenu(a,"2")}}},[t._v("详情")])]}}])})],1),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.page.total,current:t.page.pageNum,"show-elevator":"","show-total":"","page-size":t.page.pageSize,"show-sizer":""},on:{"on-change":t.pageChange,"on-page-size-change":t.limitChange}})],1),r("Distribution",{ref:"distshow"}),r("edit-from",{ref:"edits",attrs:{FromData:t.FromData},on:{submitFail:t.submitFail}}),r("user-details",{ref:"userDetails",attrs:{fromType:"order"}}),r("details-from",{ref:"detailss",attrs:{orderDatalist:t.orderDatalist,orderId:t.orderId,"row-active":t.rowActive,openErp:t.openErp,formType:1}}),r("order-remark",{ref:"remarks",attrs:{orderId:t.orderId},on:{submitFail:t.submitFail}}),r("order-record",{ref:"record"}),r("order-send",{ref:"send",attrs:{orderId:t.orderId,status:t.status,pay_type:t.pay_type},on:{submitFail:function(e){return t.submitFail(1)}}}),r("Modal",{attrs:{title:"手动批量发货","class-name":"vertical-center-modal"},on:{"on-cancel":t.manualModalCancel},model:{value:t.manualModal,callback:function(e){t.manualModal=e},expression:"manualModal"}},[r("Row",{attrs:{type:"flex"}},[r("Col",{attrs:{span:"4"}},[r("div",{staticStyle:{"line-height":"32px","text-align":"right"}},[t._v("文件：")])]),r("Col",{attrs:{span:"20"}},[r("Upload",{ref:"upload",attrs:{action:t.uploadAction,headers:t.uploadHeaders,accept:".xlsx,.xls",format:["xlsx","xls"],disabled:!!t.fileList.length,"on-success":t.uploadSuccess,"on-remove":t.removeFile}},[r("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("上传文件")])],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.manualModalCancel}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.manualModalOk}},[t._v("保存")])],1)],1),r("Modal",{staticClass:"paymentFooter",attrs:{title:"订单核销",scrollable:"",width:"400","class-name":"vertical-center-modal"},model:{value:t.modals2,callback:function(e){t.modals2=e},expression:"modals2"}},[r("Form",{ref:"writeOffFrom",staticClass:"tabform",attrs:{model:t.writeOffFrom,rules:t.writeOffRules,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{prop:"code","label-for":"code"}},[r("Input",{staticStyle:{width:"100%"},attrs:{search:"","enter-button":"验证",type:"text",placeholder:"请输入12位核销码",number:""},on:{"on-search":function(e){return t.search("writeOffFrom")}},model:{value:t.writeOffFrom.code,callback:function(e){t.$set(t.writeOffFrom,"code",t._n(e))},expression:"writeOffFrom.code"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{attrs:{type:"primary"},on:{click:t.ok}},[t._v("立即核销")]),r("Button",{on:{click:function(e){return t.del("writeOffFrom")}}},[t._v("取消")])],1)],1),r("auto-send",{ref:"sends",attrs:{selectArr:t.checkUidList,isAll:t.isAll}}),r("queue-list",{ref:"queue"}),r("Modal",{attrs:{title:"手动退款",width:"960","class-name":"refund-modal"},on:{"on-visible-change":t.visibleChange},model:{value:t.refundModal,callback:function(e){t.refundModal=e},expression:"refundModal"}},[r("Form",{attrs:{"label-width":100}},[r("FormItem",{attrs:{label:"退款金额：",required:""}},[r("InputNumber",{staticClass:"w-408",model:{value:t.refundMoney,callback:function(e){t.refundMoney=e},expression:"refundMoney"}})],1),r("FormItem",{attrs:{label:"退款说明："}},[r("Input",{staticClass:"w-408",attrs:{placeholder:"请输入退款说明"},model:{value:t.refund_explain,callback:function(e){t.refund_explain=e},expression:"refund_explain"}})],1),t.refundProductNum>1?r("FormItem",{attrs:{label:"分单退款："}},[r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.is_split_order,callback:function(e){t.is_split_order=e},expression:"is_split_order"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"tips"},[t._v("可选择表格中的商品单独退款，退款后且不能撤回，请谨慎操作！")]),t.is_split_order?r("Table",{ref:"refundTable",attrs:{"max-height":"500",columns:t.refundColumns,data:t.refundProduct},on:{"on-selection-change":t.refundSelectionChange},scopedSlots:t._u([{key:"product",fn:function(e){var a=e.row;return[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image-wrap"},[r("img",{staticClass:"image",attrs:{src:a.productInfo.attrInfo.image}})]),r("div",{staticClass:"title"},[t._v(t._s(a.productInfo.store_name))])]}},{key:"action",fn:function(e){var a=e.row;return[r("InputNumber",{attrs:{max:a.cart_num-a.refund_num,min:1,precision:0,"controls-outside":""},on:{"on-change":function(e){return t.refundNumChange(a)}},model:{value:a.refundNum,callback:function(e){t.$set(a,"refundNum",e)},expression:"row.refundNum"}})]}}],null,!1,1307172905)}):t._e()],1):t._e()],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancelRefundModal}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.putOpenRefund}},[t._v("提交")])],1)],1),r("changePrice",{ref:"changePrice",on:{submitSuccess:t.submitSuccess}})],1)}),[],!1,null,"6d4a6ec8",null).exports,cardsData:a.a},data:function(){return{currentTab:"-1",cardLists:[],selection:[],orderData:{status:"",data:"",real_name:"",field_key:"all",pay_type:""},autoDisabled:!0,isAll:-1,orderDataStatus:""}},methods:{onChangeType:function(t){this.currentTab=t},changeGetTabs:function(){this.$refs.table.getTabs()},getChangeTabs:function(t){this.$refs.table.getList()},getData:function(t){this.$refs.table&&(this.$refs.table.checkBox=!1,this.$refs.table.getList(t))},getCards:function(t){this.cardLists=t},handleResize:function(){this.$refs.ellipsis.forEach((function(t){return t.init()}))},orderSelect:function(t){this.selection=t},onOrderData:function(t){this.orderData=t},orderDatas:function(t){this.orderData=t},onAutoDisabled:function(t){this.autoDisabled=!!t},onAll:function(t){this.isAll=t},selectChange2:function(t){this.orderDataStatus=t}},mounted:function(){}});function G(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function Q(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}r("1a4a");var Y={name:"list",components:{productlistDetails:Object(f.a)(H,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[r("div",{staticClass:"new_card_pd"},[r("table-form",{attrs:{"is-all":t.isAll,"auto-disabled":t.autoDisabled,"form-selection":t.selection,orderDataStatus:t.orderDataStatus},on:{getList:t.getData,"order-data":t.orderDatas,onChangeType:t.onChangeType}})],1)]),t.cardLists.length>=0?r("cards-data",{attrs:{cardLists:t.cardLists}}):t._e(),r("Card",{attrs:{bordered:!1,"dis-hover":""}},[r("table-list",{ref:"table",attrs:{where:t.orderData,"is-all":t.isAll,currentTab:t.currentTab},on:{"on-all":t.onAll,"auto-disabled":t.onAutoDisabled,"order-data":t.onOrderData,"on-changeCards":t.getCards,changeGetTabs:t.changeGetTabs,"order-select":t.orderSelect,selectChange2:t.selectChange2}})],1)],1)}),[],!1,null,"084f29f8",null).exports},data:function(){return{spinShow:!1,currentTab:"",data:[],tablists:null}},created:function(){this.getOrderType(""),this.getOrderStatus(""),this.getOrderTime(""),this.getOrderNum(""),this.getfieldKey(""),this.onChangeTabs(""),this.getSupplier_id(""),this.getStore_id(""),this.getType_id("")},beforeDestroy:function(){this.getOrderType(""),this.getOrderStatus(""),this.getOrderTime(""),this.getOrderNum(""),this.getfieldKey(""),this.onChangeTabs(""),this.getSupplier_id(""),this.getStore_id(""),this.getType_id("")},mounted:function(){},methods:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?G(r,!0).forEach((function(e){Q(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):G(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(n.d)("admin/order",["getOrderStatus","getOrderTime","getOrderNum","getfieldKey","onChangeTabs","getOrderType","getSupplier_id","getStore_id","getType_id"]))},J=(r("753a"),Object(f.a)(Y,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[(this.currentTab,e("productlist-details",{ref:"productlist"})),this.spinShow?e("Spin",{attrs:{size:"large",fix:""}}):this._e()],1)}),[],!1,null,"fe8076ec",null));e.default=J.exports},6083:function(t,e,r){},"61f8":function(t,e,r){"use strict";var a=r("a34a"),i=r.n(a),s=r("f8b7");function n(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}var o={name:"orderMark",props:{orderId:Number,remarkType:{default:"",type:String}},data:function(){return{formValidate:{remark:""},modals:!1,ruleValidate:{remark:[{required:!0,message:"请输入备注信息",trigger:"blur"}]}}},methods:{cancel:function(t){this.modals=!1,this.$refs[t].resetFields()},putRemark:function(t){var e=this,r={id:this.orderId,remark:this.formValidate};this.$refs[t].validate((function(a){a?(e.remarkType?s.O:s.P)(r).then(function(){var r,a=(r=i.a.mark((function r(a){return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:e.$Message.success(a.msg),e.modals=!1,e.$refs[t].resetFields(),e.$emit("submitFail");case 4:case"end":return r.stop()}}),r)})),function(){var t=this,e=arguments;return new Promise((function(a,i){var s=r.apply(t,e);function o(t){n(s,a,i,o,l,"next",t)}function l(t){n(s,a,i,o,l,"throw",t)}o(void 0)}))});return function(t){return a.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)})):e.$Message.warning("请填写备注信息")}))}}},l=r("2877"),c=Object(l.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"备注",closable:!1},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"备注：",prop:"remark"}},[r("Input",{staticStyle:{width:"100%"},attrs:{maxlength:"200","show-word-limit":"",type:"textarea",placeholder:"订单备注"},model:{value:t.formValidate.remark,callback:function(e){t.$set(t.formValidate,"remark",e)},expression:"formValidate.remark"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:function(e){return t.cancel("formValidate")}}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:function(e){return t.putRemark("formValidate")}}},[t._v("提交")])],1)],1)}),[],!1,null,"19c66464",null);e.a=c.exports},"6f6b":function(t,e,r){"use strict";var a=r("f2c5");r.n(a).a},"753a":function(t,e,r){"use strict";var a=r("fc83");r.n(a).a},"7c76":function(t,e,r){},"86d6":function(t,e,r){"use strict";var a=r("2f5e");r.n(a).a},"97bc":function(t,e,r){},"9cc7":function(t,e,r){"use strict";var a=r("0a1e");r.n(a).a},a39d:function(t,e,r){"use strict";var a=r("2e31");r.n(a).a},a464:function(t,e,r){"use strict";var a={name:"table-expand",props:{row:Object}},i=(r("9cc7"),r("2877")),s=Object(i.a)(a,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"tdinfo"},[r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商品总价：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.total_price)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("下单时间：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.add_time)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("推广人：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.spread_nickname?t.row.spread_nickname:"无")}})])],1),r("Row",[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("用户备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.mark?t.row.mark:"无")}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商家备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.remark?t.row.remark:"无")}})])],1)],1)}),[],!1,null,"b662070e",null);e.a=s.exports},a553:function(t,e,r){},a584:function(t,e,r){"use strict";var a;function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=(i(a={name:"cards",data:function(){return{}},props:{cardLists:Array}},"data",(function(){return{}})),i(a,"methods",{}),i(a,"created",(function(){})),a),n=(r("e83b"),r("2877")),o=Object(n.a)(s,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Row",{staticClass:"ivu-mt",attrs:{type:"flex",align:"middle",gutter:16}},t._l(t.cardLists,(function(e,a){return r("Col",{key:a,staticClass:"ivu-mb",attrs:{xs:24,sm:24,md:12,lg:12,xl:8,xxl:{span:e.col}}},[r("Card",{staticClass:"card_cent",attrs:{shadow:"",padding:0}},[r("div",{staticClass:"card_box"},[r("div",{staticClass:"card_box_cir",class:{one:a%5==0,two:a%5==1,three:a%5==2,four:a%5==3,five:a%5==4}},[r("div",{staticClass:"card_box_cir1",class:{one1:a%5==0,two1:a%5==1,three1:a%5==2,four1:a%5==3,five1:a%5==4}},[e.type?r("span",{staticClass:"iconfont",class:e.className}):r("Icon",{attrs:{type:e.className}})],1)]),r("div",{staticClass:"card_box_txt"},[r("span",{staticClass:"sp1",domProps:{textContent:t._s(e.count||0)}}),r("span",{staticClass:"sp2",domProps:{textContent:t._s(e.name)}})])])])],1)})),1)],1)}),[],!1,null,"566bac57",null);e.a=o.exports},a6b9:function(t,e,r){"use strict";r.d(e,"F",(function(){return i})),r.d(e,"k",(function(){return s})),r.d(e,"a",(function(){return n})),r.d(e,"v",(function(){return o})),r.d(e,"w",(function(){return l})),r.d(e,"l",(function(){return c})),r.d(e,"h",(function(){return d})),r.d(e,"b",(function(){return u})),r.d(e,"c",(function(){return p})),r.d(e,"t",(function(){return f})),r.d(e,"o",(function(){return m})),r.d(e,"p",(function(){return h})),r.d(e,"r",(function(){return v})),r.d(e,"u",(function(){return _})),r.d(e,"G",(function(){return g})),r.d(e,"q",(function(){return y})),r.d(e,"j",(function(){return b})),r.d(e,"n",(function(){return w})),r.d(e,"x",(function(){return I})),r.d(e,"g",(function(){return x})),r.d(e,"f",(function(){return C})),r.d(e,"m",(function(){return D})),r.d(e,"i",(function(){return k})),r.d(e,"B",(function(){return O})),r.d(e,"C",(function(){return j})),r.d(e,"E",(function(){return S})),r.d(e,"d",(function(){return T})),r.d(e,"D",(function(){return $})),r.d(e,"e",(function(){return M})),r.d(e,"y",(function(){return P})),r.d(e,"A",(function(){return L})),r.d(e,"H",(function(){return E})),r.d(e,"z",(function(){return A})),r.d(e,"s",(function(){return F}));var a=r("b6bd");function i(t){return Object(a.a)({url:"supplier/supplier",method:"get",params:t})}function s(t){return Object(a.a)({url:"/supplier/supplier/".concat(t),method:"get"})}function n(t){return Object(a.a)({url:"supplier/supplier",method:"post",data:t})}function o(t,e){return Object(a.a)({url:"supplier/supplier/".concat(t),method:"put",data:e})}function l(t,e){return Object(a.a)({url:"/supplier/supplier/set_status/".concat(t,"/").concat(e),method:"put"})}function c(t){return Object(a.a)({url:"/supplier/list",method:"get",params:t})}function d(t){return Object(a.a)({url:"/supplier/order/list",method:"get",params:t})}function u(t){return Object(a.a)({url:"/supplier/order/deliver_remind/".concat(t.supplier_id,"/").concat(t.id),method:"put"})}function p(t){return Object(a.a)({url:"/supplier/order/distribution_info",method:"get",params:{ids:t}})}function f(t){return Object(a.a)({url:"/supplier/refund/list",method:"get",params:t})}function m(t){return Object(a.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function h(t){return Object(a.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function v(t){return Object(a.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function _(t){return Object(a.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function g(t){return Object(a.a)({url:"/supplier/supplier/login/".concat(t),method:"get"})}function y(t){return Object(a.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function b(t){return Object(a.a)({url:"/supplier/refund/detail/".concat(t),method:"get"})}function w(t){return Object(a.a)({url:"/supplier/order/no_refund/".concat(t),method:"get"})}function I(t){return Object(a.a)({url:"/supplier/order/refund_integral/".concat(t),method:"get"})}function x(t){return Object(a.a)({url:"/supplier/order/distribution/".concat(t),method:"get"})}function C(t){return Object(a.a)({url:"/supplier/apply/list",method:"get",params:t})}function D(t){return Object(a.a)({url:"/supplier/apply/verify/form/".concat(t),method:"get"})}function k(t){return Object(a.a)({url:"/supplier/apply/mark/form/".concat(t),method:"get"})}function O(t){return Object(a.a)({url:"/supplier/flowing_water/fund_record_info",method:"get",params:t})}function j(t){return Object(a.a)({url:"/supplier/flowing_water/list",method:"get",params:t})}function S(t,e){return Object(a.a)({url:"/supplier/flowing_water/mark/".concat(t),method:"put",params:e})}function T(t){return Object(a.a)({url:"/export/supplierWaterExport",method:"get",params:t})}function $(t){return Object(a.a)({url:"/supplier/flowing_water/fund_record",method:"get",params:t})}function M(t){return Object(a.a)({url:"/export/supplierWaterRecord",method:"get",params:t})}function P(t){return Object(a.a)({url:"/supplier/extract/list",method:"get",params:t})}function L(t,e){return Object(a.a)({url:"/supplier/extract/verify/".concat(t),method:"post",data:e})}function E(t){return Object(a.a)({url:"/supplier/extract/transfer/".concat(t),method:"get"})}function A(t,e){return Object(a.a)({url:"/supplier/extract/mark/".concat(t),method:"post",data:e})}function F(t){return Object(a.a)({url:"supplier/order/chart",method:"get",params:t})}},add5:function(t,e,r){var a;window,a=function(){return function(t){var e={};function r(a){if(e[a])return e[a].exports;var i=e[a]={i:a,l:!1,exports:{}};return t[a].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,a){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(a,i,function(e){return t[e]}.bind(null,i));return a},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0)}({"./src/index.js":function(t,e,r){"use strict";r.r(e),r("./src/sass/index.scss");var a=r("./src/js/init.js").default.init;"undefined"!=typeof window&&(window.printJS=a),e.default=a},"./src/js/browser.js":function(t,e,r){"use strict";r.r(e);var a={isFirefox:function(){return"undefined"!=typeof InstallTrigger},isIE:function(){return-1!==navigator.userAgent.indexOf("MSIE")||!!document.documentMode},isEdge:function(){return!a.isIE()&&!!window.StyleMedia},isChrome:function(){return!!(arguments.length>0&&void 0!==arguments[0]?arguments[0]:window).chrome},isSafari:function(){return Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")>0||-1!==navigator.userAgent.toLowerCase().indexOf("safari")},isIOSChrome:function(){return-1!==navigator.userAgent.toLowerCase().indexOf("crios")}};e.default=a},"./src/js/functions.js":function(t,e,r){"use strict";r.r(e),r.d(e,"addWrapper",(function(){return n})),r.d(e,"capitalizePrint",(function(){return o})),r.d(e,"collectStyles",(function(){return l})),r.d(e,"addHeader",(function(){return d})),r.d(e,"cleanUp",(function(){return u})),r.d(e,"isRawHTML",(function(){return p}));var a=r("./src/js/modal.js"),i=r("./src/js/browser.js");function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){return'<div style="font-family:'+e.font+" !important; font-size: "+e.font_size+' !important; width:100%;">'+t+"</div>"}function o(t){return t.charAt(0).toUpperCase()+t.slice(1)}function l(t,e){for(var r="",a=(document.defaultView||window).getComputedStyle(t,""),i=0;i<a.length;i++)(-1!==e.targetStyles.indexOf("*")||-1!==e.targetStyle.indexOf(a[i])||c(e.targetStyles,a[i]))&&a.getPropertyValue(a[i])&&(r+=a[i]+":"+a.getPropertyValue(a[i])+";");return r+"max-width: "+e.maxWidth+"px !important; font-size: "+e.font_size+" !important;"}function c(t,e){for(var r=0;r<t.length;r++)if("object"===s(e)&&-1!==e.indexOf(t[r]))return!0;return!1}function d(t,e){var r=document.createElement("div");if(p(e.header))r.innerHTML=e.header;else{var a=document.createElement("h1"),i=document.createTextNode(e.header);a.appendChild(i),a.setAttribute("style",e.headerStyle),r.appendChild(a)}t.insertBefore(r,t.childNodes[0])}function u(t){t.showModal&&a.default.close(),t.onLoadingEnd&&t.onLoadingEnd(),(t.showModal||t.onLoadingStart)&&window.URL.revokeObjectURL(t.printable);var e="mouseover";(i.default.isChrome()||i.default.isFirefox())&&(e="focus"),window.addEventListener(e,(function r(){window.removeEventListener(e,r),t.onPrintDialogClose();var a=document.getElementById(t.frameId);a&&a.remove()}))}function p(t){return new RegExp("<([A-Za-z][A-Za-z0-9]*)\\b[^>]*>(.*?)</\\1>").test(t)}},"./src/js/html.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/functions.js"),i=r("./src/js/print.js");function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.default={print:function(t,e){var r,n="object"===s(r=t.printable)&&r&&(r instanceof HTMLElement||1===r.nodeType)?t.printable:document.getElementById(t.printable);n?(t.printableElement=function t(e,r){for(var i=e.cloneNode(),s=Array.prototype.slice.call(e.childNodes),n=0;n<s.length;n++)if(-1===r.ignoreElements.indexOf(s[n].id)){var o=t(s[n],r);i.appendChild(o)}switch(r.scanStyles&&1===e.nodeType&&i.setAttribute("style",Object(a.collectStyles)(e,r)),e.tagName){case"SELECT":i.value=e.value;break;case"CANVAS":i.getContext("2d").drawImage(e,0,0)}return i}(n,t),t.header&&Object(a.addHeader)(t.printableElement,t),i.default.send(t,e)):window.console.error("Invalid HTML element id: "+t.printable)}}},"./src/js/image.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/functions.js"),i=r("./src/js/print.js"),s=r("./src/js/browser.js");e.default={print:function(t,e){t.printable.constructor!==Array&&(t.printable=[t.printable]),t.printableElement=document.createElement("div"),t.printable.forEach((function(e){var r=document.createElement("img");if(r.setAttribute("style",t.imageStyle),r.src=e,s.default.isFirefox()){var a=r.src;r.src=a}var i=document.createElement("div");i.appendChild(r),t.printableElement.appendChild(i)})),t.header&&Object(a.addHeader)(t.printableElement,t),i.default.send(t,e)}}},"./src/js/init.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/browser.js"),i=r("./src/js/modal.js"),s=r("./src/js/pdf.js"),n=r("./src/js/html.js"),o=r("./src/js/raw-html.js"),l=r("./src/js/image.js"),c=r("./src/js/json.js");function d(t){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["pdf","html","image","json","raw-html"];e.default={init:function(){var t={printable:null,fallbackPrintable:null,type:"pdf",header:null,headerStyle:"font-weight: 300;",maxWidth:800,properties:null,gridHeaderStyle:"font-weight: bold; padding: 5px; border: 1px solid #dddddd;",gridStyle:"border: 1px solid lightgray; margin-bottom: -1px;",showModal:!1,onError:function(t){throw t},onLoadingStart:null,onLoadingEnd:null,onPrintDialogClose:function(){},onIncompatibleBrowser:function(){},modalMessage:"Retrieving Document...",frameId:"printJS",printableElement:null,documentTitle:"Document",targetStyle:["clear","display","width","min-width","height","min-height","max-height"],targetStyles:["border","box","break","text-decoration"],ignoreElements:[],repeatTableHeader:!0,css:null,style:null,scanStyles:!0,base64:!1,onPdfOpen:null,font:"TimesNewRoman",font_size:"12pt",honorMarginPadding:!0,honorColor:!1,imageStyle:"max-width: 100%;"},e=arguments[0];if(void 0===e)throw new Error("printJS expects at least 1 attribute.");switch(d(e)){case"string":t.printable=encodeURI(e),t.fallbackPrintable=t.printable,t.type=arguments[1]||t.type;break;case"object":for(var r in t.printable=e.printable,t.fallbackPrintable=void 0!==e.fallbackPrintable?e.fallbackPrintable:t.printable,t.fallbackPrintable=t.base64?"data:application/pdf;base64,".concat(t.fallbackPrintable):t.fallbackPrintable,t)"printable"!==r&&"fallbackPrintable"!==r&&(t[r]=void 0!==e[r]?e[r]:t[r]);break;default:throw new Error('Unexpected argument type! Expected "string" or "object", got '+d(e))}if(!t.printable)throw new Error("Missing printable information.");if(!t.type||"string"!=typeof t.type||-1===u.indexOf(t.type.toLowerCase()))throw new Error("Invalid print type. Available types are: pdf, html, image and json.");t.showModal&&i.default.show(t),t.onLoadingStart&&t.onLoadingStart();var p=document.getElementById(t.frameId);p&&p.parentNode.removeChild(p);var f=document.createElement("iframe");switch(a.default.isFirefox()?f.setAttribute("style","width: 1px; height: 100px; position: fixed; left: 0; top: 0; opacity: 0; border-width: 0; margin: 0; padding: 0"):f.setAttribute("style","visibility: hidden; height: 0; width: 0; position: absolute; border: 0"),f.setAttribute("id",t.frameId),"pdf"!==t.type&&(f.srcdoc="<html><head><title>"+t.documentTitle+"</title>",t.css&&(Array.isArray(t.css)||(t.css=[t.css]),t.css.forEach((function(t){f.srcdoc+='<link rel="stylesheet" href="'+t+'">'}))),f.srcdoc+="</head><body></body></html>"),t.type){case"pdf":if(a.default.isIE())try{window.open(t.fallbackPrintable,"_blank").focus(),t.onIncompatibleBrowser()}catch(e){t.onError(e)}finally{t.showModal&&i.default.close(),t.onLoadingEnd&&t.onLoadingEnd()}else s.default.print(t,f);break;case"image":l.default.print(t,f);break;case"html":n.default.print(t,f);break;case"raw-html":o.default.print(t,f);break;case"json":c.default.print(t,f)}}}},"./src/js/json.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/functions.js"),i=r("./src/js/print.js");function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.default={print:function(t,e){if("object"!==s(t.printable))throw new Error("Invalid javascript data object (JSON).");if("boolean"!=typeof t.repeatTableHeader)throw new Error("Invalid value for repeatTableHeader attribute (JSON).");if(!t.properties||!Array.isArray(t.properties))throw new Error("Invalid properties array for your JSON data.");t.properties=t.properties.map((function(e){return{field:"object"===s(e)?e.field:e,displayName:"object"===s(e)?e.displayName:e,columnSize:"object"===s(e)&&e.columnSize?e.columnSize+";":100/t.properties.length+"%;"}})),t.printableElement=document.createElement("div"),t.header&&Object(a.addHeader)(t.printableElement,t),t.printableElement.innerHTML+=function(t){var e=t.printable,r=t.properties,i='<table style="border-collapse: collapse; width: 100%;">';t.repeatTableHeader&&(i+="<thead>"),i+="<tr>";for(var s=0;s<r.length;s++)i+='<th style="width:'+r[s].columnSize+";"+t.gridHeaderStyle+'">'+Object(a.capitalizePrint)(r[s].displayName)+"</th>";i+="</tr>",t.repeatTableHeader&&(i+="</thead>"),i+="<tbody>";for(var n=0;n<e.length;n++){i+="<tr>";for(var o=0;o<r.length;o++){var l=e[n],c=r[o].field.split(".");if(c.length>1)for(var d=0;d<c.length;d++)l=l[c[d]];else l=l[r[o].field];i+='<td style="width:'+r[o].columnSize+t.gridStyle+'">'+l+"</td>"}i+="</tr>"}return i+"</tbody></table>"}(t),i.default.send(t,e)}}},"./src/js/modal.js":function(t,e,r){"use strict";r.r(e);var a={show:function(t){var e=document.createElement("div");e.setAttribute("style","font-family:sans-serif; display:table; text-align:center; font-weight:300; font-size:30px; left:0; top:0;position:fixed; z-index: 9990;color: #0460B5; width: 100%; height: 100%; background-color:rgba(255,255,255,.9);transition: opacity .3s ease;"),e.setAttribute("id","printJS-Modal");var r=document.createElement("div");r.setAttribute("style","display:table-cell; vertical-align:middle; padding-bottom:100px;");var i=document.createElement("div");i.setAttribute("class","printClose"),i.setAttribute("id","printClose"),r.appendChild(i);var s=document.createElement("span");s.setAttribute("class","printSpinner"),r.appendChild(s);var n=document.createTextNode(t.modalMessage);r.appendChild(n),e.appendChild(r),document.getElementsByTagName("body")[0].appendChild(e),document.getElementById("printClose").addEventListener("click",(function(){a.close()}))},close:function(){var t=document.getElementById("printJS-Modal");t&&t.parentNode.removeChild(t)}};e.default=a},"./src/js/pdf.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/print.js"),i=r("./src/js/functions.js");function s(t,e,r){var i=new window.Blob([r],{type:"application/pdf"});i=window.URL.createObjectURL(i),e.setAttribute("src",i),a.default.send(t,e)}e.default={print:function(t,e){if(t.base64){var r=Uint8Array.from(atob(t.printable),(function(t){return t.charCodeAt(0)}));s(t,e,r)}else{t.printable=/^(blob|http|\/\/)/i.test(t.printable)?t.printable:window.location.origin+("/"!==t.printable.charAt(0)?"/"+t.printable:t.printable);var a=new window.XMLHttpRequest;a.responseType="arraybuffer",a.addEventListener("error",(function(){Object(i.cleanUp)(t),t.onError(a.statusText,a)})),a.addEventListener("load",(function(){if(-1===[200,201].indexOf(a.status))return Object(i.cleanUp)(t),void t.onError(a.statusText,a);s(t,e,a.response)})),a.open("GET",t.printable,!0),a.send()}}}},"./src/js/print.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/browser.js"),i=r("./src/js/functions.js"),s={send:function(t,e){document.getElementsByTagName("body")[0].appendChild(e);var r=document.getElementById(t.frameId);r.onload=function(){if("pdf"!==t.type){var e=r.contentWindow||r.contentDocument;if(e.document&&(e=e.document),e.body.appendChild(t.printableElement),"pdf"!==t.type&&t.style){var i=document.createElement("style");i.innerHTML=t.style,e.head.appendChild(i)}var s=e.getElementsByTagName("img");s.length>0?function(t){var e=t.map((function(t){if(t.src&&t.src!==window.location.href)return function(t){return new Promise((function(e){!function r(){t&&void 0!==t.naturalWidth&&0!==t.naturalWidth&&t.complete?e():setTimeout(r,500)}()}))}(t)}));return Promise.all(e)}(Array.from(s)).then((function(){return n(r,t)})):n(r,t)}else a.default.isFirefox()?setTimeout((function(){return n(r,t)}),1e3):n(r,t)}}};function n(t,e){try{if(t.focus(),a.default.isEdge()||a.default.isIE())try{t.contentWindow.document.execCommand("print",!1,null)}catch(e){t.contentWindow.print()}else t.contentWindow.print()}catch(t){e.onError(t)}finally{a.default.isFirefox()&&(t.style.visibility="hidden",t.style.left="-1px"),Object(i.cleanUp)(e)}}e.default=s},"./src/js/raw-html.js":function(t,e,r){"use strict";r.r(e);var a=r("./src/js/print.js");e.default={print:function(t,e){t.printableElement=document.createElement("div"),t.printableElement.setAttribute("style","width:100%"),t.printableElement.innerHTML=t.printable,a.default.send(t,e)}}},"./src/sass/index.scss":function(t,e,r){},0:function(t,e,r){t.exports=r("./src/index.js")}}).default},t.exports=a()},b1e3:function(t,e,r){"use strict";var a=r("41e0");r.n(a).a},b37b:function(t,e,r){"use strict";var a=r("97bc");r.n(a).a},bd9b:function(t,e){t.exports="data:image/jpeg;base64,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"},c4f1:function(t,e,r){},c72b:function(t,e,r){},d616:function(t,e,r){"use strict";var a=r("a34a"),i=r.n(a),s=r("2f62"),n=r("add5"),o=r.n(n),l=r("f8b7");function c(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function n(t){c(s,a,i,n,o,"next",t)}function o(t){c(s,a,i,n,o,"throw",t)}n(void 0)}))}}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function p(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var f={name:"orderSend",props:{orderId:Number,status:Number,pay_type:String},data:function(){var t=this;return{productType:0,orderStatus:0,splitSwitch:!1,formItem:{type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:"",station_type:1,delivery_type:"1",cargo_weight:0,remark:"",mark:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0,manyFormValidate:[],stopSubmit:!1,header:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"image",width:200,align:"center"},{title:"规格",slot:"value",align:"center",minWidth:120},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:100},{title:"商品优惠价",slot:"price",align:"center",minWidth:100},{title:"总数",key:"cart_num",align:"center",minWidth:80},{title:"待发数量",key:"surplus_num",align:"center",width:180,render:function(e,r){return e("div",[e("InputNumber",{props:{min:1,max:r.row.numShow,value:r.row.surplus_num||1},on:{"on-change":function(e){r.row.surplus_num=e||1,t.manyFormValidate[r.index]=r.row,t.selectData.forEach((function(e,a){e.cart_id===r.row.cart_id&&t.selectData.splice(a,1,r.row)}))}}})])}}],selectData:[],sheetInfo:{}}},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(r,!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(s.e)("admin/order",["splitOrder"])),methods:{printImg:function(t){o()({printable:t,type:"image",documentTitle:"快递信息",style:"img{\n          width: 100%;\n          height: 476px;\n        }"})},changeDelivery:function(t){},selectOne:function(t){this.selectData=t},changeModal:function(t){t||this.cancel()},changeSplitStatus:function(t){var e=this;t&&Object(l.X)(this.orderId).then((function(t){var r=t.data;r.forEach((function(t){t.numShow=t.surplus_num})),e.manyFormValidate=r}))},changeRadio:function(t){switch(this.$refs.formItem.resetFields(),t){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="1",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="",this.sheetInfo.self_delivery_status?this.formItem.delivery_type="1":this.formItem.delivery_type="2",this.sheetInfo.dada_delivery_status?this.formItem.station_type=1:this.formItem.station_type=2;break;case"3":this.formItem.fictitious_content=""}},changeExpress:function(t){switch(t){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[],this.getList(2);break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.getList(1)}},reset:function(){this.formItem={type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:"",station_type:1,delivery_type:"1",cargo_weight:0,remark:"",mark:""}},getList:function(t){var e=this,r=2===t?1:"";Object(l.j)(r).then(function(){var t=d(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.express=r.data,e.getSheetInfo();case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},putSend:function(t){var e=this,r={id:this.orderId,datas:this.formItem};if("1"===this.formItem.type&&"2"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("1"===this.formItem.type&&"1"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.delivery_id)return this.$Message.error("快递单号不能为空")}if("2"===this.formItem.type){if("1"===this.formItem.delivery_type&&""===this.formItem.sh_delivery)return this.$Message.error("送货人不能为空");if("2"===this.formItem.delivery_type&&this.formItem.cargo_weight<=0)return this.$Message.error("请输入有效的重量")}this.splitSwitch?(r.datas.cart_ids=[],this.selectData.forEach((function(t){r.datas.cart_ids.push({cart_id:t.cart_id,cart_num:t.surplus_num})})),Object(l.Y)(r).then((function(t){e.modals=!1,e.$Message.success(t.msg),e.$emit("submitFail"),e.reset(),e.splitSwitch=!1,t.data.dump.label&&e.printImg(t.data.dump.label)})).catch((function(t){e.$Message.error(t.msg)}))):Object(l.K)(r).then(function(){var t=d(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.modals=!1,e.$Message.success(r.msg),e.splitSwitch=!1,e.$emit("submitFail"),e.reset(),r.data.dump.label&&e.printImg(r.data.dump.label);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},cancel:function(t){this.modals=!1,this.orderStatus=0,this.splitSwitch=!1,this.selectData=[],this.reset()},expressChange:function(t){var e=this,r=this.express.find((function(e){return e.value===t}));void 0!==r&&(this.formItem.delivery_code=r.code,"2"===this.formItem.express_record_type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(l.z)({com:this.formItem.delivery_code}).then((function(t){e.expressTemp=t.data,t.data.length||e.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(t){e.$Message.error(t.msg)}))))},getCartInfo:function(){var t=this;Object(l.X)(this.orderId).then((function(e){var r=e.data;r.forEach((function(t){t.numShow=t.surplus_num})),t.manyFormValidate=r,t.productType=r[0].product_type,3==t.productType&&(t.formItem.type="3",t.formItem.fictitious_content="")}))},getDeliveryList:function(){var t=this;Object(l.x)().then((function(e){t.deliveryList=e.data.list})).catch((function(e){t.$Message.error(e.msg)}))},getSheetInfo:function(){var t=this;Object(l.I)().then((function(e){var r=e.data;for(var a in r)r.hasOwnProperty(a)&&(t.formItem[a]=r[a]);t.export_open=void 0===r.export_open||r.export_open,t.export_open||(t.formItem.express_record_type="1"),t.formItem.to_addr=r.to_add,t.sheetInfo=r})).catch((function(e){t.$Message.error(e.msg)}))},shDeliveryChange:function(t){if(t){var e=this.deliveryList.find((function(e){return e.id===t}));this.formItem.sh_delivery_name=e.wx_name,this.formItem.sh_delivery_id=e.phone,this.formItem.sh_delivery_uid=e.uid}},expressTempChange:function(t){this.temp=this.expressTemp.find((function(e){return t===e.temp_id})),void 0===this.temp&&(this.temp={})},preview:function(){this.$refs.viewer.$viewer.show()}}},m=(r("b1e3"),r("2877")),h=Object(m.a)(f,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,width:"1000"},on:{"on-visible-change":t.changeModal},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?r("Form",{ref:"formItem",attrs:{model:t.formItem,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"选择类型："}},[r("RadioGroup",{on:{"on-change":t.changeRadio},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[t.productType?t._e():r("Radio",{attrs:{label:"1"}},[t._v("发货")]),!t.productType&&t.sheetInfo.city_delivery_status?r("Radio",{attrs:{label:"2"}},[t._v("送货")]):t._e(),r("Radio",{attrs:{label:"3"}},[t._v("无需配送")])],1)],1),1==t.formItem.type?r("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.export_open,expression:"export_open"}],attrs:{label:"发货类型："}},[r("RadioGroup",{on:{"on-change":t.changeExpress},model:{value:t.formItem.express_record_type,callback:function(e){t.$set(t.formItem,"express_record_type",e)},expression:"formItem.express_record_type"}},[r("Radio",{attrs:{label:"1"}},[t._v("手动填写")]),r("Radio",{attrs:{label:"2"}},[t._v("电子面单打印")])],1)],1):t._e(),r("div",[1==t.formItem.type?r("FormItem",{attrs:{label:"快递公司：",required:""}},[r("Select",{staticClass:"input-add",attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":t.expressChange},model:{value:t.formItem.delivery_name,callback:function(e){t.$set(t.formItem,"delivery_name",e)},expression:"formItem.delivery_name"}},t._l(t.express,(function(e,a){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1):t._e(),"1"===t.formItem.express_record_type&&1==t.formItem.type?r("FormItem",{attrs:{label:"快递单号：",required:""}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入快递单号"},model:{value:t.formItem.delivery_id,callback:function(e){t.$set(t.formItem,"delivery_id",e)},expression:"formItem.delivery_id"}}),"顺丰速运"==t.formItem.delivery_name?r("div",{staticClass:"trips"},[r("p",[t._v("顺丰请输入单号 :收件人或寄件人手机号后四位，")]),r("p",[t._v("例如：SF000000000000:3941")])]):t._e()],1):t._e(),"2"===t.formItem.express_record_type&&"1"===t.formItem.type?[r("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单："}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择电子面单"},on:{"on-change":t.expressTempChange},model:{value:t.formItem.express_temp_id,callback:function(e){t.$set(t.formItem,"express_temp_id",e)},expression:"formItem.express_temp_id"}},t._l(t.expressTemp,(function(e,a){return r("Option",{key:a,attrs:{value:e.temp_id}},[t._v(t._s(e.title))])})),1),t.formItem.express_temp_id?r("Button",{attrs:{type:"text"},on:{click:t.preview}},[t._v("预览")]):t._e()],1),r("FormItem",{attrs:{label:"寄件人姓名："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.to_name,callback:function(e){t.$set(t.formItem,"to_name",e)},expression:"formItem.to_name"}})],1),r("FormItem",{attrs:{label:"寄件人电话："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.to_tel,callback:function(e){t.$set(t.formItem,"to_tel",e)},expression:"formItem.to_tel"}})],1),r("FormItem",{attrs:{label:"寄件人地址："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.to_addr,callback:function(e){t.$set(t.formItem,"to_addr",e)},expression:"formItem.to_addr"}})],1)]:t._e()],2),r("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.formItem.type,expression:"formItem.type === '2'"}]},[r("FormItem",{attrs:{label:"配送类型："}},[r("RadioGroup",{on:{"on-change":t.changeDelivery},model:{value:t.formItem.delivery_type,callback:function(e){t.$set(t.formItem,"delivery_type",e)},expression:"formItem.delivery_type"}},[t.sheetInfo.self_delivery_status?r("Radio",{attrs:{label:"1"}},[t._v("商家配送")]):t._e(),t.sheetInfo.dada_delivery_status||t.sheetInfo.uu_delivery_status?r("Radio",{attrs:{label:"2"}},[t._v("第三方配送")]):t._e()],1)],1),"1"===t.formItem.delivery_type?r("FormItem",{attrs:{label:"送货人：",required:""}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择送货人"},on:{"on-change":t.shDeliveryChange},model:{value:t.formItem.sh_delivery,callback:function(e){t.$set(t.formItem,"sh_delivery",e)},expression:"formItem.sh_delivery"}},t._l(t.deliveryList,(function(e,a){return r("Option",{key:a,attrs:{value:e.id}},[t._v(t._s(e.wx_name)+"（"+t._s(e.phone)+"）")])})),1)],1):t._e(),"2"===t.formItem.delivery_type?r("div",[r("FormItem",{attrs:{label:"配送平台："}},[r("RadioGroup",{model:{value:t.formItem.station_type,callback:function(e){t.$set(t.formItem,"station_type",e)},expression:"formItem.station_type"}},[t.sheetInfo.dada_delivery_status?r("Radio",{attrs:{label:1}},[t._v("达达")]):t._e(),t.sheetInfo.uu_delivery_status?r("Radio",{attrs:{label:2}},[t._v("uu跑腿")]):t._e()],1)],1),r("FormItem",{attrs:{label:"包裹重量：",required:""}},[r("InputNumber",{staticClass:"input-add",attrs:{min:0},model:{value:t.formItem.cargo_weight,callback:function(e){t.$set(t.formItem,"cargo_weight",e)},expression:"formItem.cargo_weight"}}),r("span",{staticStyle:{"margin-left":"10px"}},[t._v("kg")])],1),r("FormItem",{attrs:{label:"配送备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"配送备注"},model:{value:t.formItem.remark,callback:function(e){t.$set(t.formItem,"remark",e)},expression:"formItem.remark"}})],1),r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.mark,callback:function(e){t.$set(t.formItem,"mark",e)},expression:"formItem.mark"}})],1)],1):t._e()],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.formItem.type,expression:"formItem.type === '3'"}]},[r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.fictitious_content,callback:function(e){t.$set(t.formItem,"fictitious_content",e)},expression:"formItem.fictitious_content"}})],1)],1),t.splitOrder>1&&"3"!==t.formItem.type?r("div",[r("FormItem",{attrs:{label:"分单发货："}},[r("i-switch",{attrs:{size:"large",disabled:8===t.orderStatus},on:{"on-change":t.changeSplitStatus},model:{value:t.splitSwitch,callback:function(e){t.splitSwitch=e},expression:"splitSwitch"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"trips"},[r("p",[t._v("\n            可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n          ")])]),t.splitSwitch&&t.manyFormValidate.length?r("Table",{attrs:{data:t.manyFormValidate,columns:t.header,border:""},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var a=e.row;return e.index,[r("div",{staticClass:"product-data"},[r("img",{staticClass:"image",attrs:{src:a.cart_info.productInfo.image}}),r("div",{staticClass:"line2"},[t._v("\n                "+t._s(a.cart_info.productInfo.store_name)+"\n              ")])])]}},{key:"value",fn:function(e){var a=e.row;return e.index,[r("div",[t._v(t._s(a.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var a=e.row;return e.index,[r("div",[t._v("\n              "+t._s(a.cart_info.productInfo.attrInfo?a.cart_info.productInfo.attrInfo.price:a.cart_info.productInfo.price)+"\n            ")])]}},{key:"price",fn:function(e){var a=e.row;return e.index,[r("div",[r("div",[t._v(t._s(a.cart_info.truePrice))])])]}}],null,!1,1129913299)}):t._e()],1)],1):t._e()],1):t._e(),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancel}},[t._v("取消")]),t.stopSubmit?r("Button",{attrs:{type:"warning"}},[t._v("存在售后待处理订单")]):r("Button",{attrs:{type:"primary"},on:{click:t.putSend}},[t._v("提交")])],1),r("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:t.temp,expression:"temp"}],ref:"viewer"},[r("img",{staticClass:"display-add",attrs:{src:t.temp.pic}})])],1)}),[],!1,null,"b86cc932",null);e.a=h.exports},e434:function(t,e,r){"use strict";var a=r("c4f1");r.n(a).a},e83b:function(t,e,r){"use strict";var a=r("c72b");r.n(a).a},edaf4:function(t,e,r){"use strict";var a=r("6083");r.n(a).a},f2c5:function(t,e,r){},fc48:function(t,e,r){"use strict";var a=r("a34a"),i=r.n(a),s=r("f8b7");function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function o(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l(t,e,r,a,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var s=t.apply(e,r);function n(t){l(s,a,i,n,o,"next",t)}function o(t){l(s,a,i,n,o,"throw",t)}n(void 0)}))}}var d={components:{template:r("6112").default},name:"orderDetails",filters:{invoiceType:function(t){return 1==t?"电子普通发票":"纸质专用发票"},invoiceHeaderType:function(t){return 1==t?"个人":"企业"}},data:function(){return{isShow:0,modal2:!1,modals:!1,grid:{xl:8,lg:8,md:12,sm:24,xs:24},result:[],columns1Items:[{title:"商品编码",render:function(t,e){return t("div",e.row.productInfo.spec_type?e.row.productInfo.attrInfo.code:e.row.productInfo.code)}}],columns1:[{tree:!0,title:"商品信息",slot:"product",minWidth:400,className:"table-product-column"},{title:"售价",render:function(t,e){return t("div",e.row.productInfo.attrInfo.price)}},{title:"数量",render:function(t,e){return t("div",e.row.card_product_id?1:e.row.cart_num)}},{title:"小计",render:function(t,e){return t("div",e.row.card_product_id?e.row.productInfo.attrInfo.price:e.row.productInfo.attrInfo.price*e.row.cart_num)}},{title:"实付金额",render:function(t,e){return t("div",e.row.pay_price)}}],columns2:[{title:"订单ID",key:"oid",minWidth:40},{title:"操作记录",key:"change_message",minWidth:280},{title:"操作人",key:"change_manager",minWidth:180},{title:"操作时间",key:"change_time",width:160}],columnSplit:[{title:"订单号",slot:"order_id",minWidth:100},{title:"商品信息",slot:"product",minWidth:250},{title:"发货信息",slot:"deliveryInfo",minWidth:100},{title:"操作",slot:"action",minWidth:90}],recordData:[],activeName:"detail",orderData:{},splitList:[],logisticsChecked:{delivery_name:"",delivery_id:""},writeOffColumns:[{title:"商品ID",key:"product_id",minWidth:40},{title:"图片",slot:"product",minWidth:60},{title:"商品名称",key:"store_name",minWidth:100},{title:"核销数",key:"writeoff_num",minWidth:40},{title:"核销时间",key:"add_time",minWidth:280},{title:"核销人员",key:"staff_name",minWidth:100}],writeOffData:[]}},props:{orderDatalist:Object,orderId:Number,rowActive:Object,formType:{type:Number,default:0},openErp:{type:Boolean,default:!1},distHide:{type:Number,default:0}},watch:{orderDatalist:function(t){this.orderData=t.orderInfo,this.getList(this.formType?t.orderInfo.id:t.orderInfo.store_order_id),this.formType&&this.getSplitOrder(t.orderInfo.id),11==t.orderInfo.type?"商品编码"==this.columns1[0].title&&this.columns1.shift():"商品编码"!=this.columns1[0].title&&this.columns1.unshift(this.columns1Items[0])},orderData:function(t){var e=this;t&&t.custom_form&&t.custom_form.length&&t.custom_form.forEach((function(t){t.length&&t.forEach((function(t){if(t.value)return e.isShow=1}))})),2==t.shipping_type&&this.writeoffRecords()}},mounted:function(){var t=this;this.orderData&&this.orderData.custom_form&&this.orderData.custom_form.length&&this.orderData.custom_form.forEach((function(e){e.length&&e.forEach((function(e){if(e.value)return t.isShow=1}))}))},methods:{openItemLogistics:function(t){var e=this;this.logisticsChecked.delivery_name=t.delivery_name,this.logisticsChecked.delivery_id=t.delivery_id,this.modal2=!0,Object(s.i)(t.id).then(function(){var t=c(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.result=r.data.result;case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},openLogistics:function(){this.logisticsChecked.delivery_name=this.orderDatalist.orderInfo.delivery_name,this.logisticsChecked.delivery_id=this.orderDatalist.orderInfo.delivery_id,this.modal2=!0,this.getOrderData()},openRefundLogistics:function(){var t=this;this.modal2=!0,Object(s.p)(this.orderDatalist.orderInfo.id).then((function(e){t.logisticsChecked.delivery_name=e.data.delivery_name,t.logisticsChecked.delivery_id=e.data.delivery_id,t.result=e.data.result})).catch((function(e){t.$Message.error(e.msg)}))},btnClick:function(t){var e=this,r={supplier_id:t.supplier_id,id:t.id};Object(s.W)(r).then(function(){var t=c(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$Message.success(r.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},getOrderData:function(){var t=this;Object(s.i)(this.formType?this.orderDatalist.orderInfo.id:this.orderDatalist.orderInfo.store_order_id).then(function(){var e=c(i.a.mark((function e(r){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.result=r.data.result;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},getSplitOrder:function(t){var e=this;t&&Object(s.Z)(t,{status:2}).then((function(t){e.splitList=t.data})).catch((function(t){e.$Message.error(t.msg)}))},getList:function(t){var e=this,r={id:t,datas:this.page};this.loading=!0,Object(s.m)(r).then(function(){var t=c(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.recordData=r.data,e.loading=!1;case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},changeMenu:function(t,e,r,a){if(a)this.$parent.changeMenu(e,r,a);else if(5==t){var i={};if(this.splitList.forEach((function(t){t.is_all_refund||(i=t)})),0===Object.keys(i).length)return this.$Message.error("退款处理中！");this.$parent.changeMenu(i,t)}else this.$parent.changeMenu(this.rowActive,t)},distribution:function(t,e){e?this.$parent.distribution(t):this.$parent.distribution(this.rowActive)},edit:function(t,e){e?this.$parent.edit(t):this.$parent.edit(this.rowActive)},sendOrder:function(t,e){e?this.$parent.sendOrder(t,e):this.$parent.sendOrder(this.rowActive)},delivery:function(t,e){e?this.$parent.delivery(t,e):this.$parent.delivery(this.rowActive)},bindWrite:function(t,e){e?this.$parent.bindWrite(t):this.$parent.bindWrite(this.rowActive)},writeoffRecords:function(){var t=this;Object(s.fb)(this.orderId).then((function(e){t.writeOffData=e.data}))},handleorderCardBenefits:function(t,e){Object(s.u)(this.orderDatalist.orderInfo.store_order_id||this.orderId).then((function(t){var r=t.data.map((function(t){return function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(r,!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t.cart_info,{pay_price:t.payPrice})}));e(r)}))}},computed:{}},u=(r("b37b"),r("0866"),r("2877")),p=Object(u.a)(d,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Drawer",{attrs:{closable:!1,width:"1000","class-name":"order_box",styles:{padding:0}},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.orderDatalist?a("div",[a("div",{staticClass:"head"},[a("div",{staticClass:"full"},[a("Icon",{class:{"sale-after":-1===t.orderDatalist.orderInfo._status._type},attrs:{custom:"iconfont icondingdan",size:"60"}}),a("div",{staticClass:"text"},[a("div",{staticClass:"title"},[t._v(t._s(t.orderData.pink_name||"售后订单"))]),a("div",[t._v("订单编号："+t._s(t.orderDatalist.orderInfo.order_id))])]),null==t.rowActive.delete_time?a("div",[1===t.orderData._status_new&&0===t.orderData.paid&&"offline"===t.orderData.pay_type?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("1")}}},[t._v("确认收款\n              ")]):t._e(),2!=t.orderData._status_new||t.rowActive.split.length||t.formType&&t.orderData.refund.length||t.distHide?t._e():a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:t.distribution}},[t._v("分配\n              ")]),1==t.orderData._status_new?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:t.edit}},[t._v("订单改价\n              ")]):t._e(),2!==t.orderData._status_new&&8!==t.orderData._status_new&&4!==t.orderData.status||1!==t.orderData.shipping_type||null!==t.orderData.pinkStatus&&2!==t.orderData.pinkStatus?t._e():a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:t.sendOrder}},[t._v("发送货\n              ")]),4!==t.orderData._status_new||t.rowActive.split.length?t._e():a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:t.delivery}},[t._v("\n                配送信息\n              ")]),2==t.orderData.shipping_type&&0==t.orderData.status&&1==t.orderData.paid&&0===t.orderData.refund_status?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:t.bindWrite}},[t._v("立即核销\n              ")]):t._e(),t.orderData._status_new>=2?a("Button",{on:{click:function(e){return t.changeMenu("10")}}},[t._v("小票打印")]):t._e(),t.orderData._status_new>=3&&t.orderData.express_dump?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("11")}}},[t._v("电子面单打印\n              ")]):t._e(),t.orderData.kuaidi_label?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("13")}}},[t._v("快递面单打印\n\t\t\t  ")]):t._e(),1!=t.orderData.apply_type&&5!=t.orderData.refund_type&&(4!=t.orderData.refund_type||3!=t.orderData.apply_type)||[3,6].includes(t.orderData.refund_type)||!(parseFloat(t.orderData.pay_price)>parseFloat(t.orderData.refunded_price)||0==t.orderData.pay_price)||t.formType?t._e():a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("5")}}},[t._v("立即退款\n              ")]),[2,3].includes(t.orderData.apply_type)&&[0,1,2].includes(t.orderData.refund_type)&&!t.formType?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("55")}}},[t._v("同意退货\n              ")]):t._e(),[0,1,2,5].includes(t.orderData.refund_type)&&!t.formType?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("7")}}},[t._v("不退款\n              ")]):t._e(),t.formType?t._e():a("Button",{on:{click:function(e){return t.changeMenu("4")}}},[t._v("售后备注")]),1==t.orderData.is_del?a("Button",{class:t.openErp?"on":"",attrs:{disabled:t.openErp},on:{click:function(e){return t.changeMenu("9")}}},[t._v("删除订单\n              ")]):t._e(),1!==t.orderData._status_new&&t.formType?a("Dropdown",{on:{"on-click":t.changeMenu}},[a("Button",{attrs:{icon:"ios-more"}}),a("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[1!==t.orderData._status_new||3===t.orderData._status_new&&t.orderData.use_integral>0&&t.orderData.use_integral>=t.orderData.back_integral?a("DropdownItem",{attrs:{name:"4"}},[t._v("订单备注\n                  ")]):t._e(),(0==t.orderData.refund_type||1==t.orderData.refund_type||4==t.orderData.refund_type||6==t.orderData.refund_type||5==t.orderData.refund_type)&&1==t.orderData.paid&&2!==t.orderData.refund_status&&parseFloat(t.orderData.pay_price)>0?a("DropdownItem",{attrs:{disabled:t.openErp,name:"5"}},[t._v("立即退款\n                  ")]):t._e(),4===t.orderData._status_new?a("DropdownItem",{attrs:{disabled:t.openErp,name:"8"}},[t._v("已收货")]):t._e(),t.orderData.paid?a("DropdownItem",{attrs:{name:"12"}},[t._v("打印配货单")]):t._e()],1)],1):t._e()],1):t._e()],1),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("订单状态")]),t.formType?a("div",{staticClass:"value1"},[a("span",{domProps:{innerHTML:t._s(t.orderData.status_name.status_name)}}),!t.orderData.is_all_refund&&t.orderData.refund.length?a("span",[t._v(",部分退款中")]):t._e(),t.orderData.is_all_refund&&t.orderData.refund.length&&6!=t.orderData.refund_type?a("span",[t._v(",退款中")]):t._e()]):a("div",[0==t.orderData.refund_type&&1==t.orderData.apply_type?a("div",{staticClass:"value1"},[t._v("\n                  仅退款\n                ")]):0!=t.orderData.refund_type||2!=t.orderData.apply_type&&3!=t.orderData.apply_type?3==t.orderData.refund_type?a("div",{staticClass:"value1"},[t._v("\n                  拒绝退款\n                ")]):4==t.orderData.refund_type?a("div",{staticClass:"value1"},[t._v("\n                  商品待退货\n                ")]):5==t.orderData.refund_type?a("div",{staticClass:"value1"},[t._v("\n                  退货待收货\n                ")]):6==t.orderData.refund_type?a("div",{staticClass:"value2"},[t._v("\n                  已退款\n                ")]):t._e():a("div",{staticClass:"value1"},[t._v("\n                  退货退款\n                ")])])]),t.orderData.orderStatus?a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("主订单状态")]),a("div",[a("div",[t._v("\n                  "+t._s(t.orderData.orderStatus._title)+"\n                ")])])]):t._e(),a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("实际支付")]),a("div",[t._v("\n                ¥"+t._s(t.orderDatalist.orderInfo.paid>0?t.orderDatalist.orderInfo.pay_price:0)+"\n              ")])]),t.formType?a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("支付方式")]),a("div",[t._v(t._s(t.orderDatalist.orderInfo._status._payType||"-"))])]):a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("退款件数")]),a("div",[t._v(t._s(t.orderDatalist.orderInfo.total_num||0))])]),t.formType?a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("支付时间")]),a("div",[t._v(t._s(t.orderDatalist.orderInfo._pay_time||"-"))])]):a("li",{staticClass:"item"},[a("div",{staticClass:"title"},[t._v("退款时间")]),a("div",[t._v(t._s(t.orderDatalist.orderInfo._refund_time||"-"))])])])]),a("Tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("TabPane",{attrs:{label:"订单信息",name:"detail"}},[t.formType?t._e():a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("退款信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("退款原因：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_reason||"-")+"\n                  ")])]),parseFloat(t.orderDatalist.orderInfo.refund_price)||parseFloat(t.orderDatalist.orderInfo.refunded_price)?a("li",{staticClass:"item"},[a("div",[t._v("退款金额：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(parseFloat(t.orderDatalist.orderInfo.refunded_price)?parseFloat(t.orderDatalist.orderInfo.refunded_price):parseFloat(t.orderDatalist.orderInfo.refund_price)||0)+"\n                  ")])]):t._e(),parseFloat(t.orderDatalist.orderInfo.back_integral)?a("li",{staticClass:"item"},[a("div",[t._v("退回积分：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(parseFloat(t.orderDatalist.orderInfo.back_integral)||"-")+"\n                  ")])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("退款说明：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_explain||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("退款凭证：")]),a("div",{staticClass:"value"},t._l(t.orderDatalist.orderInfo.refund_img,(function(t,e){return a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:e,staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0)])])]),!t.formType&&t.orderDatalist.orderInfo.refund_express_name?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("退货物流信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("物流公司：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_express_name||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("物流单号：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_express||"-")+"\n\t\t\t\t\t"),a("span",{staticClass:"logisticsLook",on:{click:t.openRefundLogistics}},[t._v("查询")])])]),a("li",{staticClass:"item"},[a("div",[t._v("联系电话：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_phone||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("退货说明：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refund_goods_explain||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("退货凭证：")]),a("div",{staticClass:"value"},t._l(t.orderDatalist.orderInfo.refund_goods_img,(function(t,e){return a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:e,staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0)])])]):t._e(),a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("用户信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("用户UID：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.userInfo.uid?t.orderDatalist.userInfo.uid:"游客")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("用户昵称：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.userInfo.uid?t.orderDatalist.userInfo.nickname:"游客")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("绑定电话：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.userInfo.phone||"-")+"\n                  ")])])])]),0==t.orderDatalist.orderInfo.product_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("收货信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"value"},[t._v("\n                    收货人："+t._s(t.orderDatalist.orderInfo.real_name||"-")+"\n                  ")])])]),a("ul",{staticClass:"list"},[a("li",{staticClass:"mt10"},[a("div",{staticClass:"value"},[t._v("\n                    收货电话："+t._s(t.orderDatalist.orderInfo.user_phone||"-")+"\n                  ")])])]),a("ul",{staticClass:"list"},[a("li",{staticClass:"mt10"},[a("div",{staticClass:"value"},[t._v("\n                    收货地址："+t._s(t.orderDatalist.orderInfo.user_address||"-")+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.fictitious_content&&1!=t.orderDatalist.orderInfo.cartInfo[0].product_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("虚拟发货")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.fictitious_content)+"\n                  ")])])])]):t._e(),1==t.orderDatalist.orderInfo.cartInfo[0].product_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("卡密发货")]),t.orderDatalist.orderInfo.virtual.length?a("div",t._l(t.orderDatalist.orderInfo.virtual,(function(e,r){return a("div",{key:r,staticClass:"list"},[a("div",{staticClass:"item"},[a("div",[t._v("卡号"+t._s(r+1)+"：")]),a("div",{staticClass:"value"},[t._v(t._s(e.card_no))])]),a("div",{staticClass:"item"},[a("div",[t._v("密码"+t._s(r+1)+"：")]),a("div",{staticClass:"value"},[t._v(t._s(e.card_pwd))])])])})),0):a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.virtual_info||"-")+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.supplierInfo?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("供应商信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("供应商：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.supplierInfo.supplier_name?t.orderDatalist.orderInfo.supplierInfo.supplier_name:"游客")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("供应商姓名：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.supplierInfo.name?t.orderDatalist.orderInfo.supplierInfo.name:"游客")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("联系方式：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.supplierInfo.phone||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("供应商邮箱：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.supplierInfo.email||"-")+"\n                  ")])])])]):t._e(),6==t.orderDatalist.orderInfo.product_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("预约信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("服务类型：")]),a("div",{staticClass:"value"},[t._v(t._s(3==t.orderDatalist.orderInfo.reservation_type?"上门服务":"到店服务"))])]),a("li",{staticClass:"item"},[a("div",[t._v("预约模式：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time_id>0?"购买时预约":"先买后约"))])]),t.orderDatalist.orderInfo.reservation_time?a("li",{staticClass:"item"},[a("div",[t._v("预约日期：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time))])]):t._e(),t.orderDatalist.orderInfo.reservation_show_time?a("li",{staticClass:"item"},[a("div",[t._v("预约时段：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_show_time))])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("预约人：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),a("li",{staticClass:"item"},[a("div",[t._v("预约电话：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])])]),t.orderDatalist.orderInfo.user_address.trim()?a("div",{staticClass:"item"},[a("div",[t._v("预约地址：")]),a("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_address))])]):t._e()]):t._e(),a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("订单信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("创建时间：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo._add_time||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("商品总数：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.total_num||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("商品总价：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.total_price||"-")+"\n                  ")])]),t.orderDatalist.orderInfo.pay_integral?a("li",{staticClass:"item"},[a("div",[t._v("实付积分：")]),a("div",{staticClass:"value"},[t._v("\n\t\t\t\t    ￥"+t._s(t.orderDatalist.orderInfo.pay_integral)+"\n\t\t\t\t  ")])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("优惠券金额：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.coupon_price||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("积分抵扣：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.deduction_price||0)+"\n                  ")])]),parseFloat(t.orderDatalist.orderInfo.use_integral)?a("li",{staticClass:"item"},[a("div",[t._v("使用积分：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(parseFloat(t.orderDatalist.orderInfo.use_integral))+"\n                  ")])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("支付邮费：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.pay_postage||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("会员商品优惠：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.vip_true_price||0)+"\n                  ")])]),0!=t.orderDatalist.orderInfo.first_order_price?a("li",{staticClass:"item"},[a("div",[t._v("新人首单优惠：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(t.orderDatalist.orderInfo.first_order_price)+"\n                  ")])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?a("li",{staticClass:"item"},[a("div",[t._v("门店名称：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo._store_name||"-")+"\n                  ")])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?a("li",{staticClass:"item"},[a("div",[t._v("核销码：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.verify_code||"-")+"\n                  ")])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("推广人：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.userInfo.spread_name)+"/ID:"+t._s(t.orderDatalist.userInfo.spread_uid)+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("支付时间：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo._pay_time||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("支付方式：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo._status._payType||"-")+"\n                  ")])]),t.orderDatalist.orderInfo.store_order_sn?a("li",{staticClass:"item"},[a("div",[t._v("原订单号：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.store_order_sn)+"\n                  ")])]):t._e(),t._l(t.orderDatalist.orderInfo.promotions_detail,(function(e,r){return a("li",{key:r,staticClass:"item"},[a("div",[t._v(t._s(e.title)+"：")]),a("div",{staticClass:"value"},[t._v("\n                    ￥"+t._s(parseFloat(e.promotions_price).toFixed(2))+"\n                  ")])])}))],2)]),"express"===t.orderDatalist.orderInfo.delivery_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("物流信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("快递公司：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("快递单号：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.delivery_id)),a("span",{staticClass:"logisticsLook",on:{click:t.openLogistics}},[t._v("查询")])])])])]):t._e(),"send"===t.orderDatalist.orderInfo.delivery_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("配送信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("送货人姓名：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("送货人电话：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.delivery_id||"-")+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.custom_form&&t.orderDatalist.orderInfo.custom_form.length&&t.isShow||6==t.orderDatalist.orderInfo.product_type&&t.orderDatalist.orderInfo.reservation_time_id>0||6!=t.orderDatalist.orderInfo.product_type?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("自定义留言")]),t._l(t.orderDatalist.orderInfo.custom_form,(function(e,r){return a("div",{key:r},[6==t.orderDatalist.orderInfo.product_type&&e.length?a("div",{staticClass:"item"},[t._v(t._s(t.orderDatalist.orderInfo.custom_form_title)+t._s(r+1))]):t._e(),a("ul",{staticClass:"list"},t._l(e,(function(e,r){return e.value&&-1==["uploadPicture","dateranges"].indexOf(e.name)||e.value.length&&-1!=["uploadPicture","dateranges"].indexOf(e.name)?a("li",{key:r,staticClass:"item"},[a("div",{staticClass:"txtVal"},[t._v(t._s(e.titleConfig.value)+"：")]),"dateranges"===e.name?a("div",{staticClass:"value"},[t._v(t._s(e.value[0]+"/"+e.value[1]))]):"uploadPicture"===e.name?a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"value"},t._l(e.value,(function(t,e){return a("div",{key:e,staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0):a("div",{staticClass:"value"},[t._v(t._s(e.value||"-"))])]):t._e()})),0)])}))],2):t._e(),t.orderDatalist.orderInfo.mark?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("买家备注")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.mark||"-")+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.remark?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("订单备注")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("备注：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.remark||"-")+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.refuse_reason?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("拒绝退款原因")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.refuse_reason)+"\n                  ")])])])]):t._e(),t.orderDatalist.orderInfo.invoice?a("div",{staticClass:"section"},[a("div",{staticClass:"title"},[t._v("发票信息")]),a("ul",{staticClass:"list"},[a("li",{staticClass:"item"},[a("div",[t._v("发票类型：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t._f("invoiceType")(t.orderDatalist.orderInfo.invoice.type))+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("抬头类型：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t._f("invoiceHeaderType")(t.orderDatalist.orderInfo.invoice.header_type))+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("发票抬头：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.name||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("税号：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.duty_number||"-")+"\n                  ")])]),a("li",{staticClass:"item"},[a("div",[t._v("邮箱：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.email||"-")+"\n                  ")])]),2==t.orderDatalist.orderInfo.invoice.type?a("li",{staticClass:"item"},[a("div",[t._v("开户银行：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.bank||"-")+"\n                  ")])]):t._e(),2==t.orderDatalist.orderInfo.invoice.type?a("li",{staticClass:"item"},[a("div",[t._v("企业地址：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.address||"-")+"\n                  ")])]):t._e(),a("li",{staticClass:"item"},[a("div",[t._v("企业电话：")]),a("div",{staticClass:"value"},[t._v("\n                    "+t._s(t.orderDatalist.orderInfo.invoice.drawer_phone||"-")+"\n                  ")])])])]):t._e()]),a("TabPane",{attrs:{label:"商品信息",name:"product"}},[a("Table",{attrs:{"load-data":t.handleorderCardBenefits,columns:t.columns1,data:t.orderDatalist.orderInfo.cartInfo,"highlight-row":"","row-key":"id"},scopedSlots:t._u([{key:"product",fn:function(e){var r=e.row;return[a("Tooltip",{attrs:{theme:"dark","max-width":"300",delay:600}},[a("div",{staticClass:"product",staticStyle:{"max-width":"400px"}},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:r.productInfo.attrInfo?r.productInfo.attrInfo.image:r.productInfo.image,expression:"row.productInfo.attrInfo ? row.productInfo.attrInfo.image : row.productInfo.image"}]})]),a("div",{staticClass:"title"},[a("div",{staticClass:"line2"},[r.is_gift?a("span",{staticClass:"font-color-red"},[t._v("[赠品]")]):t._e(),t._v("\n                        "+t._s(r.productInfo.store_name)+" | "+t._s(r.productInfo.attrInfo?r.productInfo.attrInfo.suk:"")+"\n                      ")])])]),a("div",{attrs:{slot:"content"},slot:"content"},[a("div",[r.is_gift?a("p",{staticClass:"font-color-red"},[t._v("[赠品]")]):t._e(),a("p",[t._v(t._s(r.productInfo.store_name))]),a("p",[t._v(t._s(r.productInfo.attrInfo?r.productInfo.attrInfo.suk:""))])])])])]}}],null,!1,132565562)})],1),a("TabPane",{attrs:{label:"订单记录",name:"record"}},[a("Table",{attrs:{columns:t.columns2,data:t.recordData,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"}})],1),t.splitList.length?a("TabPane",{attrs:{label:"发货记录",name:"recordList",index:1}},[a("Table",{attrs:{columns:t.columnSplit,data:t.splitList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"order_id",fn:function(e){var r=e.row;return[a("div",[t._v(t._s(r.order_id))]),r.supplier_name?a("span",{staticClass:"supplierName"},[t._v("["+t._s(r.supplier_name)+"]")]):t._e(),r.store_name?a("span",{staticClass:"supplierName"},[t._v("["+t._s(r.store_name)+"]")]):t._e(),2==r.refund_status?a("div",{staticClass:"red"},[t._v("[已退款]")]):t._e()]}},{key:"product",fn:function(e){var r=e.row;return[a("Tooltip",{attrs:{theme:"dark","max-width":"300",delay:600}},[t._l(r._info,(function(e,r){return a("div",{key:r,staticClass:"product productTime"},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.image:e.cart_info.productInfo.image,expression:"j.cart_info.productInfo.attrInfo ? j.cart_info.productInfo.attrInfo.image : j.cart_info.productInfo.image"}]})]),a("div",{staticClass:"title"},[a("div",{staticClass:"line2"},[e.cart_info.is_gift?a("span",{staticClass:"font-color-red"},[t._v("[赠品]")]):t._e(),t._v("\n                        "+t._s(e.cart_info.productInfo.store_name)+" | "+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:"")+"\n                      ")])])])})),a("div",{attrs:{slot:"content"},slot:"content"},t._l(r._info,(function(e,r){return a("div",{key:r},[e.cart_info.is_gift?a("p",{staticClass:"font-color-red"},[t._v("[赠品]")]):t._e(),a("p",[t._v(t._s(e.cart_info.productInfo.store_name))]),a("p",[t._v(t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:""))]),a("p",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.cart_info.sum_price+" x "+e.cart_info.cart_num))])])})),0)],2)]}},{key:"deliveryInfo",fn:function(e){var r=e.row;return[r.add_time?a("div",[a("span",[t._v("创建时间：")]),t._v(t._s(r.add_time))]):t._e(),r.delivery_time?a("div",[a("span",[t._v("发货时间：")]),t._v(t._s(r.delivery_time))]):t._e(),"express"==r.delivery_type||"send"==r.delivery_type||"fictitious"==r.delivery_type?a("div",[t._v("\n                  发货方式：\n                  "),"express"==r.delivery_type?a("span",[t._v("物流发货")]):t._e(),"send"==r.delivery_type?a("span",[t._v("送货")]):t._e(),"fictitious"==r.delivery_type?a("span",[t._v("虚拟发货")]):t._e()]):t._e(),r.delivery_name?a("div",[t._v("\n                  快递公司："+t._s(r.delivery_name)+"\n                ")]):t._e(),r.delivery_id?["express"==r.delivery_type?a("div",{on:{click:function(e){return t.openItemLogistics(r)}}},[t._v("\n                    快递单号："),a("span",{staticStyle:{color:"#1890ff",cursor:"pointer"}},[t._v(t._s(r.delivery_id))])]):t._e(),"send"==r.delivery_type?a("div",[t._v("\n                    快递单号："),a("span",{staticStyle:{color:"#1890ff"}},[t._v(t._s(r.delivery_id))])]):t._e()]:t._e()]}},{key:"action",fn:function(e){var r=e.row;return e.index,[2!==r._status||r.supplier_name||t.distHide?t._e():a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.distribution(r,1)}}},[t._v("分配")]),2!==r._status||r.supplier_name||0!=r.store_id?t._e():a("Divider",{attrs:{type:"vertical"}}),1===r._status?a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.edit(r,1)}}},[t._v("订单改价")]):t._e(),2!==r._status&&8!==r._status&&4!==r.status||1!==r.shipping_type||null!==r.pinkStatus&&2!==r.pinkStatus||r.supplier_name||0!=r.store_id?t._e():a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.sendOrder(r,1)}}},[t._v("发送货")]),4!==r._status||r.split.length?t._e():a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.delivery(r,1)}}},[t._v("配送信息")]),4===r._status&&!r.split.length&&r.supplier_name&&0==r.store_id?a("Divider",{attrs:{type:"vertical"}}):t._e(),2==r.shipping_type&&0==r.status&&1==r.paid&&0===r.refund_status?a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.bindWrite(r,1)}}},[t._v("立即核销")]):t._e(),r.supplier_name&&"未发货"==r.status_name.status_name?a("a",{attrs:{disabled:t.openErp},on:{click:function(e){return t.btnClick(r)}}},[t._v("提醒发货")]):t._e(),1===r._status||(2===r._status||r.split.length)&&1===r.shipping_type&&(null===r.pinkStatus||2===r.pinkStatus)||4===r._status||2==r.shipping_type&&0==r.status&&1==r.paid&&0===r.refund_status||r.supplier_name&&r.supplier_name&&"未发货"==r.status_name.status_name?a("Divider",{attrs:{type:"vertical"}}):t._e(),[a("Dropdown",{attrs:{transfer:!0},on:{"on-click":function(e){return t.changeMenu("",r,e,1)}}},[a("a",{attrs:{href:"javascript:void(0)"}},[t._v("\n                      更多\n                      "),a("Icon",{attrs:{type:"ios-arrow-down"}})],1),a("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:1===r._status&&0===r.paid&&"offline"===r.pay_type,expression:"\n                          row._status === 1 &&\n                          row.paid === 0 &&\n                          row.pay_type === 'offline'\n                        "}],ref:"ones",attrs:{disabled:t.openErp,name:"1"}},[t._v("确认收款")]),a("DropdownItem",{attrs:{disabled:t.openErp,name:"3"}},[t._v("订单记录")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:r._status>=3&&r.express_dump,expression:"row._status >= 3 && row.express_dump"}],attrs:{disabled:t.openErp,name:"11"}},[t._v("电子面单打印")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:r._status>=2,expression:"row._status >= 2"}],attrs:{name:"10"}},[t._v("小票打印")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:1!==r._status||3===r._status&&r.use_integral>0&&r.use_integral>=r.back_integral,expression:"\n                          row._status !== 1 ||\n                          (row._status === 3 &&\n                            row.use_integral > 0 &&\n                            row.use_integral >= row.back_integral)\n                        "}],attrs:{name:"4"}},[t._v("订单备注")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:2!=r.refund_type&&4!=r.refund_type&&6!=r.refund_type&&1==r.paid&&2!==r.refund_status&&parseFloat(r.pay_price)>0&&(null==r.split||0==r.split.length),expression:"\n                          row.refund_type != 2 &&\n                          row.refund_type != 4 &&\n                          row.refund_type != 6 &&\n                          row.paid == 1 &&\n                          row.refund_status !== 2 &&\n                          parseFloat(row.pay_price) > 0 &&\n                          (row.split == null || row.split.length == 0)\n                        "}],attrs:{disabled:t.openErp,name:"5"}},[t._v("立即退款")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:2==r.refund_type,expression:"row.refund_type == 2"}],attrs:{disabled:t.openErp,name:"55"}},[t._v("同意退货")]),a("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:4===r._status&&!r.split.length,expression:"row._status === 4 && !row.split.length"}],attrs:{disabled:t.openErp,name:"8"}},[t._v("已收货")]),1==r.is_del?a("DropdownItem",{attrs:{disabled:t.openErp,name:"9"}},[t._v("删除订单")]):t._e()],1)],1)]]}}],null,!1,957962661)})],1):t._e(),2==t.orderData.shipping_type?a("TabPane",{attrs:{label:"核销记录",name:"writeOff",index:2}},[a("Table",{attrs:{columns:t.writeOffColumns,data:t.writeOffData,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"product",fn:function(t){var e=t.row;return[a("div",{staticClass:"product "},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])]}}],null,!1,3448605298)})],1):t._e()],1)],1):t._e()]),a("Modal",{staticClass:"order_box2",attrs:{scrollable:"",title:"物流查询",width:"350"},model:{value:t.modal2,callback:function(e){t.modal2=e},expression:"modal2"}},[a("div",{staticClass:"logistics acea-row row-top"},[a("div",{staticClass:"logistics_img"},[a("img",{attrs:{src:r("bd9b")}})]),a("div",{staticClass:"logistics_cent"},[a("span",[t._v("物流公司："+t._s(t.logisticsChecked.delivery_name))]),a("span",[t._v("物流单号："+t._s(t.logisticsChecked.delivery_id))])])]),a("div",{staticClass:"acea-row row-column-around trees-coadd"},[a("div",{staticClass:"scollhide"},[a("Timeline",t._l(t.result,(function(e,r){return a("TimelineItem",{key:r},[a("p",{staticClass:"time",domProps:{textContent:t._s(e.time)}}),a("p",{staticClass:"content",domProps:{textContent:t._s(e.status)}})])})),1)],1)])])],1)}),[],!1,null,"3693468a",null);e.a=p.exports},fc83:function(t,e,r){}}]);