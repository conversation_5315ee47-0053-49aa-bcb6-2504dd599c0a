(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-ec886204"],{"02bc":function(t,e,n){"use strict";n.r(e);var r=n("2f62"),u=n("a069"),c=n("c24f");function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var i={name:"agentAgreement",components:{WangEditor:u.a},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(n,!0).forEach((function(e){a(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile","menuCollapse"])),data:function(){return{id:0,agent:{content:"",status:1},content:"",spinShow:!1}},created:function(){this.agentAgreement()},methods:{getEditorContent:function(t){this.agent.content=t},agentAgreement:function(){var t=this;this.spinShow=!0,Object(c.a)().then((function(e){t.spinShow=!1;var n=e.data,r=(n.title,n.content),u=n.status,c=n.id;t.agent.content=r,t.content=r,t.agent.status=u,t.id=c||0})).catch((function(e){t.$Message.error(e),t.spinShow=!1}))},agentAgreementSave:function(){var t=this;this.$Spin.show(),Object(c.b)(this.id,this.agent).then((function(e){t.$Spin.hide(),t.$Message.success("保存成功"),t.agentAgreement()})).catch((function(e){t.$Spin.hide(),t.$Message.error(e.msg)}))}}},s=(n("cf375"),n("2877")),d=Object(s.a)(i,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"form-submit"},[n("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Form",{attrs:{"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[n("FormItem",{attrs:{label:"说明内容："}},[n("WangEditor",{attrs:{content:t.content},on:{editorContent:t.getEditorContent}})],1)],1),t.spinShow?n("Spin",{attrs:{fix:""}}):t._e()],1),n("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[n("Form",[n("FormItem",[n("Button",{attrs:{type:"primary"},on:{click:t.agentAgreementSave}},[t._v("保存")])],1)],1)],1)],1)}),[],!1,null,"3abd404c",null);e.default=d.exports},c24f:function(t,e,n){"use strict";n.d(e,"X",(function(){return u})),n.d(e,"N",(function(){return c})),n.d(e,"M",(function(){return o})),n.d(e,"k",(function(){return a})),n.d(e,"r",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"h",(function(){return d})),n.d(e,"g",(function(){return f})),n.d(e,"q",(function(){return m})),n.d(e,"s",(function(){return l})),n.d(e,"J",(function(){return b})),n.d(e,"P",(function(){return h})),n.d(e,"L",(function(){return g})),n.d(e,"K",(function(){return p})),n.d(e,"f",(function(){return O})),n.d(e,"e",(function(){return j})),n.d(e,"n",(function(){return _})),n.d(e,"R",(function(){return v})),n.d(e,"p",(function(){return w})),n.d(e,"Q",(function(){return y})),n.d(e,"cb",(function(){return P})),n.d(e,"U",(function(){return S})),n.d(e,"S",(function(){return k})),n.d(e,"T",(function(){return C})),n.d(e,"W",(function(){return x})),n.d(e,"V",(function(){return E})),n.d(e,"Y",(function(){return A})),n.d(e,"v",(function(){return $})),n.d(e,"w",(function(){return D})),n.d(e,"Z",(function(){return M})),n.d(e,"i",(function(){return U})),n.d(e,"bb",(function(){return F})),n.d(e,"C",(function(){return T})),n.d(e,"db",(function(){return I})),n.d(e,"m",(function(){return J})),n.d(e,"ab",(function(){return W})),n.d(e,"F",(function(){return B})),n.d(e,"B",(function(){return q})),n.d(e,"A",(function(){return z})),n.d(e,"z",(function(){return G})),n.d(e,"D",(function(){return H})),n.d(e,"y",(function(){return K})),n.d(e,"x",(function(){return L})),n.d(e,"u",(function(){return N})),n.d(e,"t",(function(){return Q})),n.d(e,"o",(function(){return R})),n.d(e,"l",(function(){return V})),n.d(e,"G",(function(){return X})),n.d(e,"I",(function(){return Y})),n.d(e,"eb",(function(){return Z})),n.d(e,"O",(function(){return tt})),n.d(e,"E",(function(){return et})),n.d(e,"b",(function(){return nt})),n.d(e,"a",(function(){return rt})),n.d(e,"fb",(function(){return ut})),n.d(e,"j",(function(){return ct})),n.d(e,"c",(function(){return ot})),n.d(e,"H",(function(){return at}));var r=n("b6bd");function u(t){return Object(r.a)({url:"user/user",method:"get",params:t})}function c(t){return Object(r.a)({url:"setting/config/user/".concat(t),method:"get"})}function o(t,e){return Object(r.a)({url:"setting/config/user/".concat(t),method:"post",data:e})}function a(t){return Object(r.a)({url:"user/user/".concat(t,"/edit"),method:"get"})}function i(t){return Object(r.a)({url:"user/set_status/".concat(t.status,"/").concat(t.id),method:"put"})}function s(t){return Object(r.a)({url:"marketing/coupon/grant",method:"get",params:t})}function d(t){return Object(r.a)({url:"user/edit_other/".concat(t),method:"get"})}function f(t){return Object(r.a)({url:"user/user/".concat(t),method:"get"})}function m(t){return Object(r.a)({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function l(t){return Object(r.a)({url:"user/user_level/vip_list",method:"get",params:t})}function b(t){return Object(r.a)({url:"user/user_level/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function h(t,e){return Object(r.a)({url:"user/user_level/task/".concat(t),method:"get",params:e})}function g(t){return Object(r.a)({url:"user/user_level/set_task_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function p(t){return Object(r.a)({url:"user/user_level/set_task_must/".concat(t.id,"/").concat(t.is_must),method:"PUT"})}function O(t){return Object(r.a)({url:"/user/user_level/create_task",method:"get",params:t})}function j(t){return Object(r.a)({url:"user/user_level/create",method:"get",params:t})}function _(t){return Object(r.a)({url:"user/give_level/".concat(t),method:"get"})}function v(t){return Object(r.a)({url:"user/user_group/list",method:"get",params:t})}function w(t){return Object(r.a)({url:"user/user_group/add/".concat(t),method:"get"})}function y(t){return Object(r.a)({url:"setting/update_admin",method:"PUT",data:t})}function P(t){return Object(r.a)({url:"user/set_group",method:"post",data:t})}function S(t){return Object(r.a)({url:"user/user_label",method:"get",params:t})}function k(t,e){return Object(r.a)({url:"user/user_label/add/".concat(t),method:"get",params:e})}function C(t){return Object(r.a)({url:"user/user_label_cate/all",method:"get",params:t})}function x(t){return Object(r.a)({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function E(t){return Object(r.a)({url:"user/user_label_cate/create",method:"get"})}function A(t){return Object(r.a)({url:"/user/member_batch/index",method:"get",params:t})}function $(t,e){return Object(r.a)({url:"/user/member_batch/save/".concat(t),method:"post",data:e})}function D(t,e){return Object(r.a)({url:"/user/member_batch/set_value/".concat(t),method:"get",params:e})}function M(t,e){return Object(r.a)({url:"/user/member_card/index/".concat(t),method:"get",params:e})}function U(t,e){return Object(r.a)({url:"/export/memberCard/".concat(t),method:"get",params:e})}function F(){return Object(r.a)({url:"/user/member/ship",method:"get"})}function T(t,e){return Object(r.a)({url:"/user/member_ship/save/".concat(t),method:"post",data:e})}function I(){return Object(r.a)({url:"/user/user/syncUsers",method:"get"})}function J(){return Object(r.a)({url:"/user/user/create",method:"get"})}function W(){return Object(r.a)({url:"/user/member_scan",method:"get"})}function B(t,e){return Object(r.a)({url:"user/label/".concat(t),method:"post",data:e})}function q(t){return Object(r.a)({url:"user/member_right/save/".concat(t.id),method:"post",data:t})}function z(){return Object(r.a)({url:"user/member/right",method:"get"})}function G(t){return Object(r.a)({url:"/user/member/record",method:"get",params:t})}function H(){return Object(r.a)({url:"user/member/ship_select",method:"get"})}function K(t){return Object(r.a)({url:"user/member_card/set_status",method:"get",params:t})}function L(t){return Object(r.a)({url:"user/member_ship/set_ship_status",method:"get",params:t})}function N(t,e){return Object(r.a)({url:"user/member_agreement/save/".concat(t),method:"post",data:e})}function Q(){return Object(r.a)({url:"user/member/agreement",method:"get"})}function R(t){return Object(r.a)({url:"user/give_level_time/".concat(t),method:"get"})}function V(t){return Object(r.a)({url:"user/label/".concat(t),method:"get"})}function X(t){return Object(r.a)({url:"user/save_set_label",method:"put",data:t})}function Y(t){return Object(r.a)({url:"setting/info",method:"get"})}function Z(t){return Object(r.a)({url:"user/visit_list/".concat(t.id),method:"get",params:t.datas})}function tt(t){return Object(r.a)({url:"user/spread_list/".concat(t.id),method:"get",params:t.datas})}function et(t){return Object(r.a)({url:"user/user/".concat(t.uid),method:"put",data:t})}function nt(t,e){return Object(r.a)({url:"agent/set_agent_agreement/".concat(t),method:"post",data:e})}function rt(){return Object(r.a)({url:"agent/get_agent_agreement",method:"get"})}function ut(){return Object(r.a)({url:"user/synchro/work/label",method:"get"})}function ct(t){return Object(r.a)({url:"user/user/extend_info/".concat(t),method:"get"})}function ot(t){return Object(r.a)({url:"user/batch_process",method:"post",data:t})}function at(t,e){return Object(r.a)({url:"/user/member/save/content/".concat(t),method:"post",data:e})}},cf375:function(t,e,n){"use strict";var r=n("f15d");n.n(r).a},f15d:function(t,e,n){}}]);