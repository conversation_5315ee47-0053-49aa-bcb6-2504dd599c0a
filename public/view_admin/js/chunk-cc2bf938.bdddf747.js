(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-cc2bf938"],{"0fc4":function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"c",(function(){return s})),r.d(e,"b",(function(){return n}));var a=r("b6bd");function o(){return Object(a.a)({url:"erp/config",method:"get"})}function s(t){return Object(a.a)({url:"store/erp/shop",method:"get",params:t})}function n(t){return Object(a.a)({url:"product/import_erp_product",method:"post",data:t})}},"24aa":function(t,e,r){"use strict";r.r(e);var a=r("a34a"),o=r.n(a),s=r("c4ad"),n=r("73f5"),i=r("0fc4"),l=r("2f62"),c=r("b0e7");function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function d(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var m={name:"erpList",data:function(){var t=this;return{loading:!1,currentid:0,columns1:[{width:60,align:"center",render:function(e,r){var a=r.row.shop_id,o=!1;o=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:o,disabled:!a},on:{"on-change":function(){s.currentid=a,t.$emit("getProductId",a)}}})])}},{title:"ID",key:"shop_id",width:120},{title:"店铺名称",key:"shop_name",minWidth:100}],erpFrom:{page:1,limit:1},list:[],disabledB:!0,disabledF:!1}},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(r,!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(l.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getList()},methods:{back:function(){this.erpFrom.page=this.erpFrom.page-1,this.getList()},forward:function(){this.erpFrom.page=this.erpFrom.page+1,this.getList()},getList:function(){var t=this;this.loading=!0,Object(i.c)(this.erpFrom).then((function(e){var r=e.data;t.list=r,t.erpFrom.page>1?t.disabledB=!1:t.disabledB=!0,r.length<t.erpFrom.limit?t.disabledF=!0:t.disabledF=!1,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.specsFrom.page=t,this.getList()}}},p=r("2877"),f=Object(p.a)(m,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("Table",{ref:"table",staticClass:"mt25",attrs:{columns:this.columns1,data:this.list,loading:this.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}}),e("div",{staticClass:"acea-row row-right page"},[e("Button",{attrs:{icon:"ios-arrow-back",disabled:this.disabledB},on:{click:this.back}}),e("Button",{staticClass:"ml10",attrs:{icon:"ios-arrow-forward",disabled:this.disabledF},on:{click:this.forward}})],1)],1)}),[],!1,null,null,null).exports,h=r("c7fc"),b=r("d708");function _(t,e,r,a,o,s,n){try{var i=t[s](n),l=i.value}catch(t){return void r(t)}i.done?e(l):Promise.resolve(l).then(a,o)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function v(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y={name:"systemStore",components:{uploadPictures:c.a,Maps:h.a,erpList:f,goodsList:s.default},props:{},data:function(){var t=this;return{currentTab:"1",routerPre:b.a.roterPre,goodsList:[],modals:!1,treeSelect:[],modalErp:!1,openErp:!1,formItem:{city_delivery_status:1,city_delivery_type:0,default_delivery:1,use_system_money:1,is_alone:0,product_category_status:0,delivery_type:1,product_id:[],cate_id:[],region_id:0,id:0,erp_shop_id:0,store_account:"",store_password:"",image:"",background_image:"",name:"",introduction:"",phone:"",is_show:1,day_time:[],address:"",detailed_address:"",latitude:"",longitude:"",addressSelect:[],valid_range:0,product_verify_status:0,product_status:1,type:1,applicable_type:1},spinShow:!1,addresData:[],ruleValidate:{valid_range:[{required:!0,message:"请输入配送范围"}],name:[{required:!0,message:"请输入门店名称",trigger:"blur"}],erp_shop_id:[{required:!0,validator:function(e,r,a){0==t.formItem.erp_shop_id?a(new Error("请选择erp门店")):a()},trigger:"change"}],store_account:[{required:!0,message:"请输入管理员账号",trigger:"blur"}],store_password:[{required:!0,message:"请输入管理员密码",trigger:"blur"}],address:[{required:!0,message:"请选择门店地址",trigger:"change"}],phone:[{required:!0,validator:function(t,e,r){if(!e)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(e)?r():r(new Error("手机号格式不正确!"))},trigger:"blur"}],detailed_address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],image:[{required:!0,validator:function(e,r,a){t.formItem.image?a():a(new Error("请上传门店照片"))},trigger:"change"}],background_image:[{required:!0,validator:function(e,r,a){t.formItem.background_image?a():a(new Error("请上传门头照片"))},trigger:"change"}],day_time:[{required:!0,type:"array",message:"请选择营业时间",trigger:"change"},{validator:function(t,e,r,a,o){""===e[0]&&r("请选择营业时间"),r()}}]},mapKey:"",grid:{xl:20,lg:20,md:20,sm:24,xs:24},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},modalPic:!1,isChoice:"单选",pid:0,isApi:0,storeAddress:"",regionList:[],picTit:""}},created:function(){this.allRegion(),this.goodsCategory(),this.getErpConfig(),this.getKey(),this.cityInfo({pid:0});var t=this.$route.params.id;t?this.getInfo(t):this.isApi=1},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(r,!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(l.e)("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),mounted:function(){},methods:{allRegion:function(){var t=this;Object(n.h)({is_show:1}).then((function(e){t.regionList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.product_id)&&e.set(t.product_id,1)}))},getGoodsId:function(t){var e=this,r=this.goodsList.concat(t),a=this.unique(r);this.goodsList=a,this.$nextTick((function(t){setTimeout((function(){e.modals=!1}),300)}))},bindDelete:function(t){this.goodsList.splice(t,1)},goodsCategory:function(){var t=this;Object(n.a)(1).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getProductId:function(t){this.formItem.erp_shop_id=t,this.modalErp=!1,this.$refs.formItem.validateField("erp_shop_id")},tapErp:function(){this.$refs.refErp.currentid=this.formItem.erp_shop_id,this.modalErp=!0,this.$refs.formItem.validateField("erp_shop_id")},getErpConfig:function(){var t=this;Object(i.a)().then((function(e){t.openErp=e.data.open_erp})).catch((function(e){t.$Message.error(e.msg)}))},addchack:function(t,e){this.formItem.addressSelect=t,this.formItem.address=e.map((function(t){return t.label})).join(""),this.storeAddress=e.map((function(t){return t.label})).join("")},cityInfo:function(t){var e=this;Object(n.e)(t).then((function(t){e.addresData=t.data}))},loadData:function(t,e){t.loading=!0,Object(n.e)({pid:t.value}).then((function(r){t.children=r.data,t.loading=!1,e()}))},resolveCity:function(t){var e=this,r={address:t};Object(n.l)(r).then((function(t){var r=[];t.data.forEach((function(t){r.push(t.id)})),e.formItem.addressSelect=r})).catch((function(t){e.$Message.error(res.msg)}))},getCoordinates:function(t){if(this.formItem.latitude=t.location.lat||34.34127,this.formItem.longitude=t.location.lng||108.93984,t.address_reference){var e=t.address_reference.landmark_l2;this.formItem.detailed_address=e.title,this.formItem.latitude=e.location.lat||34.34127,this.formItem.longitude=e.location.lng||108.93984;var r=t.address_component,a=t.address_reference.town.title;a="丈八街道"==a?"丈八沟街道":a;var o=[r.province,r.city,r.district,a];this.storeAddress=o.join(""),this.formItem.address=o.join(""),this.resolveCity(o.join("/"))}},onSearch:function(){this.$refs.mapChild&&this.$refs.mapChild.searchKeyword(this.storeAddress+this.formItem.detailed_address)},getKey:function(){var t=this;Object(n.p)().then((function(e){t.mapKey=e.data.key})).catch((function(e){t.$Message.error(e.msg)}))},getInfo:function(t){var e=this,r=this;r.formItem.id=t,r.spinShow=!0,Object(n.J)(t).then((function(t){e.isApi=1,e.formItem=t.data.info,e.storeAddress=t.data.info.address,e.formItem.erp_shop_id=t.data.info.erp_shop_id||0,e.formItem.day_time=t.data.info.timeVal,e.$set(e.formItem,"valid_range",e.formItem.valid_range/1e3),r.spinShow=!1})).catch((function(t){r.spinShow=!1,r.$Message.error(t.msg)}))},modalPicTap:function(t,e){this.modalPic=!0,this.picTit=e||"",this.$refs.formItem.validateField(e)},getPic:function(t){this.formItem[this.picTit]=t.att_dir,this.modalPic=!1,this.$refs.formItem.validateField(this.picTit)},onchangeTime:function(t){this.formItem.day_time=t},upTab:function(){this.currentTab=(Number(this.currentTab)-1).toString()},downTab:function(t){var e=this;this.$refs[t].validate((function(t){if(t){if(3==e.currentTab&&3!=e.formItem.delivery_type&&(""==e.formItem.valid_range||e.formItem.valid_range<0))return e.$Message.error("请输入有效的门店范围");e.currentTab=(Number(e.currentTab)+1).toString()}else e.$Message.warning("请完善数据")}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;if(""==e.formItem.day_time[0]&&(e.formItem.day_time=["00:00:00","23:59:59"]),(""==e.formItem.valid_range||e.formItem.valid_range<.01)&&3!=e.formItem.delivery_type)return e.$Message.error("请输入有效的门店范围");var r=[];e.goodsList.forEach((function(t){r.push(t.product_id)})),e.formItem.product_id=r,Object(n.O)(e.formItem.id,e.formItem).then(function(){var t,r=(t=o.a.mark((function t(r){return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$Message.success(r.msg),e.$router.push({path:e.routerPre+"/store/store/index"});case 2:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(a,o){var s=t.apply(e,r);function n(t){_(s,a,o,n,i,"next",t)}function i(t){_(s,a,o,n,i,"throw",t)}n(void 0)}))});return function(t){return r.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}))}}},I=(r("b7ff"),Object(p.a)(y,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"i-layout-page-header"},[r("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[r("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[r("router-link",{attrs:{to:{path:t.routerPre+"/store/store/index"}}},[r("div",{staticClass:"font-sm after-line"},[r("span",{staticClass:"iconfont iconfanhui"}),r("span",{staticClass:"pl10"},[t._v("返回")])])]),r("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑门店":"添加门店")}})],1)])],1),r("div",{staticClass:"article-manager ivu-mt"},[r("Card",{attrs:{bordered:!1,"dis-hover":"",padding:16}},[r("div",{staticClass:"new_tab"},[r("Tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[r("TabPane",{attrs:{label:"基础设置",name:"1"}}),r("TabPane",{attrs:{label:"运营设置",name:"2"}}),r("TabPane",{attrs:{label:"配送设置",name:"3"}}),t.formItem.id?t._e():r("TabPane",{attrs:{label:"商品设置",name:"4"}})],1)],1),r("Form",{ref:"formItem",staticClass:"mt20",attrs:{model:t.formItem,"label-width":t.labelWidth,"label-position":t.labelPosition,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{directives:[{name:"show",rawName:"v-show",value:1==t.currentTab,expression:"currentTab == 1"}],attrs:{type:"flex",gutter:24}},[t.openErp?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"erp门店：",prop:"erp_shop_id"}},[r("Button",{on:{click:t.tapErp}},[t._v(t._s(t.formItem.erp_shop_id?t.formItem.erp_shop_id:"请选择erp门店"))])],1)],1):t._e(),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店照片：",prop:"image"}},[r("div",{staticClass:"picBox",on:{click:function(e){return t.modalPicTap("单选","image")}}},[t.formItem.image?r("div",{staticClass:"pictrue"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formItem.image,expression:"formItem.image"}]})]):r("div",{staticClass:"upLoad"},[r("div",{staticClass:"iconfont"},[t._v("+")])])]),r("div",{staticClass:"tips"},[t._v(" 建议尺寸：70 * 70px")])])],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门头照片：",prop:"background_image"}},[r("div",{staticClass:"picBox",on:{click:function(e){return t.modalPicTap("单选","background_image")}}},[t.formItem.background_image?r("div",{staticClass:"pictrue"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formItem.background_image,expression:"formItem.background_image"}]})]):r("div",{staticClass:"upLoad"},[r("div",{staticClass:"iconfont"},[t._v("+")])])]),r("div",{staticClass:"tips"},[t._v(" 建议尺寸：375 * 192px")])])],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店名称：",prop:"name","label-for":"name"}},[r("Input",{staticClass:"inputW",attrs:{maxlength:"20","show-word-limit":"",placeholder:"请输入门店名称"},model:{value:t.formItem.name,callback:function(e){t.$set(t.formItem,"name",e)},expression:"formItem.name"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店简介：","label-for":"introduction"}},[r("Input",{staticClass:"inputW",attrs:{maxlength:"100","show-word-limit":"",rows:4,autosize:{maxRows:4,minRows:4},type:"textarea",placeholder:"请输入门店简介"},model:{value:t.formItem.introduction,callback:function(e){t.$set(t.formItem,"introduction",e)},expression:"formItem.introduction"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店分类：",prop:"cate_id","label-for":"cate_id"}},[r("Cascader",{staticClass:"inputW",attrs:{data:t.treeSelect,placeholder:"请选择门店分类","change-on-select":"",filterable:""},model:{value:t.formItem.cate_id,callback:function(e){t.$set(t.formItem,"cate_id",e)},expression:"formItem.cate_id"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店区域：",prop:"region_id","label-for":"region_id"}},[r("Select",{staticClass:"inputW",attrs:{placeholder:"请选择",clearable:""},model:{value:t.formItem.region_id,callback:function(e){t.$set(t.formItem,"region_id",e)},expression:"formItem.region_id"}},t._l(t.regionList,(function(e){return r("Option",{key:e.id,attrs:{value:e.id}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(e.name)+"\n\t\t\t\t\t\t\t\t")])})),1)],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"营业状态：","label-for":"is_show",prop:"is_show"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.is_show,callback:function(e){t.$set(t.formItem,"is_show",e)},expression:"formItem.is_show"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"营业时间：","label-for":"day_time",prop:"day_time"}},[r("TimePicker",{staticClass:"inputW",attrs:{type:"timerange",format:"HH:mm:ss",value:t.formItem.day_time,placement:"bottom-end",placeholder:"请选择营业时间"},on:{"on-change":t.onchangeTime},model:{value:t.formItem.day_time,callback:function(e){t.$set(t.formItem,"day_time",e)},expression:"formItem.day_time"}})],1)],1),0==t.formItem.id?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"管理员账号：",prop:"store_account","label-for":"store_account"}},[r("Input",{staticClass:"inputW",attrs:{placeholder:"请输入管理员账号"},model:{value:t.formItem.store_account,callback:function(e){t.$set(t.formItem,"store_account",e)},expression:"formItem.store_account"}})],1)],1):t._e(),0==t.formItem.id?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"管理员密码：",prop:"store_password","label-for":"store_password"}},[r("Input",{staticClass:"inputW",attrs:{type:"password",placeholder:"请输入管理员密码"},model:{value:t.formItem.store_password,callback:function(e){t.$set(t.formItem,"store_password",e)},expression:"formItem.store_password"}})],1)],1):t._e(),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店手机号：","label-for":"phone",prop:"phone"}},[r("Input",{staticClass:"inputW",attrs:{placeholder:"请输入门店手机号"},model:{value:t.formItem.phone,callback:function(e){t.$set(t.formItem,"phone",e)},expression:"formItem.phone"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店地址：","label-for":"address",prop:"address"}},[r("Cascader",{staticClass:"inputW",attrs:{data:t.addresData,"load-data":t.loadData},on:{"on-change":t.addchack},model:{value:t.formItem.addressSelect,callback:function(e){t.$set(t.formItem,"addressSelect",e)},expression:"formItem.addressSelect"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店详细地址：","label-for":"detailed_address",prop:"detailed_address"}},[r("div",{staticClass:"acea-row row-middle"},[t.storeAddress?r("Input",{staticClass:"w-240",attrs:{disabled:""},model:{value:t.storeAddress,callback:function(e){t.storeAddress=e},expression:"storeAddress"}}):t._e(),r("Input",{staticClass:"w-300 ml-6",attrs:{search:"","enter-button":"查找位置",placeholder:"输入详细地址"},on:{"on-search":t.onSearch},model:{value:t.formItem.detailed_address,callback:function(e){t.$set(t.formItem,"detailed_address",e)},expression:"formItem.detailed_address"}})],1),r("div",{staticClass:"tip"},[t._v("提示：为减少误差，建议门店地址与定位地区保持一致")])])],1),t.isApi?r("Col",{attrs:{span:"24"}},[t.mapKey?r("Maps",{ref:"mapChild",staticClass:"map-sty",attrs:{mapKey:t.mapKey,lat:Number(t.formItem.latitude||34.34127),lon:Number(t.formItem.longitude||108.93984),address:t.storeAddress+t.formItem.detailed_address},on:{getCoordinates:t.getCoordinates}}):t._e()],1):t._e()],1),r("Row",{directives:[{name:"show",rawName:"v-show",value:2==t.currentTab,expression:"currentTab == 2"}],attrs:{type:"flex",gutter:24}},[r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店类型：","label-for":"type",prop:"type"}},[r("RadioGroup",{model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[r("Radio",{attrs:{label:1}},[r("Icon",{attrs:{type:"social-apple"}}),r("span",[t._v("自营")])],1),r("Radio",{attrs:{label:2}},[r("Icon",{attrs:{type:"social-android"}}),r("span",[t._v("加盟")])],1)],1),r("div",{staticClass:"tips"},[t._v("自营店不支持自主上传商品，加盟店有自主上传商品的权限")])],1)],1),2==t.formItem.type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"商品免审：","label-for":"product_verify_status",prop:"product_verify_status"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.product_verify_status,callback:function(e){t.$set(t.formItem,"product_verify_status",e)},expression:"formItem.product_verify_status"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1)],1):t._e(),2==t.formItem.type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"自主添加商品：","label-for":"product_status",prop:"product_status"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.product_status,callback:function(e){t.$set(t.formItem,"product_status",e)},expression:"formItem.product_status"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1)],1):t._e(),2==t.formItem.type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"使用平台余额：","label-for":"use_system_money",prop:"use_system_money"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.use_system_money,callback:function(e){t.$set(t.formItem,"use_system_money",e)},expression:"formItem.use_system_money"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1)],1):t._e(),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"门店隔离：","label-for":"is_alone",prop:"is_alone"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.is_alone,callback:function(e){t.$set(t.formItem,"is_alone",e)},expression:"formItem.is_alone"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"tips"},[t._v("开启后，该门店在移动端门店列表中不再显示，仅可通过扫描门店推广码进入，用户进入隔离门店后不可切换至其他门店。")])],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"自建商品分类：","label-for":"product_category_status",prop:"product_category_status"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.product_category_status,callback:function(e){t.$set(t.formItem,"product_category_status",e)},expression:"formItem.product_category_status"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"tips"},[t._v("开启后，门店可自建商品分类并为门店商品设置门店分类")])],1)],1)],1),r("Row",{directives:[{name:"show",rawName:"v-show",value:3==t.currentTab,expression:"currentTab == 3"}],attrs:{type:"flex",gutter:24}},[r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"配送方式：","label-for":"delivery_type",prop:"delivery_type"}},[r("RadioGroup",{model:{value:t.formItem.delivery_type,callback:function(e){t.$set(t.formItem,"delivery_type",e)},expression:"formItem.delivery_type"}},[r("Radio",{attrs:{label:1}},[t._v("门店配送+到店核销")]),r("Radio",{attrs:{label:2}},[t._v("门店配送")]),r("Radio",{attrs:{label:3}},[t._v("到店核销")])],1),r("div",{staticClass:"tips"},[t._v("门店支持的配送方式")])],1)],1),3!=t.formItem.delivery_type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"配送范围(半径)：","label-for":"valid_range",prop:"valid_range"}},[r("InputNumber",{staticStyle:{width:"90px"},attrs:{max:1e5,formatter:function(e){return""+t.formItem.valid_range},parser:function(t){return t.replace("%","")}},model:{value:t.formItem.valid_range,callback:function(e){t.$set(t.formItem,"valid_range",e)},expression:"formItem.valid_range"}}),r("span",{staticClass:"ml10"},[t._v("km")])],1)],1):t._e(),3!=t.formItem.delivery_type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"同城配送：","label-for":"city_delivery_status",prop:"city_delivery_status"}},[r("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formItem.city_delivery_status,callback:function(e){t.$set(t.formItem,"city_delivery_status",e)},expression:"formItem.city_delivery_status"}},[r("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1)],1):t._e(),t.formItem.city_delivery_status&&3!=t.formItem.delivery_type?r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"第三方配送：","label-for":"city_delivery_type",prop:"city_delivery_type"}},[r("RadioGroup",{model:{value:t.formItem.city_delivery_type,callback:function(e){t.$set(t.formItem,"city_delivery_type",e)},expression:"formItem.city_delivery_type"}},[r("Radio",{attrs:{label:1}},[t._v("达达快送")]),r("Radio",{attrs:{label:2}},[t._v("UU跑腿")]),r("Radio",{attrs:{label:0}},[t._v("均不使用")])],1)],1)],1):t._e(),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"默认配送方式："}},[r("RadioGroup",{model:{value:t.formItem.default_delivery,callback:function(e){t.$set(t.formItem,"default_delivery",e)},expression:"formItem.default_delivery"}},[r("Radio",{attrs:{label:1}},[t._v("配送")]),r("Radio",{attrs:{label:2}},[t._v("到店")])],1),r("div",{staticClass:"tips"},[t._v("用户进入门店时默认选择的配送方式")])],1)],1)],1),r("Row",{directives:[{name:"show",rawName:"v-show",value:4==t.currentTab,expression:"currentTab == 4"}],attrs:{type:"flex",gutter:24}},[t.formItem.id?t._e():r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"同步商品："}},[r("RadioGroup",{model:{value:t.formItem.applicable_type,callback:function(e){t.$set(t.formItem,"applicable_type",e)},expression:"formItem.applicable_type"}},[r("Radio",{attrs:{label:1}},[r("Icon",{attrs:{type:"social-apple"}}),r("span",[t._v("全部商品")])],1),r("Radio",{attrs:{label:2}},[r("Icon",{attrs:{type:"social-android"}}),r("span",[t._v("指定商品")])],1),r("Radio",{attrs:{label:3}},[r("Icon",{attrs:{type:"social-android"}}),r("span",[t._v("暂不同步")])],1)],1)],1)],1),t.formItem.id||2!=t.formItem.applicable_type?t._e():r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"选择商品：","label-for":"product_id",prop:""}},[r("div",{staticClass:"box"},[t._l(t.goodsList,(function(e,a){return r("div",{key:a,staticClass:"box-item"},[r("img",{attrs:{src:e.image,alt:""}}),r("Icon",{staticClass:"icon",attrs:{type:"ios-close-circle",size:"20"},on:{click:function(e){return t.bindDelete(a)}}})],1)})),r("div",{staticClass:"upload-box",on:{click:function(e){t.modals=!0}}},[r("Icon",{attrs:{type:"ios-camera-outline",size:"36"}})],1)],2)])],1)],1),t.spinShow?r("Spin",{attrs:{size:"large",fix:""}}):t._e()],1)],1),r("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[r("Form",[r("FormItem",["1"!==t.currentTab?r("Button",{on:{click:t.upTab}},[t._v("上一步")]):t._e(),t.$route.params.id&&Number(t.currentTab)<3||!t.$route.params.id&&Number(t.currentTab)<4?r("Button",{staticClass:"ml10",attrs:{type:"primary"},on:{click:function(e){return t.downTab("formItem")}}},[t._v("下一步")]):t._e(),t.$route.params.id||!t.$route.params.id&&4==t.currentTab?r("Button",{staticClass:"ml10",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formItem")}}},[t._v("保存")]):t._e()],1)],1)],1),r("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"image"==t.picTit?"上传门店照片":"上传门头照片","mask-closable":!1,"z-index":1},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?r("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic}}):t._e()],1),r("Modal",{attrs:{width:"700px",scrollable:"","footer-hide":"",closable:"",title:"erp门店","mask-closable":!1,"z-index":1},model:{value:t.modalErp,callback:function(e){t.modalErp=e},expression:"modalErp"}},[r("erpList",{ref:"refErp",on:{getProductId:t.getProductId}})],1)],1),r("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",scrollable:"",width:"900","footer-hide":!0},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?r("goods-list",{ref:"goodslist",attrs:{chooseType:91,ischeckbox:!0,isLive:!0,storeType:1},on:{getProductId:t.getGoodsId}}):t._e()],1)],1)}),[],!1,null,"12f279d6",null));e.default=I.exports},"73f5":function(t,e,r){"use strict";r.d(e,"z",(function(){return o})),r.d(e,"t",(function(){return s})),r.d(e,"r",(function(){return n})),r.d(e,"u",(function(){return i})),r.d(e,"n",(function(){return l})),r.d(e,"x",(function(){return c})),r.d(e,"H",(function(){return u})),r.d(e,"I",(function(){return d})),r.d(e,"F",(function(){return m})),r.d(e,"G",(function(){return p})),r.d(e,"g",(function(){return f})),r.d(e,"C",(function(){return h})),r.d(e,"D",(function(){return b})),r.d(e,"E",(function(){return _})),r.d(e,"P",(function(){return g})),r.d(e,"K",(function(){return v})),r.d(e,"J",(function(){return y})),r.d(e,"e",(function(){return I})),r.d(e,"O",(function(){return w})),r.d(e,"p",(function(){return C})),r.d(e,"L",(function(){return O})),r.d(e,"M",(function(){return j})),r.d(e,"N",(function(){return k})),r.d(e,"o",(function(){return x})),r.d(e,"s",(function(){return $})),r.d(e,"A",(function(){return P})),r.d(e,"q",(function(){return F})),r.d(e,"y",(function(){return T})),r.d(e,"f",(function(){return M})),r.d(e,"B",(function(){return E})),r.d(e,"b",(function(){return S})),r.d(e,"d",(function(){return L})),r.d(e,"c",(function(){return q})),r.d(e,"a",(function(){return R})),r.d(e,"i",(function(){return z})),r.d(e,"k",(function(){return D})),r.d(e,"w",(function(){return B})),r.d(e,"j",(function(){return N})),r.d(e,"v",(function(){return A})),r.d(e,"h",(function(){return K})),r.d(e,"l",(function(){return W})),r.d(e,"m",(function(){return G}));var a=r("b6bd");function o(t){return Object(a.a)({url:"merchant/store_list",method:"get",params:t})}function s(t){return Object(a.a)({url:"store/order/list",method:"get",params:t})}function n(t){return Object(a.a)({url:"store/order/chart",method:"get",params:t})}function i(t){return Object(a.a)({url:"store/refund/list",method:"get",params:t})}function l(t){return Object(a.a)({url:"/order/no_refund/".concat(t),method:"get"})}function c(t){return Object(a.a)({url:"/order/refund_integral/".concat(t),method:"get"})}function u(t){return Object(a.a)({url:"store/finance_flow/list",method:"get",params:t})}function d(t,e){return Object(a.a)({url:"store/finance_flow/mark/".concat(t),method:"put",params:e})}function m(t){return Object(a.a)({url:"store/finance_flow/fund_record",method:"get",params:t})}function p(t){return Object(a.a)({url:"store/finance_flow/fund_record_info",method:"get",params:t})}function f(t){return Object(a.a)({url:"/export/storeFinanceRecord",method:"get",params:t})}function h(t){return Object(a.a)({url:"/store/extract/list",method:"get",params:t})}function b(t,e){return Object(a.a)({url:"store/extract/mark/".concat(t),method:"post",data:e})}function _(t,e){return Object(a.a)({url:"store/extract/verify/".concat(t),method:"post",data:e})}function g(t){return Object(a.a)({url:"store/extract/transfer/".concat(t),method:"get"})}function v(t){return Object(a.a)({url:"store/store",method:"get",params:t})}function y(t){return Object(a.a)({url:"store/store/get_info/".concat(t),method:"get"})}function I(t){return Object(a.a)({url:"city",method:"get",params:t})}function w(t,e){return Object(a.a)({url:"store/store/".concat(t),method:"post",data:e})}function C(){return Object(a.a)({url:"store/store/address",method:"get"})}function O(t){return Object(a.a)({url:"store/store/login/".concat(t),method:"get"})}function j(t,e){return Object(a.a)({url:"store/store/set_show/".concat(t,"/").concat(e),method:"put"})}function k(t){return Object(a.a)({url:"store/share/order",method:"post",params:t})}function x(t){return Object(a.a)({url:"store/home/<USER>",method:"get",params:t})}function $(t){return Object(a.a)({url:"store/home/<USER>",method:"get",params:t})}function P(t){return Object(a.a)({url:"store/home/<USER>",method:"get",params:t})}function F(t){return Object(a.a)({url:"store/home/<USER>",method:"get",params:t})}function T(t){return Object(a.a)({url:"store/store/reset_admin/".concat(t),method:"get"})}function M(t,e,r){return Object(a.a)({url:"export/storeFlowExport?store_id=".concat(t,"&keyword=").concat(e,"&data=").concat(r),method:"get"})}function E(t){return Object(a.a)({url:"/store/category",params:t,method:"get"})}function S(t){return Object(a.a)({url:"/store/category/create/".concat(t),method:"get"})}function L(t){return Object(a.a)({url:"/store/category/tree/".concat(t),method:"get"})}function q(t){return Object(a.a)({url:"/store/category/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function R(t){return Object(a.a)({url:"store/category/cascader_list/".concat(t),method:"get"})}function z(t){return Object(a.a)({url:"/store/refund/detail/".concat(t),method:"get"})}function D(t){return Object(a.a)({url:"store/region",method:"get",params:t})}function B(t,e){return Object(a.a)({url:"store/region/set_alone/".concat(t,"/").concat(e),method:"put"})}function N(t){return Object(a.a)({url:"store/region/info/".concat(t),method:"get"})}function A(t,e){return Object(a.a)({url:"store/region/".concat(e),method:"post",data:t})}function K(t){return Object(a.a)({url:"store/all_region",method:"get",params:t})}function W(t){return Object(a.a)({url:"resolve/city",method:"get",params:t})}function G(t){return Object(a.a)({url:"store/region/city",method:"get",params:t})}},"93db":function(t,e,r){},b7ff:function(t,e,r){"use strict";var a=r("93db");r.n(a).a},c7fc:function(t,e,r){"use strict";var a={props:{lat:{type:Number,default:34.34127},lon:{type:Number,default:108.93984},mapKey:{tyep:String},address:{tyep:String}},data:function(){return{geocoder:void 0,map:null,marker:null}},created:function(){this.initMap()},methods:{initMap:function(){var t,e=this;(t=this.mapKey,new Promise((function(e,r){window.init=function(){e(window.qq)};var a=document.createElement("script");a.type="text/javascript",a.src="https://map.qq.com/api/js?v=2.exp&callback=init&key=".concat(t),a.onerror=r,document.head.appendChild(a)}))).then((function(t){new t.maps.LatLng(e.lat,e.lon),e.map=new t.maps.Map(document.getElementById("container"),{zoom:15}),e.searchKeyword(e.address||"陕西省西安市"),t.maps.event.addListener(e.map,"click",e.handleMapClick)}))},searchKeyword:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"陕西省西安市",e=this;e.$jsonp("https://apis.map.qq.com/ws/geocoder/v1?",{address:"".concat(t),key:e.mapKey,output:"jsonp"}).then((function(t){if(0===t.status&&t.result.location){var r=t.result.location,a=new qq.maps.LatLng(r.lat,r.lng);e.map.setCenter(a),e.map.setZoom(15),e.marker&&e.marker.setMap(null),e.marker=new qq.maps.Marker({position:a,map:e.map}),e.$emit("getCoordinates",t.result)}else e.$Message.error(t.message)})).catch((function(t){e.$Message.error("获取城市编码失败")}))},handleMapClick:function(t){var e=this,r=t.latLng;e.marker&&e.marker.setMap(null),e.marker=new qq.maps.Marker({position:r,map:e.map}),e.$jsonp("https://apis.map.qq.com/ws/geocoder/v1?",{location:"".concat(r.getLat(),",").concat(r.getLng()),key:e.mapKey,output:"jsonp"}).then((function(t){0===t.status&&t.result.address?e.$emit("getCoordinates",t.result):e.$Message.error(t.message)})).catch((function(t){e.$Message.error("获取城市编码失败")}))}}},o=r("2877"),s=Object(o.a)(a,(function(){return this.$createElement,this._self._c,this._m(0)}),[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticStyle:{width:"100%",height:"450px"},attrs:{id:"container"}})])}],!1,null,"32c522ba",null);e.a=s.exports}}]);