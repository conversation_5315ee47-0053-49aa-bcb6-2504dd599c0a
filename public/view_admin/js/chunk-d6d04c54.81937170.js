(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-d6d04c54"],{4777:function(module,__webpack_exports__,__webpack_require__){"use strict";var _api_setting__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("90e7");function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}__webpack_exports__.a={mounted:function(){this.getNewFormBuildRule()},methods:{getNewFormBuildRule:function getNewFormBuildRule(){var _this=this;Object(_api_setting__WEBPACK_IMPORTED_MODULE_0__.u)(this.type?this.type:this.typeMole).then((function(res){_this.rules=res.data.rules,_this.url=res.data.url;var validate=res.data.validate;Object.keys(validate).map((function(key){"object"===_typeof(validate[key])&&validate[key].map((function(item){void 0!==item.pattern&&(item.pattern=eval(item.pattern))}))})),_this.ruleValidate=validate}))},setRulesValue:function(e,t){var o=this;return e.map((function(e){void 0!==e.field&&(e.value=t[e.field]||""),"object"===_typeof(e.options)&&e.options.map((function(e){void 0!==e.componentsModel&&(e.componentsModel=o.setRulesValue(e.componentsModel,t))})),"object"===_typeof(e.control)&&e.control.map((function(e){void 0!==e.componentsModel&&(e.componentsModel=o.setRulesValue(e.componentsModel,t))})),"object"===_typeof(e.componentsModel)&&(e.componentsModel=o.setRulesValue(e.componentsModel,t))})),e}}}},a007:function(e,t,o){"use strict";o.r(t);var n=o("524f"),i=o("4777"),l={name:"trade",components:{fromSubmit:n.a},mixins:[i.a],data:function(){return{ruleValidate:{},rules:[],url:"",title:"交易设置",type:"trade"}}},a=o("2877"),u=Object(a.a)(l,(function(){var e=this.$createElement;return(this._self._c||e)("from-submit",{attrs:{validate:this.ruleValidate,url:this.url,title:this.title,rules:this.rules}})}),[],!1,null,"ce973240",null);t.default=u.exports}}]);