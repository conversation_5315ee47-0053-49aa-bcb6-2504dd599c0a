(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-bee06c60"],{"7ae9":function(t,e,i){"use strict";i.r(e);var o=i("dd2e"),s=i("e449"),n=i("c24f"),l={components:{information:o.a,couponList:s.a},data:function(){return{vipForm:{member_func_status:0,sign_give_exp:"",order_give_exp:"",invite_user_exp:"",level_activate_status:1,level_extend_info:[],level_integral_status:1,level_give_integral:8,level_money_status:1,level_give_money:15,level_coupon_status:1,level_give_coupon:[]},columns3:[{title:"信息",key:"info",width:120},{title:"必填",slot:"required",width:70},{title:"信息格式",key:"label",minWidth:120},{title:"提示信息",key:"tip",minWidth:120},{title:"操作",slot:"action",minWidth:80}],listVip:[],loading:!1,promotionsData:[{threshold:0,give_integral:0,checkIntegral:!1,checkCoupon:!1,checkGoods:!1,giveProducts:[],giveCoupon:[]}],columns1:[{title:"优惠券名称",key:"title",minWidth:150},{title:"类型",slot:"coupon_type",minWidth:80},{title:"面值",slot:"coupon_price",minWidth:100},{title:"最低消费额",key:"use_min_price",minWidth:100},{title:"操作",slot:"status",align:"center",minWidth:80}],vipCopon:[],indexCoupon:0,tabVal:"basic",listOne:[]}},created:function(){this.settingUser()},methods:{settingUser:function(){var t=this;Object(n.N)(this.tabVal).then((function(e){"basic"===t.tabVal?(t.authorizedPicture=e.data.h5_avatar,t.listOne=e.data.user_extend_info,t.tabVal="level",t.settingUser()):"level"===t.tabVal&&(t.vipForm=e.data,t.vipCopon=e.data.level_give_coupon,e.data.level_extend_info.forEach((function(t){1==t.required||1==t.required?t.required=!0:t.required=!1})),t.listVip=e.data.level_extend_info)}))},delVip:function(t,e){this.listVip.splice(e,1)},informationTap:function(){this.$refs.information.isShow=!0},addCoupon:function(t){this.indexCoupon=t,this.$refs.couponTemplates.isTemplate=!0,this.$refs.couponTemplates.tableList()},delCoupon:function(t,e){"level"===this.tabVal&&this.vipCopon.splice(t,1),this.promotionsData[e].giveCoupon.splice(t,1)},handleSubmit:function(t){var e=this,i=this.vipCopon.map((function(t){return t.id}));this.vipForm.level_give_coupon=Array.from(new Set(i)),this.vipForm.level_extend_info=this.listVip,Object(n.M)(t,this.vipForm).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))},getCouponList:function(t){var e=this.indexCoupon;this.$refs.couponTemplates.isTemplate=!1,t.forEach((function(t){t.limit_num=0,t.indexCoupon=e}));var i=this.promotionsData[e].giveCoupon.concat(t),o=this.unique(i);this.vipCopon=o},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},getInfoList:function(t){var e=this.listVip.concat(t),i=this.uniqueVip(e);i.forEach((function(t){1==t.required||1==t.required?t.required=!0:t.required=!1})),this.listVip=i,this.$refs.information.isShow=!1},uniqueVip:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.info)&&e.set(t.info,1)}))}}},a=(i("f5e3"),i("2877")),r=Object(a.a)(l,(function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[i("Form",{attrs:{model:t.vipForm,"label-width":120}},[i("Row",{attrs:{gutter:24,type:"flex"}},[i("Col",{attrs:{span:"24"}},[i("FormItem",{attrs:{label:"会员卡激活："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.vipForm.level_activate_status,callback:function(e){t.$set(t.vipForm,"level_activate_status",e)},expression:"vipForm.level_activate_status"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n              开启后用户等级功能不能直接使用，需要用户填写信息，激活后才能使用用户等级\n            ")])],1)],1),1===t.vipForm.level_activate_status?i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"会员卡信息："}},[t.listVip.length>0?i("Table",{ref:"table",staticClass:"mt10 mb10 goods",attrs:{columns:t.columns3,data:t.listVip,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"required",fn:function(e){e.row;var o=e.index;return[i("Checkbox",{model:{value:t.listVip[o].required,callback:function(e){t.$set(t.listVip[o],"required",e)},expression:"listVip[index].required"}})]}},{key:"action",fn:function(e){var o=e.row,s=e.index;return[i("a",{on:{click:function(e){return t.delVip(o,s)}}},[t._v("删除")])]}}],null,!1,2765270233)}):t._e(),i("Button",{on:{click:t.informationTap}},[t._v(" 选择信息 ")])],1)],1):t._e(),1===t.vipForm.level_activate_status?i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"赠送积分："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.vipForm.level_integral_status,callback:function(e){t.$set(t.vipForm,"level_integral_status",e)},expression:"vipForm.level_integral_status"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n              用户激活会员卡后即赠送一定数额的积分\n            ")]),1===t.vipForm.level_integral_status?i("Input",{staticClass:"inputw mt10",attrs:{placeholder:"请输入赠送的积分"},model:{value:t.vipForm.level_give_integral,callback:function(e){t.$set(t.vipForm,"level_give_integral",e)},expression:"vipForm.level_give_integral"}}):t._e(),1===t.vipForm.level_integral_status?i("span",{staticClass:"span-text"},[t._v("\n              积分\n            ")]):t._e()],1)],1):t._e(),1===t.vipForm.level_activate_status?i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"赠送余额："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.vipForm.level_money_status,callback:function(e){t.$set(t.vipForm,"level_money_status",e)},expression:"vipForm.level_money_status"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n              用户激活会员卡后即赠送一定数额的储值余额\n            ")]),1===t.vipForm.level_money_status?i("Input",{staticClass:"inputw mt10",attrs:{placeholder:"请输入赠送的余额"},model:{value:t.vipForm.level_give_money,callback:function(e){t.$set(t.vipForm,"level_give_money",e)},expression:"vipForm.level_give_money"}}):t._e(),1===t.vipForm.level_money_status?i("span",{staticClass:"span-text"},[t._v("\n              元\n            ")]):t._e()],1)],1):t._e(),1===t.vipForm.level_activate_status?i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"赠送优惠券："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.vipForm.level_coupon_status,callback:function(e){t.$set(t.vipForm,"level_coupon_status",e)},expression:"vipForm.level_coupon_status"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("用户激活会员卡后即赠送优惠券")]),t._l(t.promotionsData,(function(e,o){return i("div",{key:o,staticClass:"item"},[1===t.vipForm.level_coupon_status?i("div",{staticClass:"add-coupon",on:{click:function(e){return t.addCoupon(o)}}},[t._v("\n                + 添加优惠券\n              ")]):t._e(),t.vipCopon.length>0&&1===t.vipForm.level_coupon_status?i("Table",{ref:"table",refInFor:!0,staticClass:"table",attrs:{border:"",columns:t.columns1,data:t.vipCopon,width:"700"},scopedSlots:t._u([{key:"coupon_price",fn:function(e){var o=e.row;return[1==o.coupon_type?i("span",[t._v("\n                    "+t._s(o.coupon_price)+"元\n                  ")]):t._e(),2==o.coupon_type?i("span",[t._v("\n                    "+t._s(parseFloat(o.coupon_price)/10)+"折（"+t._s(o.coupon_price.toString().split(".")[0])+"%）\n                  ")]):t._e()]}},{key:"coupon_type",fn:function(e){return[1===e.row.coupon_type?i("span",[t._v("满减券")]):i("span",[t._v("折扣券")])]}},{key:"status",fn:function(e){e.row;var s=e.index;return[i("a",{on:{click:function(e){return t.delCoupon(s,o)}}},[t._v("删除")])]}}],null,!0)}):t._e()],1)}))],2)],1):t._e(),i("Col",[i("FormItem",[i("div",{staticClass:"subBtn mt10",on:{click:function(e){return t.handleSubmit("level")}}},[t._v("保存")])])],1)],1)],1)],1),i("information",{ref:"information",attrs:{listOne:t.listOne},on:{getInfoList:t.getInfoList}}),i("coupon-list",{ref:"couponTemplates",attrs:{discount:!0},on:{getCouponList:t.getCouponList}})],1)}),[],!1,null,"060a3ffb",null);e.default=r.exports},bc34:function(t,e,i){},f5e3:function(t,e,i){"use strict";var o=i("bc34");i.n(o).a}}]);