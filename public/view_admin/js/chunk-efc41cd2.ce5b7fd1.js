(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-efc41cd2"],{"0123":function(e,t,i){"use strict";var a=i("5b19");i.n(a).a},"0f0e":function(e,t,i){"use strict";var a=i("c4c8"),n={name:"userLabel",props:{},data:function(){return{labelList:[],dataLabel:[],isUser:!1}},mounted:function(){},methods:{inArray:function(e,t){for(var i in t)if(t[i].id===e)return!0;return!1},userLabel:function(e){var t=this;this.dataLabel=e,Object(a.pb)().then((function(e){e.data.map((function(e){e.children&&e.children.length&&(t.isUser=!0,e.children.map((function(e){t.inArray(e.id,t.dataLabel)?e.disabled=!0:e.disabled=!1})))})),t.labelList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},selectLabel:function(e){if(e.disabled){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id===e.id}))[0]);this.dataLabel.splice(t,1),e.disabled=!1}else this.dataLabel.push({label_name:e.label_name,id:e.id,tag_id:e.tag_id}),e.disabled=!0},subBtn:function(){this.$emit("activeData",JSON.parse(JSON.stringify(this.dataLabel)))},cancel:function(){this.$emit("close")}}},s=(i("9b1c"),i("2877")),r=Object(s.a)(n,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"label-wrapper"},[i("div",{staticClass:"list-box"},[e._l(e.labelList,(function(t,a){return e.isUser?i("div",{key:a,staticClass:"label-box"},[t.children&&t.children.length?i("div",{staticClass:"title"},[e._v("\n          "+e._s(t.label_name)+"\n        ")]):e._e(),t.children&&t.children.length?i("div",{staticClass:"list"},e._l(t.children,(function(t,a){return i("div",{key:a,staticClass:"label-item",class:{on:t.disabled},on:{click:function(i){return e.selectLabel(t)}}},[e._v("\n            "+e._s(t.label_name)+"\n          ")])})),0):e._e()]):e._e()})),e.isUser?e._e():i("div",[e._v("暂无标签")])],2),i("div",{staticClass:"footer"},[i("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:e.cancel}},[e._v("取消")]),i("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:e.subBtn}},[e._v("确定")])],1)])}),[],!1,null,"3dda6796",null);t.a=r.exports},"5b19":function(e,t,i){},7364:function(e,t,i){},"9b1c":function(e,t,i){"use strict";var a=i("7364");i.n(a).a},ea72:function(e,t,i){"use strict";i.r(t);var a=i("2f62"),n=i("9b41"),s=i("b0e7"),r=i("c297"),l=i("0f0e"),o=i("d708"),c=i("c276"),d=i("0e5c");function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,a)}return i}function p(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?m(i,!0).forEach((function(t){f(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):m(i).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function f(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var b={data:function(){var e;return f(e={roterPre:o.a.roterPre,formItem:{name:"",type:"0",client_tag_list:[],client_type:"0",user_ids:[],welcome_words:{text:{content:""},attachments:[]},send_type:"0",send_time:""},descType:"0",modalPic:!1,picTit:"",tableIndex:0,isChoice:"",ruleValidate:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],send_time:[{required:!0,message:"发送时间不能为空",trigger:"change"}]},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},linkObj:{msgtype:"link",link:{media_id:"",title:"",url:""}},videoObj:{msgtype:"video",video:{media_id:"",url:""}},activeDepartment:{},isSite:!0,onlyDepartment:!1,openType:"",userList:[],fileUrl2:o.a.apiBaseURL+"/file/video_upload",upload_type:"",uploadData:{},header:{},progress:0},"upload_type",!0),f(e,"clientCount",0),f(e,"labelShow",!1),f(e,"dataLabel",[]),e},components:{uploadPictures:s.a,department:r.a,userLabel:l.a},computed:p({},Object(a.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),watch:{"formItem.client_tag_list":function(e,t){e!==t&&this.getClientCount()},"formItem.user_ids":function(e,t){e!==t&&this.getClientCount()},dataLabel:function(e){this.formItem.client_tag_list=e.map((function(e){return e.tag_id}))}},mounted:function(){this.setCopyrightShow({value:!1}),this.getToken(),this.getClientCount()},destroyed:function(){this.setCopyrightShow({value:!0})},methods:p({},Object(a.d)("admin/layout",["setCopyrightShow"]),{delVideo:function(){this.videoObj.video.url="",this.upload_type=!0},getWorkLabel:function(){var e=this;Object(n.P)().then((function(t){e.labelList=t.data.map((function(t){return e.mapTree(t)}))}))},mapTree:function(e){var t=this,i=Array.isArray(e.children)&&e.children.length>0;return{title:e.label,expand:!0,value:e.value,selected:!1,checked:!1,children:i?e.children.map((function(e){return t.mapTree(e)})):[]}},modalPicTap:function(e,t,i){this.modalPic=!0,this.isChoice="dan"===e?"单选":"多选",this.picTit=t,this.tableIndex=i},snedChangeTime:function(e){this.formItem.send_time=e},handleRemove:function(e){this.formItem.welcome_words.attachments.splice(e,1)},getPicD:function(e){var t=this;e.map((function(e){return e.att_dir})).forEach((function(e){var i={msgtype:"image",image:{media_id:"",pic_url:""}};i.image.pic_url=e,t.formItem.welcome_words.attachments.push(i)})),this.modalPic=!1},addUser:function(){this.userList=this.formItem.user_ids,this.$refs.department.memberStatus=!0},changeMastart:function(e,t){this.formItem.user_ids=e.map((function(e){return{userid:e.userid,name:e.name,id:e.id}}))},handleDel:function(e){var t=this.formItem.user_ids.findIndex((function(t){return t.id===e}));this.formItem.user_ids.splice(t,1),this.getClientCount()},linkBlur:function(){if(!this.linkObj.link.url)return this.$Message.warning("请输入链接")},titleBlur:function(){if(!this.linkObj.link.title)return this.$Message.warning("请输入链接标题")},submit:function(){var e=this;if(!this.formItem.welcome_words.text.content.length)return this.$Message.error("请填写消息内容1");2==this.descType?this.formItem.welcome_words.attachments.push(this.linkObj):1==this.descType&&this.formItem.welcome_words.attachments.push(this.videoObj);var t=this.deepClone(this.formItem);t.user_ids=t.user_ids.map((function(e){return e.userid})),"0"===t.client_type&&(t.client_tag_list=[]),this.$refs.formItem.validate((function(i){i&&Object(n.T)(t).then((function(t){e.$Message.success(t.msg),e.$router.push(e.roterPre+"/work/client/moment")})).catch((function(t){e.$Message.error(t.msg)}))}))},deepClone:function(e){var t=Array.isArray(e)?[]:{};if(e&&"object"===u(e))for(var i in e)e.hasOwnProperty(i)&&(t[i]=e&&"object"===u(e[i])?this.deepClone(e[i]):e[i]);return t},videoSaveToUrl:function(e){var t=this;return-1!==["video/mp4"].indexOf(e.type)?(Object(d.a)({randoms:"",file:e,pieceSize:3,success:function(e){t.upload_type=!1,t.videoObj.video.url=e.file_path,t.$Spin.hide()},error:function(e){t.$Message.error(e.msg),t.$Spin.hide()},uploading:function(e,i){t.$Spin.show()}}),!1):this.$Message.warning({content:"文件  "+e.name+"  格式不正确, 请选择格式正确的视频",duration:5})},getToken:function(){this.header["Authori-zation"]="Bearer "+c.a.cookies.get("token")},getClientCount:function(){var e=this;Object(n.B)({is_all:1==this.formItem.client_type?0:1,label:this.formItem.client_tag_list,userid:this.formItem.user_ids.map((function(e){return e.userid}))}).then((function(t){e.clientCount=t.data.sum_count}))},openLabel:function(){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)))},activeData:function(e){this.labelShow=!1,this.dataLabel=e},labelClose:function(){this.labelShow=!1},closeLabel:function(e){var t=this.dataLabel.indexOf(this.dataLabel.filter((function(t){return t.id==e.id}))[0]);this.dataLabel.splice(t,1)}})},h=(i("0123"),i("2877")),v=Object(h.a)(b,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"i-layout-page-header"},[i("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[i("div",{attrs:{slot:"title"},slot:"title"},[i("div",{staticClass:"float-l"},[i("router-link",{attrs:{to:{path:e.roterPre+"/work/client/moment"}}},[i("div",{staticClass:"font-sm after-line"},[i("span",{staticClass:"iconfont iconfanhui"}),i("span",{staticClass:"pl10"},[e._v("返回")])])])],1),i("span",{staticClass:"mr20 ml16"},[e._v("发布朋友圈")])])])],1),i("Card",{staticClass:"ivu-mt mb100",attrs:{bordered:!1,"dis-hover":""}},[i("Form",{ref:"formItem",attrs:{model:e.formItem,"label-width":110,"label-colon":!0,rules:e.ruleValidate}},[i("FormItem",{attrs:{label:"任务名称",prop:"name"}},[i("Input",{staticClass:"input-add",attrs:{placeholder:"请输入任务名称"},model:{value:e.formItem.name,callback:function(t){e.$set(e.formItem,"name",t)},expression:"formItem.name"}})],1),i("FormItem",{attrs:{label:"成员类型",required:""}},[i("RadioGroup",{model:{value:e.formItem.type,callback:function(t){e.$set(e.formItem,"type",t)},expression:"formItem.type"}},[i("Radio",{attrs:{label:"0"}},[e._v("全部成员")]),i("Radio",{attrs:{label:"1"}},[e._v("选定成员")])],1)],1),1==e.formItem.type?i("FormItem",{attrs:{label:"发表成员"}},[i("Button",{on:{click:function(t){return e.addUser()}}},[e._v("选择成员")]),i("div",{staticClass:"mt10"},e._l(e.formItem.user_ids,(function(t,a){return i("Tag",{key:a,attrs:{closable:"",name:t.name,size:"large"},on:{"on-close":function(i){return e.handleDel(t.id)}}},[e._v(e._s(t.name))])})),1)],1):e._e(),i("FormItem",{attrs:{label:"可见的客户",required:""}},[i("RadioGroup",{model:{value:e.formItem.client_type,callback:function(t){e.$set(e.formItem,"client_type",t)},expression:"formItem.client_type"}},[i("Radio",{attrs:{label:"0"}},[e._v("全部客户")]),i("Radio",{attrs:{label:"1"}},[e._v("筛选客户")])],1),i("div",{staticClass:"desc"},[e._v("预估将发送到\n            "),i("span",{staticClass:"client_count"},[e._v("["+e._s(e.clientCount)+"]")]),e._v("个客户朋友圈中。\n            "),i("span",{directives:[{name:"show",rawName:"v-show",value:1==e.formItem.client_type,expression:"formItem.client_type == 1"}]},[e._v("将消息发送给符合条件的客户")])])],1),1==e.formItem.client_type?i("FormItem",{attrs:{label:"标签"}},[i("div",{staticClass:"acea-row row-between-wrapper label-content",on:{click:e.openLabel}},[i("div",{staticClass:"label-inner"},[e.dataLabel.length?i("div",e._l(e.dataLabel,(function(t){return i("Tag",{key:t.id,attrs:{closable:""},on:{"on-close":function(i){return e.closeLabel(t)}}},[e._v(e._s(t.label_name))])})),1):i("span",{staticClass:"placeholder"},[e._v("请选择")])]),i("div",{staticClass:"iconfont iconxiayi"})])]):e._e(),i("FormItem",{attrs:{label:"消息内容1",required:""}},[i("Input",{staticClass:"input-add",attrs:{type:"textarea",rows:4,"show-word-limit":"",placeholder:"请输入消息内容",maxlength:"1000"},model:{value:e.formItem.welcome_words.text.content,callback:function(t){e.$set(e.formItem.welcome_words.text,"content",t)},expression:"formItem.welcome_words.text.content"}})],1),i("FormItem",{attrs:{label:"消息内容2"}},[i("RadioGroup",{model:{value:e.descType,callback:function(t){e.descType=t},expression:"descType"}},[i("Radio",{attrs:{label:"0"}},[e._v("图片")]),i("Radio",{attrs:{label:"1"}},[e._v("视频")]),i("Radio",{attrs:{label:"2"}},[e._v("图文链接")])],1)],1),0==e.descType?i("FormItem",[i("div",{staticClass:"acea-row"},[e._l(e.formItem.welcome_words.attachments,(function(t,a){return i("div",{key:a,staticClass:"pictrue"},["image"==t.msgtype?i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.image.pic_url,expression:"item.image.pic_url"}]}):e._e(),i("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(t){return e.handleRemove(a)}}})],1)})),e.formItem.welcome_words.attachments.length<9?i("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(t){return e.modalPicTap("duo")}}},[i("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1):e._e()],2),i("div",{staticClass:"tips"},[e._v("\n            因企业微信限制，图片长宽建议不超过1440x1080，大小不超过10M\n          ")])]):e._e(),1==e.descType?i("FormItem",[e.upload_type?i("Upload",{staticStyle:{display:"inline-block"},attrs:{"show-upload-list":!1,action:e.fileUrl2,"before-upload":e.videoSaveToUrl,data:e.uploadData,headers:e.header,multiple:!0}},[i("div",{staticClass:"videbox"},[e._v("\n\t\t\t\t      +\n\t\t\t\t    ")])]):i("Input",{staticClass:"input-add",attrs:{readonly:"",icon:"ios-close-circle-outline"},on:{"on-click":e.delVideo},model:{value:e.videoObj.video.url,callback:function(t){e.$set(e.videoObj.video,"url",t)},expression:"videoObj.video.url"}}),i("div",{staticClass:"tips"},[e._v("建议时长：9～30秒，大小10M以内")])],1):e._e(),2==e.descType?i("FormItem",{attrs:{label:"图文链接"}},[i("Input",{staticClass:"input-add",attrs:{placeholder:"链接地址请以http或https开头"},on:{"on-blur":function(t){return e.linkBlur()}},model:{value:e.linkObj.link.url,callback:function(t){e.$set(e.linkObj.link,"url",t)},expression:"linkObj.link.url"}})],1):e._e(),2==e.descType?i("FormItem",{attrs:{label:"链接标题"}},[i("Input",{staticClass:"input-add",attrs:{placeholder:"请输入链接标题"},on:{"on-blur":function(t){return e.titleBlur()}},model:{value:e.linkObj.link.title,callback:function(t){e.$set(e.linkObj.link,"title",t)},expression:"linkObj.link.title"}})],1):e._e(),i("FormItem",{attrs:{label:"发送方式",required:""}},[i("RadioGroup",{model:{value:e.formItem.send_type,callback:function(t){e.$set(e.formItem,"send_type",t)},expression:"formItem.send_type"}},[i("Radio",{attrs:{label:"0"}},[e._v("立即发送")]),i("Radio",{attrs:{label:"1"}},[e._v("定时发送")])],1)],1),1==e.formItem.send_type?i("FormItem",{attrs:{label:"定时发送时间",prop:"send_time"}},[i("DatePicker",{staticClass:"input-add",attrs:{type:"datetime",placeholder:"Select date"},on:{"on-change":e.snedChangeTime},model:{value:e.formItem.send_time,callback:function(t){e.$set(e.formItem,"send_time",t)},expression:"formItem.send_time"}})],1):e._e()],1)],1),i("Card",{staticClass:"fixed-card",attrs:{bordered:!1,"dis-hover":""}},[i("div",{staticClass:"acea-row row-center-wrapper"},[i("Button",{attrs:{type:"primary"},on:{click:function(t){return e.submit()}}},[e._v("提交")])],1)]),i("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"上传商品图","mask-closable":!1,"z-index":1},model:{value:e.modalPic,callback:function(t){e.modalPic=t},expression:"modalPic"}},[e.modalPic?i("uploadPictures",{attrs:{isChoice:e.isChoice,gridBtn:e.gridBtn,gridPic:e.gridPic},on:{getPicD:e.getPicD}}):e._e()],1),i("department",{ref:"department",attrs:{"active-department":e.activeDepartment,"is-site":e.isSite,userList:e.userList,"only-department":e.onlyDepartment},on:{changeMastart:e.changeMastart}}),i("Modal",{attrs:{scrollable:"",title:"选择用户标签",closable:!0,width:"540","footer-hide":!0,"mask-closable":!1},model:{value:e.labelShow,callback:function(t){e.labelShow=t},expression:"labelShow"}},[i("userLabel",{ref:"userLabel",on:{activeData:e.activeData,close:e.labelClose}})],1)],1)}),[],!1,null,"a1ca3ff8",null);t.default=v.exports}}]);