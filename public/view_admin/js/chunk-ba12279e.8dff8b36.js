(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-ba12279e"],{"73f5":function(t,e,r){"use strict";r.d(e,"z",(function(){return a})),r.d(e,"t",(function(){return o})),r.d(e,"r",(function(){return u})),r.d(e,"u",(function(){return i})),r.d(e,"n",(function(){return c})),r.d(e,"x",(function(){return s})),r.d(e,"H",(function(){return d})),r.d(e,"I",(function(){return l})),r.d(e,"F",(function(){return p})),r.d(e,"G",(function(){return f})),r.d(e,"g",(function(){return m})),r.d(e,"C",(function(){return h})),r.d(e,"D",(function(){return b})),r.d(e,"E",(function(){return g})),r.d(e,"P",(function(){return v})),r.d(e,"K",(function(){return O})),r.d(e,"J",(function(){return w})),r.d(e,"e",(function(){return j})),r.d(e,"O",(function(){return _})),r.d(e,"p",(function(){return V})),r.d(e,"L",(function(){return x})),r.d(e,"M",(function(){return y})),r.d(e,"N",(function(){return k})),r.d(e,"o",(function(){return C})),r.d(e,"s",(function(){return I})),r.d(e,"A",(function(){return $})),r.d(e,"q",(function(){return P})),r.d(e,"y",(function(){return F})),r.d(e,"f",(function(){return N})),r.d(e,"B",(function(){return S})),r.d(e,"b",(function(){return D})),r.d(e,"d",(function(){return q})),r.d(e,"c",(function(){return E})),r.d(e,"a",(function(){return M})),r.d(e,"i",(function(){return z})),r.d(e,"k",(function(){return B})),r.d(e,"w",(function(){return H})),r.d(e,"j",(function(){return J})),r.d(e,"v",(function(){return R})),r.d(e,"h",(function(){return A})),r.d(e,"l",(function(){return G})),r.d(e,"m",(function(){return W}));var n=r("b6bd");function a(t){return Object(n.a)({url:"merchant/store_list",method:"get",params:t})}function o(t){return Object(n.a)({url:"store/order/list",method:"get",params:t})}function u(t){return Object(n.a)({url:"store/order/chart",method:"get",params:t})}function i(t){return Object(n.a)({url:"store/refund/list",method:"get",params:t})}function c(t){return Object(n.a)({url:"/order/no_refund/".concat(t),method:"get"})}function s(t){return Object(n.a)({url:"/order/refund_integral/".concat(t),method:"get"})}function d(t){return Object(n.a)({url:"store/finance_flow/list",method:"get",params:t})}function l(t,e){return Object(n.a)({url:"store/finance_flow/mark/".concat(t),method:"put",params:e})}function p(t){return Object(n.a)({url:"store/finance_flow/fund_record",method:"get",params:t})}function f(t){return Object(n.a)({url:"store/finance_flow/fund_record_info",method:"get",params:t})}function m(t){return Object(n.a)({url:"/export/storeFinanceRecord",method:"get",params:t})}function h(t){return Object(n.a)({url:"/store/extract/list",method:"get",params:t})}function b(t,e){return Object(n.a)({url:"store/extract/mark/".concat(t),method:"post",data:e})}function g(t,e){return Object(n.a)({url:"store/extract/verify/".concat(t),method:"post",data:e})}function v(t){return Object(n.a)({url:"store/extract/transfer/".concat(t),method:"get"})}function O(t){return Object(n.a)({url:"store/store",method:"get",params:t})}function w(t){return Object(n.a)({url:"store/store/get_info/".concat(t),method:"get"})}function j(t){return Object(n.a)({url:"city",method:"get",params:t})}function _(t,e){return Object(n.a)({url:"store/store/".concat(t),method:"post",data:e})}function V(){return Object(n.a)({url:"store/store/address",method:"get"})}function x(t){return Object(n.a)({url:"store/store/login/".concat(t),method:"get"})}function y(t,e){return Object(n.a)({url:"store/store/set_show/".concat(t,"/").concat(e),method:"put"})}function k(t){return Object(n.a)({url:"store/share/order",method:"post",params:t})}function C(t){return Object(n.a)({url:"store/home/<USER>",method:"get",params:t})}function I(t){return Object(n.a)({url:"store/home/<USER>",method:"get",params:t})}function $(t){return Object(n.a)({url:"store/home/<USER>",method:"get",params:t})}function P(t){return Object(n.a)({url:"store/home/<USER>",method:"get",params:t})}function F(t){return Object(n.a)({url:"store/store/reset_admin/".concat(t),method:"get"})}function N(t,e,r){return Object(n.a)({url:"export/storeFlowExport?store_id=".concat(t,"&keyword=").concat(e,"&data=").concat(r),method:"get"})}function S(t){return Object(n.a)({url:"/store/category",params:t,method:"get"})}function D(t){return Object(n.a)({url:"/store/category/create/".concat(t),method:"get"})}function q(t){return Object(n.a)({url:"/store/category/tree/".concat(t),method:"get"})}function E(t){return Object(n.a)({url:"/store/category/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function M(t){return Object(n.a)({url:"store/category/cascader_list/".concat(t),method:"get"})}function z(t){return Object(n.a)({url:"/store/refund/detail/".concat(t),method:"get"})}function B(t){return Object(n.a)({url:"store/region",method:"get",params:t})}function H(t,e){return Object(n.a)({url:"store/region/set_alone/".concat(t,"/").concat(e),method:"put"})}function J(t){return Object(n.a)({url:"store/region/info/".concat(t),method:"get"})}function R(t,e){return Object(n.a)({url:"store/region/".concat(e),method:"post",data:t})}function A(t){return Object(n.a)({url:"store/all_region",method:"get",params:t})}function G(t){return Object(n.a)({url:"resolve/city",method:"get",params:t})}function W(t){return Object(n.a)({url:"store/region/city",method:"get",params:t})}},"7cd3":function(t,e,r){"use strict";var n=r("fdc6");r.n(n).a},"97f5":function(t,e,r){"use strict";r.r(e);var n=r("a34a"),a=r.n(n),o=r("2f62"),u=r("a6b9"),i=r("73f5"),c=r("d708");function s(t,e,r,n,a,o,u){try{var i=t[o](u),c=i.value}catch(t){return void r(t)}i.done?e(c):Promise.resolve(c).then(n,a)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function u(t){s(o,n,a,u,i,"next",t)}function i(t){s(o,n,a,u,i,"throw",t)}u(void 0)}))}}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var f={name:"",components:{},props:{},data:function(){return{roterPre:c.a.roterPre,width:150,addresData:[],formValidate:{supplier_name:"",name:"",phone:"",detailed_address:"",address:"",addressSelect:[],email:"",mark:"",account:"",pwd:"",conf_pwd:"",is_show:0,province:0,city:0,area:0,street:0},ruleValidate:{supplier_name:[{required:!0,message:"供应商不能为空",trigger:"blur"}],phone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式不正确",trigger:"blur"}],account:[{required:!0,message:"请输入用户名",trigger:"blur"}],address:[{required:!0,message:"请填写具体地址",trigger:"blur"}],addressSelect:[{required:!0,message:"请选择省市区",trigger:"blur"}],pwd:[{required:!0,message:"请输入密码",trigger:"blur"}],conf_pwd:[{required:!0,message:"密码不能为空",trigger:"blur"}]}}},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(r,!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(o.e)("admin/layout",["isMobile","menuCollapse"]),{labelPosition:function(){return this.isMobile?"top":"right"}}),watch:{},created:function(){this.cityInfo({pid:0}),this.$route.params.id&&this.getSupplier()},mounted:function(){},methods:{cityInfo:function(t){var e=this;Object(i.e)(t).then((function(t){e.addresData=t.data}))},loadData:function(t,e){t.loading=!0,Object(i.e)({pid:t.value}).then((function(r){t.children=r.data,t.loading=!1,e()}))},addchack:function(t,e){var r=this;t.forEach((function(t,e){0==e?r.formValidate.province=t:1==e?r.formValidate.city=t:2==e?r.formValidate.area=t:r.formValidate.street=t}));var n=[];e.forEach((function(t){n.push(t.label)})),this.formValidate.address=n.join("")},handleSubmit:function(){var t=this;this.$route.params.id?Object(u.v)(this.$route.params.id,this.formValidate).then(function(){var e=d(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$Message.success(r.msg),t.$router.push({path:t.roterPre+"/supplier/menu/list"});case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)})):Object(u.a)(this.formValidate).then(function(){var e=d(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$Message.success(r.msg),t.$router.push({path:t.roterPre+"/supplier/menu/list"});case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},getSupplier:function(){var t=this;Object(u.k)(this.$route.params.id).then(function(){var e=d(a.a.mark((function e(r){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.formValidate=r.data,n=[],r.data.province&&n.push(r.data.province),r.data.city&&n.push(r.data.city),r.data.area&&n.push(r.data.area),r.data.street&&n.push(r.data.street),t.formValidate.addressSelect=n;case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))}}},m=(r("7cd3"),r("2877")),h=Object(m.a)(f,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"article-manager video-icon form-submit",attrs:{id:"shopp-manager"}},[r("div",{staticClass:"i-layout-page-header"},[r("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[r("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[r("router-link",{attrs:{to:{path:t.roterPre+"/supplier/menu/list"}}},[r("div",{staticClass:"font-sm after-line"},[r("span",{staticClass:"iconfont iconfanhui"}),r("span",{staticClass:"pl10"},[t._v("返回")])])]),r("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑供应商":"添加供应商")}})],1)])],1),r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.width,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{attrs:{gutter:24,type:"flex"}},[r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"供应商名称：",prop:"supplier_name"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入供应商名称"},model:{value:t.formValidate.supplier_name,callback:function(e){t.$set(t.formValidate,"supplier_name",e)},expression:"formValidate.supplier_name"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"联系人姓名：",prop:"name"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入联系人姓名"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"联系电话：",prop:"phone"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入联系电话"},model:{value:t.formValidate.phone,callback:function(e){t.$set(t.formValidate,"phone",e)},expression:"formValidate.phone"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"省市区地址",prop:"addressSelect"}},[r("Cascader",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{data:t.addresData,"load-data":t.loadData},on:{"on-change":t.addchack},model:{value:t.formValidate.addressSelect,callback:function(e){t.$set(t.formValidate,"addressSelect",e)},expression:"formValidate.addressSelect"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"供应商地址：",prop:"detailed_address"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入供应商地址"},model:{value:t.formValidate.detailed_address,callback:function(e){t.$set(t.formValidate,"detailed_address",e)},expression:"formValidate.detailed_address"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"供应商邮箱：",prop:"email"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入供应商邮箱"},model:{value:t.formValidate.email,callback:function(e){t.$set(t.formValidate,"email",e)},expression:"formValidate.email"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"备注：",prop:"mark"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{type:"textarea",placeholder:"请输入..."},model:{value:t.formValidate.mark,callback:function(e){t.$set(t.formValidate,"mark",e)},expression:"formValidate.mark"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"供应商登录用户名：",prop:"account"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入登录名称"},model:{value:t.formValidate.account,callback:function(e){t.$set(t.formValidate,"account",e)},expression:"formValidate.account"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"供应商登录密码 ：",prop:"pwd"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请输入登录密码"},model:{value:t.formValidate.pwd,callback:function(e){t.$set(t.formValidate,"pwd",e)},expression:"formValidate.pwd"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"确认登录密码 ：",prop:"conf_pwd"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{placeholder:"请确认登录密码"},model:{value:t.formValidate.conf_pwd,callback:function(e){t.$set(t.formValidate,"conf_pwd",e)},expression:"formValidate.conf_pwd"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"排序："}},[r("InputNumber",{directives:[{name:"width",rawName:"v-width",value:460,expression:"460"}],attrs:{min:0,max:999999,placeholder:"请输入排序"},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"是否开启："}},[r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_show,callback:function(e){t.$set(t.formValidate,"is_show",e)},expression:"formValidate.is_show"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1)],1)],1)],1),r("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[r("Form",[r("FormItem",[r("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")])],1)],1)],1)],1)}),[],!1,null,"e334e804",null);e.default=h.exports},a6b9:function(t,e,r){"use strict";r.d(e,"F",(function(){return a})),r.d(e,"k",(function(){return o})),r.d(e,"a",(function(){return u})),r.d(e,"v",(function(){return i})),r.d(e,"w",(function(){return c})),r.d(e,"l",(function(){return s})),r.d(e,"h",(function(){return d})),r.d(e,"b",(function(){return l})),r.d(e,"c",(function(){return p})),r.d(e,"t",(function(){return f})),r.d(e,"o",(function(){return m})),r.d(e,"p",(function(){return h})),r.d(e,"r",(function(){return b})),r.d(e,"u",(function(){return g})),r.d(e,"G",(function(){return v})),r.d(e,"q",(function(){return O})),r.d(e,"j",(function(){return w})),r.d(e,"n",(function(){return j})),r.d(e,"x",(function(){return _})),r.d(e,"g",(function(){return V})),r.d(e,"f",(function(){return x})),r.d(e,"m",(function(){return y})),r.d(e,"i",(function(){return k})),r.d(e,"B",(function(){return C})),r.d(e,"C",(function(){return I})),r.d(e,"E",(function(){return $})),r.d(e,"d",(function(){return P})),r.d(e,"D",(function(){return F})),r.d(e,"e",(function(){return N})),r.d(e,"y",(function(){return S})),r.d(e,"A",(function(){return D})),r.d(e,"H",(function(){return q})),r.d(e,"z",(function(){return E})),r.d(e,"s",(function(){return M}));var n=r("b6bd");function a(t){return Object(n.a)({url:"supplier/supplier",method:"get",params:t})}function o(t){return Object(n.a)({url:"/supplier/supplier/".concat(t),method:"get"})}function u(t){return Object(n.a)({url:"supplier/supplier",method:"post",data:t})}function i(t,e){return Object(n.a)({url:"supplier/supplier/".concat(t),method:"put",data:e})}function c(t,e){return Object(n.a)({url:"/supplier/supplier/set_status/".concat(t,"/").concat(e),method:"put"})}function s(t){return Object(n.a)({url:"/supplier/list",method:"get",params:t})}function d(t){return Object(n.a)({url:"/supplier/order/list",method:"get",params:t})}function l(t){return Object(n.a)({url:"/supplier/order/deliver_remind/".concat(t.supplier_id,"/").concat(t.id),method:"put"})}function p(t){return Object(n.a)({url:"/supplier/order/distribution_info",method:"get",params:{ids:t}})}function f(t){return Object(n.a)({url:"/supplier/refund/list",method:"get",params:t})}function m(t){return Object(n.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function h(t){return Object(n.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function b(t){return Object(n.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function g(t){return Object(n.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function v(t){return Object(n.a)({url:"/supplier/supplier/login/".concat(t),method:"get"})}function O(t){return Object(n.a)({url:"/supplier/home/<USER>",method:"get",params:t})}function w(t){return Object(n.a)({url:"/supplier/refund/detail/".concat(t),method:"get"})}function j(t){return Object(n.a)({url:"/supplier/order/no_refund/".concat(t),method:"get"})}function _(t){return Object(n.a)({url:"/supplier/order/refund_integral/".concat(t),method:"get"})}function V(t){return Object(n.a)({url:"/supplier/order/distribution/".concat(t),method:"get"})}function x(t){return Object(n.a)({url:"/supplier/apply/list",method:"get",params:t})}function y(t){return Object(n.a)({url:"/supplier/apply/verify/form/".concat(t),method:"get"})}function k(t){return Object(n.a)({url:"/supplier/apply/mark/form/".concat(t),method:"get"})}function C(t){return Object(n.a)({url:"/supplier/flowing_water/fund_record_info",method:"get",params:t})}function I(t){return Object(n.a)({url:"/supplier/flowing_water/list",method:"get",params:t})}function $(t,e){return Object(n.a)({url:"/supplier/flowing_water/mark/".concat(t),method:"put",params:e})}function P(t){return Object(n.a)({url:"/export/supplierWaterExport",method:"get",params:t})}function F(t){return Object(n.a)({url:"/supplier/flowing_water/fund_record",method:"get",params:t})}function N(t){return Object(n.a)({url:"/export/supplierWaterRecord",method:"get",params:t})}function S(t){return Object(n.a)({url:"/supplier/extract/list",method:"get",params:t})}function D(t,e){return Object(n.a)({url:"/supplier/extract/verify/".concat(t),method:"post",data:e})}function q(t){return Object(n.a)({url:"/supplier/extract/transfer/".concat(t),method:"get"})}function E(t,e){return Object(n.a)({url:"/supplier/extract/mark/".concat(t),method:"post",data:e})}function M(t){return Object(n.a)({url:"supplier/order/chart",method:"get",params:t})}},fdc6:function(t,e,r){}}]);