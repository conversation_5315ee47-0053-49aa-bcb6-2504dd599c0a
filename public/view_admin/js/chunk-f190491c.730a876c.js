(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f190491c"],{"029e":function(t,e,n){"use strict";var r=n("0cb4");n.n(r).a},"0b65":function(t,e,n){"use strict";e.a={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,n=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,n))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},"0cb4":function(t,e,n){},"586c":function(t,e,n){t.exports=n.p+"view_admin/img/yonghu.874cd27c.png"},"73f5":function(t,e,n){"use strict";n.d(e,"z",(function(){return a})),n.d(e,"t",(function(){return i})),n.d(e,"r",(function(){return o})),n.d(e,"u",(function(){return s})),n.d(e,"n",(function(){return c})),n.d(e,"x",(function(){return u})),n.d(e,"H",(function(){return l})),n.d(e,"I",(function(){return d})),n.d(e,"F",(function(){return f})),n.d(e,"G",(function(){return h})),n.d(e,"g",(function(){return m})),n.d(e,"C",(function(){return p})),n.d(e,"D",(function(){return g})),n.d(e,"E",(function(){return b})),n.d(e,"P",(function(){return y})),n.d(e,"K",(function(){return w})),n.d(e,"J",(function(){return x})),n.d(e,"e",(function(){return v})),n.d(e,"O",(function(){return D})),n.d(e,"p",(function(){return _})),n.d(e,"L",(function(){return O})),n.d(e,"M",(function(){return j})),n.d(e,"N",(function(){return L})),n.d(e,"o",(function(){return C})),n.d(e,"s",(function(){return F})),n.d(e,"A",(function(){return S})),n.d(e,"q",(function(){return T})),n.d(e,"y",(function(){return k})),n.d(e,"f",(function(){return A})),n.d(e,"B",(function(){return V})),n.d(e,"b",(function(){return M})),n.d(e,"d",(function(){return z})),n.d(e,"c",(function(){return P})),n.d(e,"a",(function(){return E})),n.d(e,"i",(function(){return W})),n.d(e,"k",(function(){return N})),n.d(e,"w",(function(){return Y})),n.d(e,"j",(function(){return B})),n.d(e,"v",(function(){return R})),n.d(e,"h",(function(){return $})),n.d(e,"l",(function(){return I})),n.d(e,"m",(function(){return J}));var r=n("b6bd");function a(t){return Object(r.a)({url:"merchant/store_list",method:"get",params:t})}function i(t){return Object(r.a)({url:"store/order/list",method:"get",params:t})}function o(t){return Object(r.a)({url:"store/order/chart",method:"get",params:t})}function s(t){return Object(r.a)({url:"store/refund/list",method:"get",params:t})}function c(t){return Object(r.a)({url:"/order/no_refund/".concat(t),method:"get"})}function u(t){return Object(r.a)({url:"/order/refund_integral/".concat(t),method:"get"})}function l(t){return Object(r.a)({url:"store/finance_flow/list",method:"get",params:t})}function d(t,e){return Object(r.a)({url:"store/finance_flow/mark/".concat(t),method:"put",params:e})}function f(t){return Object(r.a)({url:"store/finance_flow/fund_record",method:"get",params:t})}function h(t){return Object(r.a)({url:"store/finance_flow/fund_record_info",method:"get",params:t})}function m(t){return Object(r.a)({url:"/export/storeFinanceRecord",method:"get",params:t})}function p(t){return Object(r.a)({url:"/store/extract/list",method:"get",params:t})}function g(t,e){return Object(r.a)({url:"store/extract/mark/".concat(t),method:"post",data:e})}function b(t,e){return Object(r.a)({url:"store/extract/verify/".concat(t),method:"post",data:e})}function y(t){return Object(r.a)({url:"store/extract/transfer/".concat(t),method:"get"})}function w(t){return Object(r.a)({url:"store/store",method:"get",params:t})}function x(t){return Object(r.a)({url:"store/store/get_info/".concat(t),method:"get"})}function v(t){return Object(r.a)({url:"city",method:"get",params:t})}function D(t,e){return Object(r.a)({url:"store/store/".concat(t),method:"post",data:e})}function _(){return Object(r.a)({url:"store/store/address",method:"get"})}function O(t){return Object(r.a)({url:"store/store/login/".concat(t),method:"get"})}function j(t,e){return Object(r.a)({url:"store/store/set_show/".concat(t,"/").concat(e),method:"put"})}function L(t){return Object(r.a)({url:"store/share/order",method:"post",params:t})}function C(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function F(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function S(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function T(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function k(t){return Object(r.a)({url:"store/store/reset_admin/".concat(t),method:"get"})}function A(t,e,n){return Object(r.a)({url:"export/storeFlowExport?store_id=".concat(t,"&keyword=").concat(e,"&data=").concat(n),method:"get"})}function V(t){return Object(r.a)({url:"/store/category",params:t,method:"get"})}function M(t){return Object(r.a)({url:"/store/category/create/".concat(t),method:"get"})}function z(t){return Object(r.a)({url:"/store/category/tree/".concat(t),method:"get"})}function P(t){return Object(r.a)({url:"/store/category/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function E(t){return Object(r.a)({url:"store/category/cascader_list/".concat(t),method:"get"})}function W(t){return Object(r.a)({url:"/store/refund/detail/".concat(t),method:"get"})}function N(t){return Object(r.a)({url:"store/region",method:"get",params:t})}function Y(t,e){return Object(r.a)({url:"store/region/set_alone/".concat(t,"/").concat(e),method:"put"})}function B(t){return Object(r.a)({url:"store/region/info/".concat(t),method:"get"})}function R(t,e){return Object(r.a)({url:"store/region/".concat(e),method:"post",data:t})}function $(t){return Object(r.a)({url:"store/all_region",method:"get",params:t})}function I(t){return Object(r.a)({url:"resolve/city",method:"get",params:t})}function J(t){return Object(r.a)({url:"store/region/city",method:"get",params:t})}},"8dd3":function(t,e,n){"use strict";var r=n("b93c");n.n(r).a},9901:function(t,e,n){"use strict";var r=n("313e"),a=n.n(r),i={name:"index",props:{infoList:{type:Object,default:null},styles:{type:Object,default:null},series:Array,echartsTitle:{type:String,default:""},yAxisData:{type:Array,default:function(){return[]}},bingXdata:Array},data:function(){return{infoLists:this.infoList,seriesArray:this.series}},watch:{infoList:{handler:function(t,e){this.infoLists=t,this.handleSetVisitChart()},deep:!0},series:{handler:function(t,e){this.seriesArray=t,this.handleSetVisitChart()},deep:!0}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){var t=this;this.myChart=a.a.init(document.getElementById(this.echarts));var e=null;e="circle"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:this.infoLists.bing_xdata||[]},series:[{name:"",type:"pie",radius:"60%",center:["50%","50%"],data:this.infoLists.bing_data||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}:"circle1"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{b} : {c} ({d}%)"},legend:{icon:"circle",top:"5%",left:"center",fontSize:"12",data:this.infoLists.bing_xdata||[]},series:[{name:"访问来源",type:"pie",radius:["30%","60%"],avoidLabelOverlap:!1,label:{show:!0,formatter:"{d}%",position:"inner",fontSize:"12"},emphasis:{label:{show:!0,fontSize:"15",fontWeight:"bold"}},labelLine:{show:!1},data:this.infoLists.bing_data||[]}]}:"inlie"===this.echartsTitle?{tooltip:{trigger:"axis"},toolbox:{},legend:{icon:"line",left:"left",fontWeight:"100",data:this.infoLists.legend||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.infoLists.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray}:{tooltip:{trigger:"axis"},toolbox:{},legend:{data:this.infoLists.legend||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.infoLists.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray},setTimeout((function(){t.wsFunc(),t.myChart.setOption(e,!0)}),200)},handleResize:function(){this.myChart.resize()}},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)}},o=(n("8dd3"),n("2877")),s=Object(o.a)(i,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"styles",style:this.styles,attrs:{id:this.echarts}})])}),[],!1,null,"e03fd378",null);e.a=s.exports},a584:function(t,e,n){"use strict";var r;function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var i=(a(r={name:"cards",data:function(){return{}},props:{cardLists:Array}},"data",(function(){return{}})),a(r,"methods",{}),a(r,"created",(function(){})),r),o=(n("e83b"),n("2877")),s=Object(o.a)(i,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Row",{staticClass:"ivu-mt",attrs:{type:"flex",align:"middle",gutter:16}},t._l(t.cardLists,(function(e,r){return n("Col",{key:r,staticClass:"ivu-mb",attrs:{xs:24,sm:24,md:12,lg:12,xl:8,xxl:{span:e.col}}},[n("Card",{staticClass:"card_cent",attrs:{shadow:"",padding:0}},[n("div",{staticClass:"card_box"},[n("div",{staticClass:"card_box_cir",class:{one:r%5==0,two:r%5==1,three:r%5==2,four:r%5==3,five:r%5==4}},[n("div",{staticClass:"card_box_cir1",class:{one1:r%5==0,two1:r%5==1,three1:r%5==2,four1:r%5==3,five1:r%5==4}},[e.type?n("span",{staticClass:"iconfont",class:e.className}):n("Icon",{attrs:{type:e.className}})],1)]),n("div",{staticClass:"card_box_txt"},[n("span",{staticClass:"sp1",domProps:{textContent:t._s(e.count||0)}}),n("span",{staticClass:"sp2",domProps:{textContent:t._s(e.name)}})])])])],1)})),1)],1)}),[],!1,null,"566bac57",null);e.a=s.exports},b61c:function(t,e,n){"use strict";n.r(e);var r=n("2f62"),a=n("73f5"),i=n("9901"),o=n("a584"),s=n("0b65");function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function u(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var l={name:"home",components:{cardsData:o.a,echartsFrom:i.a},data:function(){return{loading:!1,optionData:{},formValidate:{status:"",extract_type:"",nireid:"",data:"thirtyday",page:1,limit:20},timeVal:[],options:s.a,cardLists:[],extractStatistics:{},series:[],yAxisData:[],infoList:{},infoLists:{},circle:"circle1",inlie:"inlie",columns:[{title:"门店名称",key:"name",minWidth:80},{title:"门店实际收款",width:180,key:"store_price"},{title:"门店商品销售额",key:"store_order_price",minWidth:100},{title:"门店商品销量",key:"store_product_count",minWidth:100},{title:"门店新增用户数",key:"store_user_count",minWidth:100}],columns2:[{title:"头像",slot:"avatar",minWidth:50},{title:"用户名称",minWidth:100,slot:"nickname"},{title:"订单号",key:"order_id",minWidth:100},{title:"交易金额",slot:"pay_price",minWidth:100},{title:"成交时间",slot:"pay_time",minWidth:100}],tabList:[],tabList2:[]}},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(n,!0).forEach((function(e){u(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.cardList(),this.orderchart(),this.staff(),this.trends()},methods:{int:function(){this.cardList(),this.orderchart(),this.trends(),this.staff()},trends:function(){var t=this;Object(a.q)({data:this.formValidate.data}).then((function(e){t.infoList=e.data||{},t.series=t.infoList.series||[],t.yAxisData=[{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}]}))},orderchart:function(){var t=this;Object(a.s)({data:this.formValidate.data}).then((function(e){t.tabList2=e.data.order_list,t.infoLists=e.data}))},staff:function(){var t=this;Object(a.A)({data:this.formValidate.data}).then((function(e){t.tabList=e.data}))},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.int()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",""==t[0]&&(this.formValidate.data="thirtyday"),this.formValidate.page=1,this.int()},cardList:function(){var t=this;Object(a.o)({data:this.formValidate.data}).then((function(e){t.extractStatistics=e.data,t.cardLists=[{col:"4-8",count:t.extractStatistics.store_income,name:"门店订单金额",className:"iconmendiandingdanjine",type:1},{col:"4-8",count:t.extractStatistics.store_use_yue,name:"余额消耗金额",className:"iconyuexiaohaojine",type:1},{col:"4-8",count:t.extractStatistics.recharge_price,name:"用户充值金额",className:"iconmendianxinzengyonghushu",type:1},{col:"4-8",count:t.extractStatistics.cashier_order_price,name:"收银订单金额",className:"iconshouyindingdanjine",type:1},{col:"4-8",count:t.extractStatistics.vip_price,name:"付费会员金额",className:"iconfufeihuiyuanjine",type:1},{col:"4-8",count:t.extractStatistics.store_order_price,name:"分配订单金额",className:"iconfenpeidingdanjine",type:1},{col:"4-8",count:t.extractStatistics.store_writeoff_order_price,name:"核销订单金额",className:"iconhexiaodingdanjine",type:1},{col:"4-8",count:t.extractStatistics.store_user_count,name:"门店新增用户数",className:"iconxinzengyonghushu1",type:1},{col:"4-8",count:t.extractStatistics.store_pay_user_count,name:"门店成交用户数",className:"iconmendianchengjiaoyonghushu",type:1},{col:"4-8",count:t.extractStatistics.card_count,name:"微信会员卡激活数",className:"iconhuiyuankajihuoshu",type:1}]}))},handleResize:function(){this.infoList&&this.$refs.visitChart.handleResize()}}},d=(n("029e"),n("2877")),f=Object(d.a)(l,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{directives:[{name:"resize",rawName:"v-resize",value:t.handleResize,expression:"handleResize"}]},[r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"时间筛选："}},[r("DatePicker",{staticClass:"input-add",attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1),r("cards-data",{attrs:{cardLists:t.cardLists}}),r("Card",{attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("营业趋势")]),t.infoList?r("echarts-from",{ref:"visitChart",attrs:{series:t.series,echartsTitle:t.inlie,infoList:t.infoList,yAxisData:t.yAxisData}}):t._e()],1),r("Row",{staticClass:"ivu-mt",attrs:{gutter:24}},[r("Col",{staticClass:"ivu-mb dashboard-console-visit",attrs:{xl:15,lg:12,md:24,sm:24,xs:24}},[r("Card",{staticClass:"tablebox",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("交易数据")]),r("Table",{ref:"selection",attrs:{columns:t.columns2,data:t.tabList2,loading:t.loading,height:"300","no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"avatar",fn:function(t){var e=t.row;return t.index,[e.uid?r("img",{attrs:{src:e.avatar,alt:""}}):r("img",{attrs:{src:n("586c")}})]}},{key:"nickname",fn:function(e){var n=e.row;return e.index,[r("div",[t._v(t._s(n.uid?n.nickname:"游客"))])]}},{key:"pay_time",fn:function(e){var n=e.row;return e.index,[r("div",[t._v(t._s(t.$moment(1e3*n.pay_time).format("YYYY-MM-DD H:mm:ss")))])]}},{key:"pay_price",fn:function(e){var n=e.row;return e.index,[r("span",{staticClass:"colorred"},[t._v("￥ "+t._s(n.pay_price))])]}}])})],1)],1),r("Col",{attrs:{xl:9,lg:12,md:24,sm:24,xs:24}},[r("Card",{staticClass:"dashboard-console-visit",attrs:{bordered:!1,"dis-hover":""}},[r("div",{attrs:{slot:"title"},slot:"title"},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("交易类型")])]),r("echarts-from",{ref:"visitChart",attrs:{infoList:t.infoLists,echartsTitle:t.circle}})],1)],1)],1),r("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"fonts"},[t._v("门店业绩")]),r("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tabList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"}})],1)],1)}),[],!1,null,"7529ec79",null);e.default=f.exports},b93c:function(t,e,n){},c72b:function(t,e,n){},e83b:function(t,e,n){"use strict";var r=n("c72b");n.n(r).a}}]);