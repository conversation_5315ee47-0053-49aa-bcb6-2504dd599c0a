(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-a50be986"],{"09f5":function(t,e,n){},"4b65":function(t,e,n){"use strict";n.r(e);var r=n("a069"),u=n("c24f"),o=n("2f62");function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var i={components:{WangEditor:r.a},data:function(){return{id:0,agreement:{title:"",content:"",status:1},content:"",spinShow:!1}},created:function(){this.memberAgreement()},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(n,!0).forEach((function(e){a(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(o.e)("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"},labelBottom:function(){return this.isMobile?void 0:15}}),methods:{getEditorContent:function(t){this.agreement.content=t},memberAgreement:function(){var t=this;this.spinShow=!0,Object(u.t)().then((function(e){t.spinShow=!1;var n=e.data,r=n.title,u=n.content,o=n.status,c=n.id;t.agreement.title=r,t.agreement.content=u,t.content=u,t.agreement.status=o,t.id=c})).catch((function(e){t.$Message.error(e),t.spinShow=!1}))},memberAgreementSave:function(){var t=this;this.$Spin.show(),Object(u.u)(this.id,this.agreement).then((function(e){t.$Spin.hide(),t.$Message.success("保存成功"),t.memberAgreement()})).catch((function(e){t.$Spin.hide(),t.$Message.error(e.msg)}))}}},s=(n("a718"),n("2877")),d=Object(s.a)(i,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Form",{attrs:{"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[n("FormItem",{attrs:{label:"协议名称："}},[n("Input",{model:{value:t.agreement.title,callback:function(e){t.$set(t.agreement,"title",e)},expression:"agreement.title"}})],1),n("FormItem",{attrs:{label:"协议内容："}},[n("WangEditor",{attrs:{content:t.content},on:{editorContent:t.getEditorContent}})],1),n("FormItem",{attrs:{label:"开启状态："}},[n("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.agreement.status,callback:function(e){t.$set(t.agreement,"status",e)},expression:"agreement.status"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1),t.spinShow?n("Spin",{attrs:{fix:""}}):t._e()],1),n("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[n("div",{staticClass:"acea-row row-center"},[n("Button",{attrs:{type:"primary"},on:{click:t.memberAgreementSave}},[t._v("保存")])],1)])],1)}),[],!1,null,"6a7683b4",null);e.default=d.exports},a718:function(t,e,n){"use strict";var r=n("09f5");n.n(r).a},c24f:function(t,e,n){"use strict";n.d(e,"X",(function(){return u})),n.d(e,"N",(function(){return o})),n.d(e,"M",(function(){return c})),n.d(e,"k",(function(){return a})),n.d(e,"r",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"h",(function(){return d})),n.d(e,"g",(function(){return l})),n.d(e,"q",(function(){return m})),n.d(e,"s",(function(){return f})),n.d(e,"J",(function(){return b})),n.d(e,"P",(function(){return h})),n.d(e,"L",(function(){return g})),n.d(e,"K",(function(){return p})),n.d(e,"f",(function(){return O})),n.d(e,"e",(function(){return j})),n.d(e,"n",(function(){return _})),n.d(e,"R",(function(){return v})),n.d(e,"p",(function(){return w})),n.d(e,"Q",(function(){return y})),n.d(e,"cb",(function(){return P})),n.d(e,"U",(function(){return k})),n.d(e,"S",(function(){return S})),n.d(e,"T",(function(){return x})),n.d(e,"W",(function(){return C})),n.d(e,"V",(function(){return M})),n.d(e,"Y",(function(){return $})),n.d(e,"v",(function(){return E})),n.d(e,"w",(function(){return A})),n.d(e,"Z",(function(){return D})),n.d(e,"i",(function(){return U})),n.d(e,"bb",(function(){return F})),n.d(e,"C",(function(){return I})),n.d(e,"db",(function(){return T})),n.d(e,"m",(function(){return W})),n.d(e,"ab",(function(){return B})),n.d(e,"F",(function(){return J})),n.d(e,"B",(function(){return z})),n.d(e,"A",(function(){return q})),n.d(e,"z",(function(){return G})),n.d(e,"D",(function(){return H})),n.d(e,"y",(function(){return K})),n.d(e,"x",(function(){return L})),n.d(e,"u",(function(){return N})),n.d(e,"t",(function(){return Q})),n.d(e,"o",(function(){return R})),n.d(e,"l",(function(){return V})),n.d(e,"G",(function(){return X})),n.d(e,"I",(function(){return Y})),n.d(e,"eb",(function(){return Z})),n.d(e,"O",(function(){return tt})),n.d(e,"E",(function(){return et})),n.d(e,"b",(function(){return nt})),n.d(e,"a",(function(){return rt})),n.d(e,"fb",(function(){return ut})),n.d(e,"j",(function(){return ot})),n.d(e,"c",(function(){return ct})),n.d(e,"H",(function(){return at}));var r=n("b6bd");function u(t){return Object(r.a)({url:"user/user",method:"get",params:t})}function o(t){return Object(r.a)({url:"setting/config/user/".concat(t),method:"get"})}function c(t,e){return Object(r.a)({url:"setting/config/user/".concat(t),method:"post",data:e})}function a(t){return Object(r.a)({url:"user/user/".concat(t,"/edit"),method:"get"})}function i(t){return Object(r.a)({url:"user/set_status/".concat(t.status,"/").concat(t.id),method:"put"})}function s(t){return Object(r.a)({url:"marketing/coupon/grant",method:"get",params:t})}function d(t){return Object(r.a)({url:"user/edit_other/".concat(t),method:"get"})}function l(t){return Object(r.a)({url:"user/user/".concat(t),method:"get"})}function m(t){return Object(r.a)({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function f(t){return Object(r.a)({url:"user/user_level/vip_list",method:"get",params:t})}function b(t){return Object(r.a)({url:"user/user_level/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function h(t,e){return Object(r.a)({url:"user/user_level/task/".concat(t),method:"get",params:e})}function g(t){return Object(r.a)({url:"user/user_level/set_task_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function p(t){return Object(r.a)({url:"user/user_level/set_task_must/".concat(t.id,"/").concat(t.is_must),method:"PUT"})}function O(t){return Object(r.a)({url:"/user/user_level/create_task",method:"get",params:t})}function j(t){return Object(r.a)({url:"user/user_level/create",method:"get",params:t})}function _(t){return Object(r.a)({url:"user/give_level/".concat(t),method:"get"})}function v(t){return Object(r.a)({url:"user/user_group/list",method:"get",params:t})}function w(t){return Object(r.a)({url:"user/user_group/add/".concat(t),method:"get"})}function y(t){return Object(r.a)({url:"setting/update_admin",method:"PUT",data:t})}function P(t){return Object(r.a)({url:"user/set_group",method:"post",data:t})}function k(t){return Object(r.a)({url:"user/user_label",method:"get",params:t})}function S(t,e){return Object(r.a)({url:"user/user_label/add/".concat(t),method:"get",params:e})}function x(t){return Object(r.a)({url:"user/user_label_cate/all",method:"get",params:t})}function C(t){return Object(r.a)({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function M(t){return Object(r.a)({url:"user/user_label_cate/create",method:"get"})}function $(t){return Object(r.a)({url:"/user/member_batch/index",method:"get",params:t})}function E(t,e){return Object(r.a)({url:"/user/member_batch/save/".concat(t),method:"post",data:e})}function A(t,e){return Object(r.a)({url:"/user/member_batch/set_value/".concat(t),method:"get",params:e})}function D(t,e){return Object(r.a)({url:"/user/member_card/index/".concat(t),method:"get",params:e})}function U(t,e){return Object(r.a)({url:"/export/memberCard/".concat(t),method:"get",params:e})}function F(){return Object(r.a)({url:"/user/member/ship",method:"get"})}function I(t,e){return Object(r.a)({url:"/user/member_ship/save/".concat(t),method:"post",data:e})}function T(){return Object(r.a)({url:"/user/user/syncUsers",method:"get"})}function W(){return Object(r.a)({url:"/user/user/create",method:"get"})}function B(){return Object(r.a)({url:"/user/member_scan",method:"get"})}function J(t,e){return Object(r.a)({url:"user/label/".concat(t),method:"post",data:e})}function z(t){return Object(r.a)({url:"user/member_right/save/".concat(t.id),method:"post",data:t})}function q(){return Object(r.a)({url:"user/member/right",method:"get"})}function G(t){return Object(r.a)({url:"/user/member/record",method:"get",params:t})}function H(){return Object(r.a)({url:"user/member/ship_select",method:"get"})}function K(t){return Object(r.a)({url:"user/member_card/set_status",method:"get",params:t})}function L(t){return Object(r.a)({url:"user/member_ship/set_ship_status",method:"get",params:t})}function N(t,e){return Object(r.a)({url:"user/member_agreement/save/".concat(t),method:"post",data:e})}function Q(){return Object(r.a)({url:"user/member/agreement",method:"get"})}function R(t){return Object(r.a)({url:"user/give_level_time/".concat(t),method:"get"})}function V(t){return Object(r.a)({url:"user/label/".concat(t),method:"get"})}function X(t){return Object(r.a)({url:"user/save_set_label",method:"put",data:t})}function Y(t){return Object(r.a)({url:"setting/info",method:"get"})}function Z(t){return Object(r.a)({url:"user/visit_list/".concat(t.id),method:"get",params:t.datas})}function tt(t){return Object(r.a)({url:"user/spread_list/".concat(t.id),method:"get",params:t.datas})}function et(t){return Object(r.a)({url:"user/user/".concat(t.uid),method:"put",data:t})}function nt(t,e){return Object(r.a)({url:"agent/set_agent_agreement/".concat(t),method:"post",data:e})}function rt(){return Object(r.a)({url:"agent/get_agent_agreement",method:"get"})}function ut(){return Object(r.a)({url:"user/synchro/work/label",method:"get"})}function ot(t){return Object(r.a)({url:"user/user/extend_info/".concat(t),method:"get"})}function ct(t){return Object(r.a)({url:"user/batch_process",method:"post",data:t})}function at(t,e){return Object(r.a)({url:"/user/member/save/content/".concat(t),method:"post",data:e})}}}]);