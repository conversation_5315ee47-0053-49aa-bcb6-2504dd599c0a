(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-b820792a"],{"0b65":function(t,e,n){"use strict";e.a={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,n=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,n))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},2166:function(t,e,n){"use strict";n.r(e);var r=n("2f62"),a=n("0b65"),o=n("90e7");function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function c(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var i={name:"record",components:{},data:function(){return{options:a.a,timeVal:[],formData:{keyword:"",data:"",store_id:0,station_type:"",status:"",page:1,limit:20},loading:!1,columns:[{title:"ID",key:"id",width:60},{title:"门店名称",slot:"info",minWidth:150},{title:"配送平台",slot:"stationType",minWidth:90},{title:"配送订单号",key:"delivery_no",minWidth:110},{title:"配送原订单号",slot:"yOrderId",minWidth:110},{title:"配送起点",key:"from_address",minWidth:130},{title:"配送终点",key:"to_address",minWidth:130},{title:"配送状态",slot:"status",minWidth:80},{title:"配送距离",slot:"distance",minWidth:80},{title:"配送费用",key:"fee",minWidth:80},{title:"消费时间",key:"add_time",minWidth:125},{title:"备注",slot:"mark",minWidth:80},{title:"操作",slot:"action",fixed:"right",minWidth:80}],data:[],storeList:[],total:0}},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(n,!0).forEach((function(e){c(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getStoreList(),this.getList()},mounted:function(){},methods:{cancelOrder:function(t){var e=this;this.$modalForm(Object(o.j)(t.id)).then((function(){return e.getList()}))},orderReset:function(){this.formData={keyword:"",data:"",store_id:0,station_type:"",status:"",page:1,limit:20},this.timeVal=[],this.getList()},orderSearch:function(){this.formData.page=1,this.getList()},pageChange:function(t){this.formData.page=t,this.getList()},limitChange:function(t){this.formData.limit=t,this.getList()},getStoreList:function(){var t=this;Object(o.O)().then((function(e){t.storeList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getList:function(){var t=this;this.loading=!0,Object(o.k)(this.formData).then((function(e){t.loading=!1,t.data=e.data.list,t.total=e.data.count})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},onchangeTime:function(t){this.timeVal=t,this.formData.data=this.timeVal.join("-"),t[0]||(this.formData.data=""),this.orderSearch()}}},s=(n("c4b6"),n("2877")),d=Object(s.a)(i,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{staticClass:"mt15 ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[n("div",{staticClass:"new_card_pd"},[n("Form",{ref:"orderData",staticClass:"tabform",attrs:{model:t.formData,"label-width":t.labelWidth,"label-position":t.labelPosition,inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[n("FormItem",{attrs:{label:"时间选择："}},[n("DatePicker",{staticClass:"input-add",attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1),n("FormItem",{attrs:{label:"商户选择："}},[n("Select",{staticClass:"input-add",attrs:{clearable:"",filterable:""},on:{"on-change":t.orderSearch},model:{value:t.formData.store_id,callback:function(e){t.$set(t.formData,"store_id",e)},expression:"formData.store_id"}},t._l(t.storeList,(function(e){return n("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.name)+"\n                            ")])})),1)],1),n("FormItem",{attrs:{label:"配送平台："}},[n("Select",{staticClass:"input-add",attrs:{placeholder:"全部",clearable:""},on:{"on-change":t.orderSearch},model:{value:t.formData.station_type,callback:function(e){t.$set(t.formData,"station_type",e)},expression:"formData.station_type"}},[n("Option",{attrs:{value:""}},[t._v("全部")]),n("Option",{attrs:{value:"1"}},[t._v("达达")]),n("Option",{attrs:{value:"2"}},[t._v("uu跑腿")])],1)],1),n("FormItem",{attrs:{label:"配送状态："}},[n("Select",{staticClass:"input-add",attrs:{placeholder:"全部"},on:{"on-change":t.orderSearch},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}},[n("Option",{attrs:{value:""}},[t._v("全部")]),n("Option",{attrs:{value:"2"}},[t._v("待取货")]),n("Option",{attrs:{value:"3"}},[t._v("配送中")]),n("Option",{attrs:{value:"4"}},[t._v("已完成")]),n("Option",{attrs:{value:"-1"}},[t._v("已取消")]),n("Option",{attrs:{value:"9"}},[t._v("物品返回中")]),n("Option",{attrs:{value:"10"}},[t._v("物品返回完成")]),n("Option",{attrs:{value:"100"}},[t._v("骑士到店")])],1)],1),n("FormItem",{attrs:{label:"订单搜索：","label-for":"nickname"}},[n("Input",{staticClass:"input-add",attrs:{placeholder:"请输入配送订单号/原订单号"},model:{value:t.formData.keyword,callback:function(e){t.$set(t.formData,"keyword",e)},expression:"formData.keyword"}})],1),n("FormItem",[n("Button",{staticClass:"ml75",attrs:{type:"primary"},on:{click:t.orderSearch}},[t._v("查询")]),n("Button",{staticClass:"ml20",on:{click:t.orderReset}},[t._v("重置")])],1)],1)],1)]),n("Card",{staticClass:"mt15 ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Table",{ref:"table",attrs:{columns:t.columns,data:t.data,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"info",fn:function(e){var r=e.row;return[n("Tooltip",{attrs:{theme:"dark","max-width":"300",delay:600}},[n("div",{staticClass:"tabBox"},[n("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:r.storeInfo.image,expression:"row.storeInfo.image"}]})]),n("span",{staticClass:"tabBox_tit line2"},[t._v(t._s(r.storeInfo.name))])]),n("div",{attrs:{slot:"content"},slot:"content"},[n("div",[n("p",[t._v(t._s(r.storeInfo.name))])])])])]}},{key:"stationType",fn:function(e){var r=e.row;return[n("div",[t._v(t._s(1===r.station_type?"达达配送":"uu配送"))])]}},{key:"yOrderId",fn:function(e){var r=e.row;return[n("div",[t._v(t._s(r.orderInfo.order_id))])]}},{key:"status",fn:function(e){var r=e.row;return[2==r.status?n("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("待取货")]):t._e(),3==r.status?n("Tag",{attrs:{color:"blue",size:"medium"}},[t._v("配送中")]):t._e(),4==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("已完成")]):t._e(),-1==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("已取消")]):t._e(),9==r.status?n("Tag",{attrs:{color:"red",size:"medium"}},[t._v("物品返回中")]):t._e(),10==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("物品返回完成")]):t._e(),100==r.status?n("Tag",{attrs:{color:"blue",size:"medium"}},[t._v("骑士到店")]):t._e()]}},{key:"distance",fn:function(e){var n=e.row;return[t._v("\n                    "+t._s(n.distance)+"km\n                ")]}},{key:"mark",fn:function(e){var n=e.row;return[t._v("\n                    "+t._s(n.mark||"-")+"\n                ")]}},{key:"action",fn:function(e){var r=e.row;return[n("a",{on:{click:function(e){return t.cancelOrder(r)}}},[t._v("取消发单")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,current:t.formData.page,"show-elevator":"","show-total":"","page-size":t.formData.limit,"show-sizer":""},on:{"on-change":t.pageChange,"on-page-size-change":t.limitChange}})],1)],1)],1)}),[],!1,null,"192585b2",null);e.default=d.exports},88552:function(t,e,n){},"90e7":function(t,e,n){"use strict";n.d(e,"v",(function(){return a})),n.d(e,"i",(function(){return o})),n.d(e,"Fb",(function(){return u})),n.d(e,"Eb",(function(){return c})),n.d(e,"gb",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"jb",(function(){return d})),n.d(e,"T",(function(){return f})),n.d(e,"tb",(function(){return l})),n.d(e,"I",(function(){return m})),n.d(e,"sb",(function(){return h})),n.d(e,"q",(function(){return g})),n.d(e,"o",(function(){return p})),n.d(e,"p",(function(){return b})),n.d(e,"r",(function(){return O})),n.d(e,"s",(function(){return v})),n.d(e,"bb",(function(){return j})),n.d(e,"cb",(function(){return _})),n.d(e,"Z",(function(){return w})),n.d(e,"ab",(function(){return D})),n.d(e,"J",(function(){return y})),n.d(e,"C",(function(){return k})),n.d(e,"G",(function(){return T})),n.d(e,"F",(function(){return x})),n.d(e,"x",(function(){return C})),n.d(e,"H",(function(){return F})),n.d(e,"z",(function(){return P})),n.d(e,"E",(function(){return I})),n.d(e,"y",(function(){return M})),n.d(e,"w",(function(){return S})),n.d(e,"h",(function(){return L})),n.d(e,"d",(function(){return W})),n.d(e,"e",(function(){return z})),n.d(e,"Gb",(function(){return E})),n.d(e,"Hb",(function(){return Y})),n.d(e,"Ib",(function(){return G})),n.d(e,"ib",(function(){return $})),n.d(e,"ub",(function(){return B})),n.d(e,"N",(function(){return V})),n.d(e,"wb",(function(){return U})),n.d(e,"vb",(function(){return J})),n.d(e,"xb",(function(){return N})),n.d(e,"yb",(function(){return R})),n.d(e,"zb",(function(){return q})),n.d(e,"Ab",(function(){return A})),n.d(e,"Jb",(function(){return H})),n.d(e,"Kb",(function(){return K})),n.d(e,"O",(function(){return Q})),n.d(e,"f",(function(){return X})),n.d(e,"Lb",(function(){return Z})),n.d(e,"kb",(function(){return tt})),n.d(e,"lb",(function(){return et})),n.d(e,"D",(function(){return nt})),n.d(e,"A",(function(){return rt})),n.d(e,"hb",(function(){return at})),n.d(e,"mb",(function(){return ot})),n.d(e,"nb",(function(){return ut})),n.d(e,"ob",(function(){return ct})),n.d(e,"B",(function(){return it})),n.d(e,"P",(function(){return st})),n.d(e,"S",(function(){return dt})),n.d(e,"Q",(function(){return ft})),n.d(e,"R",(function(){return lt})),n.d(e,"g",(function(){return mt})),n.d(e,"u",(function(){return ht})),n.d(e,"t",(function(){return gt})),n.d(e,"db",(function(){return pt})),n.d(e,"pb",(function(){return bt})),n.d(e,"rb",(function(){return Ot})),n.d(e,"b",(function(){return vt})),n.d(e,"qb",(function(){return jt})),n.d(e,"l",(function(){return _t})),n.d(e,"a",(function(){return wt})),n.d(e,"k",(function(){return Dt})),n.d(e,"j",(function(){return yt})),n.d(e,"Bb",(function(){return kt})),n.d(e,"Cb",(function(){return Tt})),n.d(e,"Db",(function(){return xt})),n.d(e,"n",(function(){return Ct})),n.d(e,"eb",(function(){return Ft})),n.d(e,"fb",(function(){return Pt})),n.d(e,"V",(function(){return It})),n.d(e,"Y",(function(){return Mt})),n.d(e,"W",(function(){return St})),n.d(e,"U",(function(){return Lt})),n.d(e,"X",(function(){return Wt})),n.d(e,"L",(function(){return zt})),n.d(e,"K",(function(){return Et})),n.d(e,"M",(function(){return Yt})),n.d(e,"m",(function(){return Gt}));var r=n("b6bd");function a(t){return Object(r.a)({url:"setting/config/header_basics",method:"get",params:t})}function o(t,e){return Object(r.a)({url:e,method:"get",params:t})}function u(t){return Object(r.a)({url:t.url,method:"get",params:t.data})}function c(){return Object(r.a)({url:"notify/sms/temp/create",method:"get"})}function i(){return Object(r.a)({url:"serve/info",method:"get"})}function s(t){return Object(r.a)({url:"serve/captcha",method:"post",data:t})}function d(t){return Object(r.a)({url:"serve/meal_list",method:"get",params:t})}function f(t){return Object(r.a)({url:"serve/pay_meal",method:"post",data:t})}function l(){return Object(r.a)({url:"merchant/store",method:"GET"})}function m(){return Object(r.a)({url:"merchant/store/address",method:"GET"})}function h(t){return Object(r.a)({url:"merchant/store/".concat(t.id),method:"POST",data:t})}function g(t){return Object(r.a)({url:"freight/express",method:"get",params:t})}function p(){return Object(r.a)({url:"/freight/express/create",method:"get"})}function b(t){return Object(r.a)({url:"freight/express/".concat(t,"/edit"),method:"get"})}function O(t){return Object(r.a)({url:"freight/express/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function v(){return Object(r.a)({url:"freight/express/sync_express",method:"get"})}function j(t){return Object(r.a)({url:"setting/role",method:"GET",params:t})}function _(t){return Object(r.a)({url:"setting/role/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function w(t){return Object(r.a)({url:"setting/role/".concat(t.id),method:"post",data:t})}function D(t){return Object(r.a)({url:"setting/role/".concat(t,"/edit"),method:"get"})}function y(){return Object(r.a)({url:"setting/role/create",method:"get"})}function k(t){return Object(r.a)({url:"app/wechat/kefu",method:"get",params:t})}function T(t){return Object(r.a)({url:"app/wechat/kefu/create",method:"get",params:t})}function x(){return Object(r.a)({url:"app/wechat/kefu/add",method:"get"})}function C(t){return Object(r.a)({url:"app/wechat/kefu",method:"post",data:t})}function F(t){return Object(r.a)({url:"app/wechat/kefu/set_status/".concat(t.id,"/").concat(t.account_status),method:"PUT"})}function P(t){return Object(r.a)({url:"app/wechat/kefu/".concat(t,"/edit"),method:"GET"})}function I(t,e){return Object(r.a)({url:"app/wechat/kefu/record/".concat(e),method:"GET",params:t})}function M(t){return Object(r.a)({url:"app/wechat/kefu/chat_list",method:"GET",params:t})}function S(){return Object(r.a)({url:"notify/sms/is_login",method:"GET"})}function L(t){return Object(r.a)({url:"setting/city/list/".concat(t),method:"get"})}function W(t){return Object(r.a)({url:"setting/city/add/".concat(t),method:"get"})}function z(t){return Object(r.a)({url:"setting/city/".concat(t,"/edit"),method:"get"})}function E(t){return Object(r.a)({url:"setting/shipping_templates/list",method:"get",params:t})}function Y(t){return Object(r.a)({url:"setting/shipping_templates/city_list",method:"get"})}function G(t,e){return Object(r.a)({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function $(t){return Object(r.a)({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function B(){return Object(r.a)({url:"merchant/store/get_header",method:"get"})}function V(t){return Object(r.a)({url:"merchant/store",method:"get",params:t})}function U(t,e){return Object(r.a)({url:"merchant/store/set_show/".concat(t,"/").concat(e),method:"put"})}function J(t){return Object(r.a)({url:"merchant/store/get_info/".concat(t),method:"get"})}function N(t){return Object(r.a)({url:"merchant/store_staff",method:"get",params:t})}function R(){return Object(r.a)({url:"merchant/store_staff/create",method:"get"})}function q(t){return Object(r.a)({url:"merchant/store_staff/".concat(t,"/edit"),method:"get"})}function A(t,e){return Object(r.a)({url:"merchant/store_staff/set_show/".concat(t,"/").concat(e),method:"put"})}function H(t){return Object(r.a)({url:"merchant/verify_order",method:"get",params:t})}function K(t){return Object(r.a)({url:"merchant/verify/spread_info/".concat(t),method:"get"})}function Q(){return Object(r.a)({url:"merchant/store_list",method:"get"})}function X(){return Object(r.a)({url:"setting/city/clean_cache",method:"get"})}function Z(t){return Object(r.a)({url:"app/wechat/speechcraft",method:"get",params:t})}function tt(t){return Object(r.a)({url:"app/wechat/speechcraft/create",method:"get",params:t})}function et(t){return Object(r.a)({url:"app/wechat/speechcraft/".concat(t,"/edit"),method:"get"})}function nt(t){return Object(r.a)({url:"app/wechat/kefu/login/".concat(t),method:"get"})}function rt(t){return Object(r.a)({url:"app/feedback",method:"get",params:t})}function at(t){return Object(r.a)({url:"serve/sms/sign",method:"PUT",data:t})}function ot(){return Object(r.a)({url:"app/wechat/speechcraftcate",method:"get"})}function ut(){return Object(r.a)({url:"app/wechat/speechcraftcate/create",method:"get"})}function ct(t){return Object(r.a)({url:"app/wechat/speechcraftcate/".concat(t,"/edit"),method:"get"})}function it(t){return Object(r.a)({url:"app/feedback/".concat(t,"/edit"),method:"get"})}function st(t){return Object(r.a)({url:"setting/system_out/index",method:"get",params:t})}function dt(t,e){return Object(r.a)({url:"setting/system_out/set_status/".concat(t,"/").concat(e),method:"put"})}function ft(t){return Object(r.a)({url:"setting/system_out/save",method:"post",params:t})}function lt(t,e){return Object(r.a)({url:"setting/system_out/update/".concat(t),method:"post",params:e})}function mt(t){return Object(r.a)({url:"city",method:"get",params:t})}function ht(t){return Object(r.a)({url:"setting/config/edit_new_build/"+t,method:"get"})}function gt(){return Object(r.a)({url:"/setting/config/image",method:"get"})}function pt(t){return Object(r.a)({url:"setting/config/save_basics",method:"post",data:t})}function bt(t){return Object(r.a)({url:"/setting/config/storage",method:"get",params:t})}function Ot(t){return Object(r.a)({url:"/setting/config/storage/synch/".concat(t),method:"put"})}function vt(t){return Object(r.a)({url:"/setting/config/storage/create/".concat(t),method:"get"})}function jt(t){return Object(r.a)({url:"/setting/config/storage/status/".concat(t),method:"put"})}function _t(t){return Object(r.a)({url:"/setting/config/storage/domain/".concat(t),method:"get"})}function wt(t){return Object(r.a)({url:"/setting/config/storage/form/".concat(t),method:"get"})}function Dt(t){return Object(r.a)({url:"/order/delivery_order/list",method:"get",params:t})}function yt(t){return Object(r.a)({url:"/order/delivery_order/cancelForm/".concat(t),method:"get"})}function kt(t,e){return Object(r.a)({url:"/system/form/save/".concat(t),method:"post",data:e})}function Tt(t,e){return Object(r.a)({url:"/system/form/info/".concat(t),method:"get",params:e})}function xt(t){return Object(r.a)({url:"/system/form/index",method:"get",params:t})}function Ct(t,e){return Object(r.a)({url:"system/form/update_name/"+t,method:"post",data:e})}function Ft(t){return Object(r.a)({url:"setting/config/storage/save_type/".concat(t),method:"get"})}function Pt(t){return Object(r.a)({url:"/file/scan/upload",method:"post",headers:{"content-type":"multipart/form-data;"},data:t})}function It(t){return Object(r.a)({url:"/print/list",method:"get",params:t})}function Mt(t){return Object(r.a)({url:"/print/set_status/".concat(t.id,"/").concat(t.status),method:"get"})}function St(t,e){return Object(r.a)({url:"/print/save/".concat(t),method:"post",data:e})}function Lt(t,e){return Object(r.a)({url:"/print/content/".concat(t),method:"get",params:e})}function Wt(t,e){return Object(r.a)({url:"/print/save_content/".concat(t),method:"post",data:e})}function zt(t){return Object(r.a)({url:"/merchant/staff/list",method:"get",params:t})}function Et(t,e){return Object(r.a)({url:"/merchant/staff/customer/".concat(t),method:"get",params:e})}function Yt(t,e){return Object(r.a)({url:"/merchant/staff/performance/".concat(t),method:"get",params:e})}function Gt(t){return Object(r.a)({url:"/export/staffListExport",method:"get",params:t})}},c4b6:function(t,e,n){"use strict";var r=n("88552");n.n(r).a}}]);