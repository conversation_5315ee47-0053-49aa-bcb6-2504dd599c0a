(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-bf1ad5fe"],{"62c8":function(t,e,i){"use strict";var a=i("71ce");i.n(a).a},"71ce":function(t,e,i){},b621:function(t,e,i){"use strict";i.r(e);var a=i("b0e7"),o=i("e449"),s=i("c4ad"),r=i("dd2e"),n=i("a069"),l=i("c24f");function c(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?c(i,!0).forEach((function(e){d(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):c(i).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function d(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var m={name:"setupUser",components:{uploadPictures:a.a,couponList:o.a,goodsList:s.default,WangEditor:n.a,information:r.a},props:{},data:function(){return{tabVal:"basic",paySwitch:1,phoneSwitch:1,indexCoupon:0,val:"",formActive:{activeInput:0},basicsForm:{},selectArr:[],value:"",formItem:{info:"",format:"",tip:"",single:"",singlearr:[]},activityShow:!1,isChoice:"单选",modalPic:!1,loading:!1,addModel:!1,inputShow:!1,modals:!1,authorizedPicture:"",ids:[],avatar:{},loginForm:{store_user_agreement:0,newcomer_status:"1",store_user_mobile:"",newcomer_limit_status:"",newcomer_limit_time:"",register_integral_status:"",register_give_integral:"",register_money_status:"",register_give_money:"",register_coupon_status:"",register_give_coupon:[],first_order_status:"",first_order_discount:"",first_order_discount_limit:"",register_price_status:"",product:[],newcomer_agreement:"",register_notice:""},newcomer_agreement:"",product_list:[],vipForm:{member_func_status:0,sign_give_exp:"",order_give_exp:"",invite_user_exp:"",level_activate_status:1,level_extend_info:[],level_integral_status:1,level_give_integral:8,level_money_status:1,level_give_money:15,level_coupon_status:1,level_give_coupon:[]},belongForm:{belong_store_salesman:1,belong_store_svip:0,belong_store_order:0,belong_store_spread:0,belong_store_unbind_status:1,belong_store_change_svip:0},isShow:!1,formatList:[{value:"text",label:"文本"},{value:"num",label:"数字"},{value:"date",label:"日期"},{value:"radio",label:"单选项"},{value:"id",label:"身份证"},{value:"mail",label:"邮件"},{value:"phone",label:"手机号"},{value:"address",label:"地址"}],gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},columns:[{title:"",slot:"drag",width:50},{title:"信息",key:"info",width:120},{title:"使用",slot:"use",width:70},{title:"必填",slot:"required",width:70},{title:"用户端展示",slot:"user_show",minWidth:70},{title:"信息格式",key:"label",minWidth:120},{title:"提示信息",key:"tip",minWidth:120},{title:"操作",slot:"action",minWidth:80}],columns3:[{title:"信息",key:"info",width:120},{title:"必填",slot:"required",width:70},{title:"信息格式",key:"label",minWidth:120},{title:"提示信息",key:"tip",minWidth:120},{title:"操作",slot:"action",minWidth:80}],listOne:[],listVip:[],promotionsData:[{threshold:0,give_integral:0,checkIntegral:!1,checkCoupon:!1,checkGoods:!1,giveProducts:[],giveCoupon:[]}],tableData:[],columns1:[{title:"优惠券名称",key:"title",minWidth:150},{title:"类型",slot:"coupon_type",minWidth:80},{title:"面值",slot:"coupon_price",minWidth:100},{title:"最低消费额",key:"use_min_price",minWidth:100},{title:"操作",slot:"status",align:"center",minWidth:80}],ruleValidate:{info:[{required:!0,message:"信息名称不能为空",trigger:"blur"}],format:[{required:!0,message:"信息格式不能为空",trigger:"blur"}],tip:[{required:!0,message:"信息文案不能为空",trigger:"blur"}],singlearr:[{required:!0,validator:function(t,e,i){e.length<2?i(new Error("单选项最少输入2个")):i()},type:"array",trigger:"blur"}]},ruleActive:{activeInput:[{required:!0,validator:function(t,e,i){""===e||null==e||e<0?i(new Error("活动价不能为空")):i()},trigger:"blur"}]},couponType:0,vipCopon:[]}},computed:{},created:function(){"register"==this.$route.query.type?this.tabVal="register":"level"==this.$route.query.type?this.tabVal="level":this.tabVal="basic",this.settingUser()},mounted:function(){},methods:{tapCheckbox:function(t){},informationTap:function(){this.$refs.information.isShow=!0},onDragDrop:function(t,e){t=parseInt(t),e=parseInt(e);var i=this.listOne[t];if(t<e){for(var a=t+1;a<=e;a++)this.listOne.splice(a-1,1,this.listOne[a]);this.listOne.splice(e,1,i)}if(t>e){for(a=t;a>e;a--)this.listOne.splice(a,1,this.listOne[a-1]);this.listOne.splice(e,1,i)}var o=1;this.listOne.forEach((function(t){t.sort=o,o++}))},settingUser:function(){var t=this;Object(l.N)(this.tabVal).then((function(e){"basic"===t.tabVal&&(t.authorizedPicture=e.data.h5_avatar,t.listOne=e.data.user_extend_info),"register"===t.tabVal&&(t.loginForm=e.data,t.promotionsData[0].giveCoupon=e.data.register_give_coupon,t.tableData=function t(e){return e.map((function(e){return u({},e,{ativity_price:e.price,id:e.product_id,attrValue:e.attrValue?t(e.attrValue):[]})}))}(e.data.product)),"level"===t.tabVal&&(t.vipForm=e.data,t.vipCopon=e.data.level_give_coupon,e.data.level_extend_info.forEach((function(t){1==t.required||1==t.required?t.required=!0:t.required=!1})),t.listVip=e.data.level_extend_info),"belong"===t.tabVal&&(t.belongForm=e.data)}))},selectAll:function(t){this.selectArr=t.records},activityShowFn:function(){0===this.selectArr.length?this.$Message.error("请先选择设置活动价的商品！"):this.activityShow=!0},cancel:function(t){this.activityShow=!1,this.$refs[t].resetFields()},ok:function(t){var e=this;this.$refs[t].validate((function(i){i&&(e.selectArr.forEach((function(t){t.ativity_price=e.formActive.activeInput})),e.activityShow=!1,e.$refs[t].resetFields())}))},delAll:function(){var t=this;0===this.selectArr.length?this.$Message.error("请先选择删除的商品！"):this.$Modal.confirm({title:"删除确认",content:"您确认要删除这些商品？",onOk:function(){t.selectArr.forEach((function(e){t.tableData.forEach((function(i,a){e.id==i.id?t.tableData.splice(a,1):i.attrValue.forEach((function(o,s){e.id==o.id&&(1==i.attrValue.length?t.tableData.splice(a,1):i.attrValue.splice(s,1))}))}))}))}})},tabChange:function(){this.settingUser()},getPic:function(t){this.authorizedPicture=t.att_dir,this.modalPic=!1},modalPicTap:function(){this.modalPic=!0},cancelSubmit:function(){this.formItem={info:"",format:"",tip:"",single:"",singlearr:[]},this.addModel=!1,this.$refs.formValidate.resetFields()},addSubmit:function(){var t=this;this.$refs.formValidate.validate((function(e){var i=u({},t.formItem,{required:0,use:0,user_show:0,label:""});switch(i.format){case"text":i.label="文本";break;case"num":i.label="数字";break;case"date":i.label="日期";break;case"radio":i.label="单选项";break;case"id":i.label="身份证";break;case"mail":i.label="邮件";break;case"phone":i.label="手机号";break;case"address":i.label="地址"}var a=[];t.listOne.forEach((function(t){a.push(t.info)})),e&&(-1==a.indexOf(i.info)?(t.listOne.push(i),t.cancelSubmit()):t.$Message.error("该信息已经添加过"))}))},delInfo:function(t){this.listOne.splice(t,1)},delVip:function(t,e){this.listVip.splice(e,1)},addlabel:function(){this.formItem.single&&(-1===this.formItem.singlearr.indexOf(this.formItem.single)&&this.formItem.singlearr.push(this.formItem.single),this.formItem.single="")},handleSubmit:function(t){var e=this;switch(t){case"basic":var i={h5_avatar:this.authorizedPicture,user_extend_info:this.listOne};Object(l.M)(t,i).then((function(t){e.$Message.success(t.msg)}));break;case"register":this.product_list=[],this.tableData.forEach((function(t){var i={product_id:t.id,price:t.ativity_price,attr:[]};t.attrValue.length&&t.attrValue.forEach((function(t){var e={unique:t.unique,price:t.ativity_price};i.attr.push(e)})),e.product_list.push(i)}));var a=this.promotionsData[0].giveCoupon.map((function(t){return t.id}));this.loginForm.register_give_coupon=Array.from(new Set(a)),this.loginForm.product=this.product_list,this.loginForm.newcomer_agreement=this.newcomer_agreement,Object(l.M)(t,this.loginForm).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}));break;case"level":var o=this.vipCopon.map((function(t){return t.id}));this.vipForm.level_give_coupon=Array.from(new Set(o)),this.vipForm.level_extend_info=this.listVip,Object(l.M)(t,this.vipForm).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}));break;case"belong":Object(l.M)(t,this.belongForm).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))}},addCoupon:function(t){this.indexCoupon=t,this.$refs.couponTemplates.isTemplate=!0,this.$refs.couponTemplates.tableList()},handleClose:function(t,e){var i=this.formItem.singlearr.indexOf(e);this.formItem.singlearr.splice(i,1)},getCouponList:function(t){var e=this.indexCoupon;this.$refs.couponTemplates.isTemplate=!1,t.forEach((function(t){t.limit_num=0,t.indexCoupon=e}));var i=this.promotionsData[e].giveCoupon.concat(t),a=this.unique(i);"register"===this.tabVal?this.promotionsData[e].giveCoupon=a:this.vipCopon=a},delCoupon:function(t,e){"level"===this.tabVal&&this.vipCopon.splice(t,1),this.promotionsData[e].giveCoupon.splice(t,1)},addGoods:function(t){this.modals=!0},inputChange:function(t){t.attrValue.length>0&&t.attrValue.forEach((function(e){e.ativity_price=t.ativity_price}))},del:function(t){var e=this;this.tableData.forEach((function(i,a){if(t.id==i.id)return e.tableData.splice(a,1);i.attrValue.forEach((function(o,s){if(t.id==o.id)return 1==i.attrValue.length?e.tableData.splice(a,1):i.attrValue.splice(s,1)}))}))},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},getProductId:function(t){this.modals=!1;var e=this.tableData.concat(t),i=this.unique(e);i.forEach((function(t){t.attrValue.forEach((function(e){e.cate_name=t.cate_name,e.store_label=t.store_label}))})),this.tableData=function t(e){return e.map((function(e){return u({},e,{ativity_price:"",attrValue:e.attrValue?t(e.attrValue):[]})}))}(i)},getInfoList:function(t){var e=this.listVip.concat(t),i=this.uniqueVip(e);i.forEach((function(t){1==t.required||1==t.required?t.required=!0:t.required=!1})),this.listVip=i,this.$refs.information.isShow=!1},uniqueVip:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.info)&&e.set(t.info,1)}))},getEditorContent:function(t){this.newcomer_agreement=t}}},p=(i("62c8"),i("2877")),f=Object(p.a)(m,(function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[i("Tabs",{on:{"on-click":t.tabChange},model:{value:t.tabVal,callback:function(e){t.tabVal=e},expression:"tabVal"}},[i("TabPane",{attrs:{label:"基础信息",name:"basic"}},[i("Form",{attrs:{model:t.basicsForm,"label-width":130}},[i("Row",{attrs:{gutter:24,type:"flex"}},[i("Col",{attrs:{span:"24"}},[i("div",{staticClass:"basics"},[t._v("用户设置")])]),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"用户默认头像："}},[t.authorizedPicture?i("div",{staticClass:"uploadPictrue",on:{click:function(e){return t.modalPicTap("单选")}}},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.authorizedPicture,expression:"authorizedPicture"}]})]):i("div",{staticClass:"uploadPictrue",on:{click:function(e){return t.modalPicTap("单选")}}},[i("span",{staticClass:"iconfont iconshangpinshuliang-jia"})]),i("div",{staticClass:"upload-text"},[t._v("建议尺寸：120*120px")])])],1),i("Col",{staticClass:"mt10",attrs:{span:"14"}},[i("FormItem",{attrs:{label:"用户信息设置："}},[i("Table",{ref:"table",staticClass:"mt25 goods",attrs:{data:t.listOne,columns:t.columns,"highlight-row":"",draggable:!0},on:{"on-drag-drop":t.onDragDrop},scopedSlots:t._u([{key:"drag",fn:function(t){return t.row,t.index,[i("div",{staticClass:"iconfont icondrag"})]}},{key:"use",fn:function(e){e.row;var a=e.index;return[i("Checkbox",{attrs:{"true-value":1,"false-value":0},model:{value:t.listOne[a].use,callback:function(e){t.$set(t.listOne[a],"use",e)},expression:"listOne[index].use"}})]}},{key:"required",fn:function(e){e.row;var a=e.index;return[i("Checkbox",{attrs:{disabled:0==t.listOne[a].use,"true-value":1,"false-value":0},model:{value:t.listOne[a].required,callback:function(e){t.$set(t.listOne[a],"required",e)},expression:"listOne[index].required"}})]}},{key:"user_show",fn:function(e){e.row;var a=e.index;return[i("Checkbox",{attrs:{disabled:0==t.listOne[a].use,"true-value":1,"false-value":0},model:{value:t.listOne[a].user_show,callback:function(e){t.$set(t.listOne[a],"user_show",e)},expression:"listOne[index].user_show"}})]}},{key:"action",fn:function(e){e.row;var a=e.index;return[t.listOne[a].param?t._e():i("a",{on:{click:function(e){return t.delInfo(a)}}},[t._v("删除")])]}}])}),i("div",{staticClass:"upload-text goods"},[t._v("\n                    开启使用后，后台添加用户时可填写此信息；开启必填后，后台添加用户时此信息必须填写；开启用户端展示后，在商城用户个人信息中展示\n                  ")]),i("div",{staticClass:"addInfo",on:{click:function(e){t.addModel=!0}}},[t._v("新增信息")]),i("div",{staticClass:"subBtn mt20",on:{click:function(e){return t.handleSubmit("basic")}}},[t._v("\n                    保存\n                  ")])],1)],1)],1)],1)],1),i("TabPane",{attrs:{label:"登录注册",name:"register"}},[t.loginForm.register_notice?i("Alert",{attrs:{type:"warning","show-icon":""}},[t._v(t._s(t.loginForm.register_notice))]):t._e(),i("Form",{attrs:{model:t.loginForm,"label-width":130}},[i("Row",{attrs:{gutter:24,type:"flex"}},[i("Col",{attrs:{span:"24"}},[i("div",{staticClass:"basics"},[t._v("登录设置")])]),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"强制手机号绑定："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.loginForm.store_user_mobile,callback:function(e){t.$set(t.loginForm,"store_user_mobile",e)},expression:"loginForm.store_user_mobile"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("商城登录时强制手机号登陆/绑定")])],1)],1),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"用户协议："}},[i("RadioGroup",{model:{value:t.loginForm.store_user_agreement,callback:function(e){t.$set(t.loginForm,"store_user_agreement",e)},expression:"loginForm.store_user_agreement"}},[i("Radio",{attrs:{label:"0"}},[i("span",[t._v("自动同意")])]),i("Radio",{attrs:{label:"1"}},[i("span",[t._v("手动同意")])])],1),i("div",{staticClass:"upload-text"},[t._v("商城登录时用户协议选定")])],1)],1),i("Col",[i("FormItem",[i("div",{staticClass:"subBtn",staticStyle:{"margin-top":"0px"},on:{click:function(e){return t.handleSubmit("register")}}},[t._v("\n                    保存\n                  ")])])],1)],1)],1)],1),i("TabPane",{attrs:{label:"归属设置",name:"belong"}},[i("Form",{attrs:{model:t.belongForm,"label-width":130}},[i("Row",{attrs:{gutter:24,type:"flex"}},[i("Col",{attrs:{span:"24"}},[i("div",{staticClass:"basics"},[t._v("归属规则")])]),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"专属店员对应门店："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_salesman,callback:function(e){t.$set(t.belongForm,"belong_store_salesman",e)},expression:"belongForm.belong_store_salesman"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n                绑定专属店员后，将专属店员的门店作为用户的归属门店\n              ")])],1),i("FormItem",{attrs:{label:"开通付费会员门店："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_svip,callback:function(e){t.$set(t.belongForm,"belong_store_svip",e)},expression:"belongForm.belong_store_svip"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n\t\t\t\t    用户开通付费会员卡的门店，设置该用户的归属门店\n\t\t\t\t  ")])],1),i("FormItem",{attrs:{label:"下单门店："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_order,callback:function(e){t.$set(t.belongForm,"belong_store_order",e)},expression:"belongForm.belong_store_order"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n\t\t\t\t    用户下单门店，设为该用户的归属门店\n\t\t\t\t  ")])],1),i("FormItem",{attrs:{label:"推广上级对应门店："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_spread,callback:function(e){t.$set(t.belongForm,"belong_store_spread",e)},expression:"belongForm.belong_store_spread"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n\t\t\t\t    用户绑定上级分销员后，将上级分销员的归属门店设为该用户的归属门店\n\t\t\t\t  ")])],1)],1),i("Col",{attrs:{span:"24"}},[i("div",{staticClass:"basics"},[t._v("变更归属")])]),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",{attrs:{label:"绑定/换绑专属店员："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_unbind_status,callback:function(e){t.$set(t.belongForm,"belong_store_unbind_status",e)},expression:"belongForm.belong_store_unbind_status"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n\t\t\t\t      客户绑定/换绑专属店员后，该用户的归属门店将变更为专属店员所在门店\n\t\t\t\t    ")])],1),i("FormItem",{attrs:{label:"开通付费会员门店："}},[i("i-switch",{attrs:{size:"large","true-value":1,"false-value":0},model:{value:t.belongForm.belong_store_change_svip,callback:function(e){t.$set(t.belongForm,"belong_store_change_svip",e)},expression:"belongForm.belong_store_change_svip"}},[i("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),i("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),i("div",{staticClass:"upload-text"},[t._v("\n\t\t\t\t      用户开通付费会员卡，归属门店将变更为开通付费会员卡的门店\n\t\t\t\t    ")])],1)],1),i("Col",{staticClass:"mt10",attrs:{span:"24"}},[i("FormItem",[i("div",{staticClass:"subBtn",staticStyle:{"margin-top":"0px"},on:{click:function(e){return t.handleSubmit("belong")}}},[t._v("\n\t\t            保存\n\t\t          ")])])],1)],1)],1)],1)],1)],1),i("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"上传用户图片","mask-closable":!1,"z-index":1},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?i("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic}}):t._e()],1),i("Modal",{attrs:{title:"新增信息","class-name":"vertical-center-modal",scrollable:""},on:{"on-cancel":t.cancelSubmit},model:{value:t.addModel,callback:function(e){t.addModel=e},expression:"addModel"}},[i("Form",{ref:"formValidate",attrs:{model:t.formItem,rules:t.ruleValidate,"label-width":90}},[i("Row",[i("Col",[i("FormItem",{attrs:{label:"信息名称：",prop:"info"}},[i("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入信息名称"},model:{value:t.formItem.info,callback:function(e){t.$set(t.formItem,"info",e)},expression:"formItem.info"}})],1)],1),i("Col",[i("FormItem",{attrs:{label:"信息格式 ：",prop:"format"}},[i("Select",{staticStyle:{width:"300px"},model:{value:t.formItem.format,callback:function(e){t.$set(t.formItem,"format",e)},expression:"formItem.format"}},t._l(t.formatList,(function(e){return i("Option",{key:e.value,attrs:{value:e.value}},[t._v("\n                  "+t._s(e.label)+"\n                ")])})),1)],1)],1),i("Col",["radio"===t.formItem.format?i("FormItem",{attrs:{label:"单选项 ：",prop:"singlearr"}},[i("div",{staticClass:"arrbox"},[t._l(t.formItem.singlearr,(function(e,a){return i("Tag",{key:a,attrs:{name:e,closable:!0},on:{"on-close":t.handleClose}},[t._v("\n                  "+t._s(e)+"\n                ")])})),i("input",{directives:[{name:"model",rawName:"v-model",value:t.formItem.single,expression:"formItem.single"}],staticClass:"arrbox_ip percentage9",attrs:{placeholder:"请输入选项，回车确认"},domProps:{value:t.formItem.single},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.addlabel(e)},input:function(e){e.target.composing||t.$set(t.formItem,"single",e.target.value)}}})],2)]):t._e()],1),i("Col",[i("FormItem",{attrs:{label:"提示文案：",prop:"tip"}},[i("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入提示文案"},model:{value:t.formItem.tip,callback:function(e){t.$set(t.formItem,"tip",e)},expression:"formItem.tip"}})],1)],1)],1)],1),i("div",{staticClass:"acea-row row-right",attrs:{slot:"footer"},slot:"footer"},[i("Button",{on:{click:t.cancelSubmit}},[t._v("取消")]),i("Button",{attrs:{type:"primary"},on:{click:t.addSubmit}},[t._v("提交")])],1)],1),i("coupon-list",{ref:"couponTemplates",attrs:{discount:!0},on:{getCouponList:t.getCouponList}}),i("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?i("goods-list",{ref:"goodslist",attrs:{ischeckbox:!0,isdiy:!0},on:{getProductId:t.getProductId}}):t._e()],1),i("information",{ref:"information",attrs:{listOne:t.listOne},on:{getInfoList:t.getInfoList}}),i("Modal",{staticClass:"paymentFooter",attrs:{title:"设置",width:"600",closable:!1,"mask-closable":!1,"footer-hide":""},model:{value:t.activityShow,callback:function(e){t.activityShow=e},expression:"activityShow"}},[i("Form",{ref:"activityShow",attrs:{model:t.formActive,rules:t.ruleActive,"label-width":100}},[i("FormItem",{attrs:{label:"设置活动价：",prop:"activeInput"}},[i("InputNumber",{staticClass:"inputw",attrs:{placeholder:"请输入活动价格",min:0},model:{value:t.formActive.activeInput,callback:function(e){t.$set(t.formActive,"activeInput",e)},expression:"formActive.activeInput"}})],1),i("div",{staticClass:"acea-row row-right"},[i("Button",{on:{click:function(e){return t.cancel("activityShow")}}},[t._v("取消")]),i("Button",{staticClass:"ml15 mr5",attrs:{type:"primary"},on:{click:function(e){return t.ok("activityShow")}}},[t._v("提交")])],1)],1)],1)],1)}),[],!1,null,"4c54853d",null);e.default=f.exports}}]);