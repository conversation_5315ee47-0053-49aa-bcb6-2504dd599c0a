(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-ad5bcb3e"],{"1f7f":function(t,e,n){},"9b41":function(t,e,n){"use strict";n.d(e,"V",(function(){return c})),n.d(e,"P",(function(){return o})),n.d(e,"U",(function(){return a})),n.d(e,"y",(function(){return i})),n.d(e,"v",(function(){return u})),n.d(e,"x",(function(){return s})),n.d(e,"w",(function(){return l})),n.d(e,"z",(function(){return d})),n.d(e,"s",(function(){return h})),n.d(e,"k",(function(){return f})),n.d(e,"j",(function(){return m})),n.d(e,"E",(function(){return p})),n.d(e,"u",(function(){return b})),n.d(e,"F",(function(){return k})),n.d(e,"Q",(function(){return g})),n.d(e,"G",(function(){return w})),n.d(e,"J",(function(){return O})),n.d(e,"W",(function(){return j})),n.d(e,"h",(function(){return _})),n.d(e,"g",(function(){return v})),n.d(e,"t",(function(){return C})),n.d(e,"m",(function(){return x})),n.d(e,"c",(function(){return L})),n.d(e,"b",(function(){return S})),n.d(e,"a",(function(){return D})),n.d(e,"i",(function(){return y})),n.d(e,"d",(function(){return T})),n.d(e,"D",(function(){return $})),n.d(e,"H",(function(){return U})),n.d(e,"I",(function(){return N})),n.d(e,"f",(function(){return E})),n.d(e,"N",(function(){return A})),n.d(e,"M",(function(){return J})),n.d(e,"l",(function(){return M})),n.d(e,"T",(function(){return P})),n.d(e,"e",(function(){return I})),n.d(e,"L",(function(){return B})),n.d(e,"K",(function(){return H})),n.d(e,"O",(function(){return z})),n.d(e,"B",(function(){return F})),n.d(e,"r",(function(){return V})),n.d(e,"q",(function(){return q})),n.d(e,"n",(function(){return G})),n.d(e,"o",(function(){return K})),n.d(e,"p",(function(){return Q})),n.d(e,"A",(function(){return R})),n.d(e,"R",(function(){return W})),n.d(e,"S",(function(){return X})),n.d(e,"C",(function(){return Y}));var r=n("b6bd");function c(){return Object(r.a)({url:"work/tree",method:"get"})}function o(){return Object(r.a)({url:"work/label",method:"get"})}function a(){return Object(r.a)({url:"work/synchMember",method:"post"})}function i(){return Object(r.a)({url:"work/channel/cate",method:"get"})}function u(){return Object(r.a)({url:"work/channel/cate/create",method:"get"})}function s(t){return Object(r.a)({url:"/work/channel/cate/".concat(t,"/edit"),method:"get"})}function l(t){return Object(r.a)({url:"/work/channel/cate/".concat(t),method:"delete"})}function d(t){return Object(r.a)({url:"work/channel/code",method:"get",params:t})}function h(t){return Object(r.a)({url:"work/channel/code",method:"post",data:t})}function f(t){return Object(r.a)({url:"work/channel/code/".concat(t),method:"get"})}function m(t){return Object(r.a)({url:"work/channel/code/client",method:"get",params:t})}function p(t,e){return Object(r.a)({url:"work/channel/code/".concat(t),method:"put",data:e})}function b(t){return Object(r.a)({url:"work/channel/code/bactch/cate",method:"post",data:t})}function k(){return Object(r.a)({url:"work/department",method:"get"})}function g(t){return Object(r.a)({url:"work/member",method:"get",params:t})}function w(t){return Object(r.a)({url:"work/group_chat",method:"get",params:t})}function O(){return Object(r.a)({url:"work/group_chat/synch",method:"post"})}function j(t){return Object(r.a)({url:"work/welcome",method:"post",data:t})}function _(t){return Object(r.a)({url:"work/welcome",method:"get",params:t})}function v(t){return Object(r.a)({url:"work/welcome/".concat(t),method:"get"})}function C(t,e){return Object(r.a)({url:"work/welcome/".concat(t),method:"put",data:e})}function x(t){return Object(r.a)({url:"work/group_chat_auth",method:"post",data:t})}function L(t){return Object(r.a)({url:"work/group_chat_auth",method:"get",params:t})}function S(t){return Object(r.a)({url:"work/group_chat_auth/".concat(t),method:"get"})}function D(t,e){return Object(r.a)({url:"work/group_chat_auth/".concat(t),method:"put",data:e})}function y(t){return Object(r.a)({url:"work/client",method:"get",params:t})}function T(t){return Object(r.a)({url:"work/group_chat/member",method:"get",params:t})}function $(){return Object(r.a)({url:"work/client/synch",method:"get"})}function U(t){return Object(r.a)({url:"work/group_chat/statistics",method:"get",params:t})}function N(t){return Object(r.a)({url:"work/group_chat/statisticsList",method:"get",params:t})}function E(t){return Object(r.a)({url:"work/group_template",method:"get",params:t})}function A(t){return Object(r.a)({url:"work/group_template",method:"post",data:t})}function J(t){return Object(r.a)({url:"work/group_template/".concat(t),method:"get"})}function M(t){return Object(r.a)({url:"work/moment",method:"get",params:t})}function P(t){return Object(r.a)({url:"/work/moment",method:"post",data:t})}function I(t){return Object(r.a)({url:"work/group_template_chat",method:"get",params:t})}function B(t){return Object(r.a)({url:"work/group_template_chat",method:"post",data:t})}function H(t){return Object(r.a)({url:"work/group_template_chat/".concat(t),method:"get"})}function z(t){return Object(r.a)({url:"work/group_template/sendMessage",method:"post",data:t})}function F(t){return Object(r.a)({url:"work/client/count",method:"post",data:t})}function V(t,e){return Object(r.a)({url:"work/group_template/memberList/".concat(t),method:"get",params:e})}function q(t,e){return Object(r.a)({url:"work/group_template/clientList/".concat(t),method:"get",params:e})}function G(t,e){return Object(r.a)({url:"work/group_template_chat/groupChatList/".concat(t),method:"get",params:e})}function K(t,e){return Object(r.a)({url:"work/group_template_chat/groupChatOwnerList/".concat(t),method:"get",params:e})}function Q(t){return Object(r.a)({url:"work/group_template_chat/getOwnerChatList",method:"get",params:t})}function R(t){return Object(r.a)({url:"work/client/batchLabel",method:"post",data:t})}function W(t){return Object(r.a)({url:"work/moment/".concat(t),method:"get"})}function X(t){return Object(r.a)({url:"work/moment_list",method:"get",params:t})}function Y(t,e){return Object(r.a)({url:"work/client/".concat(t),method:"put",data:e})}},c297:function(t,e,n){"use strict";var r=n("9b41"),c={name:"Department",props:{isEdit:{type:Number,default:0},activeDepartment:{type:Object,default:function(){return null}},userList:{type:Array,default:function(){return[]}},isAppoint:{type:Boolean,default:!1},appointList:{type:Array,default:function(){return[]}}},data:function(){return{dialogVisible:!1,treeData:[],selectList:{},isShow:!1,memberStatus:!1,checkUserList:[],scrollH:0,filterText:"",openType:"",defaultProps:{children:"children",label:"name"}}},watch:{userList:{handler:function(t,e){this.checkUserList=JSON.parse(JSON.stringify(t))},deep:!0},activeDepartment:{handler:function(t,e){var n=this;if(t){this.isShow=!1;var r=[];!function e(n){r=n.map((function(n,r,c){return n.children?(t.hasOwnProperty(n.id)?n.isCheck=!0:n.isCheck=!1,e(n.children)):t.hasOwnProperty(n.id)?n.isCheck=!0:n.isCheck=!1,c}))}(n.treeData),this.$nextTick((function(){n.$set(n,"treeData",r[0]),n.isShow=!0,n.selectList=t}))}},deep:!0},filterText:function(t){this.$refs.tree.filter(t)},isAppoint:{handler:function(t){},deep:!0}},mounted:function(){this.getTreeData()},methods:{getTreeData:function(){var t=this;Object(r.V)().then((function(e){t.isShow=!0;var n={department_id:-1,member:[],member_count:0,name:"",parentid:1};n.member=e.data[0].member,e.data[0].children.unshift(n),e.data[0].member=[],t.treeData=e.data}))},handleClick:function(t,e){this.isShow=!1,this.$set(t,"isCheck",!0),this.isShow=!0,this.$set(this.selectList,t.id,t)},append:function(t,e){this.$Message.warning("只允许添加成员")},handleDelete:function(t){var e=this,n=[];this.isShow=!1,delete this.selectList[t.id],function e(r){n=r.map((function(n,r,c){return n.children?(n.id===t.id&&(n.isCheck=!1),e(n.children)):n.id===t.id&&(n.isCheck=!1),c}))}(e.treeData),this.$nextTick((function(){e.$set(e,"treeData",n[0]),e.isShow=!0}))},handleConfirm:function(){var t=this.checkUserList;this.$emit("changeMastart",t,this.openType),this.checkUserList=[]},reseatData:function(){var t=this;this.isShow=!1;var e=[];!function t(n){e=n.map((function(e,n,r){return e.isCheck=!1,e.children?(e.user.forEach((function(t){t.isCheck=!1})),t(e.children)):e.user.forEach((function(t){t.isCheck=!1})),r}))}(t.treeData),this.$nextTick((function(){t.checkUserList=JSON.parse(JSON.stringify(t.userList)),t.$set(t,"treeData",e[0]),t.isShow=!0}))},scroll:function(t){this.scrollH=t.target.scrollTop},checkUser:function(t){var e=this;if(0===this.checkUserList.length)this.$set(t,"isCheck",!0),this.checkUserList.push(t);else{if(this.checkUserList.some((function(e,n){return e.userid===t.userid})))return this.$Message.warning("该人员已选择");this.$set(t,"isCheck",!0),this.checkUserList.push(t)}this.$nextTick((function(){e.isShow=!0,e.$nextTick((function(){e.$refs.treeScoll.scroll(0,e.scrollH)}))}))},handleDeleteUser:function(t,e){var n=this;this.checkUserList.splice(e,1),n.treeData.forEach((function(e,n,r){e.member.forEach((function(e){e.id===t.id&&(e.isCheck=!1)}))})),this.$nextTick((function(){n.isShow=!0}))},filterNode:function(t,e,n){return!t||-1!==e.name.indexOf(t)||this.checkBelongToChooseNode(t,e,n)},checkBelongToChooseNode:function(t,e,n){var r=n.level;if(1===r)return!1;var c=n.parent;if(n.data.member.length>0)var o=n.data.member.map((function(e){return-1!==e.name.indexOf(t)}));if(void 0===o)return!1;for(var a=0;a<o.length;a++)if(!0===o[a])return!0;for(var i=0;i<r-1;){if(-1!==c.data.name.indexOf(t))return!0;c=c.parent,i++}return!1},getItemTree:function(t){var e=this;return t&&t.forEach((function(t,n){void 0!=t.user&&t.user.length>0&&t.user.forEach((function(t){t.label=t.card.name,t.children=t})),t.children&&t.children.length>0&&e.getItemTree(t.children)})),t},searchData:function(t){this.$refs.tree.filter(t)},handleConcel:function(){}}},o=(n("cbab"),n("2877")),a=Object(o.a)(c,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex"},[n("Modal",{attrs:{width:"680px",title:"选择成员","mask-closable":!1,"z-index":9},on:{"on-ok":t.handleConfirm,"on-cancel":t.handleConcel},model:{value:t.memberStatus,callback:function(e){t.memberStatus=e},expression:"memberStatus"}},[t.isShow?n("div",{staticClass:"container"},[n("div",{staticClass:"containerCon"},[n("div",{staticClass:"search-p"},[n("Input",{staticClass:"input-with-select",attrs:{placeholder:"搜索成员",search:""},on:{"on-search":t.searchData},model:{value:t.filterText,callback:function(e){t.filterText=e},expression:"filterText"}})],1),n("div",{ref:"treeScoll",staticClass:"tree-box",on:{scroll:t.scroll}},[t.isAppoint?t._e():n("el-tree",{ref:"tree",attrs:{data:t.treeData,props:t.defaultProps,"highlight-current":!0,"default-expand-all":!0,"expand-on-click-node":!1,"filter-node-method":t.filterNode},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.node,c=e.data;return n("div",{staticClass:"custom-tree-node",class:r.label?"":"ons"},[r.label?n("span",{staticClass:"flex-box",on:{click:function(){return t.append(r,c)}}},[n("Icon",{staticClass:"tree-icon",attrs:{type:"ios-folder",size:"15",color:"#FFCA28"}}),t._v("\n                "+t._s(r.label)+"\n                ")],1):t._e(),n("div",[c.member.length?n("div",{staticClass:"user-list"},t._l(c.member,(function(e,r){return n("div",{key:r,staticClass:"user-item",on:{click:function(n){return n.stopPropagation(),t.checkUser(e)}}},[""!==e.avatar?n("img",{attrs:{src:e.avatar,alt:""}}):t._e(),t._v("\n                    "+t._s(e.name)+"\n                  ")])})),0):t._e()])])}}],null,!1,1447217787)})],1)]),n("div",{staticClass:"select-box"},[n("div",{staticClass:"title"},[t._v("已选择成员")]),n("div",{staticClass:"list-box"},t._l(t.checkUserList,(function(e,r){return n("div",{key:e.id,staticClass:"select-item"},[n("div",{staticClass:"left"},[n("span",[t._v(t._s(e.name))])]),e.isDelete?t._e():n("div",{staticClass:"right-box"},[n("span",{staticClass:"el-icon-close",on:{click:function(n){return t.handleDeleteUser(e,r)}}})])])})),0)])]):t._e()])],1)}),[],!1,null,"5267de47",null);e.a=a.exports},cbab:function(t,e,n){"use strict";var r=n("1f7f");n.n(r).a}}]);