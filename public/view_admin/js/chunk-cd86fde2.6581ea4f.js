(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-cd86fde2"],{"10b2":function(t,e,a){"use strict";var n=a("a53d");a.n(n).a},a53d:function(t,e,a){},aad4:function(t,e,a){"use strict";a.r(e);var n=a("a34a"),i=a.n(n),r=a("2f62"),s=a("b7be"),o=a("d708");function c(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(t){return void a(t)}o.done?e(c):Promise.resolve(c).then(n,i)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){c(r,n,i,s,o,"next",t)}function o(t){c(r,n,i,s,o,"throw",t)}s(void 0)}))}}function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function d(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={name:"marketing_channelCode",filters:{typeFilter:function(t){return{wechat:"微信用户",routine:"小程序用户"}[t]}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(a,!0).forEach((function(e){d(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(r.e)("media",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),data:function(){var t;return d(t={isChat:!0,formValidate3:{page:1,limit:15},total3:0,loading3:!1,modals3:!1,tabList:[],formValidate5:{page:1,limit:15,uid:0,to_uid:0,id:0},total2:0,loading2:!1,tableList5:[],FromData:null,formValidate:{page:1,limit:15,data:"",type:"",nickname:""},tableList2:[],modals:!1,total:0,tableFrom:{page:1,limit:15,cate_id:0,name:""},userData:{id:0,page:1,limit:15},timeVal:[],loading:!1,tableList:[],columns4:[{title:"UID",key:"uid",minWidth:120},{title:"用户头像",slot:"avatar",minWidth:120},{title:"用户昵称",key:"nickname",minWidth:120}],columns1:[{title:"二维码",slot:"image",minWidth:100},{title:"二维码名称",key:"name",minWidth:140},{title:"总关注数",key:"follow",minWidth:100},{title:"昨日新增关注",key:"y_follow",minWidth:100},{title:"用户标签",slot:"label_name",minWidth:200},{title:"时间",slot:"add_time",minWidth:250},{title:"关联推广员",slot:"avatar",minWidth:100},{title:"状态",slot:"status",minWidth:100},{title:"操作",slot:"action",fixed:"right",width:150}]},"loading2",!1),d(t,"total2",0),d(t,"addFrom",{uids:[]}),d(t,"selections",[]),d(t,"rows",{}),d(t,"rowRecord",{}),d(t,"theme3","light"),d(t,"labelSort",[]),d(t,"sortName",""),d(t,"current",0),d(t,"uid",0),d(t,"hasFirst",!1),t},created:function(){this.hasFirst=!0,this.getUserLabelAll()},activated:function(){this.hasFirst||this.getUserLabelAll()},methods:{changeMenu:function(t,e){switch(this.orderId=t.id,e){case"1":this.downLoadCode(t.image);break;case"2":this.$router.push({path:"".concat(o.a.roterPre,"/marketing/channel_code/statistic?id=").concat(t.id)});break;case"3":this.modals=!0,this.userData.id=t.id,this.getUserList(),this.break}},downLoadCode:function(t){if(!t)return this.$Message.warning("暂无二维码");var e=new Image;e.src=t,e.setAttribute("crossOrigin","anonymous"),e.onload=function(){var t=document.createElement("canvas");t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0,e.width,e.height);var a=t.toDataURL(),n=document.createElement("a"),i=new MouseEvent("click");n.download=name||"photo",n.href=a,n.dispatchEvent(i)}},pageChangeUser:function(t){this.userData.page=t,this.getUserList()},getUserList:function(){var t=this;Object(s.fc)(this.userData).then(function(){var e=l(i.a.mark((function e(a){var n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=a.data,r=[],n.list.map((function(t){r.push(t.user)})),t.tabList=r,t.total2=n.count,t.loading2=!1;case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.tabList=[],t.$Message.error(e.msg)}))},getUserLabelAll:function(t){var e=this;Object(s.Zb)().then((function(a){e.hasFirst=!1;var n=a.data.data;n.unshift({cate_name:"全部",id:""}),n.forEach((function(t){t.status=!1})),t||(e.current||(e.sortName=n[0].id,e.tableFrom.cate_id=n[0].id),e.getList()),e.labelSort=n})).catch((function(){e.hasFirst=!1}))},addSort:function(){var t=this;this.$modalForm(Object(s.Yb)(0)).then((function(){return t.getUserLabelAll()}))},labelEdit:function(t){var e=this;this.$modalForm(Object(s.Yb)(t.id)).then((function(){return e.getUserLabelAll(1)}))},deleteSort:function(t,e,a){var n=this,i={title:e,num:a,url:"app/wechat_qrcode/cate/del/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(i).then((function(t){n.$Message.success(t.msg),n.labelSort.splice(a,1),n.labelSort=[],n.getUserLabelAll()})).catch((function(t){n.$Message.error(t.msg)}))},showMenu:function(t){this.labelSort.forEach((function(e){e.id==t.id?e.status=!t.status:e.status=!1}))},bindMenuItem:function(t,e){this.tableFrom.page=1,this.current=e,this.labelSort.forEach((function(t){t.status=!1})),this.tableFrom.cate_id=t.id,this.getList()},cancel:function(){this.formValidate={page:1,limit:10,data:"",type:"",nickname:""}},edit:function(t){this.$router.push({path:"/admin/marketing/channel_code/create?id="+t.id})},add:function(){this.$router.push({path:"/admin/marketing/channel_code/create",query:{cateId:this.tableFrom.cate_id}})},getListService:function(){var t=this;this.loading2=!0,kefucreateApi(this.formValidate).then(function(){var e=l(i.a.mark((function e(a){var n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=a.data,t.tableList2=n.list,t.total2=n.count,t.tableList2.map((function(t){t._isChecked=!1})),t.loading2=!1;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading2=!1,t.$Message.error(e.msg)}))},userSearchs:function(){this.formValidate.page=1,this.getList()},del:function(t,e,a){var n=this,i={title:e,num:a,url:"/app/wechat_qrcode/del/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(i).then((function(t){n.$Message.success(t.msg),n.tableList.splice(a,1),n.tableList.length||(n.tableFrom.page=1==n.tableFrom.page?1:n.tableFrom.page-1),n.getList()})).catch((function(t){n.$Message.error(t.msg)}))},getList:function(){var t=this;this.loading=!0,Object(s.bc)(this.tableFrom).then(function(){var e=l(i.a.mark((function e(a){var n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=a.data,t.tableList=n.list,t.total=a.data.count,t.loading=!1;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},onchangeIsShow:function(t){var e=this,a={id:t.id,status:t.status};Object(s.ec)(a).then(function(){var t=l(i.a.mark((function t(a){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$Message.success(a.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}}},m=(a("10b2"),a("2877")),p=Object(m.a)(h,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Row",{staticClass:"ivu-mt box-wrapper"},[a("Col",{staticClass:"left-wrapper",attrs:{span:"3"}},[a("Menu",{attrs:{theme:t.theme3,"active-name":t.sortName,width:"auto"}},[a("MenuGroup",t._l(t.labelSort,(function(e,n){return a("MenuItem",{key:n,staticClass:"menu-item",class:n===t.current?"showOn":"",attrs:{name:e.id},nativeOn:{click:function(a){return t.bindMenuItem(e,n)}}},[t._v("\n            "+t._s(e.cate_name)+"\n            "),0!=n?a("div",{staticClass:"icon-box"},[a("Icon",{attrs:{type:"ios-more",size:"24"},on:{click:function(a){return a.stopPropagation(),t.showMenu(e)}}})],1):t._e(),0!=n?a("div",{directives:[{name:"show",rawName:"v-show",value:e.status,expression:"item.status"}],staticClass:"right-menu ivu-poptip-inner"},[a("div",{staticClass:"ivu-poptip-body",on:{click:function(a){return t.labelEdit(e)}}},[a("div",{staticClass:"ivu-poptip-body-content"},[a("div",{staticClass:"ivu-poptip-body-content-inner"},[t._v("编辑分组")])])]),a("div",{staticClass:"ivu-poptip-body",on:{click:function(a){return t.deleteSort(e,"删除分组",n)}}},[a("div",{staticClass:"ivu-poptip-body-content"},[a("div",{staticClass:"ivu-poptip-body-content-inner"},[t._v("删除分组")])])])]):t._e()])})),1)],1)],1),a("Col",{ref:"rightBox",attrs:{span:"21"}},[a("Card",{attrs:{bordered:!1,"dis-hover":""}},[a("Row",{staticClass:"mb20",attrs:{type:"flex"}},[a("Col",{attrs:{span:"19"}},[a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["marketing-channel_code-create"],expression:"['marketing-channel_code-create']"}],staticClass:"mr10",attrs:{type:"primary"},on:{click:t.add}},[t._v("添加渠道码")]),a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["marketing-channel_code-create"],expression:"['marketing-channel_code-create']"}],staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:t.addSort}},[t._v("添加分组")])],1),a("Col",{attrs:{span:"5"}},[a("Input",{attrs:{search:"","enter-button":"搜索",placeholder:"请输入二维码名称"},on:{"on-search":t.userSearchs},model:{value:t.tableFrom.name,callback:function(e){t.$set(t.tableFrom,"name",e)},expression:"tableFrom.name"}})],1)],1),a("Table",{attrs:{columns:t.columns1,data:t.tableList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"image",fn:function(t){var e=t.row;return t.index,[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])]}},{key:"avatar",fn:function(t){var e=t.row;return t.index,[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])]}},{key:"label_name",fn:function(e){var n=e.row;return e.index,[n.label_name.length?a("div",t._l(n.label_name,(function(e,n){return a("Tag",{key:n,attrs:{checkable:!1,color:"primary"}},[t._v(t._s(e))])})),1):a("div",[t._v("--")])]}},{key:"add_time",fn:function(e){var n=e.row;return e.index,[0===n.stop?a("span",[t._v(" 永久 ")]):t._e(),1===n.stop?a("span",[t._v("\n              "+t._s(n.add_time)+" - "+t._s(n.end_time))]):t._e(),-1===n.stop?a("span",[t._v("已过期")]):t._e()]}},{key:"status",fn:function(e){var n=e.row;return e.index,[a("i-switch",{attrs:{value:n.status,"true-value":1,"false-value":0,disabled:2==n.lottery_status,size:"large"},on:{"on-change":function(e){return t.onchangeIsShow(n)}},model:{value:n.status,callback:function(e){t.$set(n,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"action",fn:function(e){var n=e.row,i=e.index;return[a("a",{on:{click:function(e){return t.edit(n)}}},[t._v("编辑")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.del(n,"删除二维码",i)}}},[t._v("删除")]),a("Divider",{attrs:{type:"vertical"}}),a("Dropdown",{attrs:{transfer:""},on:{"on-click":function(e){return t.changeMenu(n,e)}}},[a("a",{attrs:{href:"javascript:void(0)"}},[t._v("更多\n                "),a("Icon",{attrs:{type:"ios-arrow-down"}})],1),a("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[a("DropdownItem",{attrs:{name:"1"}},[t._v("下载")]),a("DropdownItem",{attrs:{name:"2"}},[t._v("统计")]),a("DropdownItem",{attrs:{name:"3"}},[t._v("用户列表")])],1)],1)]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.tableFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"渠道码用户列表","mask-closable":!1,width:"900"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Table",{ref:"selection",attrs:{columns:t.columns4,data:t.tabList,"no-data-text":"暂无数据","highlight-row":"","max-height":"400","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"avatar",fn:function(t){var e=t.row;return t.index,[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total2,"show-elevator":"","show-total":"",loading:t.loading2,"page-size":t.userData.limit},on:{"on-change":t.pageChangeUser}})],1)],1)],1)}),[],!1,null,"9cd9fc9e",null);e.default=p.exports}}]);