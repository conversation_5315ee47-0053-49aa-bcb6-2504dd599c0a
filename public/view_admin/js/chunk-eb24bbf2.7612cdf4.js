(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-eb24bbf2"],{6557:function(t,a,e){"use strict";e.r(a);var i=e("a584"),n=e("b7be"),s=e("d708"),r=e("2f62");function o(t,a){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);a&&(i=i.filter((function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable}))),e.push.apply(e,i)}return e}function l(t,a,e){return a in t?Object.defineProperty(t,a,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[a]=e,t}var c={name:"index",components:{cardsData:i.a},data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},roterPre:s.a.roterPre,id:0,tbody:[],total:0,tabs:[{type:"",label:"活动参与人"},{type:"",label:"活动订单"}],currentTab:0,loading:!1,thead:[{title:"用户姓名",key:"real_name"},{title:"购买件数",key:"goods_num"},{title:"支付订单数",key:"order_num"},{title:"支付金额",key:"total_price"},{title:"最近参与时间",key:"add_time"}],thead2:[{title:"订单号",key:"order_id"},{title:"用户",key:"real_name"},{title:"订单状态",key:"status"},{title:"订单支付金额",key:"pay_price"},{title:"订单商品数",key:"total_num"},{title:"下单时间",key:"add_time"},{title:"支付时间",key:"pay_time"}],cardLists:[{col:6,count:0,name:"下单人数（人）",className:"iconxiadanrenshu",type:!0},{col:6,count:0,name:"支付订单额（元）",className:"iconzhifudingdan",type:!0},{col:6,count:0,name:"支付人数（人）",className:"iconzhifurenshu",type:!0},{col:6,count:0,name:"剩余库存/总库存",className:"iconshengyukucun",type:!0}],pagination:{page:1,limit:15,real_name:"",status:""},type:0}},computed:function(t){for(var a=1;a<arguments.length;a++){var e=null!=arguments[a]?arguments[a]:{};a%2?o(e,!0).forEach((function(a){l(t,a,e[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):o(e).forEach((function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(e,a))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.id=this.$route.params.id,this.getStatistics(this.id),this.getList(this.id)},methods:{getStatistics:function(t){var a=this;Object(n.X)(t).then((function(t){var e=["order_count","all_price","pay_count","pay_rate"];a.cardLists.map((function(a,i){a.count=t.data[e[i]]}))}))},getList:function(t){var a=this;this.loading=!0,0==this.type?Object(n.Z)(this.id,this.pagination).then((function(t){a.loading=!1;var e=t.data,i=e.count,n=e.list;a.total=i,a.tbody=n})):Object(n.Y)(this.id,this.pagination).then((function(t){a.loading=!1;var e=t.data,i=e.count,n=e.list;a.total=i,a.tbody=n}))},onClickTab:function(){this.type!=this.currentTab&&(this.pagination.real_name="",this.pagination.status="",this.pagination.page=1),this.type=this.currentTab,this.getList(this.id)},searchList:function(){this.pagination.page=1,this.getList(this.id)},pageChange:function(t){this.pagination.page=t,this.getList(this.id)}}},u=e("2877"),d=Object(u.a)(c,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"form-submit"},[e("div",{staticClass:"i-layout-page-header"},[e("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[e("div",{attrs:{slot:"title"},slot:"title"},[e("router-link",{attrs:{to:{path:t.roterPre+"/marketing/store_seckill/index"}}},[e("div",{staticClass:"font-sm after-line"},[e("span",{staticClass:"iconfont iconfanhui"}),e("span",{staticClass:"pl10"},[t._v("返回")])])]),e("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.meta.title)}})],1)])],1),t.cardLists.length>=0?e("cards-data",{attrs:{cardLists:t.cardLists}}):t._e(),e("Card",{attrs:{bordered:!1,"dis-hover":""}},[e("Tabs",{on:{"on-click":t.onClickTab},model:{value:t.currentTab,callback:function(a){t.currentTab=a},expression:"currentTab"}},t._l(t.tabs,(function(t,a){return e("TabPane",{key:a,attrs:{label:t.label,name:t.type}})})),1),e("Form",{ref:"pagination",attrs:{model:t.pagination,"label-width":t.labelWidth,inline:"","label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[1==t.type?e("FormItem",{attrs:{label:"订单状态：","label-for":"status"}},[e("Select",{staticClass:"input-add",attrs:{placeholder:"请选择订单状态"},model:{value:t.pagination.status,callback:function(a){t.$set(t.pagination,"status",a)},expression:"pagination.status"}},[e("Option",{attrs:{value:""}},[t._v("全部")]),e("Option",{attrs:{value:"0"}},[t._v("未支付")]),e("Option",{attrs:{value:"1"}},[t._v("待发货")]),e("Option",{attrs:{value:"2"}},[t._v("待收货")]),e("Option",{attrs:{value:"3"}},[t._v("待评价")]),e("Option",{attrs:{value:"4"}},[t._v("交易完成")])],1)],1):t._e(),e("FormItem",{attrs:{label:"搜索：","label-for":"title"}},[e("Input",{staticClass:"input-add",attrs:{search:"","enter-button":"",placeholder:"请输入用户姓名/手机号/UID"},on:{"on-search":t.searchList},model:{value:t.pagination.real_name,callback:function(a){t.$set(t.pagination,"real_name",a)},expression:"pagination.real_name"}})],1)],1),e("Table",{ref:"table",attrs:{columns:t.type?t.thead2:t.thead,data:t.tbody,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}}),e("div",{staticClass:"acea-row row-right page"},[e("Page",{attrs:{total:t.total,current:t.pagination.page,"show-elevator":"","show-total":"","page-size":t.pagination.limit},on:{"on-change":t.pageChange}})],1)],1)],1)}),[],!1,null,"40a9d077",null);a.default=d.exports},a584:function(t,a,e){"use strict";var i;function n(t,a,e){return a in t?Object.defineProperty(t,a,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[a]=e,t}var s=(n(i={name:"cards",data:function(){return{}},props:{cardLists:Array}},"data",(function(){return{}})),n(i,"methods",{}),n(i,"created",(function(){})),i),r=(e("e83b"),e("2877")),o=Object(r.a)(s,(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("Row",{staticClass:"ivu-mt",attrs:{type:"flex",align:"middle",gutter:16}},t._l(t.cardLists,(function(a,i){return e("Col",{key:i,staticClass:"ivu-mb",attrs:{xs:24,sm:24,md:12,lg:12,xl:8,xxl:{span:a.col}}},[e("Card",{staticClass:"card_cent",attrs:{shadow:"",padding:0}},[e("div",{staticClass:"card_box"},[e("div",{staticClass:"card_box_cir",class:{one:i%5==0,two:i%5==1,three:i%5==2,four:i%5==3,five:i%5==4}},[e("div",{staticClass:"card_box_cir1",class:{one1:i%5==0,two1:i%5==1,three1:i%5==2,four1:i%5==3,five1:i%5==4}},[a.type?e("span",{staticClass:"iconfont",class:a.className}):e("Icon",{attrs:{type:a.className}})],1)]),e("div",{staticClass:"card_box_txt"},[e("span",{staticClass:"sp1",domProps:{textContent:t._s(a.count||0)}}),e("span",{staticClass:"sp2",domProps:{textContent:t._s(a.name)}})])])])],1)})),1)],1)}),[],!1,null,"566bac57",null);a.a=o.exports},c72b:function(t,a,e){},e83b:function(t,a,e){"use strict";var i=e("c72b");e.n(i).a}}]);