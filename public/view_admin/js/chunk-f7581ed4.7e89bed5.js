(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f7581ed4"],{"277f":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return u})),n.d(e,"d",(function(){return i}));var a=n("b6bd");function r(t){return Object(a.a)({url:"setting/notification/index?type=".concat(t),method:"get"})}function c(t,e){return Object(a.a)({url:"setting/notification/info?id=".concat(t,"&type=").concat(e),method:"get"})}function u(t){return Object(a.a)({url:"setting/notification/save",method:"post",data:t})}function i(t,e,n){return Object(a.a)({url:"setting/notification/set_status/".concat(t,"/").concat(e,"/").concat(n),method:"put"})}},6014:function(t,e,n){"use strict";var a=n("db13");n.n(a).a},aa0a:function(t,e,n){"use strict";n.r(e);var a=n("277f"),r=n("b562"),c=n("d708"),u={data:function(){return{roterPre:c.a.roterPre,modalTitle:"",notificationModal:!1,headerList:[{label:"通知会员",value:"1"},{label:"通知平台",value:"2"}],columns:[{title:"ID",key:"id",align:"center",width:50},{title:"通知类型",slot:"name",align:"center",width:200},{title:"通知场景说明",slot:"title",align:"center",minWidth:200},{title:"站内信",slot:"is_system",align:"center",minWidth:100},{title:"公众号模板",slot:"is_wechat",align:"center",minWidth:100},{title:"小程序订阅",slot:"is_routine",align:"center",minWidth:100},{title:"发送短信",slot:"is_sms",align:"center",minWidth:100},{title:"设置",slot:"setting",width:150,align:"center"}],levelLists:[],currentTab:"1",loading:!1,formData:{},industry:null}},created:function(){this.changeTab(this.currentTab)},methods:{changeSwitch:function(t,e){var n=this;Object(a.d)(e,t[e],t.id).then((function(t){n.$Message.success(t.msg)})).catch((function(t){n.$Message.error(t.msg)}))},changeTab:function(t){var e=this;this.columns=this.columns.filter((function(t){return"is_ent_wechat"!=t.slot})),2==t&&this.columns.splice(7,0,{title:"企业微信",slot:"is_ent_wechat",align:"center",minWidth:100}),Object(a.b)(t).then((function(t){e.levelLists=t.data.list,e.industry=t.data.industry}))},syncTemplate:function(){var t=this;Object(r.n)().then((function(e){t.$Message.success(e.msg),t.changeTab(t.currentTab)})).catch((function(e){t.$Message.error(e.msg)}))},wechatTemplate:function(){var t=this;Object(r.D)().then((function(e){t.$Message.success(e.msg),t.changeTab(t.currentTab)})).catch((function(e){t.$Message.error(e.msg)}))},changeStatus:function(){},notice:function(){},setting:function(t,e){this.$router.push({path:this.roterPre+"/setting/notification/notificationEdit?id="+e.id})},getData:function(t,e,n){var r=this;this.formData={},Object(a.a)(e.id,n).then((function(e){t.map((function(t,n){r.formData[t]=e.data[t]})),r.formData.type=n,r.notificationModal=!0}))}}},i=(n("6014"),n("2877")),o=Object(i.a)(u,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"message"},[n("div",{staticClass:"i-layout-page-header"},[n("div",{staticClass:"table-box"},[n("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("div",{staticClass:"new_tab"},[n("Tabs",{on:{"on-click":t.changeTab},model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.headerList,(function(t,e){return n("TabPane",{key:e,attrs:{label:t.label,name:t.value.toString()}})})),1)],1),1==t.currentTab?n("Row",{staticClass:"mb20",attrs:{type:"flex"}},[n("Col",[n("Button",{directives:[{name:"auth",rawName:"v-auth",value:["app-wechat-template-sync"],expression:"['app-wechat-template-sync']"}],staticClass:"ml20",attrs:{type:"success"},on:{click:t.syncTemplate}},[t._v("同步小程序订阅消息")]),n("Button",{directives:[{name:"auth",rawName:"v-auth",value:["app-wechat-wechat-sync"],expression:"['app-wechat-wechat-sync']"}],staticClass:"ml20",attrs:{type:"primary"},on:{click:t.wechatTemplate}},[t._v("同步公众号模板消息")])],1)],1):t._e(),t.industry?n("Alert",{attrs:{closable:""}},[n("template",{slot:"desc"},[n("div",{staticClass:"alert-wrapper"},[n("div",{staticClass:"alert-wrapper-head"},[t._v("小程序订阅消息")]),n("div",{staticClass:"alert-wrapper-body"},[n("div",[t._v("登录微信小程序后台，基本设置，服务类目增加《生活服务 > 百货/超市/便利店》 "),n("span",[t._v("(否则同步小程序订阅消息会报错)")])]),n("div",[t._v("同步小程序订阅消息 是在小程序后台未添加订阅消息模板的前提下使用的，会新增一个模板消息并把信息同步过来，如果小程序后台已经添加过的，会跳过不会更新本项目数据库。")])])]),n("div",{staticClass:"alert-wrapper"},[n("div",{staticClass:"alert-wrapper-head"},[t._v("微信模板消息")]),n("div",{staticClass:"alert-wrapper-body"},[n("div",[t._v("登录微信公众号后台，选择模板消息，在账号详情下的服务类目中手动设置服务类目，《生活服务 > 百货/超市/便利店 商业服务 > 软件/建站/技术开发 商家自营 > 办公/文具》"),n("span",[t._v("(否则同步模板消息不成功)")])]),n("div",[t._v("同步公众号模板消息 同步公众号模板会删除公众号后台现有的模板，并重新添加新的模板，然后同步信息到数据库，如果多个项目使用同一个公众号的模板，请谨慎操作。")])])])])],2):t._e(),n("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.levelLists,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"name",fn:function(e){var a=e.row;return e.index,[n("span",{staticClass:"table"},[t._v("\n              "+t._s(a.name)+"\n            ")])]}},{key:"title",fn:function(e){var a=e.row;return e.index,[n("span",{staticClass:"table"},[t._v(t._s(a.title))])]}},t._l(["is_system","is_wechat","is_routine","is_sms","is_ent_wechat"],(function(e,a){return{key:e,fn:function(a){var r=a.row;return a.index,[r[e]>0?n("i-switch",{attrs:{value:r[e],"true-value":1,"false-value":2,size:"large"},on:{"on-change":function(n){return t.changeSwitch(r,e)}},model:{value:r[e],callback:function(n){t.$set(r,e,n)},expression:"row[item]"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]):t._e()]}}})),{key:"setting",fn:function(e){var a=e.row;return e.index,[n("span",{staticClass:"setting btn",on:{click:function(e){return t.setting(t.item,a)}}},[t._v("设置")])]}}],null,!0)})],1)],1)])])}),[],!1,null,"443df78e",null);e.default=o.exports},b562:function(t,e,n){"use strict";n.d(e,"l",(function(){return r})),n.d(e,"n",(function(){return c})),n.d(e,"D",(function(){return u})),n.d(e,"h",(function(){return i})),n.d(e,"j",(function(){return o})),n.d(e,"m",(function(){return s})),n.d(e,"y",(function(){return l})),n.d(e,"a",(function(){return d})),n.d(e,"x",(function(){return p})),n.d(e,"s",(function(){return f})),n.d(e,"t",(function(){return h})),n.d(e,"C",(function(){return m})),n.d(e,"g",(function(){return b})),n.d(e,"i",(function(){return w})),n.d(e,"k",(function(){return g})),n.d(e,"d",(function(){return v})),n.d(e,"e",(function(){return _})),n.d(e,"f",(function(){return j})),n.d(e,"z",(function(){return O})),n.d(e,"B",(function(){return T})),n.d(e,"A",(function(){return y})),n.d(e,"H",(function(){return C})),n.d(e,"o",(function(){return k})),n.d(e,"c",(function(){return E})),n.d(e,"G",(function(){return G})),n.d(e,"E",(function(){return x})),n.d(e,"F",(function(){return $})),n.d(e,"w",(function(){return D})),n.d(e,"u",(function(){return M})),n.d(e,"v",(function(){return P})),n.d(e,"p",(function(){return S})),n.d(e,"b",(function(){return W})),n.d(e,"r",(function(){return L})),n.d(e,"q",(function(){return B}));var a=n("b6bd");function r(t){return Object(a.a)({url:"app/routine",method:"get",params:t})}function c(){return Object(a.a)({url:"app/routine/syncSubscribe",method:"GET"})}function u(){return Object(a.a)({url:"app/wechat/syncSubscribe",method:"GET"})}function i(){return Object(a.a)({url:"app/routine/create",method:"get"})}function o(t){return Object(a.a)({url:"app/routine/".concat(t,"/edit"),method:"get"})}function s(t){return Object(a.a)({url:"app/routine/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function l(t){return Object(a.a)({url:"app/wechat/menu",method:"get"})}function d(t){return Object(a.a)({url:"app/wechat/menu",method:"post",data:t})}function p(t){return Object(a.a)({url:"app/wechat/template",method:"get",params:t})}function f(){return Object(a.a)({url:"app/wechat/template/create",method:"get"})}function h(t){return Object(a.a)({url:"app/wechat/template/".concat(t,"/edit"),method:"get"})}function m(t){return Object(a.a)({url:"app/wechat/template/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function b(t){return Object(a.a)({url:t.url,method:"post",data:t.key})}function w(t){return Object(a.a)({url:"app/routine/download",method:"post",data:t})}function g(){return Object(a.a)({url:"app/routine/info",method:"get"})}function v(t){return Object(a.a)({url:"app/wechat/keyword",method:"get",params:t})}function _(t){return Object(a.a)({url:"app/wechat/keyword/set_status/".concat(t.id,"/").concat(t.status),method:"PUT"})}function j(t,e){return Object(a.a)({url:t,method:"get",params:e.key})}function O(t){return Object(a.a)({url:"/app/wechat/news",method:"POST",data:t})}function T(t){return Object(a.a)({url:"app/wechat/news",method:"GET",params:t})}function y(t){return Object(a.a)({url:"app/wechat/news/".concat(t),method:"GET"})}function C(t){return Object(a.a)({url:"app/wechat/user",method:"GET",params:t})}function k(){return Object(a.a)({url:"app/wechat/user/tag_group",method:"GET"})}function E(t){return Object(a.a)({url:t,method:"GET"})}function G(){return Object(a.a)({url:"app/wechat/tag",method:"GET"})}function x(){return Object(a.a)({url:"app/wechat/tag/create",method:"GET"})}function $(t){return Object(a.a)({url:"app/wechat/tag/".concat(t,"/edit"),method:"GET"})}function D(){return Object(a.a)({url:"app/wechat/group",method:"GET"})}function M(){return Object(a.a)({url:"app/wechat/group/create",method:"GET"})}function P(t){return Object(a.a)({url:"app/wechat/group/".concat(t,"/edit"),method:"GET"})}function S(t){return Object(a.a)({url:"app/wechat/action",method:"GET",params:t})}function W(t){return Object(a.a)({url:"app/wechat/code_reply/".concat(t),method:"GET"})}function L(){return Object(a.a)({url:"/app/wechat/card",method:"GET"})}function B(t){return Object(a.a)({url:"/app/wechat/card",method:"post",data:t})}},db13:function(t,e,n){}}]);