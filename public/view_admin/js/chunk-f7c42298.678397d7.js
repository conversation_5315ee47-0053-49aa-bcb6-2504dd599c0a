(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f7c42298"],{"75d1":function(t,e,a){"use strict";var r=a("8023");a.n(r).a},8023:function(t,e,a){},a20b:function(t,e,a){"use strict";a.r(e);var r=a("2f62"),o=a("8593"),s=a("d708");function i(t){return function(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function l(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(a,!0).forEach((function(e){c(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function c(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var u={filters:{formatWeek:function(t){return["周一","周二","周三","周四","周五","周六","周日"][t-1]}},data:function(){return{roterPre:s.a.roterPre,typeList:[{name:"N分钟",value:1},{name:"N小时",value:2},{name:"N天",value:5},{name:"每小时",value:3},{name:"每天",value:4},{name:"每星期",value:6},{name:"每月",value:7},{name:"每年",value:8}],task:{},loading:!1,formValidate:{name:"",mark:"",title:"",is_open:0,type:6,month:1,week:1,day:1,hour:1,minute:30,cycle:""}}},computed:{date:function(){switch(this.formValidate.month){case 1:case 3:case 5:case 7:case 8:case 10:case 12:return 31;case 2:return 28;default:return 30}}},watch:{date:function(t){t<this.formValidate.day&&(this.formValidate.day=t)},"formValidate.type":function(){this.formValidate.month=1,this.formValidate.week=1,this.formValidate.day=1,this.formValidate.hour=1,this.formValidate.minute=30,this.formValidate.cycle=""}},created:function(){var t=this;this.timerTask(),this.setCopyrightShow({value:!1}),this.$once("hook:beforeDestroy",(function(){t.setCopyrightShow({value:!0})})),this.$route.params.id&&this.timerInfo()},methods:l({},Object(r.d)("admin/layout",["setCopyrightShow"]),{timerTask:function(){var t=this;Object(o.db)().then((function(e){t.task=e.data}))},timerInfo:function(){var t=this;Object(o.cb)(this.$route.params.id).then((function(e){var a=e.data,r=a.name,o=a.mark,s=a.type,i=a.cycle,n=a.title,l=a.is_open;t.formValidate.name=r,t.formValidate.mark=o,t.formValidate.title=n,t.formValidate.is_open=l,t.formValidate.type=s;var c=i.split("/");t.$nextTick((function(){switch(s){case 1:case 3:t.formValidate.minute=Number(c[0]);break;case 2:case 4:t.formValidate.hour=Number(c[0]),t.formValidate.minute=Number(c[1]);break;case 5:case 7:t.formValidate.day=Number(c[0]),t.formValidate.hour=Number(c[1]),t.formValidate.minute=Number(c[2]);break;case 6:t.formValidate.week=Number(c[0]),t.formValidate.hour=Number(c[1]),t.formValidate.minute=Number(c[2])}}))}))},handleSubmit:function(){if(!this.formValidate.name)return this.$Message.error({content:"请选择任务名称",onClose:function(){}});var t=l({},this.formValidate),e=[t.minute];switch(t.type){case 2:case 4:e=[t.hour].concat(i(e));break;case 5:case 7:e=[t.day,t.hour].concat(i(e));break;case 6:e=[t.week,t.hour].concat(i(e));break;case 8:e=[t.month,t.day,t.hour].concat(i(e))}t.cycle=e.join("/"),delete t.month,delete t.week,delete t.day,delete t.hour,delete t.minute,this.$route.params.id?this.updateTimer(t):this.saveTimer(t)},taskChange:function(t){var e=t.label,a=t.value;this.formValidate.name=e,this.formValidate.mark=a},saveTimer:function(t){var e=this;Object(o.R)(t).then((function(t){e.$Message.success({content:t.msg,onClose:function(){e.$router.push({path:e.roterPre+"/system/crontab"})}})})).catch((function(t){e.$Message.error(t.msg)}))},updateTimer:function(t){var e=this;Object(o.eb)(this.$route.params.id,t).then((function(t){e.$Message.success({content:t.msg,onClose:function(){e.$router.push({path:e.roterPre+"/system/crontab"})}})})).catch((function(t){e.$Message.error(t.msg)}))}})},m=(a("75d1"),a("2877")),f=Object(m.a)(u,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"i-layout-page-header"},[a("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[a("div",{attrs:{slot:"title"},slot:"title"},[a("router-link",{attrs:{to:{path:t.roterPre+"/system/crontab"}}},[a("div",{staticClass:"font-sm after-line"},[a("span",{staticClass:"iconfont iconfanhui"}),a("span",{staticClass:"pl10"},[t._v("返回")])])]),a("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑定时任务":"添加定时任务")}})],1)])],1),a("Card",{staticClass:"ivu-mt form-card",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":97,"label-colon":""}},[a("FormItem",{attrs:{label:"任务名称",required:""}},[a("Row",{attrs:{gutter:10}},[a("Col",{attrs:{span:"12"}},[a("Select",{attrs:{"label-in-value":""},on:{"on-change":t.taskChange},model:{value:t.formValidate.mark,callback:function(e){t.$set(t.formValidate,"mark",e)},expression:"formValidate.mark"}},t._l(t.task,(function(e,r){return a("Option",{key:r,attrs:{value:r}},[t._v(t._s(e))])})),1)],1)],1)],1),a("FormItem",{attrs:{label:"执行周期",required:""}},[a("Row",{attrs:{gutter:10}},[a("Col",{attrs:{span:"3"}},[a("Select",{model:{value:t.formValidate.type,callback:function(e){t.$set(t.formValidate,"type",e)},expression:"formValidate.type"}},t._l(t.typeList,(function(e){return a("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.name))])})),1)],1),6==t.formValidate.type?a("Col",{attrs:{span:"3"}},[a("Select",{model:{value:t.formValidate.week,callback:function(e){t.$set(t.formValidate,"week",e)},expression:"formValidate.week"}},t._l(7,(function(e){return a("Option",{key:e,attrs:{value:e}},[t._v(t._s(t._f("formatWeek")(e)))])})),1)],1):t._e(),8==t.formValidate.type?a("Col",{attrs:{span:"3"}},[a("div",{staticClass:"suffix-wrapper"},[a("Select",{model:{value:t.formValidate.month,callback:function(e){t.$set(t.formValidate,"month",e)},expression:"formValidate.month"}},t._l(12,(function(e,r){return a("Option",{key:r,attrs:{value:e}},[t._v(t._s(e))])})),1),a("span",{staticClass:"suffix"},[t._v("月")])],1)]):t._e(),5==t.formValidate.type||7==t.formValidate.type||8==t.formValidate.type?a("Col",{attrs:{span:"3"}},[a("div",{staticClass:"suffix-wrapper"},[a("Select",{model:{value:t.formValidate.day,callback:function(e){t.$set(t.formValidate,"day",e)},expression:"formValidate.day"}},t._l(t.date,(function(e,r){return a("Option",{key:r,attrs:{value:e}},[t._v(t._s(e))])})),1),a("span",{staticClass:"suffix"},[t._v("日")])],1)]):t._e(),1!=t.formValidate.type&&3!=t.formValidate.type?a("Col",{attrs:{span:"3"}},[a("div",{staticClass:"suffix-wrapper"},[a("Select",{model:{value:t.formValidate.hour,callback:function(e){t.$set(t.formValidate,"hour",e)},expression:"formValidate.hour"}},t._l(24,(function(e,r){return a("Option",{key:r,attrs:{value:r}},[t._v(t._s(r))])})),1),a("span",{staticClass:"suffix"},[t._v("时")])],1)]):t._e(),a("Col",{attrs:{span:"3"}},[a("div",{staticClass:"suffix-wrapper"},[a("Select",{model:{value:t.formValidate.minute,callback:function(e){t.$set(t.formValidate,"minute",e)},expression:"formValidate.minute"}},t._l(60,(function(e,r){return a("Option",{key:r,attrs:{value:r}},[t._v(t._s(r))])})),1),a("span",{staticClass:"suffix"},[t._v("分")])],1)])],1)],1),a("FormItem",{attrs:{label:"任务说明"}},[a("Row",{attrs:{gutter:10}},[a("Col",{attrs:{span:"12"}},[a("Input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:5},placeholder:"请输入任务说明"},model:{value:t.formValidate.title,callback:function(e){t.$set(t.formValidate,"title",e)},expression:"formValidate.title"}})],1)],1)],1),a("FormItem",{attrs:{label:"是否开启"}},[a("Row",{attrs:{gutter:10}},[a("Col",{attrs:{span:"12"}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_open,callback:function(e){t.$set(t.formValidate,"is_open",e)},expression:"formValidate.is_open"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1)],1)],1)],1),a("Card",{staticClass:"btn-card",attrs:{bordered:!1,padding:14,"dis-hover":""}},[a("Button",{attrs:{loading:t.loading,type:"primary"},on:{click:t.handleSubmit}},[t._v("提交")])],1)],1)}),[],!1,null,"39289f7a",null);e.default=f.exports}}]);