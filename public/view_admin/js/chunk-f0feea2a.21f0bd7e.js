(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f0feea2a"],{"0f0e":function(t,e,n){"use strict";var r=n("c4c8"),a={name:"userLabel",props:{},data:function(){return{labelList:[],dataLabel:[],isUser:!1}},mounted:function(){},methods:{inArray:function(t,e){for(var n in e)if(e[n].id===t)return!0;return!1},userLabel:function(t){var e=this;this.dataLabel=t,Object(r.pb)().then((function(t){t.data.map((function(t){t.children&&t.children.length&&(e.isUser=!0,t.children.map((function(t){e.inArray(t.id,e.dataLabel)?t.disabled=!0:t.disabled=!1})))})),e.labelList=t.data})).catch((function(t){e.$Message.error(t.msg)}))},selectLabel:function(t){if(t.disabled){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id===t.id}))[0]);this.dataLabel.splice(e,1),t.disabled=!1}else this.dataLabel.push({label_name:t.label_name,id:t.id,tag_id:t.tag_id}),t.disabled=!0},subBtn:function(){this.$emit("activeData",JSON.parse(JSON.stringify(this.dataLabel)))},cancel:function(){this.$emit("close")}}},i=(n("9b1c"),n("2877")),o=Object(i.a)(a,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"label-wrapper"},[n("div",{staticClass:"list-box"},[t._l(t.labelList,(function(e,r){return t.isUser?n("div",{key:r,staticClass:"label-box"},[e.children&&e.children.length?n("div",{staticClass:"title"},[t._v("\n          "+t._s(e.label_name)+"\n        ")]):t._e(),e.children&&e.children.length?n("div",{staticClass:"list"},t._l(e.children,(function(e,r){return n("div",{key:r,staticClass:"label-item",class:{on:e.disabled},on:{click:function(n){return t.selectLabel(e)}}},[t._v("\n            "+t._s(e.label_name)+"\n          ")])})),0):t._e()]):t._e()})),t.isUser?t._e():n("div",[t._v("暂无标签")])],2),n("div",{staticClass:"footer"},[n("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:t.cancel}},[t._v("取消")]),n("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确定")])],1)])}),[],!1,null,"3dda6796",null);e.a=o.exports},5671:function(t,e,n){"use strict";var r=n("2f62"),a=n("73f5");function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var s={name:"index",props:{region:{type:Number,default:0}},data:function(){return{selectEquips:[],selectEquipsIds:[],modal_loading:!1,treeSelect:[],formValidate:{page:1,limit:10,cate_id:[],name:""},total:0,loading:!1,grid:{xl:10,lg:10,md:12,sm:24,xs:24},tableList:[],columns:[{type:"selection",width:60,align:"center"},{title:"ID",key:"id",width:60},{title:"门店图片",slot:"image",minWidth:80},{title:"门店名称",key:"name",minWidth:80},{title:"门店分类",key:"cate_name",minWidth:80},{title:"联系电话",key:"phone",minWidth:90},{title:"门店地址",key:"address",ellipsis:!0,minWidth:150},{title:"营业时间",key:"day_time",minWidth:120},{title:"营业状态",key:"status_name",minWidth:80}]}},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(n,!0).forEach((function(e){o(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.region>0&&(this.columns.shift(),this.columns.push({title:"操作",slot:"action",width:60}))},mounted:function(){this.getList(),this.goodsCategory()},methods:{delte:function(t,e,n){var r=this,a={store_id:t.id},i={title:e,num:n,url:"store/region/del_store/".concat(this.region),method:"DELETE",ids:a};this.$modalSure(i).then((function(t){r.$Message.success(t.msg),r.tableList.splice(n,1),r.tableList.length||(r.formValidate.page=1==r.formValidate.page?1:r.formValidate.page-1),r.getList()})).catch((function(t){r.$Message.error(t.msg)}))},sortData:function(){var t=this;this.selectEquipsIds.length&&this.tableList.forEach((function(e){t.selectEquipsIds.includes(e.id)&&(e._checked=!0)}))},TableSelectRow:function(t,e){this.selectEquipsIds.includes(e.id)||(this.selectEquipsIds.push(e.id),this.selectEquips.push(e))},TableSelectCancelRow:function(t,e){var n=this.selectEquipsIds.indexOf(e.id);-1!=n&&(this.selectEquipsIds.splice(n,1),this.selectEquips.splice(n,1))},selectAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectRow(null,this.tableList[t])},cancelAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectCancelRow(null,this.tableList[t])},handleSelectAll:function(){this.$refs.table.selectAll(!1)},pageChange:function(t){this.formValidate.page=t,this.getList()},goodsCategory:function(){var t=this;Object(a.a)(1).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getList:function(){var t=this;this.loading=!0,-1==this.region&&(this.formValidate.is_region=0),this.region>0&&(this.formValidate.region_id=this.region),Object(a.K)(this.formValidate).then((function(e){t.tableList=e.data.list,t.total=e.data.count,t.sortData(),t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},ok:function(){var t=[];this.selectEquips.forEach((function(e){var n={image:e.image,product_id:e.id,store_name:e.store_name,temp_id:e.temp_id};t.push(n)})),t.length>0?this.$emit("getStoreId",this.selectEquips):this.$Message.warning("请先选择商品")},userSearchs:function(){this.formValidate.page=1,this.getList()}}},c=(n("c76c"),n("2877")),l=Object(c.a)(s,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"goodList"},[t.region<1?n("Form",{ref:"formValidate",staticClass:"tabform",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition,inline:""}},[n("FormItem",{attrs:{label:"门店分类：","label-for":"pid"}},[n("Cascader",{staticClass:"input-add",attrs:{data:t.treeSelect,placeholder:"请选择门店分类","change-on-select":"",filterable:""},model:{value:t.formValidate.cate_id,callback:function(e){t.$set(t.formValidate,"cate_id",e)},expression:"formValidate.cate_id"}})],1),n("FormItem",{attrs:{label:"门店搜索：","label-for":"name"}},[n("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入门店名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}}),n("Button",{attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("搜索")])],1)],1):t._e(),n("Table",{ref:"table",staticClass:"mr-20",class:t.region<1?"store":"",attrs:{"no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果",columns:t.columns,data:t.tableList,loading:t.loading,height:"350"},on:{"on-select-all":t.selectAll,"on-select-all-cancel":t.cancelAll,"on-select":t.TableSelectRow,"on-select-cancel":t.TableSelectCancelRow},scopedSlots:t._u([{key:"image",fn:function(t){var e=t.row;return[n("viewer",[n("div",{staticClass:"tabBox_img"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])]}},{key:"action",fn:function(e){var r=e.row,a=e.index;return[n("a",{on:{click:function(e){return t.delte(r,"删除区域门店",a)}}},[t._v("删除")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1),t.region<1?n("div",{staticClass:"footer",attrs:{slot:"footer"},slot:"footer"},[n("Button",{attrs:{type:"primary",size:"large",loading:t.modal_loading,long:""},on:{click:t.ok}},[t._v("提交")])],1):t._e()],1)}),[],!1,null,"1069bd13",null);e.a=l.exports},"61f7":function(t,e,n){"use strict";function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(n,!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t,e){/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var r in n)if(new RegExp("(".concat(r,")")).test(e)){var a=n[r]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?a:s(a))}return e}function s(t){return("00"+t).substr(t.length)}n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return d}));var c={min:"%s最小长度为:min",max:"%s最大长度为:max",length:"%s长度必须为:length",range:"%s长度为:range",pattern:"$s格式错误"},l=function(t,e){t.message=function(t){return e.replace("%s",t||"")}};function u(t){return a({required:!0,message:t,type:"string"},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}function d(t){return f.pattern(/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,t)}l(u,"请输入%s"),l(d,"%s格式不正确");var f=Object.keys(c).reduce((function(t,e){return t[e]=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o="range"===e?{min:t[0],max:t[1]}:i({},e,t);return a({message:n.replace(":".concat(e),"range"===e?"".concat(t[0],"-").concat(t[1]):t),type:"string"},o,{},r)},l(t[e],c[e]),t}),{})},7364:function(t,e,n){},"73f5":function(t,e,n){"use strict";n.d(e,"z",(function(){return a})),n.d(e,"t",(function(){return i})),n.d(e,"r",(function(){return o})),n.d(e,"u",(function(){return s})),n.d(e,"n",(function(){return c})),n.d(e,"x",(function(){return l})),n.d(e,"H",(function(){return u})),n.d(e,"I",(function(){return d})),n.d(e,"F",(function(){return f})),n.d(e,"G",(function(){return h})),n.d(e,"g",(function(){return m})),n.d(e,"C",(function(){return p})),n.d(e,"D",(function(){return b})),n.d(e,"E",(function(){return g})),n.d(e,"P",(function(){return _})),n.d(e,"K",(function(){return v})),n.d(e,"J",(function(){return y})),n.d(e,"e",(function(){return w})),n.d(e,"O",(function(){return O})),n.d(e,"p",(function(){return j})),n.d(e,"L",(function(){return x})),n.d(e,"M",(function(){return k})),n.d(e,"N",(function(){return C})),n.d(e,"o",(function(){return S})),n.d(e,"s",(function(){return L})),n.d(e,"A",(function(){return V})),n.d(e,"q",(function(){return $})),n.d(e,"y",(function(){return E})),n.d(e,"f",(function(){return P})),n.d(e,"B",(function(){return I})),n.d(e,"b",(function(){return T})),n.d(e,"d",(function(){return A})),n.d(e,"c",(function(){return D})),n.d(e,"a",(function(){return N})),n.d(e,"i",(function(){return R})),n.d(e,"k",(function(){return M})),n.d(e,"w",(function(){return B})),n.d(e,"j",(function(){return F})),n.d(e,"v",(function(){return W})),n.d(e,"h",(function(){return q})),n.d(e,"l",(function(){return z})),n.d(e,"m",(function(){return J}));var r=n("b6bd");function a(t){return Object(r.a)({url:"merchant/store_list",method:"get",params:t})}function i(t){return Object(r.a)({url:"store/order/list",method:"get",params:t})}function o(t){return Object(r.a)({url:"store/order/chart",method:"get",params:t})}function s(t){return Object(r.a)({url:"store/refund/list",method:"get",params:t})}function c(t){return Object(r.a)({url:"/order/no_refund/".concat(t),method:"get"})}function l(t){return Object(r.a)({url:"/order/refund_integral/".concat(t),method:"get"})}function u(t){return Object(r.a)({url:"store/finance_flow/list",method:"get",params:t})}function d(t,e){return Object(r.a)({url:"store/finance_flow/mark/".concat(t),method:"put",params:e})}function f(t){return Object(r.a)({url:"store/finance_flow/fund_record",method:"get",params:t})}function h(t){return Object(r.a)({url:"store/finance_flow/fund_record_info",method:"get",params:t})}function m(t){return Object(r.a)({url:"/export/storeFinanceRecord",method:"get",params:t})}function p(t){return Object(r.a)({url:"/store/extract/list",method:"get",params:t})}function b(t,e){return Object(r.a)({url:"store/extract/mark/".concat(t),method:"post",data:e})}function g(t,e){return Object(r.a)({url:"store/extract/verify/".concat(t),method:"post",data:e})}function _(t){return Object(r.a)({url:"store/extract/transfer/".concat(t),method:"get"})}function v(t){return Object(r.a)({url:"store/store",method:"get",params:t})}function y(t){return Object(r.a)({url:"store/store/get_info/".concat(t),method:"get"})}function w(t){return Object(r.a)({url:"city",method:"get",params:t})}function O(t,e){return Object(r.a)({url:"store/store/".concat(t),method:"post",data:e})}function j(){return Object(r.a)({url:"store/store/address",method:"get"})}function x(t){return Object(r.a)({url:"store/store/login/".concat(t),method:"get"})}function k(t,e){return Object(r.a)({url:"store/store/set_show/".concat(t,"/").concat(e),method:"put"})}function C(t){return Object(r.a)({url:"store/share/order",method:"post",params:t})}function S(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function L(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function V(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function $(t){return Object(r.a)({url:"store/home/<USER>",method:"get",params:t})}function E(t){return Object(r.a)({url:"store/store/reset_admin/".concat(t),method:"get"})}function P(t,e,n){return Object(r.a)({url:"export/storeFlowExport?store_id=".concat(t,"&keyword=").concat(e,"&data=").concat(n),method:"get"})}function I(t){return Object(r.a)({url:"/store/category",params:t,method:"get"})}function T(t){return Object(r.a)({url:"/store/category/create/".concat(t),method:"get"})}function A(t){return Object(r.a)({url:"/store/category/tree/".concat(t),method:"get"})}function D(t){return Object(r.a)({url:"/store/category/set_show/".concat(t.id,"/").concat(t.is_show),method:"PUT"})}function N(t){return Object(r.a)({url:"store/category/cascader_list/".concat(t),method:"get"})}function R(t){return Object(r.a)({url:"/store/refund/detail/".concat(t),method:"get"})}function M(t){return Object(r.a)({url:"store/region",method:"get",params:t})}function B(t,e){return Object(r.a)({url:"store/region/set_alone/".concat(t,"/").concat(e),method:"put"})}function F(t){return Object(r.a)({url:"store/region/info/".concat(t),method:"get"})}function W(t,e){return Object(r.a)({url:"store/region/".concat(e),method:"post",data:t})}function q(t){return Object(r.a)({url:"store/all_region",method:"get",params:t})}function z(t){return Object(r.a)({url:"resolve/city",method:"get",params:t})}function J(t){return Object(r.a)({url:"store/region/city",method:"get",params:t})}},"759c":function(t,e,n){"use strict";var r=n("d73b");n.n(r).a},"939d":function(t,e,n){},"9b1c":function(t,e,n){"use strict";var r=n("7364");n.n(r).a},c76c:function(t,e,n){"use strict";var r=n("939d");n.n(r).a},cf55:function(t,e,n){"use strict";var r=n("a34a"),a=n.n(r),i=n("2f62"),o=n("c4c8");function s(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function c(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function u(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var d={name:"index",props:{goodsType:{type:Number,default:0},chooseType:{type:Number,default:0},is_new:{type:String,default:""},diy:{type:Boolean,default:!1},isdiy:{type:Boolean,default:!1},ischeckbox:{type:Boolean,default:!1},datas:{type:Object,default:function(){return{}}},isIntegral:{type:Boolean,default:!1},isCard:{type:Boolean,default:!1}},data:function(){return{labelSelect:[],cateIds:[],treeSelect:[],formValidate:{page:1,limit:10,cate_id:"",store_name:"",is_new:this.is_new,store_label_id:"",product_type:"",choose_type:this.chooseType},total:0,loading:!1,grid:{xl:10,lg:10,md:12,sm:24,xs:24},tableList:[],currentid:0,productRow:{},images:[],productTypeSelect:[{id:0,label_name:"普通商品"},{id:6,label_name:"预约商品"}]}},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?l(n,!0).forEach((function(e){u(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):l(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},Object(i.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){},mounted:function(){this.goodsCategory(),this.getList(),this.getAllLabelApi()},methods:{getAllLabelApi:function(){var t=this;Object(o.b)().then((function(e){t.labelSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},goodsCategory:function(){var t=this;Object(o.h)({type:0,relation_id:0}).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},pageChange:function(t){var e=t.currentPage,n=t.pageSize;this.formValidate.page=e,this.formValidate.limit=n,this.getList()},getList:function(){var t=this;this.loading=!0,this.goodsType&&(this.formValidate.is_presale_product=0,this.formValidate.is_vip_product=0),this.formValidate.cate_id=this.cateIds[this.cateIds.length-1],this.isIntegral&&(this.formValidate.is_integral=1),this.isCard&&(this.formValidate.is_card=1),Object(o.i)(this.formValidate).then(function(){var e,n=(e=a.a.mark((function e(n){var r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(r=n.data.list).forEach((function(t){t.attrValue.forEach((function(e){e.store_name=e.suk,e.store_names=t.store_name,e.cate_name=t.cate_name,e.store_label=t.store_label}))})),t.tableList=r,t.total=n.data.count,t.loading=!1;case 5:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){c(i,r,a,o,s,"next",t)}function s(t){c(i,r,a,o,s,"throw",t)}o(void 0)}))});return function(t){return n.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},ok:function(){var t=this.$refs.xTree.getCheckboxRecords(),e=this.$refs.xTree.getCheckboxReserveRecords(),n=[],r=[];[].concat(s(t),s(e)).forEach((function(t){t.attrValue?t.attrValue.forEach((function(t){r.includes(t.id)||(n.push(t),r.push(t.id))})):r.includes(t.id)||(n.push(t),r.push(t.id))}));var a=[];n.forEach((function(t){t.hasOwnProperty("product_id")&&a.push(t)})),a.length>0?this.$emit("getProductId",a):this.$Message.warning("请先选择商品")},treeSearchs:function(t){this.cateIds=t,this.formValidate.page=1,this.getList()},userSearchs:function(){this.formValidate.page=1,this.getList()},clear:function(){this.productRow.id="",this.currentid=""}}},f=(n("759c"),n("2877")),h=Object(f.a)(d,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"goodList"},[n("Form",{ref:"formValidate",staticClass:"tabform",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition}},[n("Row",{attrs:{type:"flex",gutter:24}},[n("Col",t._b({},"Col",t.grid,!1),[n("FormItem",{attrs:{label:"商品分类：","label-for":"pid"}},[n("Cascader",{directives:[{name:"width",rawName:"v-width",value:"200px",expression:"'200px'"}],attrs:{data:t.treeSelect,placeholder:"请选择商品分类","change-on-select":"",filterable:""},on:{"on-change":t.treeSearchs}})],1),n("FormItem",{attrs:{label:"商品标签：","label-for":"pid"}},[n("Select",{attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.formValidate.store_label_id,callback:function(e){t.$set(t.formValidate,"store_label_id",e)},expression:"formValidate.store_label_id"}},t._l(t.labelSelect,(function(e){return n("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.label_name)+"\n\t\t\t\t\t\t")])})),1)],1)],1),n("Col",t._b({},"Col",t.grid,!1),[t.isCard?n("FormItem",{attrs:{label:"商品类型："}},[n("Select",{attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.formValidate.product_type,callback:function(e){t.$set(t.formValidate,"product_type",e)},expression:"formValidate.product_type"}},t._l(t.productTypeSelect,(function(e){return n("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.label_name)+"\n\t\t\t\t\t\t")])})),1)],1):t._e(),n("FormItem",{attrs:{label:"商品搜索：","label-for":"store_name"}},[n("Input",{staticStyle:{width:"240px"},attrs:{search:"","enter-button":"",placeholder:"请输入商品名称,关键字,编号"},on:{"on-search":t.userSearchs},model:{value:t.formValidate.store_name,callback:function(e){t.$set(t.formValidate,"store_name",e)},expression:"formValidate.store_name"}})],1)],1)],1)],1),n("div",{staticClass:"vxeTable"},[n("vxe-table",{ref:"xTree",attrs:{border:"inner","column-config":{resizable:!0},"row-id":"id","tree-config":{children:"attrValue",reserve:!0},data:t.tableList,"max-height":"400","checkbox-config":{reserve:!0}}},[n("vxe-column",{attrs:{type:"checkbox",title:"多选",width:"90","tree-node":""}}),n("vxe-column",{attrs:{field:"id",title:"商品ID",width:"80"}}),n("vxe-column",{attrs:{field:"image",title:"图片","min-width":"90"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;return[n("viewer",[n("div",{staticClass:"tabBox_img"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])]}}])}),n("vxe-column",{attrs:{field:"store_name",title:"商品名称","min-width":"190"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("Tooltip",{attrs:{"max-width":"500",placement:"bottom"}},[n("span",{staticClass:"line2"},[t._v(t._s(r.store_name))]),n("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(r.store_name))])])]}}])}),n("vxe-column",{attrs:{field:"product_type",title:"商品类型","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[0==r.product_type?n("span",[t._v("普通商品")]):t._e(),1==r.product_type?n("span",[t._v("卡密商品")]):t._e(),3==r.product_type?n("span",[t._v("虚拟商品")]):t._e(),4==r.product_type?n("span",[t._v("次卡商品")]):t._e(),5==r.product_type?n("span",[t._v("卡项商品")]):t._e(),6==r.product_type?n("span",[t._v("预约商品")]):t._e()]}}])}),n("vxe-column",{attrs:{field:"cate_name",title:"商品分类","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("Tooltip",{attrs:{"max-width":"200",placement:"bottom"}},[n("span",{staticClass:"line2"},[t._v(t._s(r.cate_name))]),n("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(r.cate_name))])])]}}])}),n("vxe-column",{attrs:{field:"store_label",title:"商品标签","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("Tooltip",{attrs:{"max-width":"500",placement:"bottom"}},[n("span",{staticClass:"line2"},[t._v(t._s(r.store_label))]),n("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(r.store_label))])])]}}])})],1),n("vxe-pager",{attrs:{border:"",size:"medium","page-size":t.formValidate.limit,"current-page":t.formValidate.page,total:t.total,layouts:["PrevPage","JumpNumber","NextPage","FullJump","Total"]},on:{"page-change":t.pageChange}})],1),n("div",{staticClass:"footer",attrs:{slot:"footer"},slot:"footer"},[n("Button",{attrs:{type:"primary",size:"large",long:""},on:{click:t.ok}},[t._v("提交")])],1)],1)}),[],!1,null,"14c8fa50",null);e.a=h.exports},d73b:function(t,e,n){},dd04:function(t,e,n){},e449:function(t,e,n){"use strict";var r=n("b7be"),a=n("61f7");function i(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var o={name:"index",filters:{formatDate:function(t){if(0!==t){var e=new Date(1e3*t);return Object(a.a)(e,"yyyy-MM-dd hh:mm")}}},props:{couponids:{type:Array},updateIds:{type:Array},updateName:{type:Array},luckDraw:{type:Boolean,default:!1},discount:{type:Boolean,default:!1}},data:function(){return{currentid:0,productRow:{},isTemplate:!1,loading:!1,tableFrom:{receive_type:3,type:"send",page:1,limit:10},total:0,ids:[],texts:[],columns:[{title:"ID",key:"id",width:60},{title:"优惠券名称",key:"title",minWidth:150},{title:"适用类型",slot:"type",minWidth:80},{title:"面值",slot:"coupon_price",minWidth:100},{title:"最低消费额",key:"use_min_price",minWidth:100},{title:"发布数量",slot:"count",minWidth:120},{title:"有效期限",slot:"start_time",minWidth:120},{title:"状态",slot:"status",minWidth:80}],couponList:[],selectedIds:new Set,selectedNames:new Set,couponVal:[]}},mounted:function(){},watch:{updateIds:function(t){this.selectedIds=new Set(t)},updateName:function(t){this.selectedNames=new Set(t)}},created:function(){var t=this,e={width:60,align:"center",render:function(e,n){var r=n.row.id,a=!1;a=t.currentid===r;var i=t;return e("div",[e("Radio",{props:{value:a},on:{"on-change":function(){i.currentid=r,t.productRow=n.row}}})])}};this.luckDraw?this.columns.unshift(e):this.columns.unshift({type:"selection",width:60,align:"center"})},methods:{unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},changeCheckbox:function(t){this.couponVal=t},handleSelectAll:function(t){var e=this;if(!this.discount){if(0===t.length){var n=this;n.$refs.table.data.forEach((function(t){if(n.selectedIds.has(t.id)){n.selectedIds.delete(t.id);var e=n.unique(Array.from(n.selectedNames));n.unique(Array.from(n.selectedNames)).forEach((function(n,r){n.id===t.id&&e.splice(r,1)})),n.selectedNames=new Set(e)}}))}else t.forEach((function(t){e.selectedIds.add(t.id),e.selectedNames.add({id:t.id,title:t.title})}));this.$nextTick((function(){e.setChecked()}))}},handleSelectRow:function(t,e){var n=this;this.discount||(this.selectedIds.add(e.id),this.selectedNames.add({id:e.id,title:e.title}),this.$nextTick((function(){n.setChecked()})))},handleCancelRow:function(t,e){var n=this;if(!this.discount){this.selectedIds.delete(e.id);var r=Array.from(this.selectedNames);Array.from(this.selectedNames).forEach((function(t,n){t.id===e.id&&r.splice(n,1)})),this.selectedNames=new Set(r),this.$nextTick((function(){n.setChecked()}))}},setChecked:function(){this.ids=i(this.selectedIds),this.texts=i(this.selectedNames);var t=this.$refs.table.objData;for(var e in t)this.selectedIds.has(t[e].id)&&(t[e]._isChecked=!0)},cancel:function(){this.isTemplate=!1,this.luckDraw&&(this.currentid=0)},tableList:function(){var t=this;this.loading=!0,Object(r.tb)(this.tableFrom).then((function(e){var n=e.data;t.couponList=n.list,t.discount||t.$nextTick((function(){t.setChecked()})),t.total=n.count,t.loading=!1}))},ok:function(){this.luckDraw?(this.$emit("getCouponId",this.productRow),this.currentid=0):this.discount?this.$emit("getCouponList",this.couponVal):this.$emit("nameId",this.ids,this.texts)},receivePageChange:function(t){this.tableFrom.page=t,this.tableList()}}},s=n("2877"),c=Object(s.a)(o,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Modal",{attrs:{title:"优惠券列表",width:"60%"},on:{"on-ok":t.ok,"on-cancel":t.cancel},model:{value:t.isTemplate,callback:function(e){t.isTemplate=e},expression:"isTemplate"}},[n("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.couponList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"on-select":t.handleSelectRow,"on-select-cancel":t.handleCancelRow,"on-select-all":t.handleSelectAll,"on-select-all-cancel":t.handleSelectAll,"on-selection-change":t.changeCheckbox},scopedSlots:t._u([{key:"coupon_price",fn:function(e){var r=e.row;return[1==r.coupon_type?n("span",[t._v(t._s(r.coupon_price)+"元")]):t._e(),2==r.coupon_type?n("span",[t._v(t._s(parseFloat(r.coupon_price)/10)+"折（"+t._s(r.coupon_price.toString().split(".")[0])+"%）")]):t._e()]}},{key:"count",fn:function(e){var r=e.row;return e.index,[r.is_permanent?n("span",[t._v("不限量")]):n("div",[n("span",{staticClass:"fa"},[t._v("发布："+t._s(r.total_count))]),n("span",{staticClass:"sheng"},[t._v("剩余："+t._s(r.remain_count))])])]}},{key:"start_time",fn:function(e){var r=e.row;return e.index,[r.start_time?n("div",[t._v("\n                        "+t._s(t._f("formatDate")(r.start_time))+" - "+t._s(t._f("formatDate")(r.end_time))+"\n                    ")]):n("span",[t._v("不限时")])]}},{key:"type",fn:function(e){var r=e.row;return[1===r.type?n("span",[t._v("品类券")]):2===r.type?n("span",[t._v("商品券")]):3===r.type?n("span",[t._v("会员券")]):n("span",[t._v("通用券")])]}},{key:"status",fn:function(e){var r=e.row;return e.index,[n("Tag",{directives:[{name:"show",rawName:"v-show",value:1===r.status,expression:"row.status===1"}],attrs:{color:"blue"}},[t._v("正常")]),n("Tag",{directives:[{name:"show",rawName:"v-show",value:0===r.status,expression:"row.status===0"}],attrs:{color:"gold"}},[t._v("未开启")]),n("Tag",{directives:[{name:"show",rawName:"v-show",value:-1===r.status,expression:"row.status=== -1"}],attrs:{color:"red"}},[t._v("已失效")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.tableFrom.limit},on:{"on-change":t.receivePageChange}})],1)],1)],1)}),[],!1,null,"65e1da4a",null);e.a=c.exports},f1c7:function(t,e,n){"use strict";var r=n("c4c8"),a={name:"storeLabelList",props:{},data:function(){return{labelList:[],dataLabel:[],isStore:!1}},mounted:function(){},methods:{inArray:function(t,e){for(var n in e)if(e[n].id===t)return!0;return!1},storeLabel:function(t){var e=this;this.dataLabel=t,Object(r.kb)().then((function(t){t.data.map((function(t){t.children&&t.children.length&&(e.isStore=!0,t.children.map((function(t){e.inArray(t.id,e.dataLabel)?t.disabled=!0:t.disabled=!1})))})),e.labelList=t.data})).catch((function(t){e.$Message.error(t.msg)}))},selectLabel:function(t){if(t.disabled){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id===t.id}))[0]);this.dataLabel.splice(e,1),t.disabled=!1}else this.dataLabel.push({label_name:t.label_name,id:t.id}),t.disabled=!0},subBtn:function(){this.$emit("activeData",JSON.parse(JSON.stringify(this.dataLabel)))},cancel:function(){this.$emit("close")}}},i=(n("f447"),n("2877")),o=Object(i.a)(a,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"label-wrapper"},[n("div",{staticClass:"list-box"},[t._l(t.labelList,(function(e,r){return t.isStore?n("div",{key:r,staticClass:"label-box"},[e.children&&e.children.length?n("div",{staticClass:"title"},[t._v(t._s(e.label_name))]):t._e(),e.children&&e.children.length?n("div",{staticClass:"list"},t._l(e.children,(function(e,r){return n("div",{key:r,staticClass:"label-item",class:{on:e.disabled},on:{click:function(n){return t.selectLabel(e)}}},[t._v(t._s(e.label_name))])})),0):t._e()]):t._e()})),t.isStore?t._e():n("div",[t._v("暂无标签")])],2),n("div",{staticClass:"footer"},[n("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:t.cancel}},[t._v("取消")]),n("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确定")])],1)])}),[],!1,null,"d65495b2",null);e.a=o.exports},f447:function(t,e,n){"use strict";var r=n("dd04");n.n(r).a}}]);