(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-fbe4c24c"],{"0b65":function(t,e,a){"use strict";e.a={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},"61f7":function(t,e,a){"use strict";function i(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function n(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?i(a,!0).forEach((function(e){s(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):i(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function s(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function r(t,e){/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var a={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var i in a)if(new RegExp("(".concat(i,")")).test(e)){var n=a[i]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?n:o(n))}return e}function o(t){return("00"+t).substr(t.length)}a.d(e,"a",(function(){return r})),a.d(e,"c",(function(){return h})),a.d(e,"b",(function(){return u}));var l={min:"%s最小长度为:min",max:"%s最大长度为:max",length:"%s长度必须为:length",range:"%s长度为:range",pattern:"$s格式错误"},c=function(t,e){t.message=function(t){return e.replace("%s",t||"")}};function h(t){return n({required:!0,message:t,type:"string"},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}function u(t){return d.pattern(/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,t)}c(h,"请输入%s"),c(u,"%s格式不正确");var d=Object.keys(l).reduce((function(t,e){return t[e]=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="range"===e?{min:t[0],max:t[1]}:s({},e,t);return n({message:a.replace(":".concat(e),"range"===e?"".concat(t[0],"-").concat(t[1]):t),type:"string"},r,{},i)},c(t[e],l[e]),t}),{})},"8dd3":function(t,e,a){"use strict";var i=a("b93c");a.n(i).a},"921a":function(t,e,a){"use strict";a.r(e);var i=a("a34a"),n=a.n(i),s=a("a584"),r=a("c71e"),o=a("b7be"),l=a("61f7"),c=a("9901"),h=a("0b65"),u=a("2f62");function d(t,e,a,i,n,s,r){try{var o=t[s](r),l=o.value}catch(t){return void a(t)}o.done?e(l):Promise.resolve(l).then(i,n)}function f(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function p(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var m={name:"index",components:{cardsData:s.a,echartsNew:r.a,echartsFrom:c.a},data:function(){return{timeVal:[],style:{height:"400px"},infoList:{},infoList2:{},echartLeft:0,echartRight:1,loading:!1,loading2:!1,fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"全部",val:""},{text:"今天",val:"today"},{text:"本周",val:"week"},{text:"本月",val:"month"},{text:"本季度",val:"quarter"},{text:"本年",val:"year"}]},formValidate:{time:""},cardLists:[{col:8,count:0,name:"当前积分",className:"icondangqianjifen",type:!0},{col:8,count:0,name:"累计总积分",className:"iconleijijifen",type:!0},{col:8,count:0,name:"累计消耗积分",className:"iconxiaohaojifen",type:!0}],optionData:{},spinShow:!1,options:h.a,columns:[{title:"序号",type:"index",width:60,align:"center"},{title:"来源",key:"name",minWidth:80,align:"center"},{title:"金额",width:180,key:"value",align:"center"},{title:"占比率",slot:"percent",minWidth:100,align:"center"}],tabList:[],tabList2:[]}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?f(a,!0).forEach((function(e){p(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):f(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(u.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){var t=new Date,e=new Date;e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),this.timeVal=[e,t],this.formValidate.time=Object(l.a)(e,"yyyy/MM/dd")+"-"+Object(l.a)(t,"yyyy/MM/dd"),this.onInit()},methods:{onInit:function(){this.getPointBasic(),this.getPointTrend(),this.getChannel(),this.getType()},onSelectDate:function(t){this.formValidate.time=t,this.onInit()},getPointBasic:function(){var t=this;Object(o.O)(this.formValidate).then((function(e){var a=["now_point","all_point","pay_point"];t.cardLists.map((function(t,i){t.count=e.data[a[i]]}))}))},getChannel:function(){var t=this;this.loading=!0,Object(o.I)(this.formValidate).then((function(e){t.infoList=e.data,t.tabList=e.data.list,t.loading=!1}))},getType:function(){var t=this;this.loading2=!0,Object(o.Q)(this.formValidate).then((function(e){t.infoList2=e.data,t.tabList2=e.data.list,t.loading2=!1}))},onchangeTime:function(t){this.timeVal=t,this.formValidate.time=this.timeVal[0]?this.timeVal.join("-"):"",this.name=this.formValidate.time,this.getPointBasic(),this.getPointTrend(),this.getChannel(),this.getType()},getPointTrend:function(){var t=this;this.spinShow=!0,Object(o.P)(this.formValidate).then(function(){var e,a=(e=n.a.mark((function e(a){var i,s,r,o;return n.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=a.data.series.map((function(t){return t.name})),s=a.data.xAxis,r=["#5B8FF9","#5AD8A6","#FFAB2B","#5D7092"],o=[],a.data.series.map((function(t,e){o.push({name:t.name,type:"line",data:t.data,itemStyle:{normal:{color:r[e]}},smooth:0})})),t.optionData={tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{x:"center",data:i},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},toolbox:{feature:{saveAsImage:{name:"积分使用_"+Object(l.a)(new Date(Number((new Date).getTime())),"yyyyMMddhhmmss")}}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:0,rotate:40,textStyle:{color:"#000000"}},data:s},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},series:o},t.spinShow=!1;case 7:case"end":return e.stop()}}),e)})),function(){var t=this,a=arguments;return new Promise((function(i,n){var s=e.apply(t,a);function r(t){d(s,i,n,r,o,"next",t)}function o(t){d(s,i,n,r,o,"throw",t)}r(void 0)}))});return function(t){return a.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg),t.spinShow=!1}))}}},g=(a("ba66"),a("2877")),b=Object(g.a)(m,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"box"},[a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[a("div",{staticClass:"new_card_pd"},[a("Form",{ref:"formValidate",attrs:{inline:"","label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"时间筛选："}},[a("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"daterange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1)]),t.cardLists.length>=0?a("cards-data",{attrs:{cardLists:t.cardLists}}):t._e(),a("Card",{attrs:{bordered:!1,"dis-hover":""}},[a("h3",[t._v("积分使用趋势")]),t.optionData?a("echarts-new",{attrs:{"option-data":t.optionData,styles:t.style,height:"100%",width:"100%"}}):t._e()],1),t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e(),a("Row",{attrs:{gutter:24}},[a("Col",{attrs:{xl:12,lg:12,md:24,sm:24,xs:24}},[a("Card",{staticClass:"ivu-mt cardH",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"acea-row row-between-wrapper"},[a("h3",{staticClass:"header-title"},[t._v("积分来源分析")]),a("RadioGroup",{attrs:{type:"button"},model:{value:t.echartLeft,callback:function(e){t.echartLeft=e},expression:"echartLeft"}},[a("Radio",{attrs:{label:0}},[a("span",{staticClass:"iconfont icontongji"})]),a("Radio",{attrs:{label:1}},[a("span",{staticClass:"iconfont iconbiaoge1"})])],1)],1),a("div",{staticClass:"ech-box"},[t.echartLeft?t._e():a("echarts-from",{ref:"visitChart",attrs:{infoList:t.infoList,echartsTitle:"circle"}}),t.echartLeft?a("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tabList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"percent",fn:function(e){var i=e.row;return[a("div",{staticClass:"percent-box"},[a("div",{staticClass:"line"},[a("div",{staticClass:"bg"}),a("div",{staticClass:"percent",style:"width:"+i.percent+"%;"})]),a("div",{staticClass:"num"},[t._v(t._s(i.percent)+"%")])])]}}],null,!1,2753725256)}):t._e()],1)])],1),a("Col",{attrs:{xl:12,lg:12,md:24,sm:24,xs:24}},[a("Card",{staticClass:"ivu-mt cardH",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"acea-row row-between-wrapper"},[a("h3",{staticClass:"header-title"},[t._v("积分消耗")]),a("RadioGroup",{attrs:{type:"button"},model:{value:t.echartRight,callback:function(e){t.echartRight=e},expression:"echartRight"}},[a("Radio",{attrs:{label:0}},[a("span",{staticClass:"iconfont icontongji"})]),a("Radio",{attrs:{label:1}},[a("span",{staticClass:"iconfont iconbiaoge1"})])],1)],1),a("div",{staticClass:"ech-box"},[t.echartRight?t._e():a("echarts-from",{ref:"visitChart",attrs:{infoList:t.infoList2,echartsTitle:"circle"}}),a("Table",{directives:[{name:"show",rawName:"v-show",value:t.echartRight,expression:"echartRight"}],ref:"selection",attrs:{columns:t.columns,data:t.tabList2,loading:t.loading2,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"percent",fn:function(e){var i=e.row;return[a("div",{staticClass:"percent-box"},[a("div",{staticClass:"line"},[a("div",{staticClass:"bg"}),a("div",{staticClass:"percent",style:"width:"+i.percent+"%;"})]),a("div",{staticClass:"num"},[t._v(t._s(i.percent)+"%")])])]}}])})],1)])],1)],1)],1)}),[],!1,null,"62d5d28e",null);e.default=b.exports},9901:function(t,e,a){"use strict";var i=a("313e"),n=a.n(i),s={name:"index",props:{infoList:{type:Object,default:null},styles:{type:Object,default:null},series:Array,echartsTitle:{type:String,default:""},yAxisData:{type:Array,default:function(){return[]}},bingXdata:Array},data:function(){return{infoLists:this.infoList,seriesArray:this.series}},watch:{infoList:{handler:function(t,e){this.infoLists=t,this.handleSetVisitChart()},deep:!0},series:{handler:function(t,e){this.seriesArray=t,this.handleSetVisitChart()},deep:!0}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){var t=this;this.myChart=n.a.init(document.getElementById(this.echarts));var e=null;e="circle"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:this.infoLists.bing_xdata||[]},series:[{name:"",type:"pie",radius:"60%",center:["50%","50%"],data:this.infoLists.bing_data||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}:"circle1"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{b} : {c} ({d}%)"},legend:{icon:"circle",top:"5%",left:"center",fontSize:"12",data:this.infoLists.bing_xdata||[]},series:[{name:"访问来源",type:"pie",radius:["30%","60%"],avoidLabelOverlap:!1,label:{show:!0,formatter:"{d}%",position:"inner",fontSize:"12"},emphasis:{label:{show:!0,fontSize:"15",fontWeight:"bold"}},labelLine:{show:!1},data:this.infoLists.bing_data||[]}]}:"inlie"===this.echartsTitle?{tooltip:{trigger:"axis"},toolbox:{},legend:{icon:"line",left:"left",fontWeight:"100",data:this.infoLists.legend||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.infoLists.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray}:{tooltip:{trigger:"axis"},toolbox:{},legend:{data:this.infoLists.legend||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.infoLists.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray},setTimeout((function(){t.wsFunc(),t.myChart.setOption(e,!0)}),200)},handleResize:function(){this.myChart.resize()}},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)}},r=(a("8dd3"),a("2877")),o=Object(r.a)(s,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"styles",style:this.styles,attrs:{id:this.echarts}})])}),[],!1,null,"e03fd378",null);e.a=o.exports},"9e6d":function(t,e,a){},a584:function(t,e,a){"use strict";var i;function n(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var s=(n(i={name:"cards",data:function(){return{}},props:{cardLists:Array}},"data",(function(){return{}})),n(i,"methods",{}),n(i,"created",(function(){})),i),r=(a("e83b"),a("2877")),o=Object(r.a)(s,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Row",{staticClass:"ivu-mt",attrs:{type:"flex",align:"middle",gutter:16}},t._l(t.cardLists,(function(e,i){return a("Col",{key:i,staticClass:"ivu-mb",attrs:{xs:24,sm:24,md:12,lg:12,xl:8,xxl:{span:e.col}}},[a("Card",{staticClass:"card_cent",attrs:{shadow:"",padding:0}},[a("div",{staticClass:"card_box"},[a("div",{staticClass:"card_box_cir",class:{one:i%5==0,two:i%5==1,three:i%5==2,four:i%5==3,five:i%5==4}},[a("div",{staticClass:"card_box_cir1",class:{one1:i%5==0,two1:i%5==1,three1:i%5==2,four1:i%5==3,five1:i%5==4}},[e.type?a("span",{staticClass:"iconfont",class:e.className}):a("Icon",{attrs:{type:e.className}})],1)]),a("div",{staticClass:"card_box_txt"},[a("span",{staticClass:"sp1",domProps:{textContent:t._s(e.count||0)}}),a("span",{staticClass:"sp2",domProps:{textContent:t._s(e.name)}})])])])],1)})),1)],1)}),[],!1,null,"566bac57",null);e.a=o.exports},b93c:function(t,e,a){},ba66:function(t,e,a){"use strict";var i=a("9e6d");a.n(i).a},c71e:function(t,e,a){"use strict";var i=a("313e"),n=a.n(i),s={name:"Index",props:{styles:{type:Object,default:null},optionData:{type:Object,default:null}},data:function(){return{myChart:null}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},watch:{optionData:{handler:function(t,e){this.handleSetVisitChart()},deep:!0}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){var t;this.myChart=n.a.init(document.getElementById(this.echarts)),t=this.optionData,this.myChart.setOption(t,!0)}}},r=a("2877"),o=Object(r.a)(s,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{style:this.styles,attrs:{id:this.echarts}})])}),[],!1,null,"458f4634",null);e.a=o.exports},c72b:function(t,e,a){},e83b:function(t,e,a){"use strict";var i=a("c72b");a.n(i).a}}]);