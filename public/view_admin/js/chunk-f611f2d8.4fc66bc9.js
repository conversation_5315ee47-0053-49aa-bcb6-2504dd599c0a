(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f611f2d8"],{"3f28":function(t,e,r){"use strict";var a=r("740d");r.n(a).a},"740d":function(t,e,r){},"775b":function(t,e,r){"use strict";r.r(e);var a=r("2f62"),o=r("c4c8"),i=r("d708");function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(r,!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var d={name:"specsAdd",data:function(){var t=this;return{roterPre:i.a.roterPre,grid:{xl:7,lg:7,md:12,sm:24,xs:24},formValidate:{id:0,name:"",sort:0,specs:[]},ruleValidate:{name:[{required:!0,message:"请输入参数模板名称",trigger:"blur"}]},columns:[{title:"参数名称",key:"name",width:150,render:function(e,r){return e("div",[e("Input",{props:{value:r.row.name,placeholder:"请输入参数名称"},on:{"on-change":function(e){r.row.name=e.target.value,t.data[r.index].name=e.target.value}}})])}},{title:"参数值",key:"value",width:300,render:function(e,r){return e("div",[e("Input",{props:{value:r.row.value,placeholder:"请输入参数值"},on:{"on-change":function(e){r.row.value=e.target.value,t.data[r.index].value=e.target.value}}})])}},{title:"排序",key:"sort",width:100,render:function(e,r){return e("div",[e("InputNumber",{props:{value:parseInt(r.row.sort)||0,placeholder:"排序",precision:0},on:{"on-change":function(e){r.row.sort=e,t.data[r.index].sort=e}}})])}},{title:"操作",slot:"action",minWidth:120}],data:[]}},computed:s({},Object(a.e)("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.add(),this.getInfo()},mounted:function(){this.setCopyrightShow({value:!1})},destroyed:function(){this.setCopyrightShow({value:!0})},methods:s({},Object(a.d)("admin/layout",["setCopyrightShow"]),{getInfo:function(){var t=this;Object(o.ib)(this.$route.params.id).then((function(e){t.formValidate=e.data,t.data=e.data.specs}))},del:function(t){this.data.splice(t,1)},add:function(){this.data.push({name:"",value:"",sort:0})},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(t){e.formValidate.id=e.$route.params.id,e.formValidate.specs=e.data;for(var r=0;r<e.formValidate.specs.length;r++){var a=e.formValidate.specs[r];if(!a.name.trim())return e.$Message.error("请输入参数名称");if(!a.value.trim())return e.$Message.error("请输入参数值")}Object(o.hb)(e.formValidate).then((function(t){e.$Message.success(t.msg),e.$router.push({path:e.roterPre+"/product/specs"})})).catch((function(t){e.$Message.error(t.msg)}))}else e.$Message.error("请输入参数模板名称")}))}})},c=(r("3f28"),r("2877")),u=Object(c.a)(d,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"form-submit"},[r("div",{staticClass:"i-layout-page-header"},[r("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[r("div",{attrs:{slot:"title"},slot:"title"},[r("router-link",{attrs:{to:{path:t.roterPre+"/product/specs"}}},[r("div",{staticClass:"font-sm after-line"},[r("span",{staticClass:"iconfont iconfanhui"}),r("span",{staticClass:"pl10"},[t._v("返回")])])]),r("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s("0"!==t.$route.params.id?"编辑商品参数模板":"添加商品参数模板")}})],1)])],1),r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{attrs:{gutter:24,type:"flex"}},[r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"参数模板名称：",prop:"name"}},[r("Input",{directives:[{name:"width",rawName:"v-width",value:"400",expression:"'400'"}],attrs:{placeholder:"请输入参数模板名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"排序：",props:""}},[r("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"200",expression:"'200'"}],attrs:{placeholder:"排序","element-id":"sort",min:1,precision:0},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),r("Col",{attrs:{span:"24"}},[r("FormItem",{attrs:{label:"",props:""}},[r("Table",{ref:"table",staticClass:"table",attrs:{border:"",columns:t.columns,data:t.data,width:"700"},scopedSlots:t._u([{key:"action",fn:function(e){e.row;var a=e.index;return[a>0?r("a",{on:{click:function(e){return t.del(a)}}},[t._v("删除")]):t._e()]}}])}),r("Button",{staticClass:"mt20",on:{click:t.add}},[t._v("添加参数")])],1)],1)],1)],1)],1),r("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[r("Form",[r("FormItem",[r("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")])],1)],1)],1)],1)}),[],!1,null,"562d072e",null);e.default=u.exports}}]);