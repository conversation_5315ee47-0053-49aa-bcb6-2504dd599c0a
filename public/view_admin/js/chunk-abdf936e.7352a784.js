(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-abdf936e"],{a5df:function(t,e,a){"use strict";var r=a("aacb");a.n(r).a},aacb:function(t,e,a){},b982:function(t,e,a){"use strict";a.r(e);var r=a("a34a"),i=a.n(r),s=a("2f62"),n=a("ed08"),o=a("c4ad"),l=a("b0e7"),c=a("5334"),d=a("b7be"),u=a("c4c8"),m=a("a069"),p=a("d708");function f(t,e,a,r,i,s,n){try{var o=t[s](n),l=o.value}catch(t){return void a(t)}o.done?e(l):Promise.resolve(l).then(r,i)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var s=t.apply(e,a);function n(t){f(s,r,i,n,o,"next",t)}function o(t){f(s,r,i,n,o,"throw",t)}n(void 0)}))}}function h(t){return function(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function v(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function b(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var _={name:"storeBargainCreate",components:{goodsList:o.default,uploadPictures:l.a,WangEditor:m.a,freightTemplate:c.a},data:function(){return{roterPre:p.a.roterPre,template:!1,submitOpen:!1,spinShow:!1,isChoice:"",current:0,modalPic:!1,grid:{xl:12,lg:20,md:24,sm:24,xs:24},grid2:{xl:8,lg:8,md:12,sm:24,xs:24},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},modals:!1,modal_loading:!1,images:[],templateList:[],columns:[],specsData:[],unitNameList:[],formValidate:{is_support_refund:0,product_type:0,freight:1,delivery_type:[],images:[],info:"",title:"",store_name:"",image:"",unit_name:"",price:0,min_price:0,bargain_max_price:10,bargain_min_price:.01,cost:0,bargain_num:1,people_num:2,stock:1,sales:0,sort:0,num:1,give_integral:0,postage:0,is_postage:0,is_hot:0,status:0,section_time:[],description:"",rule:"",id:0,product_id:0,temp_id:"",attrs:[],items:[]},rule:"",description:"",ruleValidate:{image:[{required:!0,message:"请选择主图",trigger:"change"}],images:[{required:!0,type:"array",message:"请选择主图",trigger:"change"},{type:"array",min:1,message:"Choose two hobbies at best",trigger:"change"}],title:[{required:!0,message:"请输入砍价活动名称",trigger:"blur"}],info:[{required:!0,message:"请输入砍价活动简介",trigger:"blur"}],store_name:[{required:!0,message:"请输入砍价商品名称",trigger:"blur"}],section_time:[{required:!0,type:"array",message:"请选择活动时间",trigger:"change"}],unit_name:[{required:!0,message:"请输入单位",trigger:"change"}],price:[{required:!0,type:"number",message:"请输入划线价",trigger:"blur"}],min_price:[{required:!0,type:"number",message:"请输入最低购买价",trigger:"blur"}],cost:[{required:!0,type:"number",message:"请输入成本价",trigger:"blur"}],bargain_num:[{required:!0,type:"number",message:"请输入帮砍次数",trigger:"blur"}],people_num:[{required:!0,type:"number",message:"请输入砍价人数",trigger:"blur"}],stock:[{required:!0,type:"number",message:"请输入库存",trigger:"blur"}],num:[{required:!0,type:"number",message:"请输入单次允许购买数量",trigger:"blur"}]},currentid:"",picTit:"",tableIndex:0,copy:0}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?v(a,!0).forEach((function(e){b(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):v(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(s.e)("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:135},labelPosition:function(){return this.isMobile?"top":"right"}}),mounted:function(){"0"!==this.$route.params.id&&this.$route.params.id&&(this.copy=this.$route.params.copy,this.current=1,this.getInfo()),this.getAllUnit()},methods:{changeTemplate:function(t){this.template=t},addTemp:function(){this.$refs.templates.isTemplate=!0},addUnit:function(){var t=this;this.$modalForm(Object(u.mb)()).then((function(){return t.getAllUnit()}))},getAllUnit:function(){var t=this;Object(u.B)().then((function(e){t.unitNameList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getEditorContent:function(t){this.formValidate.description=t},getEditorContent2:function(t){this.formValidate.rule=t},productAttrs:function(t){var e=this,a=this;Object(d.sb)(t.id,2).then((function(t){var r=t.data.info,i={title:"选择",key:"chose",width:60,align:"center",render:function(t,a){var r=a.index,i=!1;i=e.currentid===r;var s=e;return t("div",[t("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=r;var t=[];t.push(a.row),s.formValidate.attrs=t}}})])}};a.columns=r.header,a.columns.unshift(i),a.specsData=r.attrs,a.formValidate.items=r.items})).catch((function(t){a.$Message.error(t.msg)}))},productGetTemplate:function(t){var e=this;Object(u.R)({id:t}).then((function(t){e.templateList=t.data}))},getProductId:function(t){var e=this;this.modal_loading=!1,this.modals=!1,setTimeout((function(){e.formValidate={is_support_refund:t.is_support_refund,product_type:t.product_type,images:t.slider_image,info:t.store_info,title:t.store_name,store_name:t.store_name,image:t.image,unit_name:t.unit_name,price:0,min_price:0,bargain_max_price:10,bargain_min_price:.01,cost:t.cost,bargain_num:1,people_num:2,stock:t.stock,sales:t.sales,sort:t.sort,num:1,give_integral:t.give_integral,postage:t.postage,is_postage:t.is_postage,is_hot:t.is_hot,status:0,section_time:[],description:t.description,rule:"",id:0,product_id:t.id,temp_id:t.temp_id,freight:1,delivery_type:[]},e.description=t.description,e.rule=t.rule,e.productGetTemplate(t.id),e.productAttrs(t)}),500)},cancel:function(){this.modals=!1},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var a=h(this.formValidate.images),r=a.indexOf(this.dragging),i=a.indexOf(e);a.splice.apply(a,[i,0].concat(h(a.splice(r,1)))),this.formValidate.images=a}},onchangeTime:function(t){this.formValidate.section_time=t},getInfo:function(){var t=this;this.spinShow=!0,Object(d.k)(this.$route.params.id).then(function(){var e=g(i.a.mark((function e(a){var r,s,n,o;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t,s=a.data.info,t.formValidate=s,t.description=s.description,t.rule=s.rule,t.formValidate.rule=null===s.rule?"":s.rule,t.$set(t.formValidate,"items",s.attrs.items),t.columns=s.attrs.header,n={title:"选择",key:"chose",width:60,align:"center",render:function(e,a){var r=a.index,i=!1;i=t.currentid===r;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=r;var t=[];t.push(a.row),s.formValidate.attrs=t}}})])}},r.columns.unshift(n),t.specsData=s.attrs.value,o=[],s.attrs.value.forEach((function(t,e){t.opt&&(o.push(t),r.$set(r,"currentid",e),r.$set(r.formValidate,"attrs",o))})),t.spinShow=!1;case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))},next:Object(n.a)((function(t){var e=this;3===this.current?(this.rule=this.formValidate.rule,this.$refs[t].validate((function(t){if(!t)return!1;if(""===e.currentid)return e.$Message.error("请选择属性规格");var a=e.specsData[e.currentid],r=e.formValidate.attrs[0];if(r.price=a.price,r.min_price=a.min_price,r.quota=a.quota,e.formValidate.attrs[0].quota<=0)return e.$Message.error("砍价限量必须大于0");1==e.copy&&(e.formValidate.copy=1),e.formValidate.id=e.$route.params.id||0,e.submitOpen=!0,e.formValidate.product_type||(e.formValidate.is_support_refund=1),Object(d.j)(e.formValidate).then(function(){var t=g(i.a.mark((function t(a){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.submitOpen=!1,e.$Message.success(a.msg),setTimeout((function(){e.$router.push({path:"".concat(e.roterPre,"/marketing/store_bargain/index")})}),500);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.submitOpen=!1,e.$Message.error(t.msg)}))}))):2===this.current?(this.description=this.formValidate.description,this.rule=this.formValidate.rule,this.current+=1):1===this.current?this.$refs[t].validate((function(t){return t?1==e.formValidate.people_num?e.$Message.error("砍价人数必须大于1"):0!=e.formValidate.product_type||e.formValidate.delivery_type.length?0==e.formValidate.product_type&&2==e.formValidate.freight&&e.formValidate.postage<=0?e.$Message.warning("物流设置-固定邮费不能为0"):0!=e.formValidate.product_type||3!=e.formValidate.freight||e.formValidate.temp_id?void(e.current+=1):e.$Message.warning("物流设置-运费模板不能为空"):e.$Message.warning("请选择配送方式"):e.$Message.warning("请完善商品信息")})):this.formValidate.image?this.current+=1:this.$Message.warning("请选择商品")})),step:function(){this.current--},getContent:function(t){this.formValidate.description=t},getRole:function(t){this.formValidate.rule=t},modalPicTap:function(t,e,a){this.modalPic=!0,this.isChoice="dan"===t?"单选":"多选",this.picTit=e,this.tableIndex=a},getPic:function(t){switch(this.picTit){case"danFrom":this.formValidate.image=t.att_dir;break;default:this.specsData[this.tableIndex].pic=t.att_dir,this.formValidate.attrs[0].pic=t.att_dir}this.modalPic=!1},getPicD:function(t){var e=this;this.images=t,this.images.map((function(t){e.formValidate.images.push(t.att_dir),e.formValidate.images=e.formValidate.images.splice(0,10)})),this.modalPic=!1},handleRemove:function(t){this.images.splice(t,1),this.formValidate.images.splice(t,1)},changeGoods:function(){this.modals=!0,this.$refs.goodslist.getList(),this.$refs.goodslist.goodsCategory()},validate:function(t,e,a){!1===e&&this.$Message.error(a)},addCustomDialog:function(t){window.UE.registerUI("test-dialog",(function(t,e){var a=new window.UE.ui.Dialog({iframeUrl:"/admin/widget.images/index.html?fodder=dialog",editor:t,name:e,title:"上传图片",cssRules:"width:1200px;height:500px;padding:20px;"});return this.dialog=a,new window.UE.ui.Button({name:"dialog-button",title:"上传图片",cssRules:"background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;",onclick:function(){a.render(),a.open()}})}),37)}}},C=(a("a5df"),a("2877")),y=Object(C.a)(_,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"form-submit"},[a("div",{staticClass:"i-layout-page-header"},[a("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[a("div",{attrs:{slot:"title"},slot:"title"},[a("router-link",{attrs:{to:{path:t.roterPre+"/marketing/store_bargain/index"}}},[a("div",{staticClass:"font-sm after-line"},[a("span",{staticClass:"iconfont iconfanhui"}),a("span",{staticClass:"pl10"},[t._v("返回")])])]),a("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑砍价商品":"添加砍价商品")}})],1)])],1),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Row",{staticClass:"mt30 acea-row row-middle row-center",attrs:{type:"flex"}},[a("Col",{attrs:{span:"20"}},[a("Steps",{attrs:{current:t.current}},[a("Step",{attrs:{title:"选择砍价商品"}}),a("Step",{attrs:{title:"填写基础信息"}}),a("Step",{attrs:{title:"修改商品详情"}}),a("Step",{attrs:{title:"修改商品规则"}})],1)],1),a("Col",{attrs:{span:"23"}},[a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"lines mt25"})]),a("Form",{ref:"formValidate",staticClass:"form mt30",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},on:{"on-validate":t.validate},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{directives:[{name:"show",rawName:"v-show",value:0===t.current,expression:"current === 0"}],attrs:{label:"选择商品：",prop:"image_input"}},[a("div",{staticClass:"picBox",on:{click:t.changeGoods}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.image,expression:"formValidate.image"}]})]):a("div",{staticClass:"upLoad acea-row row-center-wrapper"},[a("Icon",{staticClass:"iconfonts",attrs:{type:"ios-camera-outline",size:"26"}})],1)])]),a("Row",{directives:[{name:"show",rawName:"v-show",value:1===t.current,expression:"current === 1"}],attrs:{type:"flex"}},[a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"砍价活动名称：",prop:"title","label-for":"title"}},[a("Input",{staticClass:"perW40",attrs:{placeholder:"请输入砍价活动名称","element-id":"title"},model:{value:t.formValidate.title,callback:function(e){t.$set(t.formValidate,"title",e)},expression:"formValidate.title"}})],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"砍价活动简介：",prop:"info","label-for":"info"}},[a("Input",{staticClass:"perW40",attrs:{placeholder:"请输入砍价活动简介",type:"textarea",rows:4,"element-id":"info"},model:{value:t.formValidate.info,callback:function(e){t.$set(t.formValidate,"info",e)},expression:"formValidate.info"}})],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"单位：",prop:"unit_name","label-for":"unit_name"}},[a("Select",{staticClass:"perW40",attrs:{clearable:"",placeholder:"请输入单位"},model:{value:t.formValidate.unit_name,callback:function(e){t.$set(t.formValidate,"unit_name",e)},expression:"formValidate.unit_name"}},t._l(t.unitNameList,(function(e,r){return a("Option",{key:r,attrs:{value:e.name}},[t._v(t._s(e.name))])})),1),a("span",{staticClass:"addClass",on:{click:t.addUnit}},[t._v("新增单位")])],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{prop:"images"}},[a("div",{staticClass:"custom-label",attrs:{slot:"label"},slot:"label"},[a("div",[a("div",[t._v("商品轮播图")])]),a("div",[t._v("：")])]),a("div",{staticClass:"acea-row"},[t._l(t.formValidate.images,(function(e,r){return a("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e,expression:"item"}]}),a("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){return t.handleRemove(r)}}})],1)})),t.formValidate.images.length<10?a("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap("duo")}}},[a("Icon",{staticClass:"iconfonts",attrs:{type:"ios-camera-outline",size:"26"}})],1):t._e()],2),a("div",{staticClass:"tips"},[t._v(" 建议尺寸：800 * 800px，可拖拽改变图片顺序，默认首张图为主图，最多上传10张")])])],1),a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"lines"})]),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动时间：",prop:"section_time"}},[a("div",{staticClass:"acea-row row-middle"},[a("DatePicker",{staticClass:"perW40",attrs:{editable:!1,type:"datetimerange",format:"yyyy-MM-dd HH:mm",placeholder:"请选择活动时间",value:t.formValidate.section_time},on:{"on-change":t.onchangeTime},model:{value:t.formValidate.section_time,callback:function(e){t.$set(t.formValidate,"section_time",e)},expression:"formValidate.section_time"}})],1),a("div",{staticClass:"tips"},[t._v("设置活动开启结束时间，用户可以在设置时间内发起参与砍价")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"砍价人数：",prop:"people_num","label-for":"people_num"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"perW40",attrs:{placeholder:"请输入砍价人数","element-id":"people_num",min:1,precision:0},model:{value:t.formValidate.people_num,callback:function(e){t.$set(t.formValidate,"people_num",e)},expression:"formValidate.people_num"}}),a("span",{staticClass:"ml10"},[t._v("人")])],1),a("div",{staticClass:"tips"},[t._v("需要多少人砍价成功")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"帮砍次数：",prop:"bargain_num","label-for":"bargain_num"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"perW40",attrs:{placeholder:"请输入帮砍次数","element-id":"bargain_num",min:1,precision:0},model:{value:t.formValidate.bargain_num,callback:function(e){t.$set(t.formValidate,"bargain_num",e)},expression:"formValidate.bargain_num"}}),a("span",{staticClass:"ml10"},[t._v("次")])],1),a("div",{staticClass:"tips"},[t._v("单个商品用户可以帮砍的次数，例：次数设置为1，甲和乙同时将商品A的砍价链接发给丙，丙只能帮甲或乙其中一个人砍价")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"购买数量限制：",prop:"num"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"perW40",attrs:{placeholder:"购买数量限制",precision:0,min:1},model:{value:t.formValidate.num,callback:function(e){t.$set(t.formValidate,"num",e)},expression:"formValidate.num"}})],1),a("div",{staticClass:"tips"},[t._v("单个活动每个用户发起砍价次数限制")])])],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"规格选择："}},[a("Table",{attrs:{data:t.specsData,columns:t.columns,border:""},scopedSlots:t._u([{key:"pic",fn:function(e){e.row;var r=e.index;return[a("div",{staticClass:"acea-row row-middle row-center-wrapper",on:{click:function(e){return t.modalPicTap("dan","danTable",r)}}},[t.specsData[r].pic?a("div",{staticClass:"pictrue pictrueTab"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.specsData[r].pic,expression:"specsData[index].pic"}]})]):a("div",{staticClass:"upLoad pictrueTab acea-row row-center-wrapper"},[a("Icon",{staticClass:"iconfonts",attrs:{type:"ios-camera-outline",size:"21"}})],1)])]}},{key:"price",fn:function(e){e.row;var r=e.index;return[a("InputNumber",{staticClass:"priceBox",attrs:{min:0,"active-change":""},model:{value:t.specsData[r].price,callback:function(e){t.$set(t.specsData[r],"price",e)},expression:"specsData[index].price"}})]}},{key:"min_price",fn:function(e){e.row;var r=e.index;return[a("InputNumber",{staticClass:"priceBox",attrs:{min:0,"active-change":""},model:{value:t.specsData[r].min_price,callback:function(e){t.$set(t.specsData[r],"min_price",e)},expression:"specsData[index].min_price"}})]}},{key:"quota",fn:function(e){e.row;var r=e.index;return[a("InputNumber",{staticClass:"priceBox",attrs:{min:1,"active-change":""},model:{value:t.specsData[r].quota,callback:function(e){t.$set(t.specsData[r],"quota",e)},expression:"specsData[index].quota"}})]}}])})],1)],1),a("Col",{attrs:{span:"24"}},[a("div",{staticClass:"lines"})]),0==t.formValidate.product_type?a("Col",[a("FormItem",{attrs:{label:"配送方式："}},[a("CheckboxGroup",{model:{value:t.formValidate.delivery_type,callback:function(e){t.$set(t.formValidate,"delivery_type",e)},expression:"formValidate.delivery_type"}},[a("Checkbox",{attrs:{label:"1"}},[t._v("快递")]),a("Checkbox",{attrs:{label:"3"}},[t._v("门店配送")]),a("Checkbox",{attrs:{label:"2"}},[t._v("到店核销")])],1)],1)],1):t._e(),0==t.formValidate.product_type?a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"运费设置："}},[a("RadioGroup",{model:{value:t.formValidate.freight,callback:function(e){t.$set(t.formValidate,"freight",e)},expression:"formValidate.freight"}},[a("Radio",{attrs:{label:1}},[t._v("包邮")]),a("Radio",{attrs:{label:2}},[t._v("固定邮费")]),a("Radio",{attrs:{label:3}},[t._v("运费模板")])],1)],1)],1):t._e(),2==t.formValidate.freight&&0==t.formValidate.product_type?a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"",prop:"freight"}},[a("div",{staticClass:"acea-row row-middle"},[a("InputNumber",{staticClass:"perW20 maxW",attrs:{min:0,placeholder:"请输入金额"},model:{value:t.formValidate.postage,callback:function(e){t.$set(t.formValidate,"postage",e)},expression:"formValidate.postage"}}),a("span",{staticClass:"ml10"},[t._v("元")])],1)])],1):t._e(),3==t.formValidate.freight&&0==t.formValidate.product_type?a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"",prop:""}},[a("div",{staticClass:"acea-row"},[a("Select",{staticClass:"perW20 maxW",attrs:{clearable:""},model:{value:t.formValidate.temp_id,callback:function(e){t.$set(t.formValidate,"temp_id",e)},expression:"formValidate.temp_id"}},t._l(t.templateList,(function(e,r){return a("Option",{key:r,attrs:{value:e.id}},[t._v(t._s(e.name))])})),1),a("Button",{staticClass:"ml15",on:{click:t.addTemp}},[t._v("添加运费模板")])],1)])],1):t._e(),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"排序："}},[a("InputNumber",{staticClass:"perW20 maxW",attrs:{placeholder:"请输入排序","element-id":"sort",precision:0,min:0},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"活动状态：",props:"status","label-for":"status"}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1),t.formValidate.product_type?a("Col",{attrs:{span:"24"}},[a("FormItem",{attrs:{label:"支持退款：",props:"status","label-for":"status"}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formValidate.is_support_refund,callback:function(e){t.$set(t.formValidate,"is_support_refund",e)},expression:"formValidate.is_support_refund"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1):t._e()],1),2===t.current?a("div",[a("FormItem",{attrs:{label:"内容："}},[a("WangEditor",{key:1,staticStyle:{width:"90%"},attrs:{content:t.description},on:{editorContent:t.getEditorContent}})],1)],1):t._e(),3===t.current?a("div",[a("FormItem",{attrs:{label:"规则："}},[a("WangEditor",{key:2,staticStyle:{width:"90%"},attrs:{content:t.rule},on:{editorContent:t.getEditorContent2}})],1)],1):t._e(),t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e()],1)],1)],1)],1),a("Card",{staticClass:"fixed-card",style:{left:t.menuCollapse?t.isMobile?"0":"60px":"236px"},attrs:{bordered:!1,"dis-hover":""}},[a("Form",[a("FormItem",[a("Button",{directives:[{name:"show",rawName:"v-show",value:0!==t.current,expression:"current !== 0"}],staticClass:"submission mr15",attrs:{disabled:t.$route.params.id&&"0"!==t.$route.params.id&&1===t.current},on:{click:t.step}},[t._v("上一步")]),a("Button",{staticClass:"submission",attrs:{type:"primary"},domProps:{textContent:t._s(3===t.current?"提交":"下一步")},on:{click:function(e){return t.next("formValidate")}}})],1)],1)],1),a("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("goods-list",{ref:"goodslist",attrs:{chooseType:2},on:{getProductId:t.getProductId}})],1),a("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"上传商品图","mask-closable":!1,"z-index":1},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?a("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic,getPicD:t.getPicD}}):t._e()],1),a("freightTemplate",{ref:"templates",attrs:{template:t.template},on:{changeTemplate:t.changeTemplate}})],1)}),[],!1,null,"70a0671d",null);e.default=y.exports},ed08:function(t,e,a){"use strict";function r(t,e){if(t.length!==e.length)return!1;for(var a=t.slice().sort(),r=e.slice().sort(),i=0;i<a.length;i++)if(a[i]!==r[i])return!1;return!0}a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return i}));var i=function(t,e){var a,r=e||500;return function(){var e=this,i=arguments;a&&clearTimeout(a),a=setTimeout((function(){a=null,t.apply(e,i)}),r)}}}}]);