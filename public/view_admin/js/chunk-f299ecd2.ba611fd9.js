(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f299ecd2"],{"0a1e":function(e,t,r){},"370a":function(e,t,r){"use strict";r.r(t);var n=r("a34a"),a=r.n(n),i=r("a464"),o=r("f8b7"),s=r("2f62"),c=r("31b4"),l=r("fc48"),d=r("61f8"),u=r("417c"),m=r("d616"),f=r("a716"),p=r("d708");function h(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function v(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){h(i,n,a,o,s,"next",e)}function s(e){h(i,n,a,o,s,"throw",e)}o(void 0)}))}}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(r,!0).forEach((function(t){b(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function b(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var g={name:"table_list",components:{expandRow:i.a,editFrom:c.a,detailsFrom:l.a,orderRemark:d.a,orderRecord:u.a,orderSend:m.a,userDetails:f.a},props:["where","isAll"],data:function(){var e=this;return{roterPre:p.a.roterPre,delfromData:{},modal:!1,orderList:[],orderCards:[],loading:!1,orderId:0,columns:[{type:"expand",width:30,render:function(e,t){return e(i.a,{props:{row:t.row}})}},{width:50,align:"center",renderHeader:function(t,r){return t("div",{class:{"select-panel":!0},on:{mouseenter:function(t){e.display="block"},mouseleave:function(t){e.display="none"}}},[t("Checkbox",{props:{value:e.checkBox},on:{"on-change":function(t){e.checkBox=t,e.$refs.table.selectAll(e.checkBox),e.$emit("on-all",t?0:-1)}}}),t("div",{style:{position:"absolute",top:0,zIndex:2,display:e.display,width:"80px",height:"100%",padding:"0px 0",borderRadius:"4px",backgroundColor:"#fff",boxShadow:"0 0px 5px rgba(0, 0, 0, 0.2)",transform:"translateX(25%)"}},[t("div",{class:{"select-item":!0,on:0==e.isAll},style:{padding:"1px 6px",cursor:"pointer",height:"50%"},on:{click:function(t){0===e.isAll?(e.$emit("on-all",-1),e.checkBox=!1,e.$refs.table.selectAll(e.checkBox)):(e.$emit("on-all",0),e.formSelection.length||(e.checkBox=!0,e.$refs.table.selectAll(e.checkBox))),e.display="none"}}},"选择当页"),t("div",{class:{"select-item":!0,on:1==e.isAll},style:{padding:"1px 6px",cursor:"pointer",height:"50%"},on:{click:function(t){1===e.isAll?(e.isAll=-1,e.$emit("on-all",-1),e.checkBox=!1):(e.isAll=1,e.$emit("on-all",1),e.checkBox=!0),e.$refs.table.selectAll(e.checkBox),e.display="none"}}},"选择全部")])])},render:function(t,r){return t("Checkbox",{props:{value:r.row.checkBox},on:{"on-change":function(t){t?e.formSelection.push(r.row):(e.checkBox=!1,e.formSelection.forEach((function(t,n){t.id===r.row.id&&e.formSelection.splice(n,1)}))),e.$emit("on-all",e.formSelection.length?0:-1),r.row.checkBox=t,e.orderList[r.index].checkBox=t}},ref:"checkbox",refInFor:!0})}},{title:"订单号",align:"center",slot:"order_id",minWidth:150},{title:"订单类型",key:"pink_name",minWidth:120},{title:"用户信息",slot:"nickname",minWidth:100},{title:"商品信息",slot:"info",minWidth:330},{title:"实际支付",key:"pay_price",minWidth:70},{title:"支付时间",key:"_pay_time",minWidth:100},{title:"支付状态",key:"pay_type_name",minWidth:80},{title:"订单状态",key:"statusName",slot:"statusName",minWidth:120},{title:"操作",slot:"action",minWidth:150,align:"center"}],page:{total:0,pageNum:1,pageSize:10},data:[],FromData:null,orderDatalist:null,modalTitleSs:"",isDelIdList:[],checkBox:!1,formSelection:[],selectionCopy:[],display:"none",autoDisabled:!1,status:0}},computed:_({},Object(s.e)("admin/order",["orderPayType","orderStatus","orderTime","orderNum","fieldKey","orderType","orderChartType"])),mounted:function(){},created:function(){this.getList()},watch:{orderType:function(){this.page.pageNum=1,this.getList()},formSelection:function(e){this.$emit("order-select",e),e.length?this.$emit("auto-disabled",0):this.$emit("auto-disabled",1);var t=e.some((function(e){return 1===e.is_del}));this.getIsDel(t),this.getisDelIdListl(e)},orderList:{deep:!0,handler:function(e){var t=this;e.forEach((function(e){t.formSelection.forEach((function(t){t.id===e.id&&(e.checkBox=!0)}))}));var r=this.orderList.filter((function(e){return e.checkBox}));this.orderList.length?this.checkBox=this.orderList.length===r.length:this.checkBox=!1}}},methods:_({},Object(s.d)("admin/order",["getIsDel","getisDelIdListl"]),{selectAll:function(e){var t=this;e.length&&(this.formSelection=e,this.selectionCopy=e),this.selectionCopy.forEach((function(e,r){e.checkBox=t.checkBox,t.$set(t.orderList,r,e)}))},showUserInfo:function(e){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(e.uid)},changeMenu:function(e,t){var r=this;switch(this.orderId=e.id,t){case"1":this.delfromData={title:"修改立即支付",url:"/order/pay_offline/".concat(e.id),method:"post",ids:""},this.$modalSure(this.delfromData).then((function(e){r.$Message.success(e.msg),r.$emit("changeGetTabs"),r.getList()})).catch((function(e){r.$Message.error(e.msg)}));break;case"2":this.getData(e.id);break;case"3":this.$refs.record.modals=!0,this.$refs.record.getList(e.id);break;case"4":this.$refs.remarks.modals=!0;break;case"5":this.getOnlyRefundData(e.id,e.refund_type);break;case"55":this.getRefundData(e.id,e.refund_type);break;case"6":this.getRefundIntegral(e.id);break;case"7":this.getNoRefundData(e.id);break;case"8":this.delfromData={title:"修改确认收货",url:"/order/take/".concat(e.id),method:"put",ids:""},this.$modalSure(this.delfromData).then((function(e){r.$Message.success(e.msg),r.getList()})).catch((function(e){r.$Message.error(e.msg)}));break;case"10":this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(e.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){r.$Message.success(e.msg),r.$emit("changeGetTabs"),r.getList()})).catch((function(e){r.$Message.error(e.msg)}));break;case"11":this.delfromData={title:"立即打印电子面单",info:"您确认打印此电子面单吗?",url:"/order/order_dump/".concat(e.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){r.$Message.success(e.msg),r.getList()})).catch((function(e){r.$Message.error(e.msg)}));break;default:this.delfromData={title:"删除订单",url:"/order/del/".concat(e.id),method:"DELETE",ids:""},this.delOrder(e,this.delfromData)}},submitModel:function(){this.getList()},pageChange:function(e){this.page.pageNum=e,this.getList()},limitChange:function(e){this.page.pageSize=e,this.getList()},getOrderList:function(e){Object(o.ab)(e).then((function(e){}))},getList:function(e){var t=this;this.page.pageNum=1===e?1:this.page.pageNum,this.loading=!0,Object(o.ab)(this.$route.query.id).then(function(){var e=v(a.a.mark((function e(r){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=r.data,t.orderList=n.map((function(e){return 1===t.isAll?e.checkBox=!0:e.checkBox=!1,e})),t.orderCards=n.stat,t.page.total=n.count,t.$emit("on-changeCards",n.stat),t.loading=!1;case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},onSelectTab:function(e){this.formSelection=e;var t=e.some((function(e){return 1===e.is_del}));this.getIsDel(t),this.getisDelIdListl(e)},edit:function(e){this.getOrderData(e.id)},splitOrderDetail:function(e){this.$router.push({path:this.roterPre+"split_order",query:{id:e.id}})},delOrder:function(e,t){var r=this;1===e.is_del?this.$modalSure(t).then((function(e){r.$Message.success(e.msg),r.getList()})).catch((function(e){r.$Message.error(e.msg)})):this.$Modal.error({title:"错误！",content:"<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>"})},getOrderData:function(e){var t=this;Object(o.k)(e).then(function(){var e=v(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!1!==r.data.status){e.next=2;break}return e.abrupt("return",t.$authLapse(r.data));case 2:t.$authLapse(r.data),t.FromData=r.data,t.$refs.edits.modals=!0;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},getData:function(e){var t=this;Object(o.g)(e).then(function(){var e=v(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.$refs.detailss.modals=!0,t.orderDatalist=r.data,t.orderDatalist.orderInfo.refund_reason_wap_img)try{t.orderDatalist.orderInfo.refund_reason_wap_img=JSON.parse(t.orderDatalist.orderInfo.refund_reason_wap_img)}catch(e){t.orderDatalist.orderInfo.refund_reason_wap_img=[]}case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},submitFail:function(){this.status=0,this.getList()},getOnlyRefundData:function(e,t){var r=this;this.$modalForm(Object(o.q)(e)).then((function(){r.getList(),r.$emit("changeGetTabs")}))},getRefundData:function(e,t){var r=this;this.$route.query.orderChartType,this.delfromData={title:"是否立即退货退款",url:"/refund/agree/".concat(e),method:"get"},this.$modalSure(this.delfromData).then((function(e){r.$Message.success(e.msg),r.getList(),r.$emit("changeGetTabs")})).catch((function(e){r.$Message.error(e.msg)}))},getRefundIntegral:function(e){var t=this;Object(o.U)(e).then(function(){var e=v(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=r.data,t.$refs.edits.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},getNoRefundData:function(e){var t=this;this.$modalForm(Object(o.r)(e)).then((function(){t.getList(),t.$emit("changeGetTabs")}))},sendOrder:function(e){var t=this;this.$refs.send.modals=!0,this.orderId=e.id,this.status=e._status,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(r){t.$refs.send.getCartInfo(e._status,e.id)}))},delivery:function(e){var t=this;Object(o.h)(e.id).then(function(){var e=v(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.FromData=r.data,t.$refs.edits.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},change:function(e){},exportData:function(){this.$refs.table.exportCsv({filename:"商品列表"})},bindWrite:function(e){var t=this;this.$Modal.confirm({title:"提示",content:"确定要核销该订单吗？",cancelText:"取消",closable:!0,maskClosable:!0,onOk:function(){Object(o.eb)(e.order_id).then((function(e){t.$Message.success(e.msg),t.getList()}))},onCancel:function(){}})},onSelectCancel:function(e,t){}})},w=(r("3e13"),r("2877")),x=Object(w.a)(g,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"i-layout-page-header"},[r("PageHeader",{staticClass:"product_tabs",attrs:{title:"子订单列表","hidden-breadcrumb":""}})],1),r("Table",{ref:"table",staticClass:"orderData mt25",attrs:{columns:e.columns,data:e.orderList,loading:e.loading,"highlight-row":"","no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"},on:{"on-selection-change":e.onSelectTab,"on-select-all":e.selectAll,"on-select-all-cancel":e.selectAll,"on-select-cancel":e.onSelectCancel},scopedSlots:e._u([{key:"order_id",fn:function(t){var n=t.row;return t.index,[r("span",{staticStyle:{display:"block"},domProps:{textContent:e._s(n.order_id)}}),r("span",{directives:[{name:"show",rawName:"v-show",value:1===n.is_del,expression:"row.is_del === 1"}],staticClass:"span-del"},[e._v("用户已删除")])]}},{key:"nickname",fn:function(t){var n=t.row;return t.index,[r("a",{on:{click:function(t){return e.showUserInfo(n)}}},[e._v(e._s(n.nickname))])]}},{key:"info",fn:function(t){var n=t.row;return t.index,e._l(n._info,(function(t,n){return r("div",{key:n,staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.cart_info.productInfo.attrInfo?t.cart_info.productInfo.attrInfo.image:t.cart_info.productInfo.image,expression:"\n                val.cart_info.productInfo.attrInfo\n                  ? val.cart_info.productInfo.attrInfo.image\n                  : val.cart_info.productInfo.image\n              "}]})]),r("span",{staticClass:"tabBox_tit"},[e._v("\n            "+e._s(t.cart_info.productInfo.store_name+" | ")+e._s(t.cart_info.productInfo.attrInfo?t.cart_info.productInfo.attrInfo.suk:"")+"\n          ")]),r("span",{staticClass:"tabBox_pice"},[e._v(e._s("￥"+t.cart_info.truePrice+" x "+t.cart_info.cart_num))])])}))}},{key:"statusName",fn:function(t){var n=t.row;return t.index,[r("div",{staticClass:"pt5",domProps:{innerHTML:e._s(n.status_name.status_name)}}),r("div",{staticClass:"pictrue-box"},e._l(n.status_name.pics||[],(function(t,a){return n.status_name.pics?r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:a},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"item"}],staticClass:"pictrue mr10",attrs:{src:t}})]):e._e()})),0)]}},{key:"action",fn:function(t){var n=t.row;return t.index,[1===n._status?r("a",{on:{click:function(t){return e.edit(n)}}},[e._v("编辑")]):e._e(),2!==n._status&&8!==n._status||1!==n.shipping_type||null!==n.pinkStatus&&2!==n.pinkStatus?e._e():r("a",{on:{click:function(t){return e.sendOrder(n)}}},[e._v("发送货")]),4===n._status?r("a",{on:{click:function(t){return e.delivery(n)}}},[e._v("配送信息")]):e._e(),2==n.shipping_type&&0==n.status&&1==n.paid&&0===n.refund_status?r("a",{on:{click:function(t){return e.bindWrite(n)}}},[e._v("立即核销")]):e._e(),2===n._status||8===n._status?r("Divider",{attrs:{type:"vertical"}}):e._e(),8===n._status?r("a",{on:{click:function(t){return e.splitOrderDetail(n)}}},[e._v("查看子订单")]):e._e(),1===n._status||(2===n._status||8===n._status)&&(null===n.pinkStatus||2===n.pinkStatus)||4===n._status||2==n.shipping_type&&0==n.status&&1==n.paid&&0===n.refund_status?r("Divider",{attrs:{type:"vertical"}}):e._e(),[r("Dropdown",{on:{"on-click":function(t){return e.changeMenu(n,t)}}},[r("a",{attrs:{href:"javascript:void(0)"}},[e._v("\n              更多\n              "),r("Icon",{attrs:{type:"ios-arrow-down"}})],1),r("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:1===n._status&&0===n.paid&&"offline"===n.pay_type,expression:"\n                  row._status === 1 &&\n                  row.paid === 0 &&\n                  row.pay_type === 'offline'\n                "}],ref:"ones",attrs:{name:"1"}},[e._v("立即支付")]),r("DropdownItem",{attrs:{name:"2"}},[e._v("订单详情")]),r("DropdownItem",{attrs:{name:"3"}},[e._v("订单记录")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:n._status>=3&&n.express_dump,expression:"row._status >= 3 && row.express_dump"}],attrs:{name:"11"}},[e._v("电子面单打印")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:n._status>=2,expression:"row._status >= 2"}],attrs:{name:"10"}},[e._v("小票打印")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:1!==n._status||3===n._status&&n.use_integral>0&&n.use_integral>=n.back_integral,expression:"\n                  row._status !== 1 ||\n                  (row._status === 3 &&\n                    row.use_integral > 0 &&\n                    row.use_integral >= row.back_integral)\n                "}],attrs:{name:"4"}},[e._v("订单备注")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:2!=n.refund_type&&4!=n.refund_type&&6!=n.refund_type&&1==n.paid&&2!==n.refund_status&&parseFloat(n.pay_price)>0,expression:"row.refund_type != 2 && row.refund_type != 4 && row.refund_type != 6 && row.paid==1 && row.refund_status !==2 && parseFloat(row.pay_price) > 0"}],attrs:{name:"5"}},[e._v("立即退款")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:2==n.refund_type,expression:"row.refund_type == 2"}],attrs:{name:"55"}},[e._v("同意退货")]),r("DropdownItem",{directives:[{name:"show",rawName:"v-show",value:4===n._status,expression:"row._status === 4"}],attrs:{name:"8"}},[e._v("已收货")]),1==n.is_del?r("DropdownItem",{attrs:{name:"9"}},[e._v("删除订单")]):e._e()],1)],1)]]}}])}),r("div",{staticClass:"acea-row row-right page"}),r("edit-from",{ref:"edits",attrs:{FromData:e.FromData},on:{submitFail:e.submitFail}}),r("user-details",{ref:"userDetails"}),r("details-from",{ref:"detailss",attrs:{orderDatalist:e.orderDatalist,orderId:e.orderId}}),r("order-remark",{ref:"remarks",attrs:{orderId:e.orderId},on:{submitFail:e.submitFail}}),r("order-record",{ref:"record"}),r("order-send",{ref:"send",attrs:{orderId:e.orderId,status:e.status},on:{submitFail:e.submitFail}})],1)}),[],!1,null,"79dd0929",null);t.default=x.exports},"3e13":function(e,t,r){"use strict";var n=r("c84ab");r.n(n).a},"41e0":function(e,t,r){},"9cc7":function(e,t,r){"use strict";var n=r("0a1e");r.n(n).a},a464:function(e,t,r){"use strict";var n={name:"table-expand",props:{row:Object}},a=(r("9cc7"),r("2877")),i=Object(a.a)(n,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"tdinfo"},[r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[e._v("商品总价：")]),r("span",{staticClass:"expand-value",domProps:{textContent:e._s(e.row.total_price)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[e._v("下单时间：")]),r("span",{staticClass:"expand-value",domProps:{textContent:e._s(e.row.add_time)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[e._v("推广人：")]),r("span",{staticClass:"expand-value",domProps:{textContent:e._s(e.row.spread_nickname?e.row.spread_nickname:"无")}})])],1),r("Row",[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[e._v("用户备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:e._s(e.row.mark?e.row.mark:"无")}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[e._v("商家备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:e._s(e.row.remark?e.row.remark:"无")}})])],1)],1)}),[],!1,null,"b662070e",null);t.a=i.exports},add5:function(e,t,r){var n;window,n=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}({"./src/index.js":function(e,t,r){"use strict";r.r(t),r("./src/sass/index.scss");var n=r("./src/js/init.js").default.init;"undefined"!=typeof window&&(window.printJS=n),t.default=n},"./src/js/browser.js":function(e,t,r){"use strict";r.r(t);var n={isFirefox:function(){return"undefined"!=typeof InstallTrigger},isIE:function(){return-1!==navigator.userAgent.indexOf("MSIE")||!!document.documentMode},isEdge:function(){return!n.isIE()&&!!window.StyleMedia},isChrome:function(){return!!(arguments.length>0&&void 0!==arguments[0]?arguments[0]:window).chrome},isSafari:function(){return Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")>0||-1!==navigator.userAgent.toLowerCase().indexOf("safari")},isIOSChrome:function(){return-1!==navigator.userAgent.toLowerCase().indexOf("crios")}};t.default=n},"./src/js/functions.js":function(e,t,r){"use strict";r.r(t),r.d(t,"addWrapper",(function(){return o})),r.d(t,"capitalizePrint",(function(){return s})),r.d(t,"collectStyles",(function(){return c})),r.d(t,"addHeader",(function(){return d})),r.d(t,"cleanUp",(function(){return u})),r.d(t,"isRawHTML",(function(){return m}));var n=r("./src/js/modal.js"),a=r("./src/js/browser.js");function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return'<div style="font-family:'+t.font+" !important; font-size: "+t.font_size+' !important; width:100%;">'+e+"</div>"}function s(e){return e.charAt(0).toUpperCase()+e.slice(1)}function c(e,t){for(var r="",n=(document.defaultView||window).getComputedStyle(e,""),a=0;a<n.length;a++)(-1!==t.targetStyles.indexOf("*")||-1!==t.targetStyle.indexOf(n[a])||l(t.targetStyles,n[a]))&&n.getPropertyValue(n[a])&&(r+=n[a]+":"+n.getPropertyValue(n[a])+";");return r+"max-width: "+t.maxWidth+"px !important; font-size: "+t.font_size+" !important;"}function l(e,t){for(var r=0;r<e.length;r++)if("object"===i(t)&&-1!==t.indexOf(e[r]))return!0;return!1}function d(e,t){var r=document.createElement("div");if(m(t.header))r.innerHTML=t.header;else{var n=document.createElement("h1"),a=document.createTextNode(t.header);n.appendChild(a),n.setAttribute("style",t.headerStyle),r.appendChild(n)}e.insertBefore(r,e.childNodes[0])}function u(e){e.showModal&&n.default.close(),e.onLoadingEnd&&e.onLoadingEnd(),(e.showModal||e.onLoadingStart)&&window.URL.revokeObjectURL(e.printable);var t="mouseover";(a.default.isChrome()||a.default.isFirefox())&&(t="focus"),window.addEventListener(t,(function r(){window.removeEventListener(t,r),e.onPrintDialogClose();var n=document.getElementById(e.frameId);n&&n.remove()}))}function m(e){return new RegExp("<([A-Za-z][A-Za-z0-9]*)\\b[^>]*>(.*?)</\\1>").test(e)}},"./src/js/html.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/functions.js"),a=r("./src/js/print.js");function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.default={print:function(e,t){var r,o="object"===i(r=e.printable)&&r&&(r instanceof HTMLElement||1===r.nodeType)?e.printable:document.getElementById(e.printable);o?(e.printableElement=function e(t,r){for(var a=t.cloneNode(),i=Array.prototype.slice.call(t.childNodes),o=0;o<i.length;o++)if(-1===r.ignoreElements.indexOf(i[o].id)){var s=e(i[o],r);a.appendChild(s)}switch(r.scanStyles&&1===t.nodeType&&a.setAttribute("style",Object(n.collectStyles)(t,r)),t.tagName){case"SELECT":a.value=t.value;break;case"CANVAS":a.getContext("2d").drawImage(t,0,0)}return a}(o,e),e.header&&Object(n.addHeader)(e.printableElement,e),a.default.send(e,t)):window.console.error("Invalid HTML element id: "+e.printable)}}},"./src/js/image.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/functions.js"),a=r("./src/js/print.js"),i=r("./src/js/browser.js");t.default={print:function(e,t){e.printable.constructor!==Array&&(e.printable=[e.printable]),e.printableElement=document.createElement("div"),e.printable.forEach((function(t){var r=document.createElement("img");if(r.setAttribute("style",e.imageStyle),r.src=t,i.default.isFirefox()){var n=r.src;r.src=n}var a=document.createElement("div");a.appendChild(r),e.printableElement.appendChild(a)})),e.header&&Object(n.addHeader)(e.printableElement,e),a.default.send(e,t)}}},"./src/js/init.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/browser.js"),a=r("./src/js/modal.js"),i=r("./src/js/pdf.js"),o=r("./src/js/html.js"),s=r("./src/js/raw-html.js"),c=r("./src/js/image.js"),l=r("./src/js/json.js");function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var u=["pdf","html","image","json","raw-html"];t.default={init:function(){var e={printable:null,fallbackPrintable:null,type:"pdf",header:null,headerStyle:"font-weight: 300;",maxWidth:800,properties:null,gridHeaderStyle:"font-weight: bold; padding: 5px; border: 1px solid #dddddd;",gridStyle:"border: 1px solid lightgray; margin-bottom: -1px;",showModal:!1,onError:function(e){throw e},onLoadingStart:null,onLoadingEnd:null,onPrintDialogClose:function(){},onIncompatibleBrowser:function(){},modalMessage:"Retrieving Document...",frameId:"printJS",printableElement:null,documentTitle:"Document",targetStyle:["clear","display","width","min-width","height","min-height","max-height"],targetStyles:["border","box","break","text-decoration"],ignoreElements:[],repeatTableHeader:!0,css:null,style:null,scanStyles:!0,base64:!1,onPdfOpen:null,font:"TimesNewRoman",font_size:"12pt",honorMarginPadding:!0,honorColor:!1,imageStyle:"max-width: 100%;"},t=arguments[0];if(void 0===t)throw new Error("printJS expects at least 1 attribute.");switch(d(t)){case"string":e.printable=encodeURI(t),e.fallbackPrintable=e.printable,e.type=arguments[1]||e.type;break;case"object":for(var r in e.printable=t.printable,e.fallbackPrintable=void 0!==t.fallbackPrintable?t.fallbackPrintable:e.printable,e.fallbackPrintable=e.base64?"data:application/pdf;base64,".concat(e.fallbackPrintable):e.fallbackPrintable,e)"printable"!==r&&"fallbackPrintable"!==r&&(e[r]=void 0!==t[r]?t[r]:e[r]);break;default:throw new Error('Unexpected argument type! Expected "string" or "object", got '+d(t))}if(!e.printable)throw new Error("Missing printable information.");if(!e.type||"string"!=typeof e.type||-1===u.indexOf(e.type.toLowerCase()))throw new Error("Invalid print type. Available types are: pdf, html, image and json.");e.showModal&&a.default.show(e),e.onLoadingStart&&e.onLoadingStart();var m=document.getElementById(e.frameId);m&&m.parentNode.removeChild(m);var f=document.createElement("iframe");switch(n.default.isFirefox()?f.setAttribute("style","width: 1px; height: 100px; position: fixed; left: 0; top: 0; opacity: 0; border-width: 0; margin: 0; padding: 0"):f.setAttribute("style","visibility: hidden; height: 0; width: 0; position: absolute; border: 0"),f.setAttribute("id",e.frameId),"pdf"!==e.type&&(f.srcdoc="<html><head><title>"+e.documentTitle+"</title>",e.css&&(Array.isArray(e.css)||(e.css=[e.css]),e.css.forEach((function(e){f.srcdoc+='<link rel="stylesheet" href="'+e+'">'}))),f.srcdoc+="</head><body></body></html>"),e.type){case"pdf":if(n.default.isIE())try{window.open(e.fallbackPrintable,"_blank").focus(),e.onIncompatibleBrowser()}catch(t){e.onError(t)}finally{e.showModal&&a.default.close(),e.onLoadingEnd&&e.onLoadingEnd()}else i.default.print(e,f);break;case"image":c.default.print(e,f);break;case"html":o.default.print(e,f);break;case"raw-html":s.default.print(e,f);break;case"json":l.default.print(e,f)}}}},"./src/js/json.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/functions.js"),a=r("./src/js/print.js");function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.default={print:function(e,t){if("object"!==i(e.printable))throw new Error("Invalid javascript data object (JSON).");if("boolean"!=typeof e.repeatTableHeader)throw new Error("Invalid value for repeatTableHeader attribute (JSON).");if(!e.properties||!Array.isArray(e.properties))throw new Error("Invalid properties array for your JSON data.");e.properties=e.properties.map((function(t){return{field:"object"===i(t)?t.field:t,displayName:"object"===i(t)?t.displayName:t,columnSize:"object"===i(t)&&t.columnSize?t.columnSize+";":100/e.properties.length+"%;"}})),e.printableElement=document.createElement("div"),e.header&&Object(n.addHeader)(e.printableElement,e),e.printableElement.innerHTML+=function(e){var t=e.printable,r=e.properties,a='<table style="border-collapse: collapse; width: 100%;">';e.repeatTableHeader&&(a+="<thead>"),a+="<tr>";for(var i=0;i<r.length;i++)a+='<th style="width:'+r[i].columnSize+";"+e.gridHeaderStyle+'">'+Object(n.capitalizePrint)(r[i].displayName)+"</th>";a+="</tr>",e.repeatTableHeader&&(a+="</thead>"),a+="<tbody>";for(var o=0;o<t.length;o++){a+="<tr>";for(var s=0;s<r.length;s++){var c=t[o],l=r[s].field.split(".");if(l.length>1)for(var d=0;d<l.length;d++)c=c[l[d]];else c=c[r[s].field];a+='<td style="width:'+r[s].columnSize+e.gridStyle+'">'+c+"</td>"}a+="</tr>"}return a+"</tbody></table>"}(e),a.default.send(e,t)}}},"./src/js/modal.js":function(e,t,r){"use strict";r.r(t);var n={show:function(e){var t=document.createElement("div");t.setAttribute("style","font-family:sans-serif; display:table; text-align:center; font-weight:300; font-size:30px; left:0; top:0;position:fixed; z-index: 9990;color: #0460B5; width: 100%; height: 100%; background-color:rgba(255,255,255,.9);transition: opacity .3s ease;"),t.setAttribute("id","printJS-Modal");var r=document.createElement("div");r.setAttribute("style","display:table-cell; vertical-align:middle; padding-bottom:100px;");var a=document.createElement("div");a.setAttribute("class","printClose"),a.setAttribute("id","printClose"),r.appendChild(a);var i=document.createElement("span");i.setAttribute("class","printSpinner"),r.appendChild(i);var o=document.createTextNode(e.modalMessage);r.appendChild(o),t.appendChild(r),document.getElementsByTagName("body")[0].appendChild(t),document.getElementById("printClose").addEventListener("click",(function(){n.close()}))},close:function(){var e=document.getElementById("printJS-Modal");e&&e.parentNode.removeChild(e)}};t.default=n},"./src/js/pdf.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/print.js"),a=r("./src/js/functions.js");function i(e,t,r){var a=new window.Blob([r],{type:"application/pdf"});a=window.URL.createObjectURL(a),t.setAttribute("src",a),n.default.send(e,t)}t.default={print:function(e,t){if(e.base64){var r=Uint8Array.from(atob(e.printable),(function(e){return e.charCodeAt(0)}));i(e,t,r)}else{e.printable=/^(blob|http|\/\/)/i.test(e.printable)?e.printable:window.location.origin+("/"!==e.printable.charAt(0)?"/"+e.printable:e.printable);var n=new window.XMLHttpRequest;n.responseType="arraybuffer",n.addEventListener("error",(function(){Object(a.cleanUp)(e),e.onError(n.statusText,n)})),n.addEventListener("load",(function(){if(-1===[200,201].indexOf(n.status))return Object(a.cleanUp)(e),void e.onError(n.statusText,n);i(e,t,n.response)})),n.open("GET",e.printable,!0),n.send()}}}},"./src/js/print.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/browser.js"),a=r("./src/js/functions.js"),i={send:function(e,t){document.getElementsByTagName("body")[0].appendChild(t);var r=document.getElementById(e.frameId);r.onload=function(){if("pdf"!==e.type){var t=r.contentWindow||r.contentDocument;if(t.document&&(t=t.document),t.body.appendChild(e.printableElement),"pdf"!==e.type&&e.style){var a=document.createElement("style");a.innerHTML=e.style,t.head.appendChild(a)}var i=t.getElementsByTagName("img");i.length>0?function(e){var t=e.map((function(e){if(e.src&&e.src!==window.location.href)return function(e){return new Promise((function(t){!function r(){e&&void 0!==e.naturalWidth&&0!==e.naturalWidth&&e.complete?t():setTimeout(r,500)}()}))}(e)}));return Promise.all(t)}(Array.from(i)).then((function(){return o(r,e)})):o(r,e)}else n.default.isFirefox()?setTimeout((function(){return o(r,e)}),1e3):o(r,e)}}};function o(e,t){try{if(e.focus(),n.default.isEdge()||n.default.isIE())try{e.contentWindow.document.execCommand("print",!1,null)}catch(t){e.contentWindow.print()}else e.contentWindow.print()}catch(e){t.onError(e)}finally{n.default.isFirefox()&&(e.style.visibility="hidden",e.style.left="-1px"),Object(a.cleanUp)(t)}}t.default=i},"./src/js/raw-html.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/js/print.js");t.default={print:function(e,t){e.printableElement=document.createElement("div"),e.printableElement.setAttribute("style","width:100%"),e.printableElement.innerHTML=e.printable,n.default.send(e,t)}}},"./src/sass/index.scss":function(e,t,r){},0:function(e,t,r){e.exports=r("./src/index.js")}}).default},e.exports=n()},b1e3:function(e,t,r){"use strict";var n=r("41e0");r.n(n).a},c84ab:function(e,t,r){},d616:function(e,t,r){"use strict";var n=r("a34a"),a=r.n(n),i=r("2f62"),o=r("add5"),s=r.n(o),c=r("f8b7");function l(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){l(i,n,a,o,s,"next",e)}function s(e){l(i,n,a,o,s,"throw",e)}o(void 0)}))}}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var f={name:"orderSend",props:{orderId:Number,status:Number,pay_type:String},data:function(){var e=this;return{productType:0,orderStatus:0,splitSwitch:!1,formItem:{type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:"",station_type:1,delivery_type:"1",cargo_weight:0,remark:"",mark:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0,manyFormValidate:[],stopSubmit:!1,header:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"image",width:200,align:"center"},{title:"规格",slot:"value",align:"center",minWidth:120},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:100},{title:"商品优惠价",slot:"price",align:"center",minWidth:100},{title:"总数",key:"cart_num",align:"center",minWidth:80},{title:"待发数量",key:"surplus_num",align:"center",width:180,render:function(t,r){return t("div",[t("InputNumber",{props:{min:1,max:r.row.numShow,value:r.row.surplus_num||1},on:{"on-change":function(t){r.row.surplus_num=t||1,e.manyFormValidate[r.index]=r.row,e.selectData.forEach((function(t,n){t.cart_id===r.row.cart_id&&e.selectData.splice(n,1,r.row)}))}}})])}}],selectData:[],sheetInfo:{}}},computed:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(r,!0).forEach((function(t){m(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},Object(i.e)("admin/order",["splitOrder"])),methods:{printImg:function(e){s()({printable:e,type:"image",documentTitle:"快递信息",style:"img{\n          width: 100%;\n          height: 476px;\n        }"})},changeDelivery:function(e){},selectOne:function(e){this.selectData=e},changeModal:function(e){e||this.cancel()},changeSplitStatus:function(e){var t=this;e&&Object(c.X)(this.orderId).then((function(e){var r=e.data;r.forEach((function(e){e.numShow=e.surplus_num})),t.manyFormValidate=r}))},changeRadio:function(e){switch(this.$refs.formItem.resetFields(),e){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="1",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="",this.sheetInfo.self_delivery_status?this.formItem.delivery_type="1":this.formItem.delivery_type="2",this.sheetInfo.dada_delivery_status?this.formItem.station_type=1:this.formItem.station_type=2;break;case"3":this.formItem.fictitious_content=""}},changeExpress:function(e){switch(e){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[],this.getList(2);break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.getList(1)}},reset:function(){this.formItem={type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:"",station_type:1,delivery_type:"1",cargo_weight:0,remark:"",mark:""}},getList:function(e){var t=this,r=2===e?1:"";Object(c.j)(r).then(function(){var e=d(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.express=r.data,t.getSheetInfo();case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},putSend:function(e){var t=this,r={id:this.orderId,datas:this.formItem};if("1"===this.formItem.type&&"2"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("1"===this.formItem.type&&"1"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.delivery_id)return this.$Message.error("快递单号不能为空")}if("2"===this.formItem.type){if("1"===this.formItem.delivery_type&&""===this.formItem.sh_delivery)return this.$Message.error("送货人不能为空");if("2"===this.formItem.delivery_type&&this.formItem.cargo_weight<=0)return this.$Message.error("请输入有效的重量")}this.splitSwitch?(r.datas.cart_ids=[],this.selectData.forEach((function(e){r.datas.cart_ids.push({cart_id:e.cart_id,cart_num:e.surplus_num})})),Object(c.Y)(r).then((function(e){t.modals=!1,t.$Message.success(e.msg),t.$emit("submitFail"),t.reset(),t.splitSwitch=!1,e.data.dump.label&&t.printImg(e.data.dump.label)})).catch((function(e){t.$Message.error(e.msg)}))):Object(c.K)(r).then(function(){var e=d(a.a.mark((function e(r){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.modals=!1,t.$Message.success(r.msg),t.splitSwitch=!1,t.$emit("submitFail"),t.reset(),r.data.dump.label&&t.printImg(r.data.dump.label);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},cancel:function(e){this.modals=!1,this.orderStatus=0,this.splitSwitch=!1,this.selectData=[],this.reset()},expressChange:function(e){var t=this,r=this.express.find((function(t){return t.value===e}));void 0!==r&&(this.formItem.delivery_code=r.code,"2"===this.formItem.express_record_type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(c.z)({com:this.formItem.delivery_code}).then((function(e){t.expressTemp=e.data,e.data.length||t.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(e){t.$Message.error(e.msg)}))))},getCartInfo:function(){var e=this;Object(c.X)(this.orderId).then((function(t){var r=t.data;r.forEach((function(e){e.numShow=e.surplus_num})),e.manyFormValidate=r,e.productType=r[0].product_type,3==e.productType&&(e.formItem.type="3",e.formItem.fictitious_content="")}))},getDeliveryList:function(){var e=this;Object(c.x)().then((function(t){e.deliveryList=t.data.list})).catch((function(t){e.$Message.error(t.msg)}))},getSheetInfo:function(){var e=this;Object(c.I)().then((function(t){var r=t.data;for(var n in r)r.hasOwnProperty(n)&&(e.formItem[n]=r[n]);e.export_open=void 0===r.export_open||r.export_open,e.export_open||(e.formItem.express_record_type="1"),e.formItem.to_addr=r.to_add,e.sheetInfo=r})).catch((function(t){e.$Message.error(t.msg)}))},shDeliveryChange:function(e){if(e){var t=this.deliveryList.find((function(t){return t.id===e}));this.formItem.sh_delivery_name=t.wx_name,this.formItem.sh_delivery_id=t.phone,this.formItem.sh_delivery_uid=t.uid}},expressTempChange:function(e){this.temp=this.expressTemp.find((function(t){return e===t.temp_id})),void 0===this.temp&&(this.temp={})},preview:function(){this.$refs.viewer.$viewer.show()}}},p=(r("b1e3"),r("2877")),h=Object(p.a)(f,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,width:"1000"},on:{"on-visible-change":e.changeModal},model:{value:e.modals,callback:function(t){e.modals=t},expression:"modals"}},[e.modals?r("Form",{ref:"formItem",attrs:{model:e.formItem,"label-width":100},nativeOn:{submit:function(e){e.preventDefault()}}},[r("FormItem",{attrs:{label:"选择类型："}},[r("RadioGroup",{on:{"on-change":e.changeRadio},model:{value:e.formItem.type,callback:function(t){e.$set(e.formItem,"type",t)},expression:"formItem.type"}},[e.productType?e._e():r("Radio",{attrs:{label:"1"}},[e._v("发货")]),!e.productType&&e.sheetInfo.city_delivery_status?r("Radio",{attrs:{label:"2"}},[e._v("送货")]):e._e(),r("Radio",{attrs:{label:"3"}},[e._v("无需配送")])],1)],1),1==e.formItem.type?r("FormItem",{directives:[{name:"show",rawName:"v-show",value:e.export_open,expression:"export_open"}],attrs:{label:"发货类型："}},[r("RadioGroup",{on:{"on-change":e.changeExpress},model:{value:e.formItem.express_record_type,callback:function(t){e.$set(e.formItem,"express_record_type",t)},expression:"formItem.express_record_type"}},[r("Radio",{attrs:{label:"1"}},[e._v("手动填写")]),r("Radio",{attrs:{label:"2"}},[e._v("电子面单打印")])],1)],1):e._e(),r("div",[1==e.formItem.type?r("FormItem",{attrs:{label:"快递公司：",required:""}},[r("Select",{staticClass:"input-add",attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":e.expressChange},model:{value:e.formItem.delivery_name,callback:function(t){e.$set(e.formItem,"delivery_name",t)},expression:"formItem.delivery_name"}},e._l(e.express,(function(t,n){return r("Option",{key:t.value,attrs:{value:t.value}},[e._v(e._s(t.value))])})),1)],1):e._e(),"1"===e.formItem.express_record_type&&1==e.formItem.type?r("FormItem",{attrs:{label:"快递单号：",required:""}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入快递单号"},model:{value:e.formItem.delivery_id,callback:function(t){e.$set(e.formItem,"delivery_id",t)},expression:"formItem.delivery_id"}}),"顺丰速运"==e.formItem.delivery_name?r("div",{staticClass:"trips"},[r("p",[e._v("顺丰请输入单号 :收件人或寄件人手机号后四位，")]),r("p",[e._v("例如：SF000000000000:3941")])]):e._e()],1):e._e(),"2"===e.formItem.express_record_type&&"1"===e.formItem.type?[r("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单："}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择电子面单"},on:{"on-change":e.expressTempChange},model:{value:e.formItem.express_temp_id,callback:function(t){e.$set(e.formItem,"express_temp_id",t)},expression:"formItem.express_temp_id"}},e._l(e.expressTemp,(function(t,n){return r("Option",{key:n,attrs:{value:t.temp_id}},[e._v(e._s(t.title))])})),1),e.formItem.express_temp_id?r("Button",{attrs:{type:"text"},on:{click:e.preview}},[e._v("预览")]):e._e()],1),r("FormItem",{attrs:{label:"寄件人姓名："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人姓名"},model:{value:e.formItem.to_name,callback:function(t){e.$set(e.formItem,"to_name",t)},expression:"formItem.to_name"}})],1),r("FormItem",{attrs:{label:"寄件人电话："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人电话"},model:{value:e.formItem.to_tel,callback:function(t){e.$set(e.formItem,"to_tel",t)},expression:"formItem.to_tel"}})],1),r("FormItem",{attrs:{label:"寄件人地址："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入寄件人地址"},model:{value:e.formItem.to_addr,callback:function(t){e.$set(e.formItem,"to_addr",t)},expression:"formItem.to_addr"}})],1)]:e._e()],2),r("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.formItem.type,expression:"formItem.type === '2'"}]},[r("FormItem",{attrs:{label:"配送类型："}},[r("RadioGroup",{on:{"on-change":e.changeDelivery},model:{value:e.formItem.delivery_type,callback:function(t){e.$set(e.formItem,"delivery_type",t)},expression:"formItem.delivery_type"}},[e.sheetInfo.self_delivery_status?r("Radio",{attrs:{label:"1"}},[e._v("商家配送")]):e._e(),e.sheetInfo.dada_delivery_status||e.sheetInfo.uu_delivery_status?r("Radio",{attrs:{label:"2"}},[e._v("第三方配送")]):e._e()],1)],1),"1"===e.formItem.delivery_type?r("FormItem",{attrs:{label:"送货人：",required:""}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择送货人"},on:{"on-change":e.shDeliveryChange},model:{value:e.formItem.sh_delivery,callback:function(t){e.$set(e.formItem,"sh_delivery",t)},expression:"formItem.sh_delivery"}},e._l(e.deliveryList,(function(t,n){return r("Option",{key:n,attrs:{value:t.id}},[e._v(e._s(t.wx_name)+"（"+e._s(t.phone)+"）")])})),1)],1):e._e(),"2"===e.formItem.delivery_type?r("div",[r("FormItem",{attrs:{label:"配送平台："}},[r("RadioGroup",{model:{value:e.formItem.station_type,callback:function(t){e.$set(e.formItem,"station_type",t)},expression:"formItem.station_type"}},[e.sheetInfo.dada_delivery_status?r("Radio",{attrs:{label:1}},[e._v("达达")]):e._e(),e.sheetInfo.uu_delivery_status?r("Radio",{attrs:{label:2}},[e._v("uu跑腿")]):e._e()],1)],1),r("FormItem",{attrs:{label:"包裹重量：",required:""}},[r("InputNumber",{staticClass:"input-add",attrs:{min:0},model:{value:e.formItem.cargo_weight,callback:function(t){e.$set(e.formItem,"cargo_weight",t)},expression:"formItem.cargo_weight"}}),r("span",{staticStyle:{"margin-left":"10px"}},[e._v("kg")])],1),r("FormItem",{attrs:{label:"配送备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"配送备注"},model:{value:e.formItem.remark,callback:function(t){e.$set(e.formItem,"remark",t)},expression:"formItem.remark"}})],1),r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:e.formItem.mark,callback:function(t){e.$set(e.formItem,"mark",t)},expression:"formItem.mark"}})],1)],1):e._e()],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.formItem.type,expression:"formItem.type === '3'"}]},[r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticClass:"input-add",attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:e.formItem.fictitious_content,callback:function(t){e.$set(e.formItem,"fictitious_content",t)},expression:"formItem.fictitious_content"}})],1)],1),e.splitOrder>1&&"3"!==e.formItem.type?r("div",[r("FormItem",{attrs:{label:"分单发货："}},[r("i-switch",{attrs:{size:"large",disabled:8===e.orderStatus},on:{"on-change":e.changeSplitStatus},model:{value:e.splitSwitch,callback:function(t){e.splitSwitch=t},expression:"splitSwitch"}},[r("span",{attrs:{slot:"open"},slot:"open"},[e._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[e._v("关闭")])]),r("div",{staticClass:"trips"},[r("p",[e._v("\n            可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n          ")])]),e.splitSwitch&&e.manyFormValidate.length?r("Table",{attrs:{data:e.manyFormValidate,columns:e.header,border:""},on:{"on-selection-change":e.selectOne},scopedSlots:e._u([{key:"image",fn:function(t){var n=t.row;return t.index,[r("div",{staticClass:"product-data"},[r("img",{staticClass:"image",attrs:{src:n.cart_info.productInfo.image}}),r("div",{staticClass:"line2"},[e._v("\n                "+e._s(n.cart_info.productInfo.store_name)+"\n              ")])])]}},{key:"value",fn:function(t){var n=t.row;return t.index,[r("div",[e._v(e._s(n.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(t){var n=t.row;return t.index,[r("div",[e._v("\n              "+e._s(n.cart_info.productInfo.attrInfo?n.cart_info.productInfo.attrInfo.price:n.cart_info.productInfo.price)+"\n            ")])]}},{key:"price",fn:function(t){var n=t.row;return t.index,[r("div",[r("div",[e._v(e._s(n.cart_info.truePrice))])])]}}],null,!1,1129913299)}):e._e()],1)],1):e._e()],1):e._e(),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:e.cancel}},[e._v("取消")]),e.stopSubmit?r("Button",{attrs:{type:"warning"}},[e._v("存在售后待处理订单")]):r("Button",{attrs:{type:"primary"},on:{click:e.putSend}},[e._v("提交")])],1),r("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:e.temp,expression:"temp"}],ref:"viewer"},[r("img",{staticClass:"display-add",attrs:{src:e.temp.pic}})])],1)}),[],!1,null,"b86cc932",null);t.a=h.exports}}]);