(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-c71fa76a"],{"0b65":function(t,e,a){"use strict";e.a={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},a612:function(t,e,a){"use strict";a.r(e);var i=a("2f62"),n=a("b7be"),s=a("0b65"),r=a("d708");function o(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function l(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var c={data:function(){return{roterPre:r.a.roterPre,options:s.a,timeVal:[],dataTimeVal:[],formValidate:{name:"",status:"",time:"",create_time:"",page:1,limit:15},list:[{status:0,title:"未开始"},{status:1,title:"进行中"},{status:-1,title:"已结束"}],columns:[{title:"ID",key:"id",width:80},{title:"活动名称",key:"name",minWidth:170},{title:"活动时间",slot:"time",minWidth:260},{title:"参与商品数",key:"product_count",minWidth:100},{title:"活动状态",slot:"start_status",minWidth:100},{title:"是否开启",slot:"status",minWidth:100},{title:"创建时间",key:"add_time",minWidth:140},{title:"操作",slot:"action",minWidth:110,fixed:"right"}],dataList:[],loading:!1,total:0}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?o(a,!0).forEach((function(e){l(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):o(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(i.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.activityFrameList()},methods:{createTime:function(t){this.timeVal=t,this.formValidate.create_time=this.timeVal.join("-"),this.selChange()},dataTime:function(t){this.dataTimeVal=t,this.formValidate.time=this.dataTimeVal.join("-"),this.selChange()},del:function(t,e,a){var i=this,n={title:e,num:a,url:"marketing/activity_frame/del/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(n).then((function(t){i.$Message.success(t.msg),i.dataList.splice(a,1),i.dataList.length||(i.formValidate.page=1==i.formValidate.page?1:i.formValidate.page-1),i.activityFrameList()})).catch((function(t){i.$Message.error(t.msg)}))},onchangeIsShow:function(t){var e=this;Object(n.h)(t.id,t.status).then((function(t){e.$Message.success(t.msg),e.activityFrameList()})).catch((function(t){e.$Message.error(t.msg)}))},edit:function(t){this.$router.push({path:this.roterPre+"/marketing/activity_frame/create/"+t})},add:function(){this.$router.push({path:this.roterPre+"/marketing/activity_frame/create/0"})},activityFrameList:function(){var t=this;Object(n.f)(this.formValidate).then((function(e){t.dataList=e.data.list,t.total=e.data.count}))},pageChange:function(t){this.formValidate.page=t,this.activityFrameList()},selChange:function(){this.formValidate.page=1,this.activityFrameList()},reset:function(){this.formValidate={name:"",status:"",time:"",create_time:"",page:1,limit:15},this.timeVal=[],this.dataTimeVal=[],this.activityFrameList()}}},u=(a("b74e"),a("2877")),m=Object(u.a)(c,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[a("div",{staticClass:"new_card_pd"},[a("Form",{ref:"formValidate",attrs:{inline:"",model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"活动搜索："}},[a("Input",{staticClass:"input-width",attrs:{placeholder:"请输入活动名称/ID"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),a("FormItem",{attrs:{label:"活动状态："}},[a("Select",{staticClass:"input-add",attrs:{clearable:""},on:{"on-change":t.selChange},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},t._l(t.list,(function(e,i){return a("Option",{key:i,attrs:{value:e.status}},[t._v(t._s(e.title))])})),1)],1),a("FormItem",{attrs:{label:"活动时间："}},[a("DatePicker",{staticClass:"input-add",attrs:{editable:!1,value:t.dataTimeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"活动时间",options:t.options},on:{"on-change":t.dataTime}})],1),a("div",{staticStyle:{display:"inline-block"}},[a("FormItem",{attrs:{label:"创建时间："}},[a("DatePicker",{staticClass:"input-add",attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"创建时间",options:t.options},on:{"on-change":t.createTime}})],1),a("Button",{staticClass:"mr14 mt1",attrs:{type:"primary"},on:{click:t.selChange}},[t._v("查询")]),a("Button",{staticClass:"mt1",on:{click:function(e){return t.reset()}}},[t._v("重置")])],1)],1)],1)]),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["marketing-store_seckill-create"],expression:"['marketing-store_seckill-create']"}],staticClass:"mr10",attrs:{type:"primary"},on:{click:t.add}},[t._v("添加活动边框")]),a("Table",{ref:"table",staticClass:"ivu-mt",attrs:{columns:t.columns,data:t.dataList,loading:t.loading,"no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"time",fn:function(e){var i=e.row;return e.index,[a("div",[t._v(t._s(i.start_time)+" 至 "+t._s(i.stop_time))])]}},{key:"start_status",fn:function(e){var i=e.row;return e.index,[a("Tag",{directives:[{name:"show",rawName:"v-show",value:0===i.start_status,expression:"row.start_status === 0"}],attrs:{color:"red",size:"medium"}},[t._v("未开始")]),a("Tag",{directives:[{name:"show",rawName:"v-show",value:1===i.start_status,expression:"row.start_status === 1"}],attrs:{color:"green",size:"medium"}},[t._v("进行中")]),a("Tag",{directives:[{name:"show",rawName:"v-show",value:-1===i.start_status,expression:"row.start_status === -1"}],attrs:{color:"default",size:"medium"}},[t._v("已结束")])]}},{key:"status",fn:function(e){var i=e.row;return e.index,[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.onchangeIsShow(i)}},model:{value:i.status,callback:function(e){t.$set(i,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"action",fn:function(e){var i=e.row,n=e.index;return[a("a",{on:{click:function(e){return t.edit(i.id)}}},[t._v("编辑")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.del(i,"删除活动边框",n)}}},[t._v("删除")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)}),[],!1,null,"676cf3e6",null);e.default=m.exports},ab0e:function(t,e,a){},b74e:function(t,e,a){"use strict";var i=a("ab0e");a.n(i).a}}]);