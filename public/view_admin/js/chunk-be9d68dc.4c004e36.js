(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-be9d68dc"],{"2f2c":function(t,e,a){"use strict";var r=a("e874");a.n(r).a},3123:function(t,e,a){},"6fd3":function(t,e,a){"use strict";var r=a("2f62"),i=a("c4c8");function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function s(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var o={name:"addAttr",data:function(){return{spinShow:!1,modal_loading:!1,grid:{xl:3,lg:3,md:12,sm:24,xs:24},modal:!1,index:1,rules:{rule_name:[{required:!0,message:"请输入分类名称",trigger:"blur"}]},formDynamic:{rule_name:"",spec:[]},attrsName:"",attrsVal:"",formDynamicNameData:[],isBtn:!1,formDynamicName:[],results:[],result:[],ids:0,title:"添加商品规格"}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(a,!0).forEach((function(e){s(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:110},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:{onCancel:function(){this.clear()},addBtn:function(){if(""==this.formDynamic.rule_name.trim())return this.$Message.error("请输入分类名称");this.isBtn=!0},getIofo:function(t){var e=this;this.spinShow=!0,this.ids=t.id,this.title="编辑商品规格",Object(i.sb)(t.id).then((function(t){e.formDynamic=t.data.info,e.spinShow=!1})).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;if(0===e.formDynamic.spec.length)return e.$Message.warning("请至少添加一条商品规格！");for(var a=0;a<e.formDynamic.spec.length;a++){if(!e.formDynamic.spec[a].value.trim())return e.$Message.warning("请添加规格名称！");if(!e.formDynamic.spec[a].detail.length)return e.$Message.warning("请添加规格值！")}e.modal_loading=!0,setTimeout((function(){Object(i.rb)(e.formDynamic,e.ids).then((function(t){e.$Message.success(t.msg),setTimeout((function(){e.modal=!1,e.modal_loading=!1}),500),setTimeout((function(){e.$emit("getList"),e.clear()}),600)})).catch((function(t){e.modal_loading=!1,e.$Message.error(t.msg)}))}),1200)}))},clear:function(){this.$refs.formDynamic.resetFields(),this.formDynamic.spec=[],this.isBtn=!1,this.ids=0,this.title="添加商品规格",this.attrsName="",this.attrsVal=""},offAttrName:function(){this.isBtn=!1},cancle:function(){this.modal=!1,this.clear()},handleRemove:function(t){this.formDynamic.spec.splice(t,1)},handleRemove2:function(t,e){t.splice(e,1)},createAttrName:function(){if(this.attrsName&&this.attrsVal){var t={value:this.attrsName,detail:[this.attrsVal]};this.formDynamic.spec.push(t);var e={};this.formDynamic.spec=this.formDynamic.spec.reduce((function(t,a){return!e[a.value]&&(e[a.value]=t.push(a)),t}),[]),this.attrsName="",this.attrsVal="",this.isBtn=!1}else this.$Message.warning("请添加规格名称或规格值")},createAttr:function(t,e){if(t){this.formDynamic.spec[e].detail.push(t);var a={};this.formDynamic.spec[e].detail=this.formDynamic.spec[e].detail.reduce((function(t,e){return!a[e]&&(a[e]=t.push(e)),t}),[])}else this.$Message.warning("请添加属性")}}},l=(a("b76f"),a("2877")),c=Object(l.a)(o,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Modal",{attrs:{scrollable:"",title:t.title,"class-name":"vertical-center-modal",width:"700"},on:{"on-cancel":t.onCancel},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("Form",{ref:"formDynamic",staticClass:"attrFrom",attrs:{model:t.formDynamic,rules:t.rules,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{gutter:24}},[a("Col",{attrs:{span:"24"}},[a("Col",{staticClass:"mb15",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"分类名称：",prop:"rule_name"}},[a("Input",{attrs:{placeholder:"请输入分类名称",maxlength:20},model:{value:t.formDynamic.rule_name,callback:function(e){t.$set(t.formDynamic,"rule_name",e)},expression:"formDynamic.rule_name"}})],1)],1)],1),t._l(t.formDynamic.spec,(function(e,r){return a("Col",{key:r,staticClass:"noForm",attrs:{span:"23"}},[a("FormItem",[a("div",{staticClass:"acea-row row-middle"},[a("span",{staticClass:"mr5"},[t._v(t._s(e.value))]),a("Icon",{attrs:{type:"ios-close-circle"},on:{click:function(e){return t.handleRemove(r)}}})],1),a("div",{staticClass:"rulesBox"},[t._l(e.detail,(function(r,i){return a("Tag",{key:i,attrs:{type:"dot",closable:"",color:"primary",name:r},on:{"on-close":function(a){return t.handleRemove2(e.detail,i)}}},[t._v(t._s(r))])})),a("Input",{staticClass:"width20",attrs:{maxlength:"30","show-word-limit":"",search:"","enter-button":"添加",placeholder:"请输入属性名称"},on:{"on-search":function(a){return t.createAttr(e.detail.attrsVal,r)}},model:{value:e.detail.attrsVal,callback:function(a){t.$set(e.detail,"attrsVal",a)},expression:"item.detail.attrsVal"}})],2)])],1)})),t.isBtn?a("Col",{staticClass:"mt10",attrs:{span:"24"}},[a("Col",{staticClass:"mt10 mr15",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"规格名称："}},[a("Input",{attrs:{placeholder:"请输入规格名称",maxlength:"30","show-word-limit":""},model:{value:t.attrsName,callback:function(e){t.attrsName=e},expression:"attrsName"}})],1)],1),a("Col",{staticClass:"mt10 mr20",attrs:{span:"10"}},[a("FormItem",{attrs:{label:"规格值："}},[a("Input",{attrs:{maxlength:"30","show-word-limit":"",placeholder:"请输入规格值"},model:{value:t.attrsVal,callback:function(e){t.attrsVal=e},expression:"attrsVal"}})],1)],1),a("Col",{staticClass:"mr20",attrs:{span:"10"}},[a("div",{staticClass:"sub"},[a("Button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),a("Button",{on:{click:t.offAttrName}},[t._v("取消")])],1)])],1):t._e(),t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e()],2),t.isBtn?t._e():a("Button",{staticClass:"ml110 mt10",attrs:{type:"primary"},on:{click:t.addBtn}},[t._v("添加新规格")])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{on:{click:t.cancle}},[t._v("取消")]),a("Button",{attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formDynamic")}}},[t._v("确定")])],1)],1)}),[],!1,null,"48ec42b7",null);e.a=c.exports},"7f08":function(t,e,a){"use strict";a.r(e);var r=a("2f62"),i=a("6fd3"),n=a("c4c8");function s(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var l={name:"productAttr",components:{addAttr:i.a},data:function(){return{loading:!1,artFrom:{page:1,limit:10,rule_name:""},columns4:[{title:"ID",key:"id",width:80},{title:"分类",key:"rule_name",minWidth:150},{title:"规格名",key:"attr_name",minWidth:250},{title:"规格值",slot:"attr_value",minWidth:300},{title:"操作",slot:"action",fixed:"right",width:120}],tableList:[],total:0}},computed:function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?s(a,!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):s(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({},Object(r.e)("admin/layout",["isMobile"]),{},Object(r.e)("admin/order",["orderChartType"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getDataList()},methods:{del:function(t,e,a){var r=this,i={title:e,num:a,url:"product/product/rule/delete/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(i).then((function(t){r.$Message.success(t.msg),r.tableList.splice(a,1),r.tableList.length||(r.artFrom.page=1==r.artFrom.page?1:r.artFrom.page-1),r.getDataList()})).catch((function(t){r.$Message.error(t.msg)}))},addAttr:function(){this.$refs.addattr.ids=0,this.$refs.addattr.modal=!0},edit:function(t){this.$refs.addattr.modal=!0,this.$refs.addattr.getIofo(t)},getDataList:function(){var t=this;this.loading=!0,Object(n.tb)(this.artFrom).then((function(e){var a=e.data;t.tableList=a.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.artFrom.page=t,this.getDataList()},userSearchs:function(){this.artFrom.page=1,this.getDataList()}}},c=(a("2f2c"),a("2877")),u=Object(c.a)(l,(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[a("div",{staticClass:"new_card_pd"},[a("Form",{ref:"artFrom",staticClass:"tabform",attrs:{inline:"",model:t.artFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{gutter:24,type:"flex",justify:"end"}},[a("Col",{staticClass:"ivu-text-left",attrs:{span:"24"}},[a("FormItem",{attrs:{label:"规格搜索："}},[a("Input",{staticClass:"input-add",attrs:{placeholder:"请输入规格分类名称"},model:{value:t.artFrom.rule_name,callback:function(e){t.$set(t.artFrom,"rule_name",e)},expression:"artFrom.rule_name"}}),a("Button",{attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("查询")])],1)],1)],1)],1)],1)]),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["product-rule-save"],expression:"['product-rule-save']"}],staticClass:"mr20",attrs:{type:"primary"},on:{click:t.addAttr}},[t._v("添加商品规格")]),a("Table",{ref:"selection",staticClass:"mt25",attrs:{columns:t.columns4,data:t.tableList,loading:t.loading,"highlight-row":"","no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"attr_value",fn:function(e){var r=e.row;return t._l(r.attr_value,(function(e,r){return a("span",{key:r,staticStyle:{display:"block"},domProps:{textContent:t._s(e)}})}))}},{key:"action",fn:function(e){var r=e.row,i=e.index;return[a("a",{on:{click:function(e){return t.edit(r)}}},[t._v("编辑")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.del(r,"删除规格",i)}}},[t._v("删除")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.artFrom.page,"show-elevator":"","show-total":"","page-size":t.artFrom.limit},on:{"on-change":t.pageChange}})],1)],1),a("add-attr",{ref:"addattr",on:{getList:t.userSearchs}})],1)}),[],!1,null,"94d60d86",null);e.default=u.exports},b76f:function(t,e,a){"use strict";var r=a("3123");a.n(r).a},e874:function(t,e,a){}}]);