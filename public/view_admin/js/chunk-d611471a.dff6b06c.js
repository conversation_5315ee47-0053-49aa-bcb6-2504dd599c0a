(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-d611471a"],{1336:function(t,e,r){"use strict";r.d(e,"e",(function(){return a})),r.d(e,"d",(function(){return i})),r.d(e,"f",(function(){return o})),r.d(e,"a",(function(){return s})),r.d(e,"c",(function(){return c})),r.d(e,"b",(function(){return l})),r.d(e,"h",(function(){return u})),r.d(e,"g",(function(){return d}));var n=r("b6bd");function a(t){return Object(n.a)({url:"cms/cms",method:"get",params:t})}function i(t){return Object(n.a)({url:"cms/cms",method:"post",data:t})}function o(t){return Object(n.a)({url:"cms/cms/".concat(t),method:"get"})}function s(){return Object(n.a)({url:"cms/category/create",method:"GET"})}function c(t){return Object(n.a)({url:"cms/category",method:"GET",params:t})}function l(t){return Object(n.a)({url:"cms/category/".concat(t,"/edit"),method:"GET"})}function u(t){return Object(n.a)({url:"cms/category/set_status/".concat(t.id,"/").concat(t.status),method:"put"})}function d(t,e){return Object(n.a)({url:"cms/cms/relation/".concat(e),method:"put",data:t})}},"2cf7":function(t,e,r){},"61f7":function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(r,!0).forEach((function(e){i(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t,e){/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var r={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var n in r)if(new RegExp("(".concat(n,")")).test(e)){var a=r[n]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?a:s(a))}return e}function s(t){return("00"+t).substr(t.length)}r.d(e,"a",(function(){return o})),r.d(e,"c",(function(){return u})),r.d(e,"b",(function(){return d}));var c={min:"%s最小长度为:min",max:"%s最大长度为:max",length:"%s长度必须为:length",range:"%s长度为:range",pattern:"$s格式错误"},l=function(t,e){t.message=function(t){return e.replace("%s",t||"")}};function u(t){return a({required:!0,message:t,type:"string"},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}function d(t){return m.pattern(/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,t)}l(u,"请输入%s"),l(d,"%s格式不正确");var m=Object.keys(c).reduce((function(t,e){return t[e]=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o="range"===e?{min:t[0],max:t[1]}:i({},e,t);return a({message:r.replace(":".concat(e),"range"===e?"".concat(t[0],"-").concat(t[1]):t),type:"string"},o,{},n)},l(t[e],c[e]),t}),{})},"794f":function(t,e,r){"use strict";var n=r("2cf7");r.n(n).a},"7a0c":function(t,e,r){"use strict";r.r(e);var n=r("a34a"),a=r.n(n),i=r("2f62"),o=r("1336");function s(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}var c={name:"relation",data:function(){return{modals:!1,grid:{xl:12,lg:12,md:12,sm:24,xs:24}}},methods:{handleReset:function(){this.modals=!1},userSearchs:function(){this.getList()},getList:function(){var t=this;this.loading=!0,taskListApi(this.levelId,this.levelFrom).then(function(){var e,r=(e=a.a.mark((function e(r){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=r.data,t.levelLists=n.list,t.total=r.data.count,t.loading=!1;case 4:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(t){s(i,n,a,o,c,"next",t)}function c(t){s(i,n,a,o,c,"throw",t)}o(void 0)}))});return function(t){return r.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.levelFrom.page=t,this.getList()}}},l=r("2877"),u=Object(l.a)(c,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"选择商品","mask-closable":!1,width:"950"},on:{"on-cancel":t.handleReset},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Form",{ref:"levelFrom",attrs:{model:t.levelFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{attrs:{type:"flex",gutter:24}},[r("Col",t._b({},"Col",t.grid,!1),[r("FormItem",{attrs:{label:"商品名称：",prop:"status2","label-for":"status2"}},[r("Input",{staticStyle:{width:"100%"},attrs:{search:"","enter-button":"",placeholder:"请输入商品名称"},on:{"on-search":t.userSearchs},model:{value:t.levelFrom.name,callback:function(e){t.$set(t.levelFrom,"name",e)},expression:"levelFrom.name"}})],1)],1)],1)],1),r("Divider",{attrs:{dashed:""}}),r("Table",{ref:"table",attrs:{columns:t.columns1,data:t.levelLists,loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"is_shows",fn:function(e){var n=e.row;return e.index,[r("i-switch",{attrs:{value:n.is_show,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.onchangeIsShow(n)}},model:{value:n.is_show,callback:function(e){t.$set(n,"is_show",e)},expression:"row.is_show"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("显示")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("隐藏")])])]}},{key:"is_musts",fn:function(e){var n=e.row;return e.index,[r("i-switch",{attrs:{value:n.is_must,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.onchangeIsMust(n)}},model:{value:n.is_must,callback:function(e){t.$set(n,"is_must",e)},expression:"row.is_must"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("全部完成")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("达成其一")])])]}},{key:"action",fn:function(e){var n=e.row;return e.index,[r("a",{on:{click:function(e){return t.edit(n)}}},[t._v("编辑  | ")]),r("a",{on:{click:function(e){return t.del(n,"删除任务")}}},[t._v("  删除")])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.levelFrom.limit},on:{"on-change":t.pageChange}})],1),r("edit-from",{ref:"edits",attrs:{FromData:t.FromData,titleType:t.titleType},on:{submitFail:t.submitFail}})],1)}),[],!1,null,"1fa1a756",null).exports,d=r("61f7"),m=r("c4ad"),f=r("d708");function h(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){h(i,n,a,o,s,"next",t)}function s(t){h(i,n,a,o,s,"throw",t)}o(void 0)}))}}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var b={name:"addArticle",data:function(){return{roterPre:f.a.roterPre,modalTitleSs:"",currentTab:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},loading:!1,artFrom:{pid:0,title:"",page:1,limit:20},total:0,columns1:[{title:"ID",key:"id",width:80},{title:"文章图片",slot:"image_inputs",minWidth:90},{title:"文章名称",slot:"titles",minWidth:130},{title:"关联商品",key:"store_name",minWidth:130},{title:"浏览量",key:"visit",minWidth:80},{title:"时间",key:"add_time",sortable:!0,render:function(t,e){return t("div",Object(d.a)(new Date(1e3*Number(e.row.add_time)),"yyyy-MM-dd hh:mm"))},minWidth:120},{title:"操作",slot:"action",fixed:"right",minWidth:150}],cmsList:[],treeData:[],list:[],cid:0,cmsId:0,formValidate:{type:1},rows:{},modal_loading:!1,modals:!1}},components:{relationList:u,goodsList:m.default},computed:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(r,!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Object(i.e)("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),mounted:function(){this.artFrom.pid=this.$route.query.id?this.$route.query.id:0,this.getList(),this.getClass()},methods:{getProductId:function(t){var e=this,r={product_id:t.id};Object(o.g)(r,this.rows.id).then(function(){var r=p(a.a.mark((function r(n){return a.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:e.$Message.success(n.msg),t.id=0,e.modal_loading=!1,e.modals=!1,setTimeout((function(){e.getList()}),500);case 5:case"end":return r.stop()}}),r)})));return function(t){return r.apply(this,arguments)}}()).catch((function(t){e.modal_loading=!1,e.loading=!1,e.$Message.error(t.msg)}))},cancel:function(){this.modals=!1},getList:function(){var t=this;this.loading=!0,Object(o.e)(this.artFrom).then(function(){var e=p(a.a.mark((function e(r){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=r.data,t.cmsList=n.list,t.total=n.count,t.loading=!1;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},getClass:function(){var t=this;Object(o.c)(this.formValidate).then(function(){var e=p(a.a.mark((function e(r){var n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=r.data,t.treeData=n,i={id:0,title:"全部"},t.treeData.unshift(i);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},pageChange:function(t){this.artFrom.page=t,this.getList()},edit:function(t){this.$router.push({path:this.roterPre+"/cms/article/add_article/"+t.id})},artRelation:function(t,e,r){var n=this;if(this.rows=t,0===t.product_id)this.modals=!0;else{var a={title:e,num:r,url:"/cms/cms/unrelation/".concat(t.id),method:"PUT",ids:""};this.$modalSure(a).then((function(t){n.$Message.success(t.msg),n.getList()})).catch((function(t){n.$Message.error(t.msg)}))}},del:function(t,e,r){var n=this,a={title:e,num:r,url:"cms/cms/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(a).then((function(t){n.$Message.success(t.msg),n.cmsList.splice(r,1),n.cmsList.length||(n.artFrom.page=1==n.artFrom.page?1:n.artFrom.page-1),n.getList()})).catch((function(t){n.$Message.error(t.msg)}))},userSearchs:function(){this.artFrom.page=1,this.getList()}}},w=(r("794f"),Object(l.a)(b,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[r("div",{staticClass:"new_card_pd"},[r("Form",{ref:"artFrom",attrs:{inline:"",model:t.artFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"文章分类：","label-for":"pid"}},[r("Select",{staticClass:"input-add",attrs:{placeholder:"请选择","element-id":"pid",clearable:""},on:{"on-change":t.userSearchs},model:{value:t.artFrom.pid,callback:function(e){t.$set(t.artFrom,"pid",e)},expression:"artFrom.pid"}},t._l(t.treeData,(function(e,n){return r("Option",{key:n,attrs:{value:e.id}},[t._v(t._s(e.title))])})),1)],1),r("FormItem",{attrs:{label:"文章搜索：","label-for":"title"}},[r("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入"},on:{"on-search":t.userSearchs},model:{value:t.artFrom.title,callback:function(e){t.$set(t.artFrom,"title",e)},expression:"artFrom.title"}}),r("Button",{attrs:{type:"primary"},on:{click:function(e){return t.userSearchs()}}},[t._v("查询")])],1)],1)],1)]),r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("router-link",{directives:[{name:"auth",rawName:"v-auth",value:["cms-article-creat"],expression:"['cms-article-creat']"}],attrs:{to:t.roterPre+"/cms/article/add_article"}},[r("Button",{staticClass:"bnt",attrs:{type:"primary"}},[t._v("添加文章")])],1),r("Table",{ref:"table",staticClass:"ivu-mt",attrs:{columns:t.columns1,data:t.cmsList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"titles",fn:function(e){var n=e.row;return e.index,[r("span",[t._v(t._s(" [ "+n.catename+" ] "+n.title))])]}},{key:"image_inputs",fn:function(e){var n=e.row;return e.index,[0!==n.image_input.length?r("viewer",t._l(n.image_input,(function(t,e){return r("div",{key:e,staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"item"}]})])})),0):t._e()]}},{key:"action",fn:function(e){var n=e.row,a=e.index;return[r("a",{on:{click:function(e){return t.edit(n)}}},[t._v("编辑")]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.artRelation(n,"取消关联",a)}}},[t._v(t._s(0===n.product_id?"关联":"取消关联"))]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.del(n,"删除文章",a)}}},[t._v("删除")])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,current:t.artFrom.page,"show-elevator":"","show-total":"","page-size":t.artFrom.limit},on:{"on-change":t.pageChange}})],1)],1),r("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?r("goods-list",{ref:"goodslist",on:{getProductId:t.getProductId}}):t._e()],1)],1)}),[],!1,null,"0f739516",null));e.default=w.exports}}]);