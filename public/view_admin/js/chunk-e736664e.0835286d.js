(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-e736664e"],{"0f1c":function(e,t,a){"use strict";var n=a("f5b0");a.n(n).a},"318e":function(e,t,a){"use strict";a.r(t);var n=a("a34a"),i=a.n(n),s=a("5723"),r=a("6987"),o=a("3dda"),c=a.n(o),u=a("d708"),l=a("c276"),d=a("bc3a"),m=a.n(d),p=a("5d39");function h(e,t,a,n,i,s,r){try{var o=e[s](r),c=o.value}catch(e){return void a(e)}o.done?t(c):Promise.resolve(c).then(n,i)}function f(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var s=e.apply(t,a);function r(e){h(s,n,i,r,o,"next",e)}function o(e){h(s,n,i,r,o,"throw",e)}r(void 0)}))}}var g={name:"AccountLogin",mixins:[c.a],components:{Verify:p.a},data:function(){return{fullWidth:document.documentElement.clientWidth,swiperOption:{pagination:".swiper-pagination",autoplay:!0},isShow:!1,loading:!1,autoLogin:!0,imgcode:"",iscaptcha:!1,formInline:{username:"",password:"",phone:"",new_pwd:""},ruleInline:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],phone:[{required:!0,message:"请填写手机号码",trigger:"change"},{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式不正确",trigger:"change"}]},errorNum:0,login_logo:"",swiperList:[],defaultSwiperList:a("433f"),loginTab:["账号登录","短信登录"],active:0,isSms:!1,disabled:!1,text:"获取验证码",resetStatus:!0,copyright:"",version:""}},created:function(){var e=this;top!=window&&(top.location.href=location.href),document.onkeydown=function(t){"login"===e.$route.name&&13===window.event.keyCode&&e.handleSubmit("formInline")},window.addEventListener("resize",this.handleResize)},watch:{fullWidth:function(e){if(!this.timer){this.screenWidth=e,this.timer=!0;var t=this;setTimeout((function(){t.timer=!1}),400)}},$route:function(e){this.captchas()}},mounted:function(){var e=this;this.$nextTick((function(){e.screenWidth<768?document.getElementsByTagName("canvas")[0]&&document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0]&&(document.getElementsByTagName("canvas")[0].className="index_bg"),e.swiperData()})),this.captchas(),this.getCopyright()},methods:{loginTabSwitch:function(e){this.active=e,this.formInline.code=""},sendCode:function(e){var t=this;this.disabled&&this.closeModel(e),this.disabled=!0;var a=60;this.text="剩余 "+a+"s";var n=setInterval((function(){(a-=1)<0&&clearInterval(n),t.text="剩余 "+a+"s",t.text<"剩余 0s"&&(t.disabled=!1,t.text="重新获取")}),1e3)},code:function(){return this.isSms=!0,this.formInline.phone?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formInline.phone)?void this.$refs.verify.show():this.$Message.error("请输入正确的手机号码"):this.$Message.error("请填写手机号码")},resetUpwd:function(){this.resetStatus?(this.$set(this,"loginTab",["忘记密码"]),this.resetStatus=!1):(this.$set(this,"loginTab",["账号登录","短信登录"]),this.resetStatus=!0,this.active=0),this.$refs.formInline.resetFields()},swiperData:function(){var e=this;Object(s.g)().then((function(t){var n=t.data||{};e.login_logo=n.login_logo?n.login_logo:a("9d64"),e.swiperList=n.slide.length?n.slide:[e.defaultSwiperList],e.$cache.local.setJSON("file_size_max",n.upload_file_size_max)})).catch((function(t){e.$Message.error(t.msg)}))},getChilden:function(e){return e.length&&e[0].children?this.getChilden(e[0].children):e[0].path},getCopyright:function(){var e=this;Object(s.d)().then((function(t){var a=t.data;e.copyright=a.copyrightContext,e.version=a.version})).catch((function(t){e.$Message.error(t.msg)}))},closeModel:function(e){var t=this;if(0==this.resetStatus){if(""==this.formInline.phone)return this.$Message.error("手机号不能为空");if(""==this.formInline.new_pwd)return this.$Message.error("新密码不能为空");Object(s.k)({phone:this.formInline.phone,code:this.formInline.code,new_pwd:this.formInline.new_pwd}).then((function(e){t.$Message.success(e.msg),t.$set(t,"loginTab",["账号登录","短信登录"]),t.resetStatus=!0,t.active=0,t.$refs.formInline.resetFields()})).catch((function(e){t.$Message.error(e.msg)}))}else if(1==this.resetStatus&&0==this.active){if(""==this.formInline.username)return this.$Message.error("账号不能为空");if(""==this.formInline.password)return this.$Message.error("密码不能为空");var a=this.$Message.loading({content:"登录中...",duration:0});Object(s.a)({account:this.formInline.username,pwd:this.formInline.password,captchaType:"clickWord",captchaVerification:e.captchaVerification}).then(function(){var e=f(i.a.mark((function e(n){var s,o,c,d,m,p;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a(),n.data.unique_auth.length){e.next=3;break}return e.abrupt("return",t.$Message.error("您暂无任何菜单权限"));case 3:return t.$store.dispatch("admin/account/setPageTitle"),s=n.data.expires_time,l.a.cookies.set("uuid",n.data.user_info.id,{expires:s}),l.a.cookies.set("token",n.data.token,{expires:s}),l.a.cookies.set("expires_time",n.data.expires_time,{expires:s}),e.next=10,t.$store.dispatch("admin/db/database",{user:!0});case 10:return e.sent.set("unique_auth",n.data.unique_auth).set("user_info",n.data.user_info).write(),l.a.makeMenu(u.a.roterPre,n.data.menus),o=n.data.menus,t.$store.commit("admin/menus/getmenusNav",o),c=Object(r.d)(n.data.menus),t.$store.commit("admin/menu/setHeader",c),d=t.getChilden(n.data.menus),m=Object(r.c)({path:d,query:{},params:{}},o),p=Object(r.e)(o,m),t.$store.commit("admin/menu/setSider",p[0].children),t.$store.commit("admin/menus/setIndexPath",d),t.$store.dispatch("admin/user/set",{name:n.data.user_info.account,avatar:n.data.user_info.head_pic,access:n.data.unique_auth,logo:n.data.logo,logoSmall:n.data.logo_square,version:n.data.version,newOrderAudioLink:n.data.newOrderAudioLink}),e.abrupt("return",t.$router.replace({path:t.$route.query.redirect||d||"/"}));case 24:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){a();var n=void 0===e?{}:e;t.errorNum++,t.captchas(),t.$Message.error(n.msg||"登录失败")}))}else{var n=this.$Message.loading({content:"登录中...",duration:0});Object(s.j)({phone:this.formInline.phone,code:this.formInline.code}).then(function(){var e=f(i.a.mark((function e(a){var s,o,c,d,m,p;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n(),a.data.unique_auth.length){e.next=3;break}return e.abrupt("return",t.$Message.error("您暂无任何菜单权限"));case 3:return t.$store.dispatch("admin/account/setPageTitle"),s=a.data.expires_time,l.a.cookies.set("uuid",a.data.user_info.id,{expires:s}),l.a.cookies.set("token",a.data.token,{expires:s}),l.a.cookies.set("expires_time",a.data.expires_time,{expires:s}),e.next=10,t.$store.dispatch("admin/db/database",{user:!0});case 10:return e.sent.set("unique_auth",a.data.unique_auth).set("user_info",a.data.user_info).write(),l.a.makeMenu(u.a.roterPre,a.data.menus),o=a.data.menus,t.$store.commit("admin/menus/getmenusNav",o),c=Object(r.d)(a.data.menus),t.$store.commit("admin/menu/setHeader",c),d=t.getChilden(a.data.menus),m=Object(r.c)({path:d,query:{},params:{}},o),p=Object(r.e)(o,m),t.$store.commit("admin/menu/setSider",p[0].children),t.$store.commit("admin/menus/setIndexPath",d),t.$store.dispatch("admin/user/set",{name:a.data.user_info.account,avatar:a.data.user_info.head_pic,access:a.data.unique_auth,logo:a.data.logo,logoSmall:a.data.logo_square,version:a.data.version,newOrderAudioLink:a.data.newOrderAudioLink}),e.abrupt("return",t.$router.replace({path:t.$route.query.redirect||d||"/"}));case 24:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){n();var a=void 0===e?{}:e;t.errorNum++,t.captchas(),t.$Message.error(a.msg||"登录失败")}))}},getExpiresTime:function(e){var t=Math.round(new Date/1e3);return parseFloat(parseFloat(parseFloat((e-t)/60)/60)/24)},handleResize:function(e){this.fullWidth=document.documentElement.clientWidth,this.fullWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"},captchas:function(){this.imgcode=u.a.apiBaseURL+"/captcha_pro?"+Date.parse(new Date)},getLogin:function(){var e=this;if(""==this.formInline.username)return this.$Message.error("账号不能为空");if(""==this.formInline.password)return this.$Message.error("密码不能为空");var t=this.$Message.loading({content:"登录中...",duration:0}),a={account:this.formInline.username,pwd:this.formInline.password,captchaType:"",captchaVerification:""};Object(s.a)(a).then(function(){var a=f(i.a.mark((function a(n){var s,o,c,d,m,p;return i.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(t(),n.data.unique_auth.length){a.next=3;break}return a.abrupt("return",e.$Message.error("您暂无任何菜单权限"));case 3:return e.$store.dispatch("admin/account/setPageTitle"),s=n.data.expires_time,l.a.cookies.set("uuid",n.data.user_info.id,{expires:s}),l.a.cookies.set("token",n.data.token,{expires:s}),l.a.cookies.set("expires_time",n.data.expires_time,{expires:s}),a.next=10,e.$store.dispatch("admin/db/database",{user:!0});case 10:return a.sent.set("unique_auth",n.data.unique_auth).set("user_info",n.data.user_info).write(),l.a.makeMenu(u.a.roterPre,n.data.menus),o=n.data.menus,e.$store.commit("admin/menus/getmenusNav",o),c=Object(r.d)(n.data.menus),e.$store.commit("admin/menu/setHeader",c),d=e.getChilden(n.data.menus),m=Object(r.c)({path:d,query:{},params:{}},o),p=Object(r.e)(o,m),e.$store.commit("admin/menu/setSider",p[0].children),e.$store.commit("admin/menus/setIndexPath",d),e.$store.dispatch("admin/user/set",{name:n.data.user_info.account,avatar:n.data.user_info.head_pic,access:n.data.unique_auth,logo:n.data.logo,logoSmall:n.data.logo_square,version:n.data.version,newOrderAudioLink:n.data.newOrderAudioLink}),a.abrupt("return",e.$router.replace({path:e.$route.query.redirect||d||"/"}));case 24:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}()).catch((function(a){t();var n=void 0===a?{}:a;e.errorNum++,e.captchas(),e.$Message.error(n.msg||"登录失败")}))},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){e&&0==t.active?Object(s.f)({account:t.formInline.username}).then((function(e){t.iscaptcha=e.data.is_captcha,!0===t.iscaptcha?t.$refs.verify.show():t.getLogin()})):t.closeModel()}))},resetPwd:function(e){var t=this;this.$refs[e].validate((function(e){e&&t.$refs.verify.show()}))},success:function(e){var t=this;if(this.isSms){var a=u.a.apiBaseURL.replace(/adminapi/,"api");m.a.get(a+"/verify_code").then((function(n){var i={type:"login",phone:t.formInline.phone,key:n.data.data.key,code:"",captchaType:"clickWord",captchaVerification:e.captchaVerification};fetch(a+"/register/verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)}).then((function(e){t.sendCode()})).catch((function(e){t.$Message.error(e.msg)}))}))}else this.closeModel(e)},onClose:function(){this.isShow=!1}},beforeCreate:function(){this.fullWidth<768?document.getElementsByTagName("canvas")[0]&&document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0]&&(document.getElementsByTagName("canvas")[0].className="index_bg")},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize),document.getElementsByTagName("canvas")[0]&&document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg")}},v=(a("0f1c"),a("2877")),w=Object(v.a)(g,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-account"},[a("div",{staticClass:"container",class:[e.fullWidth>768?"containerSamll":"containerBig"]},[e.fullWidth>768?a("swiper",{staticClass:"swiperPross",attrs:{options:e.swiperOption}},[e._l(e.swiperList,(function(e,t){return a("swiper-slide",{key:t,staticClass:"swiperPic"},[a("img",{staticStyle:{"object-fit":"fill"},attrs:{src:e}})])})),a("div",{staticClass:"swiper-pagination",attrs:{slot:"pagination"},slot:"pagination"})],2):e._e(),a("div",{staticClass:"index_from page-account-container"},[a("div",{staticClass:"page-account-top "},[a("div",{staticClass:"page-account-top-logo"},[a("img",{attrs:{src:e.login_logo,alt:"logo"}})])]),this.resetStatus?a("div",{staticClass:"login_tab flex"},e._l(e.loginTab,(function(t,n){return a("div",{key:n,staticClass:"login_tab_item",class:e.active==n?"active_tab":"",on:{click:function(t){return e.loginTabSwitch(n)}}},[e._v(e._s(t))])})),0):e._e(),a("Form",{directives:[{name:"show",rawName:"v-show",value:!e.active&&e.resetStatus,expression:"!active && resetStatus"}],ref:"formInline",attrs:{model:e.formInline,rules:e.ruleInline},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit("formInline")}}},[a("FormItem",{attrs:{prop:"username"}},[a("Input",{attrs:{type:"text",prefix:"ios-contact-outline",placeholder:"请输入用户名",size:"large"},model:{value:e.formInline.username,callback:function(t){e.$set(e.formInline,"username",t)},expression:"formInline.username"}})],1),a("FormItem",{attrs:{prop:"password"}},[a("Input",{attrs:{type:"password",prefix:"ios-lock-outline",placeholder:"请输入密码",size:"large"},model:{value:e.formInline.password,callback:function(t){e.$set(e.formInline,"password",t)},expression:"formInline.password"}})],1),a("FormItem",[a("Button",{staticClass:"btn",attrs:{type:"primary",long:"",size:"large"},on:{click:function(t){return e.handleSubmit("formInline")}}},[e._v(e._s(e.$t("page.login.submit"))+"\n                        ")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.resetStatus,expression:"resetStatus"}],staticClass:"primary",on:{click:function(t){return e.resetUpwd()}}},[e._v("忘记密码")])],1),a("Form",{directives:[{name:"show",rawName:"v-show",value:e.active&&e.resetStatus,expression:"active && resetStatus"}],ref:"formInline",attrs:{model:e.formInline,rules:e.ruleInline},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit("formInline")}}},[a("FormItem",{attrs:{prop:"phone"}},[a("Input",{staticStyle:{width:"100%"},attrs:{maxlength:"11",type:"text",prefix:"ios-contact-outline",placeholder:"请输入手机号",size:"large"},model:{value:e.formInline.phone,callback:function(t){e.$set(e.formInline,"phone",t)},expression:"formInline.phone"}})],1),a("FormItem",{attrs:{prop:"code"}},[a("div",{staticClass:"code"},[a("Input",{staticClass:"captch_input",attrs:{type:"text",prefix:"ios-keypad-outline",placeholder:"请输入验证码",size:"large"},model:{value:e.formInline.code,callback:function(t){e.$set(e.formInline,"code",t)},expression:"formInline.code"}}),a("Button",{staticClass:"send_code",attrs:{disabled:e.disabled,size:"large"},on:{click:e.code}},[e._v(e._s(e.text))])],1)]),a("FormItem",[a("Button",{staticClass:"btn",attrs:{type:"primary",long:"",size:"large"},on:{click:function(t){return e.handleSubmit("formInline")}}},[e._v(e._s(e.$t("page.login.submit"))+"\n                        ")])],1)],1),a("Form",{directives:[{name:"show",rawName:"v-show",value:!e.resetStatus,expression:"!resetStatus"}],ref:"formInline",attrs:{model:e.formInline,rules:e.ruleInline},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit("formInline")}}},[a("FormItem",[a("Input",{attrs:{type:"text",maxlength:"11",prefix:"ios-contact-outline",placeholder:"请输入手机号",size:"large"},model:{value:e.formInline.phone,callback:function(t){e.$set(e.formInline,"phone",t)},expression:"formInline.phone"}})],1),a("FormItem",{attrs:{prop:"code"}},[a("div",{staticClass:"code"},[a("Input",{staticClass:"captch_input",attrs:{type:"text",prefix:"ios-keypad-outline",placeholder:"请输入验证码",size:"large"},model:{value:e.formInline.code,callback:function(t){e.$set(e.formInline,"code",t)},expression:"formInline.code"}}),a("Button",{staticClass:"send_code",attrs:{disabled:e.disabled,size:"large"},on:{click:e.code}},[e._v(e._s(e.text))])],1)]),a("FormItem",[a("Input",{attrs:{type:"text",prefix:"ios-contact-outline",placeholder:"请输入密码",size:"large"},model:{value:e.formInline.new_pwd,callback:function(t){e.$set(e.formInline,"new_pwd",t)},expression:"formInline.new_pwd"}})],1),a("FormItem",[a("Button",{staticClass:"btn",attrs:{type:"primary",long:"",size:"large"},on:{click:function(t){return e.resetPwd("formInline")}}},[e._v("提交")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.resetStatus,expression:"!resetStatus"}],staticClass:"primary",on:{click:function(t){return e.resetUpwd()}}},[e._v("返回登录")])],1)],1)],1),a("Verify",{ref:"verify",attrs:{captchaType:"clickWord",imgSize:{width:"330px",height:"155px"}},on:{success:e.success}}),a("div",{staticClass:"footer"},[e.copyright?a("div",{staticClass:"pull-right"},[e._v(e._s(e.copyright))]):a("div",{staticClass:"pull-right"},[e._v("Copyright ©2014-2024 "),a("a",{staticClass:"infoUrl",attrs:{href:"https://www.crmeb.com",target:"_blank"}},[e._v(e._s(e.version))])])])],1)}),[],!1,null,"34334bd2",null);t.default=w.exports},"3dda":function(e,t){},"433f":function(e,t,a){e.exports=a.p+"view_admin/img/sw.95ce5637.jpg"},f5b0:function(e,t,a){}}]);