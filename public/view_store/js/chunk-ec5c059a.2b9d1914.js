(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ec5c059a"],{"0b65":function(t,e,r){"use strict";e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,r=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,r))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},4937:function(t,e,r){},"7dc5":function(t,e,r){"use strict";var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单备注",closable:!1},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"备注：",prop:"remark"}},[r("Input",{staticStyle:{width:"100%"},attrs:{maxlength:"200","show-word-limit":"",type:"textarea",placeholder:"订单备注"},model:{value:t.formValidate.remark,callback:function(e){t.$set(t.formValidate,"remark",e)},expression:"formValidate.remark"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:function(e){return t.cancel("formValidate")}}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:function(e){return t.putRemark("formValidate")}}},[t._v("提交")])],1)],1)},a=[],o=r("a34a"),c=r.n(o),i=r("f8b7");function u(t,e,r,n,a,o,c){try{var i=t[o](c),u=i.value}catch(d){return void r(d)}i.done?e(u):Promise.resolve(u).then(n,a)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function c(t){u(o,n,a,c,i,"next",t)}function i(t){u(o,n,a,c,i,"throw",t)}c(void 0)}))}}var s={name:"orderMark",data:function(){return{formValidate:{remark:""},modals:!1,ruleValidate:{remark:[{required:!0,message:"请输入备注信息",trigger:"blur"}]}}},props:{orderId:Number,currentTab:{type:String,default:""},remarkType:{default:"",type:String}},created:function(){},methods:{cancel:function(t){this.modals=!1,this.$refs[t].resetFields()},refundmark:function(t){this.formValidate.remark=t},getRemark:function(t){var e=this;Object(i["ab"])(t).then((function(t){e.formValidate.remark=t.data.remarks})).catch((function(t){e.$Message.error(t.msg)}))},getVipRemark:function(t){var e=this;Object(i["hb"])(t).then((function(t){e.formValidate.remark=t.data.remarks})).catch((function(t){e.$Message.error(t.msg)}))},putRemark:function(t){var e=this,r={id:this.orderId,remark:this.formValidate};this.$refs[t].validate((function(n){if(n){var a=null;a=e.remarkType?Object(i["X"])(r):3==e.currentTab?Object(i["S"])(r.id,{remarks:e.formValidate.remark}):4==e.currentTab?Object(i["T"])(r.id,{remarks:e.formValidate.remark}):Object(i["Y"])(r),a.then(function(){var r=d(c.a.mark((function r(n){return c.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.$Message.success(n.msg),e.modals=!1,e.$refs[t].resetFields(),e.$emit("submitFail");case 4:case"end":return r.stop()}}),r)})));return function(t){return r.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}else e.$Message.warning("请填写备注信息")}))}}},l=s,f=r("2877"),m=Object(f["a"])(l,n,a,!1,null,"343d1227",null);e["a"]=m.exports},d4ef:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{staticClass:"tabform",attrs:{model:t.rechargeData,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",[r("Col",{staticClass:"ivu-text-left mr"},[r("FormItem",{attrs:{label:"创建时间："}},[r("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1),r("Col",{staticClass:"ivu-text-left mr"},[r("FormItem",{attrs:{label:"选择店员："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.rechargeData.staff_id,callback:function(e){t.$set(t.rechargeData,"staff_id",e)},expression:"rechargeData.staff_id"}},t._l(t.staffData,(function(e){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label)+"\n                ")])})),1)],1)],1),r("Col",[r("Row",[r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"用户搜索：","label-for":"nickname"}},[r("Input",{staticStyle:{width:"250px"},attrs:{"enter-button":"",placeholder:"请输入用户名"},model:{value:t.rechargeData.nickname,callback:function(e){t.$set(t.rechargeData,"nickname",e)},expression:"rechargeData.nickname"}}),r("Button",{staticClass:"ml20 search",attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("搜索")]),r("Button",{staticClass:"ml15 search",attrs:{icon:"ios-share-outline"},on:{click:t.exports}},[t._v("导出")])],1)],1)],1)],1)],1)],1)],1),r("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[r("Table",{staticClass:"ivu-mt",attrs:{columns:t.rechargeColumns,data:t.tableList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"userInfo",fn:function(e){var n=e.row;e.index;return[r("div",{staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:n.avatar,expression:"row.avatar"}]})]),r("span",{staticClass:"tabBox_tit"},[t._v(t._s(n.nickname))])])]}},{key:"action",fn:function(e){var n=e.row;e.index;return[[r("a",{on:{click:function(e){return t.marker(n)}}},[t._v("订单备注")]),(parseFloat(n.refund_price)||0)<parseFloat(n.price)&&null==n.delete_time?r("Divider",{attrs:{type:"vertical"}}):t._e(),(parseFloat(n.refund_price)||0)<parseFloat(n.price)&&null==n.delete_time?r("a",{on:{click:function(e){return t.rechRefund(n)}}},[t._v("立即退款")]):t._e()]]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,current:t.rechargeData.page,"show-elevator":"","show-total":"","page-size":t.rechargeData.limit},on:{"on-change":t.pageChange}})],1)],1),r("order-remark",{ref:"remarks",attrs:{orderId:t.orderId,currentTab:"3"},on:{submitFail:t.submitFail}})],1)},a=[],o=r("a34a"),c=r.n(o),i=r("2f62"),u=(r("d708"),r("7dc5")),d=r("2e83"),s=r("0b65"),l=r("f8b7"),f=r("b4ea");function m(t,e,r,n,a,o,c){try{var i=t[o](c),u=i.value}catch(d){return void r(d)}i.done?e(u):Promise.resolve(u).then(n,a)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function c(t){m(o,n,a,c,i,"next",t)}function i(t){m(o,n,a,c,i,"throw",t)}c(void 0)}))}}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(r,!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function p(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var v={name:"index",components:{orderRemark:u["a"]},data:function(){return{orderId:0,options:s["a"],staffData:[],timeVal:[],rechargeData:{page:1,limit:15,nickname:"",staff_id:"",data:""},tableList:[],total:0,loading:!1,rechargeColumns:[{title:"ID",key:"id",width:60},{title:"订单号",key:"order_id",minWidth:180},{title:"用户信息",slot:"userInfo",minWidth:200},{title:"支付金额",key:"price",minWidth:110},{title:"充值类型",key:"_recharge_type",minWidth:100},{title:"支付时间",key:"_pay_time",minWidth:130},{title:"关联店员",key:"staff_name",minWidth:100},{title:"操作",slot:"action",fixed:"right",minWidth:200,align:"center"}]}},computed:b({},Object(i["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getRecharge(),this.staffList()},mounted:function(){},methods:{rechRefund:function(t){var e=this;this.$modalForm(Object(l["db"])(t.id)).then((function(){return e.getRecharge()}))},marker:function(t){this.orderId=t.id,this.$refs.remarks.modals=!0,this.$refs.remarks.getRemark(t.id)},submitFail:function(){this.getRecharge()},clearData:function(){this.rechargeData={page:1,limit:15,nickname:"",staff_id:"",data:""}},staffList:function(){var t=this;Object(f["r"])().then((function(e){t.staffData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},userSearchs:function(){this.rechargeData.page=1,this.getRecharge()},getRecharge:function(){var t=this;this.loading=!0,Object(l["L"])(this.rechargeData).then((function(e){var r=e.data;t.tableList=r.list,t.total=r.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.rechargeData.page=t,this.getRecharge()},onchangeTime:function(t){this.timeVal=t,this.rechargeData.data=this.timeVal[0]?this.timeVal.join("-"):"",this.rechargeData.page=1,this.getRecharge()},exports:function(){var t=h(c.a.mark((function t(){var e,r,n,a,o,i,u,s;return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e={orderType:3,staff_id:this.rechargeData.staff_id,nickname:this.rechargeData.nickname,data:this.rechargeData.data},r=[],n=[],a=[],o="",i=JSON.parse(JSON.stringify(e)),i.page=1,u=0;case 5:if(!(u<i.page+1)){t.next=22;break}return t.next=8,this.getExcelData(i);case 8:if(s=t.sent,o||(o=s.filename),n.length||(n=s.filekey),r.length||(r=s.header),!s.export.length){t.next=17;break}a=a.concat(s.export),i.page++,t.next=19;break;case 17:return Object(d["a"])(r,n,o,a),t.abrupt("return");case 19:u++,t.next=5;break;case 22:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getExcelData:function(t){return new Promise((function(e,r){Object(l["I"])(t,2).then((function(t){return e(t.data)}))}))}}},w=v,O=(r("ecc5"),r("2877")),j=Object(O["a"])(w,n,a,!1,null,"6c2aa58a",null);e["default"]=j.exports},ecc5:function(t,e,r){"use strict";var n=r("4937"),a=r.n(n);a.a},f8b7:function(t,e,r){"use strict";r.d(e,"m",(function(){return a})),r.d(e,"e",(function(){return o})),r.d(e,"p",(function(){return c})),r.d(e,"f",(function(){return i})),r.d(e,"o",(function(){return u})),r.d(e,"a",(function(){return d})),r.d(e,"j",(function(){return s})),r.d(e,"c",(function(){return l})),r.d(e,"d",(function(){return f})),r.d(e,"q",(function(){return m})),r.d(e,"b",(function(){return h})),r.d(e,"g",(function(){return g})),r.d(e,"i",(function(){return b})),r.d(e,"l",(function(){return p})),r.d(e,"K",(function(){return v})),r.d(e,"G",(function(){return w})),r.d(e,"L",(function(){return O})),r.d(e,"O",(function(){return j})),r.d(e,"y",(function(){return D})),r.d(e,"v",(function(){return k})),r.d(e,"B",(function(){return y})),r.d(e,"U",(function(){return _})),r.d(e,"fb",(function(){return x})),r.d(e,"J",(function(){return T})),r.d(e,"H",(function(){return F})),r.d(e,"N",(function(){return C})),r.d(e,"eb",(function(){return M})),r.d(e,"s",(function(){return V})),r.d(e,"kb",(function(){return P})),r.d(e,"u",(function(){return $})),r.d(e,"r",(function(){return I})),r.d(e,"A",(function(){return R})),r.d(e,"z",(function(){return S})),r.d(e,"Y",(function(){return E})),r.d(e,"X",(function(){return Y})),r.d(e,"gb",(function(){return W})),r.d(e,"M",(function(){return L})),r.d(e,"C",(function(){return B})),r.d(e,"W",(function(){return N})),r.d(e,"D",(function(){return J})),r.d(e,"bb",(function(){return z})),r.d(e,"I",(function(){return q})),r.d(e,"ab",(function(){return X})),r.d(e,"hb",(function(){return A})),r.d(e,"T",(function(){return G})),r.d(e,"S",(function(){return H})),r.d(e,"ib",(function(){return K})),r.d(e,"n",(function(){return Q})),r.d(e,"h",(function(){return U})),r.d(e,"jb",(function(){return Z})),r.d(e,"Z",(function(){return tt})),r.d(e,"R",(function(){return et})),r.d(e,"Q",(function(){return rt})),r.d(e,"x",(function(){return nt})),r.d(e,"w",(function(){return at})),r.d(e,"k",(function(){return ot})),r.d(e,"db",(function(){return ct})),r.d(e,"t",(function(){return it})),r.d(e,"P",(function(){return ut})),r.d(e,"V",(function(){return dt})),r.d(e,"lb",(function(){return st})),r.d(e,"cb",(function(){return lt})),r.d(e,"F",(function(){return ft})),r.d(e,"E",(function(){return mt}));var n=r("b6bd");function a(t){return Object(n["a"])({url:"order/cashier/product",method:"get",params:t})}function o(){return Object(n["a"])({url:"order/cashier/cate",method:"get"})}function c(t){return Object(n["a"])({url:"order/cashier/user",method:"post",data:t})}function i(t){return Object(n["a"])({url:"order/cashier/code",method:"post",data:t})}function u(t){return Object(n["a"])({url:"order/cashier/staff",method:"get",params:t})}function d(t,e){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"post",data:e})}function s(t,e){return Object(n["a"])({url:"order/cashier/detail/".concat(t,"/").concat(e),method:"get"})}function l(t,e,r){return Object(n["a"])({url:"order/cashier/cart/".concat(t,"/").concat(e),method:"get",params:r})}function f(t,e){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"put",data:e})}function m(t){return Object(n["a"])({url:"order/cashier/changeCart",method:"put",data:t})}function h(t,e){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"DELETE",data:e})}function g(t,e){return Object(n["a"])({url:"order/cashier/compute/".concat(t),method:"post",data:e})}function b(t,e){return Object(n["a"])({url:"order/cashier/create/".concat(t),method:"post",data:e})}function p(t,e){return Object(n["a"])({url:"order/cashier/pay/".concat(t),method:"post",data:e})}function v(t){return Object(n["a"])({url:"order/list",method:"get",params:t})}function w(t){return Object(n["a"])({url:"order/chart",method:"get",params:t})}function O(t){return Object(n["a"])({url:"order/recharge",method:"get",params:t})}function j(t){return Object(n["a"])({url:"order/vip_order",method:"get",params:t})}function D(t){return Object(n["a"])({url:"order/edit/".concat(t),method:"get"})}function k(t){return Object(n["a"])({url:"order/express_list?status="+t,method:"get"})}function y(t){return Object(n["a"])({url:"/refund/express/".concat(t),method:"get"})}function _(t){return Object(n["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function x(t){return Object(n["a"])({url:"/order/split_delivery/".concat(t.id),method:"put",data:t.datas})}function T(t){return Object(n["a"])({url:"/order/express/temp",method:"get",params:t})}function F(){return Object(n["a"])({url:"/order/delivery/list",method:"get"})}function C(){return Object(n["a"])({url:"/order/sheet_info",method:"get"})}function M(t){return Object(n["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function V(t){return Object(n["a"])({url:"/order/distribution/".concat(t),method:"get"})}function P(t){return Object(n["a"])({url:"/order/write_update/".concat(t),method:"put"})}function $(t){return Object(n["a"])({url:"/order/express/".concat(t),method:"get"})}function I(t){return Object(n["a"])({url:"/order/info/".concat(t),method:"get"})}function R(t){return Object(n["a"])({url:"/refund/detail/".concat(t),method:"get"})}function S(t){return Object(n["a"])({url:"/order/status/".concat(t.id),method:"get",params:t.datas})}function E(t){return Object(n["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function Y(t){return Object(n["a"])({url:"/refund/remark/".concat(t.id),method:"put",data:t.remark})}function W(t){return Object(n["a"])({url:"order/split_order/".concat(t),method:"get"})}function L(t){return Object(n["a"])({url:"refund/list",method:"get",params:t})}function B(t){return Object(n["a"])({url:"/order/refund/".concat(t),method:"get"})}function N(t){return Object(n["a"])({url:"/refund/refund/".concat(t.id),method:"put",data:t})}function J(t){return Object(n["a"])({url:"/order/no_refund/".concat(t),method:"get"})}function z(t){return Object(n["a"])({url:"/order/refund_integral/".concat(t),method:"get"})}function q(t,e){return Object(n["a"])({url:"order/export/".concat(e),method:"post",data:t})}function X(t){return Object(n["a"])({url:"order/recharge/remark/".concat(t),method:"get"})}function A(t){return Object(n["a"])({url:"order/vip/remark/".concat(t),method:"get"})}function G(t,e){return Object(n["a"])({url:"order/vip/remark/".concat(t),method:"put",data:e})}function H(t,e){return Object(n["a"])({url:"order/recharge/remark/".concat(t),method:"put",data:e})}function K(t){return Object(n["a"])({url:"order/vip/status/".concat(t),method:"get"})}function Q(){return Object(n["a"])({url:"order/cashier/cashier_scan",method:"get"})}function U(t,e){return Object(n["a"])({url:"order/cashier/coupon_list/".concat(t),method:"post",data:e})}function Z(t){return Object(n["a"])({url:"order/writeOff/cartInfo",method:"get",params:t})}function tt(t,e){return Object(n["a"])({url:"order/write_update/".concat(t),method:"put",data:e})}function et(t,e){return Object(n["a"])({url:"order/cashier/switch/".concat(e),method:"post",data:t})}function rt(t){return Object(n["a"])({url:"order/cashier/hang",method:"post",data:t})}function nt(t,e){return Object(n["a"])({url:"order/cashier/hang/list/".concat(t),method:"get",params:e})}function at(t){return Object(n["a"])({url:"order/cashier/hang/".concat(t),method:"get"})}function ot(t){return Object(n["a"])({url:"order/cashier/hang",method:"DELETE",params:{id:t}})}function ct(t){return Object(n["a"])({url:"order/recharge/".concat(t,"/refund_edit"),method:"get"})}function it(t){return Object(n["a"])({url:"/order/distribution_info",method:"get",params:{ids:t}})}function ut(t){return Object(n["a"])({url:"/order/write/form/".concat(t),method:"get"})}function dt(t){return Object(n["a"])({url:"/order/open/refund/".concat(t.id),method:"put",data:t})}function st(t){return Object(n["a"])({url:"order/writeoff/records/".concat(t),method:"get"})}function lt(){return Object(n["a"])({url:"refund/reason",method:"get"})}function ft(t){return Object(n["a"])({url:"order/card/benefits/".concat(t),method:"get"})}function mt(t){return Object(n["a"])({url:"order/benefits/".concat(t),method:"get"})}}}]);