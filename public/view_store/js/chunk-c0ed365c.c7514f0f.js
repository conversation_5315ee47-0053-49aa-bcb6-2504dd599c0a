(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c0ed365c"],{"18c2":function(t,e,a){"use strict";var r=a("c0aa"),n=a.n(r);n.a},3643:function(t,e,a){},a584:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Row",{staticClass:"ivu-mt",attrs:{type:"flex",align:"middle",gutter:10}},t._l(t.cardLists,(function(e,r){return a("Col",{key:r,staticClass:"ivu-mb",attrs:{xl:e.col,lg:6,md:12,sm:24,xs:24}},[a("Card",{staticClass:"card_cent",attrs:{shadow:"",padding:0}},[a("div",{staticClass:"card_box"},[a("div",{staticClass:"card_box_cir",class:{one:r%5==0,two:r%5==1,three:r%5==2,four:r%5==3,five:r%5==4}},[a("div",{staticClass:"card_box_cir1",class:{one1:r%5==0,two1:r%5==1,three1:r%5==2,four1:r%5==3,five1:r%5==4}},[e.type?a("span",{staticClass:"iconfont",class:e.className}):a("Icon",{attrs:{type:e.className}})],1)]),a("div",{staticClass:"card_box_txt"},[a("span",{staticClass:"sp1",domProps:{textContent:t._s(e.count||0)}}),a("span",{staticClass:"sp2",domProps:{textContent:t._s(e.name)}})])])])],1)})),1)],1)},n=[],i={name:"cards",data:function(){return{}},props:{cardLists:Array},methods:{},created:function(){}},s=i,o=(a("f296"),a("2877")),c=Object(o["a"])(s,r,n,!1,null,"4bbf6041",null);e["a"]=c.exports},c0aa:function(t,e,a){},c24f:function(t,e,a){"use strict";a.d(e,"r",(function(){return n})),a.d(e,"k",(function(){return i})),a.d(e,"l",(function(){return s})),a.d(e,"a",(function(){return o})),a.d(e,"q",(function(){return c})),a.d(e,"j",(function(){return u})),a.d(e,"m",(function(){return l})),a.d(e,"u",(function(){return d})),a.d(e,"d",(function(){return f})),a.d(e,"f",(function(){return m})),a.d(e,"c",(function(){return h})),a.d(e,"e",(function(){return p})),a.d(e,"p",(function(){return b})),a.d(e,"n",(function(){return v})),a.d(e,"t",(function(){return y})),a.d(e,"o",(function(){return g})),a.d(e,"s",(function(){return _})),a.d(e,"g",(function(){return x})),a.d(e,"i",(function(){return j})),a.d(e,"b",(function(){return w})),a.d(e,"h",(function(){return O}));var r=a("b6bd");function n(){return Object(r["a"])({url:"user/user_label_cate",method:"get"})}function i(){return Object(r["a"])({url:"user/user_label_cate/create",method:"get"})}function s(t){return Object(r["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function o(t){return Object(r["a"])({url:"user/user_label",method:"get",params:t})}function c(){return Object(r["a"])({url:"user/user_label/create",method:"get"})}function u(t){return Object(r["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function l(t){return Object(r["a"])({url:"user/user",method:"get",params:t})}function d(t){return Object(r["a"])({url:"user/search",method:"get",params:t})}function f(t){return Object(r["a"])({url:"user/label/".concat(t),method:"get"})}function m(t,e){return Object(r["a"])({url:"user/label/".concat(t),method:"post",data:e})}function h(t){return Object(r["a"])({url:"user/user/".concat(t),method:"get"})}function p(t){return Object(r["a"])({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function b(t){return Object(r["a"])({url:"user/set_label",method:"post",data:t})}function v(){return Object(r["a"])({url:"user/recharge/meal",method:"get"})}function y(){return Object(r["a"])({url:"user/member/ship",method:"get"})}function g(t){return Object(r["a"])({url:"user/recharge",method:"post",data:t})}function _(t){return Object(r["a"])({url:"/user/member",method:"post",data:t})}function x(t){return Object(r["a"])({url:"staff/binding/user",method:"post",data:t})}function j(t){return Object(r["a"])({url:"updatePwd",method:"PUT",data:t})}function w(t,e){return Object(r["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function O(){return Object(r["a"])({url:"staff/staff_info",method:"get"})}},f296:function(t,e,a){"use strict";var r=a("3643"),n=a.n(r);n.a},f75a:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{directives:[{name:"resize",rawName:"v-resize",value:t.handleResize,expression:"handleResize"}],staticClass:"box"},[r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"时间筛选："}},[r("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1),r("cards-data",{attrs:{cardLists:t.cardLists}}),r("Row",{staticClass:"ivu-mt Box",attrs:{gutter:24}},[r("Col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("营业趋势")]),t.infoList?r("echarts-from",{ref:"visitChart",attrs:{series:t.series,echartsTitle:t.inlie,infoList:t.infoList,yAxisData:t.yAxisData}}):t._e()],1)],1),r("Row",{staticClass:"ivu-mt",attrs:{gutter:24}},[r("Col",{staticClass:"ivu-mb dashboard-console-visit",attrs:{xl:15,lg:12,md:24,sm:24,xs:24}},[r("Card",{staticClass:"tablebox",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("交易数据")]),r("Table",{ref:"selection",attrs:{columns:t.columns2,data:t.tabList2,loading:t.loading,height:"300","no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"avatar",fn:function(t){var e=t.row;t.index;return[e.uid?r("img",{attrs:{src:e.avatar,width:"36",height:"36"}}):r("img",{attrs:{src:a("586c"),width:"36",height:"36"}})]}},{key:"nickname",fn:function(e){var a=e.row;e.index;return[r("span",[t._v(t._s(a.uid?a.nickname:"游客"))])]}},{key:"pay_time",fn:function(e){var a=e.row;e.index;return[r("div",[t._v(t._s(t.$moment(1e3*a.pay_time).format("YYYY-MM-DD H:mm:ss")))])]}},{key:"pay_price",fn:function(e){var a=e.row;e.index;return[r("span",{staticClass:"colorred"},[t._v("￥ "+t._s(a.pay_price))])]}}])})],1)],1),r("Col",{attrs:{xl:9,lg:12,md:24,sm:24,xs:24}},[r("Card",{staticClass:"dashboard-console-visit",attrs:{bordered:!1,"dis-hover":""}},[r("div",{attrs:{slot:"title"},slot:"title"},[r("div",{staticClass:"ivu-pl-8 fonts"},[t._v("交易类型")])]),r("echarts-from",{ref:"visitChart",attrs:{infoList:t.infoLists,echartsTitle:t.circle}})],1)],1)],1),r("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"fonts"},[t._v("店员业绩")]),r("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tabList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"}})],1)],1)},n=[],i=a("a34a"),s=a.n(i),o=a("2f62"),c=a("b6bd");function u(t){return Object(c["a"])({url:"home/header",method:"get",params:t})}function l(t){return Object(c["a"])({url:"home/orderChart",method:"get",params:t})}function d(t){return Object(c["a"])({url:"home/staff",method:"get",params:t})}function f(t){return Object(c["a"])({url:"home/operate",method:"get",params:t})}var m=a("c24f"),h=a("61f7"),p=a("9901"),b=a("a584"),v=a("0b65"),y=a("d708");function g(t,e,a,r,n,i,s){try{var o=t[i](s),c=o.value}catch(u){return void a(u)}o.done?e(c):Promise.resolve(c).then(r,n)}function _(t){return function(){var e=this,a=arguments;return new Promise((function(r,n){var i=t.apply(e,a);function s(t){g(i,r,n,s,o,"next",t)}function o(t){g(i,r,n,s,o,"throw",t)}s(void 0)}))}}function x(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function j(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?x(a,!0).forEach((function(e){w(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):x(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function w(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var O={name:"home",components:{cardsData:b["a"],echartsFrom:p["a"]},data:function(){return{loading:!1,optionData:{},formValidate:{status:"",extract_type:"",nireid:"",data:"thirtyday",page:1,limit:20},timeVal:[],fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"昨天",val:"yesterday"},{text:"今天",val:"today"},{text:"最近7天",val:"sevenday"},{text:"近30天",val:"thirtyday"},{text:"本月",val:"month"},{text:"本年",val:"year"}]},options:v["a"],cardLists:[],extractStatistics:{},series:[],yAxisData:[],infoList:{},infoLists:{},circle:"circle1",inlie:"inlie",columns:[{title:"店员名称",key:"staff_name",minWidth:80},{title:"推广用户数",width:180,key:"spread_count"},{title:"推广用户消费金额",key:"speread_order_price",minWidth:100},{title:"推广付费会员数",key:"vip_count",minWidth:100},{title:"推广付费会员金额",key:"vip_price",minWidth:100}],columns2:[{title:"头像",slot:"avatar",minWidth:50},{title:"用户名称",minWidth:100,slot:"nickname"},{title:"订单号",key:"order_id",minWidth:100},{title:"交易金额",slot:"pay_price",minWidth:100},{title:"成交时间",slot:"pay_time",minWidth:100}],tabList:[],tabList2:[]}},computed:j({},Object(o["e"])("store/layout",["isMobile"]),{},Object(o["c"])("store/menu",["filterSider"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),created:function(){var t=new Date,e=new Date;e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),this.timeVal=[e,t],this.formValidate.data=Object(h["a"])(e,"yyyy/MM/dd")+"-"+Object(h["a"])(t,"yyyy/MM/dd"),this.cardList(),this.orderchart(),this.staff(),this.getInfo()},mounted:function(){this.$nextTick((function(){this.trends()}))},methods:{int:function(){this.cardList(),this.orderchart(),this.trends(),this.staff()},trends:function(){var t=this;f({data:this.formValidate.data}).then(function(){var e=_(s.a.mark((function e(a){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.infoList=a.data||{},t.series=t.infoList.series||[],t.yAxisData=[{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}];case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},orderchart:function(){var t=this;l({data:this.formValidate.data}).then((function(e){t.tabList2=e.data.order_list,t.infoLists=e.data}))},staff:function(){var t=this;d({data:this.formValidate.data}).then((function(e){t.tabList=e.data}))},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.int()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",""==t[0]&&(this.formValidate.data="thirtyday"),this.formValidate.page=1,this.int()},cardList:function(){var t=this;u({data:this.formValidate.data}).then((function(e){t.extractStatistics=e.data,t.cardLists=[{col:6,count:t.extractStatistics.store_income,name:"门店订单金额",className:"iconmendiandingdanjine",type:1},{col:6,count:t.extractStatistics.store_use_yue,name:"余额消耗金额",className:"iconyuexiaohaojine",type:1},{col:6,count:t.extractStatistics.recharge_price,name:"用户充值金额",className:"iconmendianxinzengyonghushu",type:1},{col:6,count:t.extractStatistics.cashier_order_price,name:"收银订单金额",className:"iconshouyindingdanjine",type:1},{col:6,count:t.extractStatistics.vip_price,name:"付费会员金额",className:"iconfufeihuiyuanjine",type:1},{col:6,count:t.extractStatistics.store_order_price,name:"分配订单金额",className:"iconfenpeidingdanjine",type:1},{col:6,count:t.extractStatistics.store_writeoff_order_price,name:"核销订单金额",className:"iconhexiaodingdanjine",type:1},{col:6,count:t.extractStatistics.store_user_count,name:"门店新增用户数",className:"iconxinzengyonghushu1",type:1},{col:6,count:t.extractStatistics.store_pay_user_count,name:"门店成交用户数",className:"iconmendianchengjiaoyonghushu",type:1},{col:6,count:t.extractStatistics.card_count,name:"微信会员卡激活数",className:"iconhuiyuankajihuoshu",type:1}]}))},handleResize:function(){(this.infoList||this.infoLists)&&this.$refs.visitChart.handleResize()},getInfo:function(){var t=this;Object(m["h"])().then((function(e){var a=e.data.product_category_status,r=t.$store.state.store.menus.menusName;function n(t){return t.map((function(t){return Array.isArray(t.children)&&(t.children=n(t.children)),t.isShow=!0,t.path=="".concat(y["a"].routePre,"/product/category")&&(t.isShow=!!a),t}))}var i=n(r);t.$store.commit("store/user/setProductCategoryStatus",e.data.product_category_status),t.$store.commit("store/menus/setmenusNav",i),t.$store.commit("store/menu/setSider",i)})).catch((function(e){t.$Message.error(e.msg)}))}}},C=O,L=(a("18c2"),a("2877")),k=Object(L["a"])(C,r,n,!1,null,"1fc09318",null);e["default"]=k.exports}}]);