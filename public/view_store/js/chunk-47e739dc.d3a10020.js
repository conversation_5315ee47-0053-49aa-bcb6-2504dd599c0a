(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-47e739dc","chunk-79a32fbc"],{"0996":function(t,e,r){"use strict";var a=r("d4e5"),i=r.n(a);i.a},"16c6":function(t,e,r){"use strict";var a=r("f133"),i=r.n(a);i.a},"1a3d":function(t,e,r){t.exports=r.p+"view_store/img/process2.f9f8c6c6.png"},"526a":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"headerCard",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"header acea-row row-between-wrapper"},[a("div",{staticClass:"title"},[1!=t.$route.query.isShow?a("router-link",{attrs:{to:{path:t.routePre+"/order/index"}}},[a("Button",{staticClass:"mr10",attrs:{size:"small"}},[t._v("返回")])],1):t._e(),t._v("\n\t\t\t\t\t收银台\n\t\t\t\t\t")],1),a("div",{staticClass:"right acea-row row-middle"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.storeInfos.avatar}})]),a("div",{staticClass:"info"},[a("span",[t._v(t._s(t.storeInfos.account)+"(ID: "+t._s(t.storeInfos.id)+")")])]),a("div",{staticClass:"storeBnt",on:{click:t.storeTap}},[t._v("切换店员")])])])]),a("div",{staticClass:"goodsCard acea-row row-between"},[a("div",{staticClass:"conter"},[a("Card",{staticClass:"ivu-mt cart",style:"height:"+t.clientHeight+"px;",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"acea-row"},[a("div",{staticClass:"tourist",style:"height:"+t.clientHeight+"px;"},t._l(t.hangData,(function(e,i){return a("div",{key:i,staticClass:"item acea-row row-middle",class:t.activeHangon==i?"on":"",on:{click:function(r){return t.hangDataTap(i,e)}}},[a("div",{staticClass:"pictrue"},[e.uid?a("img",{attrs:{src:e.avatar}}):a("img",{attrs:{src:r("586c")}})]),a("div",{staticClass:"name line1"},[t._v(t._s(e.uid?e.nickname:"游客"))]),e.is_check?t._e():a("div",{staticClass:"iconfont iconguadan"})])})),0),a("div",{staticClass:"acea-row row-between row-bottom"},[a("div",{staticClass:"left"},[0==t.checkOut?a("div",[t.userInfo?a("div",{staticClass:"title acea-row row-middle"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.userInfo.avatar}})]),a("div",{staticClass:"text"},[a("div",{staticClass:"textCon line1"},[a("span",{staticClass:"name"},[t._v(t._s(t.userInfo.nickname))]),t._v("[关联店员："+t._s(t.storeInfos.account)+"]")]),t.userInfo.uid?a("div",[t.userInfo.phone?a("span",{staticClass:"mr10"},[t._v(t._s(t.userInfo.phone))]):t._e(),a("span",{staticClass:"balance"},[t._v("余额"),a("span",{staticClass:"num"},[t._v(t._s(t.userInfo.now_money))])]),a("span",{staticClass:"recharge",on:{click:t.rechargeBnt}},[t._v("充值")]),a("span",{staticClass:"balance"},[t._v("积分"),a("span",{staticClass:"num"},[t._v(t._s(t.userInfo.integral))])])]):t._e()]),a("Dropdown",{staticClass:"switchs",attrs:{trigger:"click"},on:{"on-click":function(e){return t.changeMenu(e)}}},[a("a",{attrs:{href:"javascript:void(0)"}},[t._v("\n\t\t\t\t\t\t\t\t\t\t\t    切换会员\n\t\t\t\t\t\t\t\t\t\t\t  "),a("Icon",{attrs:{type:"ios-arrow-down"}})],1),a("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[a("DropdownItem",{attrs:{name:"1"}},[t._v("选择用户")]),a("DropdownItem",{attrs:{name:"2"}},[t._v("游客")])],1)],1)],1):t._e(),a("div",{staticClass:"listCon",style:"height:"+t.cartHeight+"px;"},[a("div",{staticClass:"list"},t._l(t.cartList,(function(e,r){return a("div",{key:r,staticClass:"item acea-row row-middle"},[a("div",{staticClass:"pictrue"},[e.productInfo.attrInfo?a("img",{attrs:{src:e.productInfo.attrInfo.image}}):a("img",{attrs:{src:e.productInfo.image}})]),a("div",{staticClass:"text"},[a("div",{staticClass:"name line1"},[t._v(t._s(e.productInfo.store_name))]),e.productInfo.attrInfo&&e.productInfo.spec_type?a("div",{staticClass:"info",on:{click:function(r){return t.cartAttr(e)}}},[t._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t"+t._s(e.productInfo.attrInfo.suk)),a("span",{staticClass:"iconfont iconxiayi"})]):a("div",{staticClass:"info"},[t._v("默认")]),a("div",[t._v("¥ "+t._s(e.truePrice))])]),a("div",{staticClass:"del",on:{click:function(a){return t.delCart(e,r)}}},[t._v("删除")]),a("div",{staticClass:"cartBnt acea-row row-center-wrapper"},[a("InputNumber",{attrs:{min:1,max:e.branch_stock,editable:!1,size:"small","controls-outside":""},on:{"on-change":function(r){return t.cartChange(e)}},model:{value:e.cart_num,callback:function(r){t.$set(e,"cart_num",r)},expression:"item.cart_num"}})],1)])})),0),a("div",{staticClass:"list"},t._l(t.invalidList,(function(e,r){return a("div",{key:r,staticClass:"item acea-row row-middle"},[a("div",{staticClass:"pictrue"},[e.productInfo.attrInfo?a("img",{attrs:{src:e.productInfo.attrInfo.image}}):a("img",{attrs:{src:e.productInfo.image}})]),a("div",{staticClass:"text invalid"},[a("div",{staticClass:"name line1"},[t._v(t._s(e.productInfo.store_name))]),e.productInfo.attrInfo?a("div",{staticClass:"info"},[t._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t"+t._s(e.productInfo.attrInfo.suk)),a("span",{staticClass:"iconfont iconxiayi"})]):a("div",{staticClass:"info"},[t._v("默认")]),a("div",{staticClass:"end"},[t._v("该商品已失效")])]),a("div",{staticClass:"del",on:{click:function(a){return t.delCart(e,r,1)}}},[t._v("删除")])])})),0),t.invalidList.length||t.cartList.length?t._e():a("div",{staticClass:"noCart acea-row row-center-wrapper"},[a("div",[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("df84")}})]),a("div",{staticClass:"tip"},[t._v("购物车暂时无商品")])])])])]):a("div",{staticStyle:{"padding-top":"15px"},style:"height:"+(t.cartHeight+105)+"px;"},[a("Form",{ref:"lodgeFrom",attrs:{model:t.lodgeFrom,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"",labelWidth:20,"label-for":"nickname"}},[a("Row",[a("Col",[a("Input",{staticStyle:{width:"370px"},attrs:{placeholder:"请输入用户名称/ID/手机号","element-id":"nickname",search:"","enter-button":""},on:{"on-search":t.storeSearch},model:{value:t.lodgeFrom.keyword,callback:function(e){t.$set(t.lodgeFrom,"keyword",e)},expression:"lodgeFrom.keyword"}})],1)],1)],1)],1),a("Table",{ref:"selection",staticClass:"tableList",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.tableHang},scopedSlots:t._u([{key:"nickname",fn:function(e){var r=e.row;e.index;return[a("div",[t._v(t._s(r.uid?r.nickname:"游客"))])]}},{key:"action",fn:function(e){var r=e.row,i=e.index;return[a("a",{on:{click:function(e){return t.billHang(r,i)}}},[t._v("提单")]),a("a",{staticClass:"ml10",on:{click:function(e){return t.hangDel(r,i)}}},[t._v("删除")])]}}])}),a("div",{staticClass:"acea-row row-right page mr5"},[a("Page",{attrs:{total:t.totalHang,size:"small","show-total":"",current:t.lodgeFrom.page,"page-size":t.lodgeFrom.limit},on:{"on-change":t.pageHangChange}})],1)],1),a("div",{staticClass:"truePrice"},[t._v("实付：¥ "),a("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])]),a("div",{staticClass:"footer"},[a("div",{staticClass:"conInfo acea-row row-between-wrapper"},[a("div",[t._v("共计 "+t._s(t.cartSum)+" 件，已优惠\n\t\t\t\t\t\t\t\t\t\t\t¥"+t._s(this.$computes.Add(this.$computes.Add(t.priceInfo.couponPrice,t.priceInfo.deductionPrice),t.priceInfo.vipPrice)||0)+"\n\t\t\t\t\t\t\t\t\t\t")]),t.cartList.length?a("div",{staticClass:"detailed",on:{click:t.discountCon}},[t._v("优惠明细 >")]):t._e()]),t.cartList.length?a("div",{staticClass:"pay acea-row row-between-wrapper"},[a("div",{staticClass:"bnt",on:{click:function(e){return t.payPrice("cash")}}},[t._v("现金收款")]),a("div",{staticClass:"bnt",on:{click:function(e){return t.payPrice("")}}},[t._v("微信/支付宝")]),t.userInfo.uid&&t.userInfo.now_money>=(t.priceInfo.payPrice||0)?a("div",{staticClass:"bnt on",on:{click:function(e){return t.payPrice("yue")}}},[t._v("余额收款")]):a("div",{staticClass:"bnt on bntUid"},[t._v("余额收款")])]):a("div",{staticClass:"pay noCart acea-row row-between-wrapper"},[a("div",{staticClass:"bnt"},[t._v("现金收款")]),a("div",{staticClass:"bnt"},[t._v("微信/支付宝")]),a("div",{staticClass:"bnt on"},[t._v("余额收款")])])])]),a("div",{staticClass:"right"},[a("div",{staticClass:"navTabs"},[a("img",{class:t.checkOut?"":"label01",attrs:{src:r("9779")},on:{click:function(e){return t.navTab(0)}}}),a("img",{staticClass:"label02",attrs:{src:r("6aa25")},on:{click:function(e){return t.navTab(1)}}})]),t.cartList.length?a("div",[t.checkOut?t._e():a("div",{staticClass:"item",on:{click:t.lodgeTap}},[t._v("挂单")]),a("div",{staticClass:"item",class:t.integral?"on":"",on:{click:t.integralTap}},[t._v("\n\t\t\t\t\t\t\t\t\t\t积分\n\t\t\t\t\t\t\t\t\t")]),a("div",{staticClass:"item",class:t.coupon?"on":"",on:{click:t.couponTap}},[t._v("\n\t\t\t\t\t\t\t\t\t\t优惠券\n\t\t\t\t\t\t\t\t\t\t"),t.coupon?a("span",{staticClass:"iconfont iconcha",on:{click:function(e){return e.stopPropagation(),t.closeCoupon(e)}}}):t._e()]),a("div",{staticClass:"item",on:{click:t.changePrice}},[t._v("改价")]),a("div",{staticClass:"item",on:{click:t.remarks}},[t._v("备注")])]):a("div",{staticClass:"noCart"},[t.checkOut?t._e():a("div",{staticClass:"item"},[t._v("挂单")]),a("div",{staticClass:"item"},[t._v("\n\t\t\t\t\t\t\t\t\t\t积分\n\t\t\t\t\t\t\t\t\t")]),a("div",{staticClass:"item"},[t._v("优惠券")]),a("div",{staticClass:"item"},[t._v("改价")]),a("div",{staticClass:"item"},[t._v("备注")])]),t.cartList.length||t.invalidList.length?a("div",{staticClass:"item",on:{click:t.delAll}},[t._v("\n\t\t\t\t\t\t\t\t\t清空\n\t\t\t\t\t\t\t\t")]):a("div",{staticClass:"noCart"},[a("div",{staticClass:"item"},[t._v("\n\t\t\t\t\t\t\t\t\t\t清空\n\t\t\t\t\t\t\t\t\t")])])])])])])],1),a("div",{staticClass:"goods"},[a("Card",{staticClass:"ivu-mt",style:"height:"+t.clientHeight+"px;",attrs:{bordered:!1,"dis-hover":""}},[a("Tabs",{attrs:{animated:!1},model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[a("TabPane",{attrs:{label:"扫码",name:"1"}}),a("TabPane",{attrs:{label:"商品",name:"2"}})],1),1==t.currentTab?a("div",{staticClass:"smCode"},[a("Input",{ref:"goodsNum",attrs:{size:"large",type:"url",placeholder:"请点击输入框聚焦扫码"},on:{input:t.inputSaoMa},model:{value:t.codeNum,callback:function(e){t.codeNum=e},expression:"codeNum"}})],1):t._e(),2==t.currentTab?a("div",{staticClass:"acea-row row-between"},[a("div",{staticClass:"goodsCon"},[a("Input",{staticClass:"input",attrs:{search:"","enter-button":"",placeholder:"搜索商品名称/ID/唯一码","element-id":"name"},on:{"on-search":t.orderSearch},model:{value:t.goodFrom.store_name,callback:function(e){t.$set(t.goodFrom,"store_name",e)},expression:"goodFrom.store_name"}},[a("Select",{staticStyle:{width:"90px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.goodFrom.field_key,callback:function(e){t.$set(t.goodFrom,"field_key",e)},expression:"goodFrom.field_key"}},[a("Option",{attrs:{value:"all"}},[t._v("全部")]),a("Option",{attrs:{value:"store_name"}},[t._v("商品名称")]),a("Option",{attrs:{value:"id"}},[t._v("ID")]),a("Option",{attrs:{value:"bar_code"}},[t._v("唯一码")])],1)],1),t.goodData.length?a("div",{staticClass:"list acea-row",style:"height:"+t.goodsHeight+"px;"},t._l(t.goodData,(function(e,r){return a("div",{key:r,staticClass:"item",on:{click:function(r){return t.attrTap(e)}}},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:e.image}})]),a("div",{staticClass:"text"},[a("div",{staticClass:"name line1"},[t._v(t._s(e.store_name))]),a("div",{staticClass:"stock"},[t._v("库存 "+t._s(e.stock))]),a("div",{staticClass:"money"},[t._v("¥ "+t._s(e.price))]),e.cart_num?a("div",{staticClass:"iconfont iconxuanzhong6"}):t._e()])])})),0):a("div",{staticClass:"noGood acea-row row-center-wrapper"},[a("div",[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("df84")}})]),a("div",{staticClass:"tip"},[t._v("暂时无商品")])])]),t.goodData.length?a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.goodFrom.limit},on:{"on-change":t.pageChange}})],1):t._e()],1),a("div",{staticClass:"goodClass acea-row row-center",style:"height:"+(Number(t.goodsHeight)+87)+"px;"},[a("div",t._l(t.cateData,(function(e,r){return a("div",{key:r,staticClass:"item line1",class:t.currentCate==r?"on":"",on:{click:function(a){return t.cateTap(e,r)}}},[t._v(t._s(e.cate_name))])})),0)])]):t._e()],1)],1)]),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户列表","mask-closable":!1,width:"900"},model:{value:t.modalUser,callback:function(e){t.modalUser=e},expression:"modalUser"}},[t.modalUser?a("userList",{ref:"users",on:{getUserId:t.getUserId}}):t._e()],1),a("recharge",{ref:"recharge",attrs:{userInfo:t.userInfo},on:{getSuccess:t.getSuccess}}),t.userInfo&&t.cartList.length?a("couponList",{ref:"coupon",attrs:{uid:t.userInfo.uid,cartList:t.cartList},on:{getCouponId:t.getCouponId}}):t._e(),a("storeList",{ref:"store",on:{getStoreId:t.getStoreId,getUserInfo:t.getUserInfo}}),a("productAttr",{ref:"attrs",attrs:{attr:t.attr,disabled:t.disabled,isCart:t.isCart},on:{ChangeAttr:t.ChangeAttr,goCat:t.goCat}}),a("Modal",{attrs:{title:"备注","footer-hide":""},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("form-create",{staticClass:"remark",attrs:{rule:t.rule},on:{"on-submit":t.onSubmit},model:{value:t.fapi,callback:function(e){t.fapi=e},expression:"fapi"}})],1),a("Modal",{attrs:{title:"订单改价","footer-hide":""},on:{"on-cancel":t.cancelPrice},model:{value:t.modal2,callback:function(e){t.modal2=e},expression:"modal2"}},[a("Form",{attrs:{model:t.formItem,"label-width":100}},[a("FormItem",{attrs:{label:"订单改价："}},[a("div",{staticClass:"acea-row"},[a("div",{staticClass:"inputNum"},[a("InputNumber",{attrs:{min:0},on:{"on-change":t.tapPrice},model:{value:t.formItem.price,callback:function(e){t.$set(t.formItem,"price",e)},expression:"formItem.price"}}),t._v("元")],1),a("div",{staticClass:"inputNum discount"},[a("InputNumber",{attrs:{min:0},on:{"on-change":t.tapDiscount},model:{value:t.discountPrice,callback:function(e){t.discountPrice=e},expression:"discountPrice"}}),t._v("%")],1)])]),a("FormItem",{attrs:{label:"改价后金额："}},[a("div",{staticClass:"changePrice"},[t._v("¥"),a("span",{staticClass:"price"},[t._v(t._s(t.formItem.price||0))])])]),a("div",{staticClass:"acea-row row-center-wrapper"},[a("Button",{staticClass:"buttonPrice",attrs:{type:"primary"},on:{click:t.onSubmit2}},[t._v("确认改价")])],1)],1)],1),a("Modal",{staticClass:"modalPay",attrs:{"footer-hide":"",width:"430px"},on:{"on-cancel":t.modalPayCancel},model:{value:t.modalPay,callback:function(e){t.modalPay=e},expression:"modalPay"}},[a("div",{staticClass:"payPage"},[a("div",{staticClass:"header acea-row row-center-wrapper"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")])]),a("div",{staticClass:"money"},[t._v("¥"),a("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])]),a("Input",{ref:"focusNum",staticStyle:{"margin-top":"16px"},attrs:{size:"large",type:"url",placeholder:"请点击输入框聚焦扫码或输入编码号"},on:{input:t.inputSaoMa},model:{value:t.payNum,callback:function(e){t.payNum=e},expression:"payNum"}}),a("div",{staticClass:"process"},[a("div",{staticClass:"pictrue"},["yue"==t.createOrder.pay_type?a("img",{attrs:{src:r("fbde")}}):a("img",{attrs:{src:r("1a3d")}})]),a("div",{staticClass:"list acea-row row-between-wrapper"},[a("div",{staticClass:"item one"},[a("div",{staticClass:"name"},[t._v(t._s("yue"==t.createOrder.pay_type?"出示付款码":"扫描收银码"))]),a("div",[t._v(t._s("yue"==t.createOrder.pay_type?"用户打开个人中心":"引导用户扫描"))])]),a("div",{staticClass:"item two"},[a("div",{staticClass:"name"},[t._v(t._s("yue"==t.createOrder.pay_type?"扫描付款码":"完成支付"))]),a("div",[t._v(t._s("yue"==t.createOrder.pay_type?"扫码枪":"用户线上支付"))])]),a("div",{staticClass:"item three"},[a("div",{staticClass:"name"},[t._v("确认收款")]),a("div",[t._v("收银台点击确认")])])])])],1)]),a("Modal",{staticClass:"cash",attrs:{"footer-hide":"",width:"770px"},on:{"on-cancel":t.cancel},model:{value:t.modalCash,callback:function(e){t.modalCash=e},expression:"modalCash"}},[a("div",{staticClass:"cashPage acea-row"},[a("div",{staticClass:"left"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")]),a("div",{staticClass:"money"},[t._v("¥"),a("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])])]),a("div",{staticClass:"right"},[a("div",{staticClass:"rightCon"},[a("div",{staticClass:"top acea-row row-between-wrapper"},[a("div",[t._v("实际收款(元)")]),a("div",{staticClass:"num"},[t._v(t._s(t.collection))])]),a("div",{staticClass:"center acea-row row-between-wrapper"},[a("div",[t._v("需找零(元)")]),this.$computes.Sub(t.collection,t.priceInfo.payPrice?t.priceInfo.payPrice:0)>0?a("div",{staticClass:"num"},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(this.$computes.Sub(t.collection,t.priceInfo.payPrice?t.priceInfo.payPrice:0))+"\n\t\t\t\t\t\t\t")]):a("div",{staticClass:"num"},[t._v("0")])]),a("div",{staticClass:"bottom acea-row"},[t._l(t.numList,(function(e,r){return a("div",{key:r,staticClass:"item acea-row row-center-wrapper",class:"."==e?"spot":"",on:{click:function(r){return t.numTap(e)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(e)+"\n\t\t\t\t\t\t\t")])})),a("div",{staticClass:"item acea-row row-center-wrapper",on:{click:t.delNum}},[a("Icon",{attrs:{type:"ios-backspace"}})],1)],2)]),a("Button",{attrs:{type:"primary"},on:{click:t.cashBnt}},[t._v("确认")])],1)])]),a("Modal",{attrs:{title:"优惠明细","footer-hide":"",width:"400"},model:{value:t.discount,callback:function(e){t.discount=e},expression:"discount"}},[a("div",{staticClass:"discountCon"},[a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("会员优惠金额：")]),a("div",[t._v("￥"+t._s(t.priceInfo.vipPrice||0))])]),a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("优惠券金额：")]),a("div",[t._v("￥"+t._s(t.priceInfo.couponPrice||0))])]),a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("积分抵扣：")]),a("div",[t._v("￥"+t._s(t.priceInfo.deductionPrice||0))])])])])],1)},i=[],s=r("d708"),n=r("bff0"),o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{width:"100%"}},[r("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"店员列表","mask-closable":!1,width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"用户搜索：",labelWidth:100,"label-for":"nickname"}},[r("Row",[r("Col",[r("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户名称/ID/手机号","element-id":"nickname",search:"","enter-button":""},on:{"on-search":t.storeSearch},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}})],1)],1)],1)],1),r("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.dataList},scopedSlots:t._u([{key:"avatar",fn:function(t){var e=t.row;t.index;return[r("viewer",[r("div",{staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.userFrom.page,"page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},c=[],u=r("f8b7"),d={name:"userList",data:function(){var t=this;return{modals:!1,total:0,userFrom:{keyword:"",page:1,limit:15},loading:!1,dataList:[],currentid:"",columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,r){var a=r.row.id,i=!1;i=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=a,s.modals=!1,s.$emit("getStoreId",r.row)}}})])}},{title:"ID",key:"id",width:80},{title:"头像",slot:"avatar",minWidth:50},{title:"昵称",key:"staff_name",minWidth:70},{title:"手机号",key:"phone",minWidth:70}]}},created:function(){this.getList()},methods:{getList:function(t){var e=this;this.loading=!0,Object(u["o"])(this.userFrom).then((function(r){e.loading=!1,e.total=r.data.count;var a=r.data.staffList,i=r.data.staffInfo;e.dataList=a,"search"!=t&&e.$emit("getUserInfo",{users:i,storeList:a})})).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},storeSearch:function(){this.userFrom.page=1,this.getList("search")},cancel:function(){this.currentid=""},pageChange:function(t){this.userFrom.page=t,this.getList()}}},l=d,h=(r("0996"),r("2877")),f=Object(h["a"])(l,o,c,!1,null,"637e080a",null),m=f.exports,p=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{width:"100%"}},[r("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"优惠券列表","mask-closable":!1,width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.dataList},scopedSlots:t._u([{key:"coupon_price",fn:function(e){var a=e.row;return[1==a.coupon_type?r("span",[t._v(t._s(a.coupon_price)+"元")]):t._e(),2==a.coupon_type?r("span",[t._v(t._s(parseFloat(a.coupon_price)/10)+"折（"+t._s(a.coupon_price.toString().split(".")[0])+"%）")]):t._e()]}},{key:"start_time",fn:function(e){var a=e.row;e.index;return[a.start_time?r("div",[t._v("\n\t\t\t        "+t._s(t._f("formatDate")(a.start_time))+" - "+t._s(t._f("formatDate")(a.end_time))+"\n\t\t\t    ")]):r("span",[t._v("不限时")])]}},{key:"type",fn:function(e){var a=e.row;return[1===a.type?r("span",[t._v("品类券")]):2===a.type?r("span",[t._v("商品券")]):3===a.type?r("span",[t._v("会员券")]):r("span",[t._v("通用券")])]}}])})],1)],1)},g=[],v=r("61f7"),_={name:"userList",filters:{formatDate:function(t){if(0!==t){var e=new Date(1e3*t);return Object(v["a"])(e,"yyyy-MM-dd")}}},props:{uid:{type:Number,value:0},cartList:{type:Array,value:0}},data:function(){var t=this;return{modals:!1,loading:!1,dataList:[],currentid:"",columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,r){var a=r.row.id,i=!1;i=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=a,s.modals=!1,s.$emit("getCouponId",r.row)}}})])}},{title:"ID",key:"id",width:80},{title:"优惠券名称",key:"coupon_title",minWidth:70},{title:"优惠券类型",slot:"type",minWidth:70},{title:"面值",slot:"coupon_price",minWidth:70},{title:"最低消费额",key:"use_min_price",minWidth:60},{title:"有效期限",slot:"start_time",minWidth:90}]}},created:function(){},methods:{getList:function(){var t=this;this.loading=!0;var e=[];this.cartList.forEach((function(t){e.push(t.id)})),Object(u["h"])(this.uid,{cart_id:e}).then((function(e){t.loading=!1,t.dataList=e.data})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},cancel:function(){this.currentid=""}}},b=_,C=(r("16c6"),Object(h["a"])(b,p,g,!1,null,"543c4f64",null)),y=C.exports,I=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"商品规格","mask-closable":!1,width:"600"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("div",{staticClass:"productAttr"},[r("div",{staticClass:"acea-row"},[r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.attr.productSelect.image}})]),r("div",{staticClass:"text"},[r("div",{staticClass:"name line1"},[t._v(t._s(t.attr.productSelect.store_name))]),r("div",{staticClass:"info"},[t._v("库存 "+t._s(t.attr.productSelect.stock))]),r("div",{staticClass:"money"},[t._v("¥"),r("span",{staticClass:"num"},[t._v(t._s(t.attr.productSelect.price))])]),r("div",{staticClass:"attr"},t._l(t.attr.productAttr,(function(e,a){return r("div",{key:a,staticClass:"list"},[r("div",{staticClass:"title"},[t._v(t._s(e.attr_name))]),r("div",{staticClass:"listn acea-row"},t._l(e.attr_value,(function(i,s){return r("div",{key:s,staticClass:"item acea-row row-center-wrapper",class:e.index===i.attr?"on":"",on:{click:function(e){return t.tapAttr(a,s)}}},[t._v(t._s(i.attr))])})),0)])})),0)])]),r("button",{staticClass:"bnt acea-row row-center-wrapper",attrs:{type:"primary",disabled:t.disabled},on:{click:t.goCat}},[t._v("确定")])])])],1)},w=[],k={name:"productAttr",props:{attr:{type:Object,default:function(){}},isCart:{type:Number,value:0},disabled:{type:Boolean,value:!1}},data:function(){return{modals:!1}},created:function(){},methods:{goCat:function(){this.$emit("goCat",this.isCart)},tapAttr:function(t,e){var r=this;r.$emit("attrVal",{indexw:t,indexn:e}),this.$set(this.attr.productAttr[t],"index",this.attr.productAttr[t].attr_values[e]);var a=r.getCheckedValue().join(",");r.$emit("ChangeAttr",a)},getCheckedValue:function(){for(var t=this.attr.productAttr,e=[],r=0;r<t.length;r++)for(var a=0;a<t[r].attr_values.length;a++)t[r].index===t[r].attr_values[a]&&e.push(t[r].attr_values[a]);return e}}},O=k,$=(r("7ff4"),Object(h["a"])(O,I,w,!1,null,"9aaef790",null)),j=$.exports,S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Modal",{attrs:{width:"583"},on:{"on-cancel":t.clear},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("Tabs",{on:{"on-click":t.changeTabs},model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[a("TabPane",{attrs:{label:"优惠充值",name:"0"}}),a("TabPane",{attrs:{label:"自定义充值",name:"1"}})],1),a("div",{staticClass:"discount"},[t.userInfo?a("div",{staticClass:"infoData acea-row row-middle"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.userInfo.avatar}})]),a("div",{staticClass:"info"},[a("span",[t._v(t._s(t.userInfo.nickname)+"(ID: "+t._s(t.userInfo.uid)+")")]),a("span",[t._v("余额："+t._s(t.userInfo.now_money))]),a("span",[t._v("积分："+t._s(t.userInfo.integral))])])]):t._e(),0==t.currentTab?a("div",{staticClass:"list acea-row"},t._l(t.moneyList,(function(e,r){return a("div",{staticClass:"item",class:t.active==r?"on":"",on:{click:function(a){return t.activeMoney(r,e)}}},[a("div",{staticClass:"money"},[a("span",[t._v("¥")]),t._v(t._s(e.price))]),a("div",[t._v("额外赠送：¥ "+t._s(e.give_money))])])})),0):t._e(),1==t.currentTab?a("div",{staticClass:"custom"},[a("InputNumber",{staticClass:"inputNum",attrs:{size:"large",min:0},model:{value:t.payPrice,callback:function(e){t.payPrice=e},expression:"payPrice"}}),a("div",{staticClass:"tip"},[t._v("自定义充值的金额无赠送优惠")])],1):t._e()]),a("div",{staticClass:"acea-row row-center-wrapper",attrs:{slot:"footer"},slot:"footer"},[a("Button",{staticClass:"button",attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1)],1),a("Modal",{staticClass:"modalPay",attrs:{"footer-hide":"",width:"430px"},on:{"on-cancel":t.yuePayClear},model:{value:t.modalPay,callback:function(e){t.modalPay=e},expression:"modalPay"}},[a("div",{staticClass:"payPage"},[a("div",{staticClass:"header acea-row row-center-wrapper"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")])]),a("div",{staticClass:"money"},[t._v("¥"),a("span",{staticClass:"num"},[t._v(t._s(t.rechargeData.price))])]),a("Input",{ref:"rechargeNum",staticStyle:{"margin-top":"16px"},attrs:{size:"large",type:"url",placeholder:"请点击输入框聚焦扫码或输入编码号"},on:{input:t.inputSaoMa},model:{value:t.payNum,callback:function(e){t.payNum=e},expression:"payNum"}}),a("div",{staticClass:"process"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:r("fbde")}})]),a("div",{staticClass:"list acea-row row-between-wrapper"},[a("div",{staticClass:"item one"},[a("div",{staticClass:"name"},[t._v("出示付款码")]),a("div",[t._v("用户打开个人中心")])]),a("div",{staticClass:"item two"},[a("div",{staticClass:"name"},[t._v("扫描付款码")]),a("div",[t._v("扫码枪")])]),a("div",{staticClass:"item three"},[a("div",{staticClass:"name"},[t._v("确认收款")]),a("div",[t._v("收银台点击确认")])])])])],1)])],1)},P=[],L=r("c24f"),x={name:"recharge",props:{userInfo:{type:Object,default:function(){}}},data:function(){return{modal:!1,timer:null,currentTab:0,moneyList:[],active:0,modalPay:!1,payNum:"",payPrice:0,rechargeData:{uid:0,price:"",rechar_id:0,pay_type:3,auth_code:""},givePrice:0,totalPrice:0}},watch:{modal:function(t){if(t){var e=this;document.onkeydown=function(t){13==t.which&&e.payNum&&(e.rechargeData.auth_code=e.payNum,e.confirm())}}}},created:function(){this.getList()},methods:{clear:function(){this.payPrice=0,this.currentTab="0"},yuePayClear:function(){this.$Message.destroy(),this.timer&&(clearInterval(this.timer),this.timer=null)},inputSaoMa:function(t){},changeTabs:function(t){0==t&&(this.active=0)},getList:function(){var t=this;Object(L["n"])().then((function(e){t.moneyList=e.data.recharge_quota}))},activeMoney:function(t,e){this.active=t},save:function(){var t=this;this.modalPay=!0,this.$nextTick((function(){t.$refs.rechargeNum.focus()})),1==this.currentTab?(this.rechargeData.rechar_id=0,this.givePrice=0,this.rechargeData.price=this.payPrice):(this.rechargeData.price=this.moneyList[this.active].price,this.rechargeData.rechar_id=this.moneyList[this.active].id,this.givePrice=this.moneyList[this.active].give_money),this.totalPrice=this.$computes.Add(this.rechargeData.price,this.givePrice)},confirm:function(){var t=this;this.rechargeData.uid=this.userInfo.uid,Object(L["o"])(this.rechargeData).then((function(e){t.payNum="";var r=e.data.status,a=e.data.data.order_id;switch(r){case"SUCCESS":t.$Message.success("支付成功"),t.modalPay=!1,t.modal=!1,t.$emit("getSuccess",t.totalPrice);break;case"PAY_ING":var i=t.$Message.loading({content:"等待支付中...",duration:0});t.checkOrderTime(a,i);break;default:t.$Message.warning("支付失败");break}})).catch((function(e){t.payNum="",t.$Message.error(e.msg)}))},checkOrderTime:function(t,e){var r=this,a=1,i=this.timer=setInterval((function(){r.checkOrder(t,i,e),a++,a>=60&&(clearInterval(i),e(),r.$Message.success("支付失败"))}),1e3)},checkOrder:function(t,e,r){var a=this;Object(L["b"])(1,{order_id:t}).then((function(t){1==t.data.status&&(r(),a.$Message.success("支付成功"),a.$emit("getSuccess",a.totalPrice),a.modalPay=!1,a.modal=!1,clearInterval(e))})).catch((function(t){r(),a.$Message.error(t.msg)}))}}},M=x,F=(r("c007"),Object(h["a"])(M,S,P,!1,null,"49bbe374",null)),D=F.exports,T={name:"index",components:{userList:n["default"],storeList:m,productAttr:j,couponList:y,recharge:D},data:function(){var t=this;return{routePre:s["a"].routePre,formItem:{price:0},discountPrice:100,loading:!1,totalHang:0,tableHang:[],activeHangon:-1,hangData:[],lodgeFrom:{keyword:"",page:1,limit:10},currentid:"",columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,r){var a=r.row.id,i=!1;i=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){s.currentid=a,s.activeHangon=r.index;var e={uid:r.row.uid},i=r.row.tourist_uid;r.row.uid?t.userInfoData(e):t.setUp(i)}}})])}},{title:"用户",slot:"nickname",minWidth:70},{title:"订单金额",key:"price",minWidth:70},{title:"时间",key:"_add_time",minWidth:70},{title:"操作",slot:"action",minWidth:100,align:"center"}],checkOut:0,modalUser:!1,flag:!0,goodFrom:{store_name:"",field_key:"",cate_id:"",page:1,limit:30,uid:0,staff_id:0},total:0,goodData:[],cateData:[],currentCate:0,currentTab:"1",codeNum:"",payNum:"",userInfo:{},storeInfos:{},storeList:[],attr:{productAttr:[],productSelect:{}},storeInfo:{},productValue:[],attrValue:"",productId:0,cartList:[],isCart:0,cartInfo:{cart_id:0,product_id:0,unique:""},modal:!1,fapi:{},rule:[{type:"input",field:"remarks",title:"备注",props:{type:"textarea",maxlength:100,"show-word-limit":!0}}],modal2:!1,rule2:[{type:"InputNumber",field:"change_price",title:"实付款",value:0,props:{min:0}}],integral:!1,coupon:!1,couponId:0,modalPay:!1,cartSum:0,priceInfo:{},createOrder:{remarks:"",change_price:0,cart_id:[],userCode:"",is_price:0,auth_code:""},modalCash:!1,numList:["7","8","9","4","5","6","1","2","3","0","."],collectionArray:[],collection:0,isOrderCreate:0,discount:!1,payTape:"",orderId:"",clientHeight:0,cartHeight:0,goodsHeight:0,invalidList:[],defaultcalc:!1,orderSystem:{loadingMsg:null,timer:null},disabled:!1,unchangedPrice:0}},created:function(){this.userInfo=JSON.parse(window.localStorage.getItem("cashierUser")),this.cateList()},mounted:function(){var t=this;this.$nextTick((function(){t.$refs.goodsNum.focus(),t.clientHeight="".concat(document.documentElement.clientHeight)-80,t.cartHeight="".concat(document.documentElement.clientHeight)-387,t.goodsHeight="".concat(document.documentElement.clientHeight)-250;var e=t;window.onresize=function(){e.clientHeight="".concat(document.documentElement.clientHeight)-80,e.cartHeight="".concat(document.documentElement.clientHeight)-387,e.goodsHeight="".concat(document.documentElement.clientHeight)-250}}))},methods:{tapDiscount:function(){this.formItem.price=this.$computes.Mul(this.unchangedPrice||0,this.discountPrice/100||0)||0},tapPrice:function(){var t=this.$computes.Div(this.formItem.price||0,this.unchangedPrice||0);this.discountPrice=(100*t).toFixed(2)||0},cancelPrice:function(){this.formItem.price=this.priceInfo.payPrice||0,this.tapPrice()},getSuccess:function(t){var e=this.$computes.Add(this.userInfo.now_money,t);this.userInfo.now_money=e;var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(this.userInfo))},clear:function(){this.priceInfo.couponPrice=0,this.priceInfo.payPrice=0,this.priceInfo.deductionPrice=0,this.priceInfo.totalPrice=0,this.priceInfo.vipPrice=0,this.cartList=[],this.cartSum=0,this.collection=0,this.collectionArray=[],this.createOrder.change_price=0,this.createOrder.remarks="",this.coupon=!1,this.couponId=0,this.integral=!1,this.createOrder.is_price=0},cancel:function(){this.collection=0,this.collectionArray=[]},hangDel:function(t,e){var r=this;Object(u["k"])(t.id).then((function(t){1==r.tableHang.length?(r.lodgeFrom.page=1,r.hangList()):(r.tableHang.splice(e,1),r.totalHang=r.totalHang-1),r.hangData[e].is_check=1,r.$Message.success(t.msg)})).catch((function(t){r.$Message.error(t.msg)}))},hangDataTap:function(t,e){this.activeHangon=t,this.checkOut=0;var r=e.tourist_uid,a={uid:e.uid};e.uid?this.userInfoData(a):this.setUp(r)},hangList:function(){var t=this;this.loading=!0;var e=this.storeInfos.id;Object(u["x"])(e,this.lodgeFrom).then((function(e){t.loading=!1,t.tableHang=e.data.data,t.totalHang=e.data.count})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageHangChange:function(t){this.lodgeFrom.page=t,this.hangList()},billHang:function(t,e){this.checkOut=0,this.activeHangon=e;var r=t.tourist_uid,a={uid:t.uid};t.uid?this.userInfoData(a):this.setUp(r)},hangDataList:function(){var t=this,e=this.storeInfos.id;Object(u["w"])(e).then((function(e){t.hangData=e.data,t.defaultSel()})).catch((function(e){t.$Message.error(e.msg)}))},lodgeTap:function(){var t=this,e={cart_ids:this.createOrder.cart_id,uid:this.userInfo.uid||0,tourist_uid:this.userInfo.touristId||"",staff_id:this.storeInfos.id,price:this.priceInfo.payPrice||0};Object(u["Q"])(e).then((function(e){t.activeHangon=-1,t.$Message.success(e.msg),t.hangDataList(),t.hangList(),t.setUp()})).catch((function(e){t.$Message.error(e.msg)}))},storeSearch:function(){this.lodgeFrom.page=1,this.hangList()},defaultSel:function(t){var e=this,r=this.userInfo.uid,a=this.userInfo.touristId;if(r){var i=0;this.hangData.forEach((function(t,a){t.uid==r&&(i=1,e.activeHangon=a)})),i||(this.activeHangon=-1)}else a&&this.hangData.forEach((function(t,r){t.tourist_uid==a&&(e.activeHangon=r)}))},navTab:function(t){this.checkOut=t,1==t?(this.currentid="",this.activeHangon=-1,this.clear(),this.lodgeFrom.page=1,this.hangList()):(this.getCartList(),this.defaultSel())},rechargeBnt:function(){this.$refs.recharge.modal=!0},discountCon:function(){this.discount=!0},cashBnt:function(){this.isOrderCreate?this.getCashierPay("cash"):this.orderCreate()},delNum:function(){this.collectionArray.pop(),this.collection=this.collectionArray.length?this.collectionArray.join(""):0},numTap:function(t){!1===this.defaultcalc&&(this.collection="",this.defaultcalc=!0),this.collectionArray.push(t),this.collection=this.collectionArray.join("")},checkOrderTime:function(t){var e=this,r=1,a=this.orderSystem.timer=setInterval((function(){e.confirmOrder(a,t),r++,r>=60&&(clearInterval(a),t(),e.isOrderCreate=1,e.$Message.success("支付失败"))}),1e3)},confirmOrder:function(t,e){var r=this,a={order_id:this.orderId};Object(L["b"])(3,a).then((function(a){if(1==a.data.status){e(),clearInterval(t),r.isOrderCreate=0,r.$Message.success("支付成功"),r.goodList(),r.modalPay=!1,r.changePoints();var i=window.localStorage;i.setItem("cashierUser",JSON.stringify(r.userInfo)),r.clear()}})).catch((function(t){e(),r.$Message.error(t.msg)}))},payPrice:function(t){var e=this;if(this.payTape=t,""==t||"yue"==t){this.createOrder.userCode="",this.createOrder.auth_code="",this.payNum="",this.modalPay=!0;var r=this;this.$nextTick((function(){e.$refs.focusNum.focus(),document.onkeydown=function(t){13==t.which&&(r.payNum&&(r.createOrder.userCode=r.payNum,r.createOrder.auth_code=r.payNum,r.confirm()),r.codeNum&&r.codeInfo({bar_code:r.codeNum}))}}))}else"cash"==t&&(this.modalCash=!0,this.collection=this.priceInfo.payPrice?this.priceInfo.payPrice:0,this.keyboard());if(this.createOrder.integral=this.integral,this.createOrder.coupon=this.coupon,this.createOrder.coupon_id=this.couponId,this.coupon&&!this.couponId)return this.$Message.error("请选择有效优惠券");this.createOrder.pay_type=t,this.createOrder.staff_id=this.storeInfos.id,this.fapi.resetFields()},confirm:function(){if("yue"==this.payTape){if(!this.createOrder.userCode)return this.$Message.error("请扫描个人中心二维码");this.isOrderCreate?this.getCashierPay("yue"):this.orderCreate()}else if(""==this.payTape){if(!this.createOrder.auth_code)return this.$Message.error("请扫描您的付款码");this.isOrderCreate?this.getCashierPay(""):this.orderCreate()}},modalPayCancel:function(){this.$Message.destroy(),this.orderSystem.timer&&(clearInterval(this.orderSystem.timer),this.orderSystem.timer=null)},getCashierPay:function(t){var e=this,r={payType:t,userCode:this.payNum,auth_code:this.payNum};if("cash"==t&&parseFloat(this.priceInfo.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");Object(u["l"])(this.orderId,r).then((function(t){if(e.payNum="","SUCCESS"==t.data.status){e.isOrderCreate=0,e.$Message.success("支付成功"),e.modalCash=!1,e.modalPay=!1,e.changePoints();var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(e.userInfo)),e.clear(),e.goodList()}else if("PAY_ING"==t.data.status){var a=e.$Message.loading({content:"等待支付中...",duration:0});e.orderSystem.loadingMsg=a,e.orderId=t.data.order_id,e.checkOrderTime(a)}else e.isOrderCreate=1,e.orderId=t.data.order_id,e.$Message.error(t.data.message)})).catch((function(t){e.payNum="",e.$Message.error(t.msg)}))},orderCreate:function(){var t=this;if("cash"==this.payTape&&parseFloat(this.priceInfo.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");this.createOrder.tourist_uid=this.userInfo.touristId,Object(u["i"])(this.userInfo.uid,this.createOrder).then((function(e){var r=window.localStorage;if(t.payNum="","yue"==t.payTape)if(t.modalPay=!1,t.payNum="",t.createOrder.userCode="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("SUCCESS"==e.data.status){t.isOrderCreate=0,t.$Message.success("支付成功");var a=t.$computes.Sub(t.userInfo.now_money,t.priceInfo.payPrice);t.userInfo.now_money=a,t.changePoints(),r.setItem("cashierUser",JSON.stringify(t.userInfo)),t.goodList(),t.clear()}else t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message);if("cash"==t.payTape&&"SUCCESS"==e.data.status&&(t.$Message.success("支付成功"),t.changePoints(),r.setItem("cashierUser",JSON.stringify(t.userInfo)),t.goodList(),t.modalCash=!1,t.clear()),""==t.payTape)if(t.payNum="",t.createOrder.auth_code="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("PAY_ING"==e.data.status){var i=t.$Message.loading({content:"等待支付中...",duration:0});t.orderId=e.data.order_id,t.checkOrderTime(i)}else"SUCCESS"==e.data.status?(t.$Message.success("支付成功"),t.changePoints(),r.setItem("cashierUser",JSON.stringify(t.userInfo)),t.goodList(),t.modalPay=!1,t.clear()):(t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message))})).catch((function(e){t.payNum="",t.$Message.error(e.msg)}))},changePoints:function(){var t=this,e=this.$computes.Sub(this.userInfo.integral,this.priceInfo.usedIntegral);this.userInfo.integral=e,this.hangDataList(),this.tableHang.forEach((function(e,r){e.uid?t.userInfo.uid==e.uid&&t.tableHang.splice(r,1):t.userInfo.touristId==e.tourist_uid&&t.tableHang.splice(r,1)}))},cartCompute:function(){var t=this,e=[];this.cartList.forEach((function(t){e.push(t.id)})),this.createOrder.cart_id=e;var r={integral:this.integral,coupon:this.coupon,coupon_id:this.couponId,cart_id:e};Object(u["g"])(this.userInfo.uid,r).then((function(e){t.priceInfo=e.data,t.unchangedPrice=t.priceInfo.payPrice||0,t.formItem.price=t.priceInfo.payPrice||0,t.tapPrice();var r=t.priceInfo.cartInfo.map((function(t,e){return t.cart_num}));t.cartSum=r.reduce((function(t,e){return t+e}),0)})).catch((function(e){t.$Message.error(e.msg),t.coupon=!1}))},couponTap:function(){this.$refs.coupon.modals=!0,this.$refs.coupon.currentid=this.couponId||0,this.$refs.coupon.getList()},getCouponId:function(t){this.couponId=t.id,this.coupon=!0,this.cartCompute()},closeCoupon:function(){this.coupon=!1,this.couponId=0,this.cartCompute()},integralTap:function(){this.integral=!this.integral,this.cartCompute()},changePrice:function(){this.modal2=!0},remarks:function(){this.modal=!0},onSubmit:function(t){this.createOrder.remarks=t.remarks,this.modal=!1},onSubmit2:function(){if(!(this.formItem.price>=0&&null!=this.formItem.price))return this.$Message.error("价格不能为空");this.priceInfo.payPrice=this.formItem.price,this.$Message.success("改价成功"),this.createOrder.is_price=1,this.createOrder.change_price=this.formItem.price,this.cancelPrice(),this.modal2=!1},del:function(t,e,r,a){var i=this;this.$Modal.confirm({title:"删除该购物车",content:"<p>确定要删除该购物车吗？</p><p>删除该购物车后将无法恢复，请谨慎操作！</p>",onOk:function(){Object(u["b"])(i.userInfo.uid,t).then((function(t){i.$Message.success("删除成功"),i.goodList(),e?(i.clear(),i.invalidList=[],i.hangDataList()):a?i.invalidList.splice(r,1):(i.cartList.splice(r,1),i.cartList.length?i.cartCompute():(i.hangDataList(),i.clear()))})).catch((function(t){i.$Message.error(t.msg)}))},onCancel:function(){}})},delAll:function(){var t=[];this.cartList.forEach((function(e){t.push(e.id)})),this.invalidList.forEach((function(e){t.push(e.id)})),this.del({ids:t},1)},delCart:function(t,e,r){var a=[];a.push(t.id),this.del({ids:a},0,e,r)},cartAttr:function(t){this.disabled=!1,this.$refs.attrs.modals=!0,this.isCart=1,this.cartInfo.cart_id=t.id,this.cartInfo.product_id=t.product_id,this.goodsInfo(t.product_id)},joinCart:function(t){var e=this,r=this;if(t){var a=r.productValue[this.attrValue];if(r.attr.productAttr.length&&void 0===a)return this.$Message.warning("产品库存不足，请选择其它")}var i=this.userInfo.uid,s={productId:this.productId,cartNum:1,uniqueId:t&&void 0!==this.attr.productSelect?this.attr.productSelect.unique:"",staff_id:this.storeInfos.id,tourist_uid:this.userInfo.touristId};Object(u["a"])(i,s).then((function(t){e.$refs.attrs.modals=!1,e.$Message.success("添加购物车成功"),e.getCartList(),e.goodList(),e.hangDataList(),e.disabled=!0})).catch((function(t){e.$Message.error(t.msg)}))},cartChange:function(t){var e=this,r=t.uid,a={number:t.cart_num,id:t.id};Object(u["d"])(r,a).then((function(t){e.cartCompute()})).catch((function(t){e.$Message.error(t.msg)}))},changeCartAttr:function(){var t=this;this.cartInfo.unique=void 0!==this.attr.productSelect?this.attr.productSelect.unique:"",Object(u["q"])(this.cartInfo).then((function(e){t.disabled=!0,t.$Message.success(e.msg),t.$refs.attrs.modals=!1,t.getCartList(),t.cartCompute()})).catch((function(e){t.$Message.error(e.msg)}))},goCat:function(t){t?this.changeCartAttr():this.joinCart(1)},getCartList:function(){var t=this,e=this.userInfo.uid,r=this.storeInfos.id;if(e>=0){var a={tourist_uid:this.userInfo.touristId};Object(u["c"])(e,r,a).then((function(e){t.cartList=e.data.valid,t.invalidList=e.data.invalid,e.data.valid.length?t.cartCompute():t.clear()})).catch((function(e){t.$Message.error(e.msg)}))}else this.$Message.error("请添加或选择用户")},attrTap:function(t){if(this.disabled=!1,this.userInfo&&this.userInfo.uid>=0){this.productId=t.product_id;var e=t.product.spec_type;e?(this.isCart=0,this.$refs.attrs.modals=!0,this.goodsInfo(t.product_id)):this.joinCart(0)}else this.$Message.error("请添加或选择用户")},goodsInfo:function(t){var e=this;Object(u["j"])(t,this.userInfo.uid).then((function(t){var r=t.data;e.storeInfo=r.storeInfo,e.productValue=r.productValue,e.$set(e.attr,"productAttr",r.productAttr),e.DefaultSelect()})).catch((function(t){e.$Message.error(t.msg)}))},DefaultSelect:function(){var t=this,e=this.attr.productAttr,r=[];for(var a in this.productValue)if(this.productValue[a].stock>0){r=this.attr.productAttr.length?a.split(","):[];break}if(this.isCart){var i=[];this.cartList.forEach((function(e){e.id==t.cartInfo.cart_id&&(i=e.productInfo.attrInfo.suk.split(","))}));for(var s=0;s<e.length;s++)this.$set(e[s],"index",i[s])}else for(var n=0;n<e.length;n++)this.$set(e[n],"index",r[n]);var o=this.productValue[r.join(",")];o&&e.length?(this.$set(this.attr.productSelect,"store_name",this.storeInfo.store_name),this.$set(this.attr.productSelect,"image",o.image),this.$set(this.attr.productSelect,"price",o.price),this.$set(this.attr.productSelect,"stock",o.stock),this.$set(this.attr.productSelect,"unique",o.unique),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",r.join(","))):!o&&e.length?(this.$set(this.attr.productSelect,"store_name",this.storeInfo.store_name),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue","")):o||e.length||(this.$set(this.attr.productSelect,"store_name",this.storeInfo.store_name),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",this.storeInfo.stock),this.$set(this.attr.productSelect,"unique",this.storeInfo.unique||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",""))},ChangeAttr:function(t){var e=this.productValue[t];e&&e.stock>0?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.unique),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vip_price",e.vip_price),this.$set(this,"attrValue",t)):(this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this.attr.productSelect,"vip_price",this.storeInfo.vip_price),this.$set(this,"attrValue",""))},storeTap:function(){this.$refs.store.modals=!0,this.$refs.store.cancel()},setUp:function(t){var e=(new Date).getTime(),a={avatar:r("586c"),nickname:"游客",uid:0,touristId:t||e};this.userInfo=a;var i=window.localStorage;i.setItem("cashierUser",JSON.stringify(a)),this.getCartList(),this.goodList()},changeMenu:function(t){1==t?this.setUser():(this.activeHangon=-1,this.clear(),this.setUp())},setUser:function(){this.modalUser=!0,this.$refs.users.currentid=0},getStoreId:function(t){var e=this;this.clear(),this.storeList.forEach((function(r){r.id==t.id&&(e.goodFrom.staff_id=t.id,e.storeInfos=r,e.getCartList(),e.goodList(),e.hangDataList(),e.hangList())}))},getUserInfo:function(t){this.storeInfos=t.users,this.storeList=t.storeList,this.goodFrom.staff_id=t.users.id,this.userInfo?this.getCartList():this.setUp(),this.goodList(),this.hangDataList(),this.hangList()},cashierSwitch:function(t){var e=this;Object(u["R"])(t,this.storeInfos.id).then((function(t){})).catch((function(t){e.$Message.error(t)}))},getUserId:function(t){this.clear(),this.modalUser=!1;var e={uid:t.uid},r={uid:this.userInfo.touristId,to_uid:t.uid,is_tourist:1};this.cashierSwitch(r),this.userInfoData(e)},userInfoData:function(t){var e=this;Object(u["p"])(t).then((function(t){e.userInfo=t.data;var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(t.data)),e.getCartList(),e.goodList(),e.defaultSel(1)})).catch((function(t){e.$Message.error(t)}))},inputSaoMa:function(t){var e=this,r=t;if(""===r)return!1;clearTimeout(this.endTimeout),this.endTimeout=null,this.endTimeout=setTimeout((function(){e.codeNum===r&&(clearTimeout(e.endTimeout),r&&e.codeInfo({bar_code:r}))}),500)},codeInfo:function(t){var e=this;if(t.uid=this.userInfo?this.userInfo.uid:0,t.staff_id=this.storeInfos.id,t.tourist_uid=this.userInfo.touristId,null==this.userInfo)return this.codeNum="",this.$Message.error("请添加或选择用户");Object(u["f"])(t).then((function(t){e.codeNum="";var r=t.data;if(r.hasOwnProperty("userInfo"))if(e.userInfo)e.$Modal.confirm({title:"切换用户",content:"<p>确定要切换用户吗？</p>",onOk:function(){e.userInfo=t.data.userInfo;var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(t.data.userInfo)),e.getCartList()},onCancel:function(){}});else{e.userInfo=t.data.userInfo;var a=window.localStorage;a.setItem("cashierUser",JSON.stringify(t.data.userInfo))}e.goodList(),e.getCartList()})).catch((function(t){e.codeNum="",e.$Message.error(t.msg)}))},cateTap:function(t,e){this.currentCate=e,this.goodFrom.cate_id=t.id,this.goodFrom.page=1,this.goodList()},cateList:function(){var t=this;Object(u["e"])().then((function(e){var r={cate_name:"全部商品",id:""};e.data.unshift(r),t.cateData=e.data})).catch((function(e){t.$Message.error(e)}))},goodList:function(){var t=this;this.goodFrom.uid=this.userInfo?this.userInfo.uid:0,Object(u["m"])(this.goodFrom).then((function(e){var r=e.data;t.total=r.count,t.goodData=r.list})).catch((function(e){t.$Message.error(e)}))},orderSearch:function(){this.goodFrom.page=1,this.goodList()},pageChange:function(t){this.goodFrom.page=t,this.goodList()},keyboard:function(){var t=this;function e(e){t.collectionArray.pop(),t.collection=t.collectionArray.length?t.collectionArray.join(""):0}function r(e){!1===t.defaultcalc&&(t.collection="",t.defaultcalc=!0),t.collectionArray.push(e),t.collection=t.collectionArray.join("")}document.onkeydown=function(a){var i=a||window.event,s=i.keyCode;switch(t.modalCash&&(a.stopPropagation(),a.preventDefault()),s){case 96:case 48:r(0);break;case 97:case 49:r(1);break;case 98:case 50:r(2);break;case 99:case 51:r(3);break;case 100:case 52:r(4);break;case 101:case 53:r(5);break;case 102:case 54:r(6);break;case 103:case 55:r(7);break;case 104:case 56:r(8);break;case 105:case 57:r(9);break;case 110:r(".");break;case 190:r(".");break;case 8:e();break}}}}},N=T,H=(r("5582"),Object(h["a"])(N,a,i,!1,null,"bbfc8898",null));e["default"]=H.exports},5582:function(t,e,r){"use strict";var a=r("d471"),i=r.n(a);i.a},"61f7":function(t,e,r){"use strict";function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(r,!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function n(t,e){/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var r={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var a in r)if(new RegExp("(".concat(a,")")).test(e)){var i=r[a]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?i:o(i))}return e}function o(t){return("00"+t).substr(t.length)}r.d(e,"a",(function(){return n}));var c={min:"%s最小长度为:min",max:"%s最大长度为:max",length:"%s长度必须为:length",range:"%s长度为:range",pattern:"$s格式错误"};var u=function(t,e){t.message=function(t){return e.replace("%s",t||"")}};function d(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i({required:!0,message:t,type:"string"},e)}function l(t){return h.pattern(/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,t)}u(d,"请输入%s"),u(l,"%s格式不正确");var h=Object.keys(c).reduce((function(t,e){return t[e]=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n="range"===e?{min:t[0],max:t[1]}:s({},e,t);return i({message:r.replace(":".concat(e),"range"===e?"".concat(t[0],"-").concat(t[1]):t),type:"string"},n,{},a)},u(t[e],c[e]),t}),{})},"6aa25":function(t,e,r){t.exports=r.p+"view_store/img/label02.c4a169c4.png"},"7ff4":function(t,e,r){"use strict";var a=r("d3bd"),i=r.n(a);i.a},9779:function(t,e,r){t.exports=r.p+"view_store/img/label01.a16e8991.png"},9914:function(t,e,r){"use strict";var a=r("f5a4"),i=r.n(a);i.a},ad18:function(t,e,r){t.exports=r.p+"view_store/img/gold.67ecfa42.png"},bff0:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{width:"100%","background-color":"#fff"}},[r("Form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"用户搜索：",labelWidth:100,"label-for":"nickname"}},[r("Row",[r("Col",[r("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入ID或者手机号","element-id":"nickname",search:"","enter-button":""},on:{"on-search":t.orderSearch},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}},[r("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.userFrom.field_key,callback:function(e){t.$set(t.userFrom,"field_key",e)},expression:"userFrom.field_key"}},[r("Option",{attrs:{value:"all"}},[t._v("全部")]),r("Option",{attrs:{value:"uid"}},[t._v("ID")]),r("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1)],1)],1),r("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.dataList,height:"330"},on:{"on-sort-change":t.sortChanged},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[r("viewer",[r("div",{staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.userFrom.page,"page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)},i=[],s=r("c24f"),n={name:"userList",data:function(){var t=this;return{total:0,userFrom:{keyword:"",page:1,limit:15,field_key:"all"},loading:!1,dataList:[],currentid:0,columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,r){var a=r.row.uid,i=!1;i=t.currentid===a;var s=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){if(s.currentid=a,s.$emit("getUserId",r.row),r.row.uid){if("image"===t.$route.query.fodder){var e={image:r.row.avatar,uid:r.row.uid};form_create_helper.set("image",e),form_create_helper.close("image")}}else t.$Message.warning("请先选择用户")}}})])}},{title:"ID",key:"uid",width:80},{title:"头像",slot:"avatars",minWidth:50},{title:"昵称",key:"nickname",minWidth:70},{title:"手机号",key:"phone",minWidth:70},{title:"用户类型",key:"user_type",minWidth:70},{title:"余额",key:"now_money",sortable:"custom",minWidth:70}]}},created:function(){this.getList()},methods:{sortChanged:function(t){this.userFrom[t.key]=t.order,this.getList()},getList:function(){var t=this;this.loading=!0,Object(s["m"])(this.userFrom).then((function(e){t.loading=!1,t.total=e.data.count,t.dataList=e.data.list})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},usersearchList:function(){var t=this;this.loading=!0,Object(s["u"])(this.userFrom).then((function(e){t.dataList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},orderSearch:function(){this.userFrom.page=1,""==this.userFrom.keyword?this.getList():this.usersearchList()},pageChange:function(t){this.userFrom.page=t,""==this.userFrom.keyword?this.getList():this.usersearchList()}}},o=n,c=(r("9914"),r("2877")),u=Object(c["a"])(o,a,i,!1,null,"45517fd8",null);e["default"]=u.exports},c007:function(t,e,r){"use strict";var a=r("d0b1"),i=r.n(a);i.a},c24f:function(t,e,r){"use strict";r.d(e,"r",(function(){return i})),r.d(e,"k",(function(){return s})),r.d(e,"l",(function(){return n})),r.d(e,"a",(function(){return o})),r.d(e,"q",(function(){return c})),r.d(e,"j",(function(){return u})),r.d(e,"m",(function(){return d})),r.d(e,"u",(function(){return l})),r.d(e,"d",(function(){return h})),r.d(e,"f",(function(){return f})),r.d(e,"c",(function(){return m})),r.d(e,"e",(function(){return p})),r.d(e,"p",(function(){return g})),r.d(e,"n",(function(){return v})),r.d(e,"t",(function(){return _})),r.d(e,"o",(function(){return b})),r.d(e,"s",(function(){return C})),r.d(e,"g",(function(){return y})),r.d(e,"i",(function(){return I})),r.d(e,"b",(function(){return w})),r.d(e,"h",(function(){return k}));var a=r("b6bd");function i(){return Object(a["a"])({url:"user/user_label_cate",method:"get"})}function s(){return Object(a["a"])({url:"user/user_label_cate/create",method:"get"})}function n(t){return Object(a["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function o(t){return Object(a["a"])({url:"user/user_label",method:"get",params:t})}function c(){return Object(a["a"])({url:"user/user_label/create",method:"get"})}function u(t){return Object(a["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function d(t){return Object(a["a"])({url:"user/user",method:"get",params:t})}function l(t){return Object(a["a"])({url:"user/search",method:"get",params:t})}function h(t){return Object(a["a"])({url:"user/label/".concat(t),method:"get"})}function f(t,e){return Object(a["a"])({url:"user/label/".concat(t),method:"post",data:e})}function m(t){return Object(a["a"])({url:"user/user/".concat(t),method:"get"})}function p(t){return Object(a["a"])({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function g(t){return Object(a["a"])({url:"user/set_label",method:"post",data:t})}function v(){return Object(a["a"])({url:"user/recharge/meal",method:"get"})}function _(){return Object(a["a"])({url:"user/member/ship",method:"get"})}function b(t){return Object(a["a"])({url:"user/recharge",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/user/member",method:"post",data:t})}function y(t){return Object(a["a"])({url:"staff/binding/user",method:"post",data:t})}function I(t){return Object(a["a"])({url:"updatePwd",method:"PUT",data:t})}function w(t,e){return Object(a["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function k(){return Object(a["a"])({url:"staff/staff_info",method:"get"})}},d0b1:function(t,e,r){},d3bd:function(t,e,r){},d471:function(t,e,r){},d4e5:function(t,e,r){},df84:function(t,e,r){t.exports=r.p+"view_store/img/no-thing.426652cb.png"},f133:function(t,e,r){},f5a4:function(t,e,r){},f8b7:function(t,e,r){"use strict";r.d(e,"m",(function(){return i})),r.d(e,"e",(function(){return s})),r.d(e,"p",(function(){return n})),r.d(e,"f",(function(){return o})),r.d(e,"o",(function(){return c})),r.d(e,"a",(function(){return u})),r.d(e,"j",(function(){return d})),r.d(e,"c",(function(){return l})),r.d(e,"d",(function(){return h})),r.d(e,"q",(function(){return f})),r.d(e,"b",(function(){return m})),r.d(e,"g",(function(){return p})),r.d(e,"i",(function(){return g})),r.d(e,"l",(function(){return v})),r.d(e,"K",(function(){return _})),r.d(e,"G",(function(){return b})),r.d(e,"L",(function(){return C})),r.d(e,"O",(function(){return y})),r.d(e,"y",(function(){return I})),r.d(e,"v",(function(){return w})),r.d(e,"B",(function(){return k})),r.d(e,"U",(function(){return O})),r.d(e,"fb",(function(){return $})),r.d(e,"J",(function(){return j})),r.d(e,"H",(function(){return S})),r.d(e,"N",(function(){return P})),r.d(e,"eb",(function(){return L})),r.d(e,"s",(function(){return x})),r.d(e,"kb",(function(){return M})),r.d(e,"u",(function(){return F})),r.d(e,"r",(function(){return D})),r.d(e,"A",(function(){return T})),r.d(e,"z",(function(){return N})),r.d(e,"Y",(function(){return H})),r.d(e,"X",(function(){return A})),r.d(e,"gb",(function(){return E})),r.d(e,"M",(function(){return U})),r.d(e,"C",(function(){return W})),r.d(e,"W",(function(){return q})),r.d(e,"D",(function(){return R})),r.d(e,"bb",(function(){return V})),r.d(e,"I",(function(){return z})),r.d(e,"ab",(function(){return B})),r.d(e,"hb",(function(){return J})),r.d(e,"T",(function(){return G})),r.d(e,"S",(function(){return Y})),r.d(e,"ib",(function(){return Q})),r.d(e,"n",(function(){return K})),r.d(e,"h",(function(){return X})),r.d(e,"jb",(function(){return Z})),r.d(e,"Z",(function(){return tt})),r.d(e,"R",(function(){return et})),r.d(e,"Q",(function(){return rt})),r.d(e,"x",(function(){return at})),r.d(e,"w",(function(){return it})),r.d(e,"k",(function(){return st})),r.d(e,"db",(function(){return nt})),r.d(e,"t",(function(){return ot})),r.d(e,"P",(function(){return ct})),r.d(e,"V",(function(){return ut})),r.d(e,"lb",(function(){return dt})),r.d(e,"cb",(function(){return lt})),r.d(e,"F",(function(){return ht})),r.d(e,"E",(function(){return ft}));var a=r("b6bd");function i(t){return Object(a["a"])({url:"order/cashier/product",method:"get",params:t})}function s(){return Object(a["a"])({url:"order/cashier/cate",method:"get"})}function n(t){return Object(a["a"])({url:"order/cashier/user",method:"post",data:t})}function o(t){return Object(a["a"])({url:"order/cashier/code",method:"post",data:t})}function c(t){return Object(a["a"])({url:"order/cashier/staff",method:"get",params:t})}function u(t,e){return Object(a["a"])({url:"order/cashier/cart/".concat(t),method:"post",data:e})}function d(t,e){return Object(a["a"])({url:"order/cashier/detail/".concat(t,"/").concat(e),method:"get"})}function l(t,e,r){return Object(a["a"])({url:"order/cashier/cart/".concat(t,"/").concat(e),method:"get",params:r})}function h(t,e){return Object(a["a"])({url:"order/cashier/cart/".concat(t),method:"put",data:e})}function f(t){return Object(a["a"])({url:"order/cashier/changeCart",method:"put",data:t})}function m(t,e){return Object(a["a"])({url:"order/cashier/cart/".concat(t),method:"DELETE",data:e})}function p(t,e){return Object(a["a"])({url:"order/cashier/compute/".concat(t),method:"post",data:e})}function g(t,e){return Object(a["a"])({url:"order/cashier/create/".concat(t),method:"post",data:e})}function v(t,e){return Object(a["a"])({url:"order/cashier/pay/".concat(t),method:"post",data:e})}function _(t){return Object(a["a"])({url:"order/list",method:"get",params:t})}function b(t){return Object(a["a"])({url:"order/chart",method:"get",params:t})}function C(t){return Object(a["a"])({url:"order/recharge",method:"get",params:t})}function y(t){return Object(a["a"])({url:"order/vip_order",method:"get",params:t})}function I(t){return Object(a["a"])({url:"order/edit/".concat(t),method:"get"})}function w(t){return Object(a["a"])({url:"order/express_list?status="+t,method:"get"})}function k(t){return Object(a["a"])({url:"/refund/express/".concat(t),method:"get"})}function O(t){return Object(a["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function $(t){return Object(a["a"])({url:"/order/split_delivery/".concat(t.id),method:"put",data:t.datas})}function j(t){return Object(a["a"])({url:"/order/express/temp",method:"get",params:t})}function S(){return Object(a["a"])({url:"/order/delivery/list",method:"get"})}function P(){return Object(a["a"])({url:"/order/sheet_info",method:"get"})}function L(t){return Object(a["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function x(t){return Object(a["a"])({url:"/order/distribution/".concat(t),method:"get"})}function M(t){return Object(a["a"])({url:"/order/write_update/".concat(t),method:"put"})}function F(t){return Object(a["a"])({url:"/order/express/".concat(t),method:"get"})}function D(t){return Object(a["a"])({url:"/order/info/".concat(t),method:"get"})}function T(t){return Object(a["a"])({url:"/refund/detail/".concat(t),method:"get"})}function N(t){return Object(a["a"])({url:"/order/status/".concat(t.id),method:"get",params:t.datas})}function H(t){return Object(a["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function A(t){return Object(a["a"])({url:"/refund/remark/".concat(t.id),method:"put",data:t.remark})}function E(t){return Object(a["a"])({url:"order/split_order/".concat(t),method:"get"})}function U(t){return Object(a["a"])({url:"refund/list",method:"get",params:t})}function W(t){return Object(a["a"])({url:"/order/refund/".concat(t),method:"get"})}function q(t){return Object(a["a"])({url:"/refund/refund/".concat(t.id),method:"put",data:t})}function R(t){return Object(a["a"])({url:"/order/no_refund/".concat(t),method:"get"})}function V(t){return Object(a["a"])({url:"/order/refund_integral/".concat(t),method:"get"})}function z(t,e){return Object(a["a"])({url:"order/export/".concat(e),method:"post",data:t})}function B(t){return Object(a["a"])({url:"order/recharge/remark/".concat(t),method:"get"})}function J(t){return Object(a["a"])({url:"order/vip/remark/".concat(t),method:"get"})}function G(t,e){return Object(a["a"])({url:"order/vip/remark/".concat(t),method:"put",data:e})}function Y(t,e){return Object(a["a"])({url:"order/recharge/remark/".concat(t),method:"put",data:e})}function Q(t){return Object(a["a"])({url:"order/vip/status/".concat(t),method:"get"})}function K(){return Object(a["a"])({url:"order/cashier/cashier_scan",method:"get"})}function X(t,e){return Object(a["a"])({url:"order/cashier/coupon_list/".concat(t),method:"post",data:e})}function Z(t){return Object(a["a"])({url:"order/writeOff/cartInfo",method:"get",params:t})}function tt(t,e){return Object(a["a"])({url:"order/write_update/".concat(t),method:"put",data:e})}function et(t,e){return Object(a["a"])({url:"order/cashier/switch/".concat(e),method:"post",data:t})}function rt(t){return Object(a["a"])({url:"order/cashier/hang",method:"post",data:t})}function at(t,e){return Object(a["a"])({url:"order/cashier/hang/list/".concat(t),method:"get",params:e})}function it(t){return Object(a["a"])({url:"order/cashier/hang/".concat(t),method:"get"})}function st(t){return Object(a["a"])({url:"order/cashier/hang",method:"DELETE",params:{id:t}})}function nt(t){return Object(a["a"])({url:"order/recharge/".concat(t,"/refund_edit"),method:"get"})}function ot(t){return Object(a["a"])({url:"/order/distribution_info",method:"get",params:{ids:t}})}function ct(t){return Object(a["a"])({url:"/order/write/form/".concat(t),method:"get"})}function ut(t){return Object(a["a"])({url:"/order/open/refund/".concat(t.id),method:"put",data:t})}function dt(t){return Object(a["a"])({url:"order/writeoff/records/".concat(t),method:"get"})}function lt(){return Object(a["a"])({url:"refund/reason",method:"get"})}function ht(t){return Object(a["a"])({url:"order/card/benefits/".concat(t),method:"get"})}function ft(t){return Object(a["a"])({url:"order/benefits/".concat(t),method:"get"})}},fbde:function(t,e,r){t.exports=r.p+"view_store/img/process1.617f1e82.png"}}]);