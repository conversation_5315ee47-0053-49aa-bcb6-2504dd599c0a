(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79a32fbc"],{9914:function(t,e,r){"use strict";var n=r("f5a4"),a=r.n(n);a.a},bff0:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{width:"100%","background-color":"#fff"}},[r("Form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"用户搜索：",labelWidth:100,"label-for":"nickname"}},[r("Row",[r("Col",[r("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入ID或者手机号","element-id":"nickname",search:"","enter-button":""},on:{"on-search":t.orderSearch},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}},[r("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.userFrom.field_key,callback:function(e){t.$set(t.userFrom,"field_key",e)},expression:"userFrom.field_key"}},[r("Option",{attrs:{value:"all"}},[t._v("全部")]),r("Option",{attrs:{value:"uid"}},[t._v("ID")]),r("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1)],1)],1),r("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.dataList,height:"330"},on:{"on-sort-change":t.sortChanged},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[r("viewer",[r("div",{staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.userFrom.page,"page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)},a=[],o=r("c24f"),u={name:"userList",data:function(){var t=this;return{total:0,userFrom:{keyword:"",page:1,limit:15,field_key:"all"},loading:!1,dataList:[],currentid:0,columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,r){var n=r.row.uid,a=!1;a=t.currentid===n;var o=t;return e("div",[e("Radio",{props:{value:a},on:{"on-change":function(){if(o.currentid=n,o.$emit("getUserId",r.row),r.row.uid){if("image"===t.$route.query.fodder){var e={image:r.row.avatar,uid:r.row.uid};form_create_helper.set("image",e),form_create_helper.close("image")}}else t.$Message.warning("请先选择用户")}}})])}},{title:"ID",key:"uid",width:80},{title:"头像",slot:"avatars",minWidth:50},{title:"昵称",key:"nickname",minWidth:70},{title:"手机号",key:"phone",minWidth:70},{title:"用户类型",key:"user_type",minWidth:70},{title:"余额",key:"now_money",sortable:"custom",minWidth:70}]}},created:function(){this.getList()},methods:{sortChanged:function(t){this.userFrom[t.key]=t.order,this.getList()},getList:function(){var t=this;this.loading=!0,Object(o["m"])(this.userFrom).then((function(e){t.loading=!1,t.total=e.data.count,t.dataList=e.data.list})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},usersearchList:function(){var t=this;this.loading=!0,Object(o["u"])(this.userFrom).then((function(e){t.dataList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},orderSearch:function(){this.userFrom.page=1,""==this.userFrom.keyword?this.getList():this.usersearchList()},pageChange:function(t){this.userFrom.page=t,""==this.userFrom.keyword?this.getList():this.usersearchList()}}},i=u,s=(r("9914"),r("2877")),c=Object(s["a"])(i,n,a,!1,null,"45517fd8",null);e["default"]=c.exports},c24f:function(t,e,r){"use strict";r.d(e,"r",(function(){return a})),r.d(e,"k",(function(){return o})),r.d(e,"l",(function(){return u})),r.d(e,"a",(function(){return i})),r.d(e,"q",(function(){return s})),r.d(e,"j",(function(){return c})),r.d(e,"m",(function(){return d})),r.d(e,"u",(function(){return l})),r.d(e,"d",(function(){return f})),r.d(e,"f",(function(){return h})),r.d(e,"c",(function(){return m})),r.d(e,"e",(function(){return g})),r.d(e,"p",(function(){return b})),r.d(e,"n",(function(){return p})),r.d(e,"t",(function(){return v})),r.d(e,"o",(function(){return w})),r.d(e,"s",(function(){return _})),r.d(e,"g",(function(){return k})),r.d(e,"i",(function(){return O})),r.d(e,"b",(function(){return y})),r.d(e,"h",(function(){return j}));var n=r("b6bd");function a(){return Object(n["a"])({url:"user/user_label_cate",method:"get"})}function o(){return Object(n["a"])({url:"user/user_label_cate/create",method:"get"})}function u(t){return Object(n["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function i(t){return Object(n["a"])({url:"user/user_label",method:"get",params:t})}function s(){return Object(n["a"])({url:"user/user_label/create",method:"get"})}function c(t){return Object(n["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function d(t){return Object(n["a"])({url:"user/user",method:"get",params:t})}function l(t){return Object(n["a"])({url:"user/search",method:"get",params:t})}function f(t){return Object(n["a"])({url:"user/label/".concat(t),method:"get"})}function h(t,e){return Object(n["a"])({url:"user/label/".concat(t),method:"post",data:e})}function m(t){return Object(n["a"])({url:"user/user/".concat(t),method:"get"})}function g(t){return Object(n["a"])({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function b(t){return Object(n["a"])({url:"user/set_label",method:"post",data:t})}function p(){return Object(n["a"])({url:"user/recharge/meal",method:"get"})}function v(){return Object(n["a"])({url:"user/member/ship",method:"get"})}function w(t){return Object(n["a"])({url:"user/recharge",method:"post",data:t})}function _(t){return Object(n["a"])({url:"/user/member",method:"post",data:t})}function k(t){return Object(n["a"])({url:"staff/binding/user",method:"post",data:t})}function O(t){return Object(n["a"])({url:"updatePwd",method:"PUT",data:t})}function y(t,e){return Object(n["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function j(){return Object(n["a"])({url:"staff/staff_info",method:"get"})}},f5a4:function(t,e,r){}}]);