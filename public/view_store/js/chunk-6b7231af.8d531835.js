(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b7231af"],{1836:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Row",[n("Col",{attrs:{span:"5"}},[0==t.roleList.length?n("div",{staticClass:"menu"},[n("div",{staticClass:"title acea-row row-between-wrapper"},[n("span",[t._v("管理员身份")]),n("span",{directives:[{name:"auth",rawName:"v-auth",value:["system-role-create"],expression:"['system-role-create']"}],staticClass:"add",on:{click:t.roleAdd}},[t._v("添加")])]),n("div",{staticClass:"title titletips"},[t._v("暂无数据")])]):t._e(),t.roleList.length?n("Menu",{staticClass:"menu",attrs:{"active-name":t.active,width:"auto"}},[n("div",{staticClass:"title acea-row row-between-wrapper"},[n("span",[t._v("管理员身份")]),n("span",{directives:[{name:"auth",rawName:"v-auth",value:["system-role-create"],expression:"['system-role-create']"}],staticClass:"add",on:{click:t.roleAdd}},[t._v("添加")])]),n("MenuGroup",t._l(t.roleList,(function(e,a){return n("MenuItem",{key:a,staticClass:"menu-item",class:e.status?"":"disable",attrs:{name:e.id},nativeOn:{click:function(n){return t.roleManager(e)}}},[n("div",{on:{mouseleave:t.onMouseOver}},[n("div",{staticClass:"acea-row row-between-wrapper"},[n("span",[t._v(t._s(e.role_name)+" ("+t._s(e.count)+")")]),e.status?t._e():n("span",{staticClass:"stop"},[t._v("禁用")]),n("Icon",{staticClass:"icon-box",attrs:{type:"ios-more",size:"24"},on:{click:function(e){return e.stopPropagation(),t.showMenu(a)}}})],1),t.activeIndex===a?n("div",{staticClass:"ivu-select-dropdown"},[n("ul",{staticClass:"ivu-dropdown-menu"},[n("li",{staticClass:"ivu-dropdown-item",on:{click:function(n){return t.roleEdit(e.id)}}},[t._v("编辑")]),n("li",{staticClass:"ivu-dropdown-item",on:{click:function(n){return t.del(e,"删除",a)}}},[t._v("删除")])])]):t._e()])])})),1)],1):t._e()],1),n("Col",{staticClass:"pl15",attrs:{span:"19"}},[n("Button",{directives:[{name:"auth",rawName:"v-auth",value:["system-admin-create"],expression:"['system-admin-create']"}],attrs:{type:"primary"},on:{click:t.addManage}},[t._v("添加管理员")]),n("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.manageList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"status",fn:function(e){var a=e.row;e.index;return[n("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},on:{"on-change":function(e){return t.changeSwitch(a)}},model:{value:a.status,callback:function(e){t.$set(a,"status",e)},expression:"row.status"}},[n("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])]}},{key:"avatar",fn:function(e){var a=e.row;e.index;return[a.avatar?n("viewer",[n("div",{staticClass:"tabBox_img"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a.avatar,expression:"row.avatar"}]})])]):t._e()]}},{key:"action",fn:function(e){var a=e.row,r=e.index;return[n("a",{on:{click:function(e){return t.editManage(a.id)}}},[t._v("编辑")]),n("Divider",{attrs:{type:"vertical"}}),n("a",{on:{click:function(e){return t.manageDel(a,"删除",r)}}},[t._v("删除")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.manageFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1),n("Modal",{staticClass:"Box",attrs:{"z-index":1e3,scrollable:"","footer-hide":"",closable:"",title:t.modelTit+"管理员身份","mask-closable":!1,width:"700"},on:{"on-cancel":t.onCancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[n("limits",{ref:"mychild",attrs:{manage:1},on:{updateRole:t.updateRole}})],1)],1)},r=[],s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Form",{ref:"formInline",staticStyle:{"padding-top":"16px"},attrs:{"label-width":100,model:t.formInline,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[t.manage?n("FormItem",{attrs:{label:"身份名称：",prop:"role_name"}},[n("Input",{staticStyle:{width:"95%"},attrs:{placeholder:"请输入身份名称"},model:{value:t.formInline.role_name,callback:function(e){t.$set(t.formInline,"role_name",e)},expression:"formInline.role_name"}})],1):n("FormItem",{attrs:{label:"菜单搜索："}},[n("Input",{staticStyle:{width:"50%"},attrs:{suffix:"ios-search",placeholder:"请输入菜单名称"}})],1),t.manage?n("FormItem",{attrs:{label:"状态：",prop:"status"}},[n("i-switch",{attrs:{size:"large","false-value":0,"true-value":1},model:{value:t.formInline.status,callback:function(e){t.$set(t.formInline,"status",e)},expression:"formInline.status"}},[n("span",{attrs:{slot:"open","true-value":1},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close","false-value":0},slot:"close"},[t._v("关闭")])])],1):t._e(),n("Row",{attrs:{gutter:24}},[n("Col",[n("div",{staticClass:"treeList"},[n("FormItem",{attrs:{label:"菜单设置："}},[n("Tree",{ref:"tree",attrs:{data:t.menusList,"show-checkbox":""}})],1),n("FormItem",{attrs:{label:"收银台设置："}},[n("Tree",{ref:"treeCash",attrs:{data:t.cashList,"show-checkbox":""}})],1)],1)])],1),t.spinShow?n("Spin",{attrs:{size:"large",fix:""}}):t._e(),n("div",{staticClass:"footer acea-row row-center-wrapper"},[n("Button",{staticStyle:{"margin-right":"14px"},attrs:{type:"default"},on:{click:function(e){return t.cancle("formInline")}}},[t._v("取消")]),n("Button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formInline")}}},[t._v("确认")])],1)],1)],1)},i=[],o=n("a34a"),c=n.n(o),u=n("d708"),l=n("90e7");function d(t,e,n,a,r,s,i){try{var o=t[s](i),c=o.value}catch(u){return void n(u)}o.done?e(c):Promise.resolve(c).then(a,r)}function m(t){return function(){var e=this,n=arguments;return new Promise((function(a,r){var s=t.apply(e,n);function i(t){d(s,a,r,i,o,"next",t)}function o(t){d(s,a,r,i,o,"throw",t)}i(void 0)}))}}var h={name:"limits",props:{manage:{type:Number,default:0}},data:function(){return{grid:{xl:12,lg:12,md:8,sm:20,xs:24},routePre:u["a"].routePre,menusList:[],cashList:[],SonMenusList:[],spinShow:!1,formInline:{role_name:"",status:1,checked_menus:[],id:0,son_menus:[],checked_cashier_menus:[]},status:1,ruleValidate:{role_name:[{required:!0,message:"请输入管理员身份",trigger:"blur"}],status:[{required:!0,type:"number",message:"请选择是否开启",trigger:"change"}]}}},created:function(){},methods:{onCancel:function(){this.formInline={role_name:"",status:1,checked_menus:[],checked_cashier_menus:[],id:0,son_menus:[]},this.SonMenusList=[]},checkedFun:function(t){var e=this;t.forEach((function(t){t.menu_path=="".concat(e.routePre,"/home")&&(t.disabled=!0,t.children.forEach((function(t){t.menu_path=="".concat(e.routePre,"/home/<USER>")&&(e.formInline.checked_menus.push(t.id),t.checked=!0,t.disabled=!0,t.children.length&&t.children.forEach((function(t){t.checked=!0,t.disabled=!0})))})))}))},getMenuList:function(){var t=this;this.spinShow=!0,Object(l["u"])().then((function(e){t.spinShow=!1,t.checkedFun(e.data),t.$set(t,"menusList",e.data)})).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))},getCashierList:function(){var t=this;Object(l["g"])().then((function(e){e.data.forEach((function(t){"/cashier/cashier/index"==t.menu_path&&(t.checked=!0,t.disabled=!0)})),t.$set(t,"cashList",e.data)})).catch((function(e){t.$Message.error(e.msg)}))},getInfo:function(t){var e=this;this.spinShow=!0,this.formInline.id=t,Object(l["B"])(t).then(function(){var t=m(c.a.mark((function t(n){var a;return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=n.data,e.formInline=a.role||e.formInline,e.formInline.checked_menus=Array.from(new Set(e.formInline.rules)),e.formInline.checked_cashier_menus=Array.from(new Set(e.formInline.cashier_rules)),e.tidyRes(a.menus,0),e.tidyRes(a.cashier_menus,1),e.spinShow=!1;case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},tidyRes:function(t,e){var n=this,a=[];t.map((function(t){a.push(n.initMenu(t,e))})),e?this.$set(this,"cashList",a):(this.checkedFun(a),this.$set(this,"menusList",a))},initMenu:function(t,e){var n=this,a={},r=","+this.formInline.checked_menus.join(",")+",",s=","+this.formInline.checked_cashier_menus.join(",")+",";return a.title=t.title,a.id=t.id,a.menu_path=t.menu_path,t.children&&t.children.length>0?(a.children=[],t.children.map((function(t){a.children.push(n.initMenu(t,e))}))):a.checked=e?-1!==s.indexOf(String(","+a.id+",")):-1!==r.indexOf(String(","+a.id+",")),a},cancle:function(){this.onCancel(),this.$emit("updateRole",!1)},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(n){if(!n)return!1;e.formInline.checked_menus=[],e.formInline.checked_cashier_menus=[],e.$refs.tree.getCheckedAndIndeterminateNodes().map((function(t){e.formInline.checked_menus.push(t.id)})),e.$refs.treeCash.getCheckedAndIndeterminateNodes().map((function(t){e.formInline.checked_cashier_menus.push(t.id)}));var a=e.formInline.checked_menus;a=a.concat(e.formInline.son_menus),e.formInline.checked_menus=Array.from(new Set(a)),Object(l["A"])(e.formInline).then(function(){var n=m(c.a.mark((function n(a){return c.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.$Message.success(a.msg),e.$emit("updateRole",!1),e.$refs[t].resetFields(),e.onCancel();case 4:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}))}}},f=h,p=(n("3b93"),n("2877")),g=Object(p["a"])(f,s,i,!1,null,"3ee86a73",null),v=g.exports,b={name:"index",components:{limits:v},data:function(){return{modals:!1,modelTit:"",loading:!1,roleList:[],active:"",activeIndex:"",columns:[{title:"ID",key:"id",width:60},{title:"头像",slot:"avatar",minWidth:100},{title:"名称",key:"staff_name",minWidth:100},{title:"账号",key:"account",minWidth:120},{title:"管理员身份",key:"roles",minWidth:200},{title:"状态",slot:"status",minWidth:100},{title:"操作",slot:"action",fixed:"right",minWidth:120}],manageList:[],total:0,manageFrom:{page:1,limit:15,roles:0},tatus:1}},created:function(){},mounted:function(){this.roleInfo(this.tatus)},methods:{roleManager:function(t){this.manageFrom.roles=t.id,this.manageFrom.page=1,t.status&&this.adminList()},onCancel:function(){this.$refs.mychild.onCancel()},addManage:function(){var t=this;this.$modalForm(Object(l["d"])()).then((function(e){t.$Message.success(e.msg),t.adminList(),t.roleInfo()}))},editManage:function(t){var e=this;this.$modalForm(Object(l["e"])(t)).then((function(t){e.$Message.success(t.msg),e.adminList(),e.roleInfo()}))},changeSwitch:function(t){var e=this;Object(l["f"])(t.id,t.status).then((function(t){e.$Message.success(t.msg),e.manageList()})).catch((function(t){e.$Message.error(t.msg)}))},adminList:function(){var t=this;this.loading=!0,Object(l["G"])(this.manageFrom).then((function(e){var n=e.data;t.manageList=n.list,t.total=n.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.manageFrom.page=t,this.adminList()},updateRole:function(t){this.modals=t,this.roleInfo()},roleInfo:function(t){var e=this;Object(l["I"])().then((function(n){e.roleList=n.data.list||[],e.active=n.data.list[0].id||"",e.manageFrom.roles=e.manageFrom.roles||n.data.list[0].id,1==t&&e.roleManager(e.roleList[0])})).catch((function(t){e.$Message.error(t.msg)}))},manageDel:function(t,e,n){var a=this,r={title:e,num:n,url:"system/admin/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(r).then((function(t){a.$Message.success(t.msg),a.manageList.splice(n,1),a.manageList.length||(a.manageFrom.page=1==a.manageFrom.page?1:a.manageFrom.page-1),a.adminList(),a.roleInfo()})).catch((function(t){a.$Message.error(t.msg)}))},del:function(t,e,n){var a=this,r={title:e,num:n,url:"system/role/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(r).then((function(t){a.$Message.success(t.msg),a.roleList.splice(n,1)})).catch((function(t){a.$Message.error(t.msg)}))},roleAdd:function(){this.modelTit="添加",this.modals=!0,this.$refs.mychild.getMenuList(),this.$refs.mychild.getCashierList()},roleEdit:function(t){this.modelTit="编辑",this.modals=!0,this.$refs.mychild.getInfo(t)},showMenu:function(t){this.activeIndex=t},onMouseOver:function(){this.activeIndex=""}}},_=b,w=(n("edf2"),Object(p["a"])(_,a,r,!1,null,"07bb6265",null));e["default"]=w.exports},"3b93":function(t,e,n){"use strict";var a=n("8251"),r=n.n(a);r.a},8251:function(t,e,n){},"90e7":function(t,e,n){"use strict";n.d(e,"I",(function(){return r})),n.d(e,"A",(function(){return s})),n.d(e,"B",(function(){return i})),n.d(e,"u",(function(){return o})),n.d(e,"G",(function(){return c})),n.d(e,"f",(function(){return u})),n.d(e,"d",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"t",(function(){return m})),n.d(e,"D",(function(){return h})),n.d(e,"E",(function(){return f})),n.d(e,"h",(function(){return p})),n.d(e,"J",(function(){return g})),n.d(e,"K",(function(){return v})),n.d(e,"C",(function(){return b})),n.d(e,"i",(function(){return _})),n.d(e,"k",(function(){return w})),n.d(e,"j",(function(){return y})),n.d(e,"l",(function(){return k})),n.d(e,"m",(function(){return I})),n.d(e,"F",(function(){return O})),n.d(e,"s",(function(){return j})),n.d(e,"a",(function(){return C})),n.d(e,"q",(function(){return x})),n.d(e,"b",(function(){return L})),n.d(e,"r",(function(){return M})),n.d(e,"c",(function(){return $})),n.d(e,"g",(function(){return F})),n.d(e,"L",(function(){return S})),n.d(e,"H",(function(){return E})),n.d(e,"n",(function(){return A})),n.d(e,"o",(function(){return R})),n.d(e,"x",(function(){return z})),n.d(e,"v",(function(){return T})),n.d(e,"w",(function(){return B})),n.d(e,"p",(function(){return D})),n.d(e,"y",(function(){return P})),n.d(e,"z",(function(){return N}));var a=n("b6bd");function r(){return Object(a["a"])({url:"system/role",method:"get"})}function s(t){return Object(a["a"])({url:"system/role/".concat(t.id),method:"post",data:t})}function i(t){return Object(a["a"])({url:"system/role/".concat(t,"/edit"),method:"get"})}function o(){return Object(a["a"])({url:"system/menusList",method:"get"})}function c(t){return Object(a["a"])({url:"system/admin",method:"get",params:t})}function u(t,e){return Object(a["a"])({url:"system/admin/set_status/".concat(t,"/").concat(e),method:"put"})}function l(){return Object(a["a"])({url:"system/admin/create",method:"get"})}function d(t){return Object(a["a"])({url:"system/admin/".concat(t,"/edit"),method:"get"})}function m(){return Object(a["a"])({url:"config",method:"get"})}function h(){return Object(a["a"])({url:"system/store/info",method:"get"})}function f(t){return Object(a["a"])({url:"system/store/update",method:"put",data:t})}function p(t){return Object(a["a"])({url:"city",method:"get",params:t})}function g(t){return Object(a["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function v(t,e){return Object(a["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function b(t){return Object(a["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function _(t){return Object(a["a"])({url:"city",method:"get",params:t})}function w(t){return Object(a["a"])({url:"/order/delivery_order/list",method:"get",params:t})}function y(t){return Object(a["a"])({url:"/order/delivery_order/cancelForm/".concat(t),method:"get"})}function k(t){return Object(a["a"])({url:"/system/store/getBusiness/".concat(t),method:"get"})}function I(t){return Object(a["a"])({url:"/system/config/".concat(t),method:"get"})}function O(t,e){return Object(a["a"])({url:"/system/config/".concat(t),method:"post",data:e})}function j(){return Object(a["a"])({url:"/table/seats/list",method:"get"})}function C(t,e){return Object(a["a"])({url:"/table/add/seats/".concat(t),method:"get",params:e})}function x(t){return Object(a["a"])({url:"/table/cate/list",method:"get",params:t})}function L(t,e){return Object(a["a"])({url:"/table/add/cate/".concat(t),method:"get",params:e})}function M(t){return Object(a["a"])({url:"/table/qrcodes/list",method:"get",params:t})}function $(t,e){return Object(a["a"])({url:"/table/add/qrcode/".concat(t),method:"post",data:e})}function F(){return Object(a["a"])({url:"/system/cashierMenusList",method:"get"})}function S(t,e){return Object(a["a"])({url:"/table/update/using/".concat(t),method:"get",params:e})}function E(t,e){return Object(a["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function A(t){return Object(a["a"])({url:"system/config/edit_new_build/"+t,method:"get"})}function R(t){return Object(a["a"])({url:"/system/printer/list",method:"get",params:t})}function z(t){return Object(a["a"])({url:"/system/printer/status/".concat(t.id,"/").concat(t.status),method:"post"})}function T(t,e){return Object(a["a"])({url:"/system/printer/".concat(t),method:"post",data:e})}function B(t){return Object(a["a"])({url:"/system/printer/info/".concat(t),method:"get"})}function D(t){return Object(a["a"])({url:"resolve/city",method:"get",params:t})}function P(t,e){return Object(a["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function N(t,e){return Object(a["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},e101:function(t,e,n){},edf2:function(t,e,n){"use strict";var a=n("e101"),r=n.n(a);r.a}}]);