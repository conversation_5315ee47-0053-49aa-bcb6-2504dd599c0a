(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-71bec66a"],{"2d28":function(t,r,e){"use strict";e.r(r);var n=function(){var t=this,r=t.$createElement,e=t._self._c||r;return e("div",{staticStyle:{"background-color":"#fff"}},t._l(t.newArrayData,(function(r,n){return e("div",{key:n,staticClass:"putSupplier"},[e("div",{staticClass:"header acea-row row-between-wrapper"},[e("div",{staticClass:"left acea-row row-middle"},[e("div",{staticClass:"pictrue",attrs:{id:"qrCodeUrl"+n}}),e("div",{staticClass:"info on"},[e("div",[e("span",{staticClass:"name"},[t._v("收货人：")]),t._v(t._s(r.real_name))]),e("div",[e("span",{staticClass:"name"},[t._v("收货地址：")]),t._v(t._s(r.user_address))]),e("div",[e("span",{staticClass:"name"},[t._v("手机号：")]),e("span",[t._v(t._s(r.user_phone))])])])]),e("div",{staticClass:"info"},[e("div",[e("span",{staticClass:"name"},[t._v("订单编号：")]),t._v(t._s(r.order_id))]),e("div",[e("span",{staticClass:"name"},[t._v("支付时间：")]),t._v(t._s(r.pay_time))]),e("div",[e("span",{staticClass:"name"},[t._v("支付方式：")]),t._v(t._s(r.pay_type_name))])])]),e("div",{staticClass:"mt20"},[e("Table",{attrs:{border:"",columns:t.columns,data:r.goodsList,"disabled-hover":!0},scopedSlots:t._u([{key:"store_name",fn:function(r){var n=r.row;return[e("div",{staticClass:"line2"},[t._v(t._s(n.store_name))])]}},{key:"suk",fn:function(r){var n=r.row;return[e("div",{staticClass:"line2"},[t._v(t._s(n.suk))])]}}],null,!0)})],1),e("div",{staticClass:"bottom acea-row row-between-wrapper"},[e("div",{staticClass:"acea-row row-middle"},[e("div",{staticClass:"item"},[e("span",{staticClass:"name"},[t._v("运费：")]),t._v(t._s(r.freight_price))]),e("div",{staticClass:"item"},[e("span",{staticClass:"name"},[t._v("优惠：")]),t._v(t._s(r.coupon_price))]),e("div",{staticClass:"item"},[e("span",{staticClass:"name"},[t._v("会员折扣：")]),t._v(t._s(r.vip_true_price))]),e("div",{staticClass:"item"},[e("span",{staticClass:"name"},[t._v("积分抵扣：")]),t._v(t._s(r.deduction_price))])]),e("div",{staticClass:"pricePay"},[t._v("实付金额：￥"+t._s(r.pay_price))])]),e("div",{staticClass:"bottom acea-row"},[e("div",{staticClass:"name"},[t._v("用户备注："),e("span",{staticClass:"con"},[t._v(t._s(r.mark||"-"))])])]),e("div",{staticClass:"h50"},[t.data.site_name||t.data.refund_address||t.data.refund_phone?e("div",{staticClass:"delivery"},[t.data.site_name?e("div",[t._v("店铺信息："+t._s(t.data.site_name))]):t._e(),t.data.refund_address?e("div",[t._v("地址："+t._s(t.data.refund_address))]):t._e(),t.data.refund_phone?e("div",[t._v("联系方式："+t._s(t.data.refund_phone))]):t._e()]):t._e()])])})),0)},a=[],c=e("f8b7"),u=e("d044"),o=e.n(u),i=e("d708"),d={data:function(){return{columns:[{title:"商品编号",key:"index",align:"center",width:60},{title:"商品名称",slot:"store_name",align:"center",width:253},{title:"商品规格",slot:"suk",align:"center",width:219},{title:"单价",key:"truePrice",align:"center",width:100},{title:"数量",key:"cart_num",align:"center",width:60},{title:"金额",key:"subtotal",align:"center",width:100}],data:{},goods:[],BaseURL:i["a"].apiBaseURL.replace(/storeapi/,""),newArrayData:[]}},created:function(){this.getDistribution()},mounted:function(){this.$nextTick((function(){var t=this;setTimeout((function(){t.creatQrCode()}),200)}))},methods:{creatQrCode:function(){var t=this.BaseURL;this.newArrayData.forEach((function(r,e){var n=document.getElementById("qrCodeUrl"+e);new o.a(n,{text:t,width:90,height:90,colorDark:"#000000",colorLight:"#ffffff",correctLevel:o.a.CorrectLevel.H})}))},getDistribution:function(){var t=this;Object(c["t"])(this.$route.query.id).then((function(r){t.data=r.data,t.goods=r.data.list;var e=r.data,n=[];e.list.forEach((function(t,r){for(var e=[],a=t.list,c=Math.ceil(a.length/6),u=0;u<c;u++){var o=a.slice(6*u,6*u+6);o.length&&e.push(o)}var i=6-e[e.length-1].length;if(i)for(var d=0;d<i;d++)e[e.length-1].push({cart_num:"",index:"",store_name:"",subtotal:"",suk:"",truePrice:""});e.forEach((function(r){n.push({real_name:t.real_name,user_address:t.user_address,user_phone:t.user_phone,freight_price:t.freight_price,coupon_price:t.coupon_price,vip_true_price:t.vip_true_price,deduction_price:t.deduction_price,use_integral:t.use_integral,pay_price:t.pay_price,mark:t.mark,order_id:t.order_id,pay_time:t.pay_time,pay_type_name:t.pay_type_name,goodsList:r})}))})),t.newArrayData=n})).catch((function(r){t.$Message.error(r.msg)}))}}},s=d,f=(e("5c13"),e("2877")),l=Object(f["a"])(s,n,a,!1,null,"24467a48",null);r["default"]=l.exports},"5c13":function(t,r,e){"use strict";var n=e("df4e"),a=e.n(n);a.a},df4e:function(t,r,e){},f8b7:function(t,r,e){"use strict";e.d(r,"m",(function(){return a})),e.d(r,"e",(function(){return c})),e.d(r,"p",(function(){return u})),e.d(r,"f",(function(){return o})),e.d(r,"o",(function(){return i})),e.d(r,"a",(function(){return d})),e.d(r,"j",(function(){return s})),e.d(r,"c",(function(){return f})),e.d(r,"d",(function(){return l})),e.d(r,"q",(function(){return h})),e.d(r,"b",(function(){return m})),e.d(r,"g",(function(){return p})),e.d(r,"i",(function(){return _})),e.d(r,"l",(function(){return b})),e.d(r,"K",(function(){return v})),e.d(r,"G",(function(){return g})),e.d(r,"L",(function(){return j})),e.d(r,"O",(function(){return O})),e.d(r,"y",(function(){return w})),e.d(r,"v",(function(){return C})),e.d(r,"B",(function(){return y})),e.d(r,"U",(function(){return k})),e.d(r,"fb",(function(){return E})),e.d(r,"J",(function(){return x})),e.d(r,"H",(function(){return L})),e.d(r,"N",(function(){return D})),e.d(r,"eb",(function(){return T})),e.d(r,"s",(function(){return U})),e.d(r,"kb",(function(){return A})),e.d(r,"u",(function(){return B})),e.d(r,"r",(function(){return q})),e.d(r,"A",(function(){return P})),e.d(r,"z",(function(){return R})),e.d(r,"Y",(function(){return S})),e.d(r,"X",(function(){return $})),e.d(r,"gb",(function(){return I})),e.d(r,"M",(function(){return J})),e.d(r,"C",(function(){return M})),e.d(r,"W",(function(){return Q})),e.d(r,"D",(function(){return H})),e.d(r,"bb",(function(){return z})),e.d(r,"I",(function(){return F})),e.d(r,"ab",(function(){return G})),e.d(r,"hb",(function(){return K})),e.d(r,"T",(function(){return N})),e.d(r,"S",(function(){return V})),e.d(r,"ib",(function(){return W})),e.d(r,"n",(function(){return X})),e.d(r,"h",(function(){return Y})),e.d(r,"jb",(function(){return Z})),e.d(r,"Z",(function(){return tt})),e.d(r,"R",(function(){return rt})),e.d(r,"Q",(function(){return et})),e.d(r,"x",(function(){return nt})),e.d(r,"w",(function(){return at})),e.d(r,"k",(function(){return ct})),e.d(r,"db",(function(){return ut})),e.d(r,"t",(function(){return ot})),e.d(r,"P",(function(){return it})),e.d(r,"V",(function(){return dt})),e.d(r,"lb",(function(){return st})),e.d(r,"cb",(function(){return ft})),e.d(r,"F",(function(){return lt})),e.d(r,"E",(function(){return ht}));var n=e("b6bd");function a(t){return Object(n["a"])({url:"order/cashier/product",method:"get",params:t})}function c(){return Object(n["a"])({url:"order/cashier/cate",method:"get"})}function u(t){return Object(n["a"])({url:"order/cashier/user",method:"post",data:t})}function o(t){return Object(n["a"])({url:"order/cashier/code",method:"post",data:t})}function i(t){return Object(n["a"])({url:"order/cashier/staff",method:"get",params:t})}function d(t,r){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"post",data:r})}function s(t,r){return Object(n["a"])({url:"order/cashier/detail/".concat(t,"/").concat(r),method:"get"})}function f(t,r,e){return Object(n["a"])({url:"order/cashier/cart/".concat(t,"/").concat(r),method:"get",params:e})}function l(t,r){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"put",data:r})}function h(t){return Object(n["a"])({url:"order/cashier/changeCart",method:"put",data:t})}function m(t,r){return Object(n["a"])({url:"order/cashier/cart/".concat(t),method:"DELETE",data:r})}function p(t,r){return Object(n["a"])({url:"order/cashier/compute/".concat(t),method:"post",data:r})}function _(t,r){return Object(n["a"])({url:"order/cashier/create/".concat(t),method:"post",data:r})}function b(t,r){return Object(n["a"])({url:"order/cashier/pay/".concat(t),method:"post",data:r})}function v(t){return Object(n["a"])({url:"order/list",method:"get",params:t})}function g(t){return Object(n["a"])({url:"order/chart",method:"get",params:t})}function j(t){return Object(n["a"])({url:"order/recharge",method:"get",params:t})}function O(t){return Object(n["a"])({url:"order/vip_order",method:"get",params:t})}function w(t){return Object(n["a"])({url:"order/edit/".concat(t),method:"get"})}function C(t){return Object(n["a"])({url:"order/express_list?status="+t,method:"get"})}function y(t){return Object(n["a"])({url:"/refund/express/".concat(t),method:"get"})}function k(t){return Object(n["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function E(t){return Object(n["a"])({url:"/order/split_delivery/".concat(t.id),method:"put",data:t.datas})}function x(t){return Object(n["a"])({url:"/order/express/temp",method:"get",params:t})}function L(){return Object(n["a"])({url:"/order/delivery/list",method:"get"})}function D(){return Object(n["a"])({url:"/order/sheet_info",method:"get"})}function T(t){return Object(n["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function U(t){return Object(n["a"])({url:"/order/distribution/".concat(t),method:"get"})}function A(t){return Object(n["a"])({url:"/order/write_update/".concat(t),method:"put"})}function B(t){return Object(n["a"])({url:"/order/express/".concat(t),method:"get"})}function q(t){return Object(n["a"])({url:"/order/info/".concat(t),method:"get"})}function P(t){return Object(n["a"])({url:"/refund/detail/".concat(t),method:"get"})}function R(t){return Object(n["a"])({url:"/order/status/".concat(t.id),method:"get",params:t.datas})}function S(t){return Object(n["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function $(t){return Object(n["a"])({url:"/refund/remark/".concat(t.id),method:"put",data:t.remark})}function I(t){return Object(n["a"])({url:"order/split_order/".concat(t),method:"get"})}function J(t){return Object(n["a"])({url:"refund/list",method:"get",params:t})}function M(t){return Object(n["a"])({url:"/order/refund/".concat(t),method:"get"})}function Q(t){return Object(n["a"])({url:"/refund/refund/".concat(t.id),method:"put",data:t})}function H(t){return Object(n["a"])({url:"/order/no_refund/".concat(t),method:"get"})}function z(t){return Object(n["a"])({url:"/order/refund_integral/".concat(t),method:"get"})}function F(t,r){return Object(n["a"])({url:"order/export/".concat(r),method:"post",data:t})}function G(t){return Object(n["a"])({url:"order/recharge/remark/".concat(t),method:"get"})}function K(t){return Object(n["a"])({url:"order/vip/remark/".concat(t),method:"get"})}function N(t,r){return Object(n["a"])({url:"order/vip/remark/".concat(t),method:"put",data:r})}function V(t,r){return Object(n["a"])({url:"order/recharge/remark/".concat(t),method:"put",data:r})}function W(t){return Object(n["a"])({url:"order/vip/status/".concat(t),method:"get"})}function X(){return Object(n["a"])({url:"order/cashier/cashier_scan",method:"get"})}function Y(t,r){return Object(n["a"])({url:"order/cashier/coupon_list/".concat(t),method:"post",data:r})}function Z(t){return Object(n["a"])({url:"order/writeOff/cartInfo",method:"get",params:t})}function tt(t,r){return Object(n["a"])({url:"order/write_update/".concat(t),method:"put",data:r})}function rt(t,r){return Object(n["a"])({url:"order/cashier/switch/".concat(r),method:"post",data:t})}function et(t){return Object(n["a"])({url:"order/cashier/hang",method:"post",data:t})}function nt(t,r){return Object(n["a"])({url:"order/cashier/hang/list/".concat(t),method:"get",params:r})}function at(t){return Object(n["a"])({url:"order/cashier/hang/".concat(t),method:"get"})}function ct(t){return Object(n["a"])({url:"order/cashier/hang",method:"DELETE",params:{id:t}})}function ut(t){return Object(n["a"])({url:"order/recharge/".concat(t,"/refund_edit"),method:"get"})}function ot(t){return Object(n["a"])({url:"/order/distribution_info",method:"get",params:{ids:t}})}function it(t){return Object(n["a"])({url:"/order/write/form/".concat(t),method:"get"})}function dt(t){return Object(n["a"])({url:"/order/open/refund/".concat(t.id),method:"put",data:t})}function st(t){return Object(n["a"])({url:"order/writeoff/records/".concat(t),method:"get"})}function ft(){return Object(n["a"])({url:"refund/reason",method:"get"})}function lt(t){return Object(n["a"])({url:"order/card/benefits/".concat(t),method:"get"})}function ht(t){return Object(n["a"])({url:"order/benefits/".concat(t),method:"get"})}}}]);