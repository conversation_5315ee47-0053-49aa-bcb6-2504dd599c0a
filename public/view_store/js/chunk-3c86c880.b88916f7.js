(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3c86c880"],{"4d03":function(t,e,a){},"523a":function(t,e,a){"use strict";var n=a("5285"),i=a.n(n);i.a},5285:function(t,e,a){},8745:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"f",(function(){return l})),a.d(e,"i",(function(){return c})),a.d(e,"j",(function(){return u})),a.d(e,"k",(function(){return d})),a.d(e,"g",(function(){return f})),a.d(e,"h",(function(){return m})),a.d(e,"a",(function(){return h}));var n=a("b6bd");function i(){return Object(n["a"])({url:"finance/info",method:"get"})}function r(t){return Object(n["a"])({url:"finance/info",method:"post",data:t})}function o(t){return Object(n["a"])({url:"finance/storeExtract/list",method:"get",params:t})}function s(t){return Object(n["a"])({url:"finance/storeExtract/cash",method:"post",data:t})}function l(t,e){return Object(n["a"])({url:"finance/storeExtract/mark/".concat(t),method:"post",data:e})}function c(t){return Object(n["a"])({url:"finance/store_finance_flow/list",method:"get",params:t})}function u(t,e){return Object(n["a"])({url:"finance/store_finance_flow/mark/".concat(t),method:"post",data:e})}function d(){return Object(n["a"])({url:"finance/store_finance_flow/staff",method:"get"})}function f(t){return Object(n["a"])({url:"finance/store_finance_flow/fund_record",method:"get",params:t})}function m(t){return Object(n["a"])({url:"finance/store_finance_flow/fund_record_info",method:"get",params:t})}function h(t){return Object(n["a"])({url:"/export/financeRecord",method:"get",params:t})}},aad2:function(t,e,a){"use strict";var n=a("c1df"),i=a.n(n);e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本季度",value:function(){var t=i()(i()().quarter(i()().quarter()).startOf("quarter").valueOf()).format("YYYY-MM-DD"),e=i()(i()().quarter(i()().quarter()).endOf("quarter").valueOf()).format("YYYY-MM-DD");return[t,e]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},b585:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"创建时间："}},[a("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1),a("Card",{staticClass:"ive-mt tablebox",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"product_tabs tabbox"},[a("Tabs",{on:{"on-click":t.onClickTab}},[a("TabPane",{attrs:{label:"日账单",name:"day"}}),a("TabPane",{attrs:{label:"周账单",name:"week"}}),a("TabPane",{attrs:{label:"月账单",name:"month"}})],1)],1),a("div",{staticClass:"btnbox"}),a("div",{staticClass:"table"},[a("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"income_num",fn:function(e){var n=e.row;e.index;return[a("span",{staticStyle:{color:"#F5222D"}},[t._v("￥"+t._s(n.income_num))])]}},{key:"exp_num",fn:function(e){var n=e.row;e.index;return[a("span",{staticStyle:{color:"#00C050"}},[t._v("￥"+t._s(n.exp_num))])]}},{key:"entry_num",fn:function(e){var n=e.row;e.index;return[a("span",[t._v("￥"+t._s(n.entry_num))])]}},{key:"action",fn:function(e){var n=e.row;e.index;return[a("a",{on:{click:function(e){return t.Info(n)}}},[t._v("账单详情")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.download(n)}}},[t._v("下载")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)]),a("commission-details",{ref:"commission"})],1)},i=[],r=a("a34a"),o=a.n(r),s=a("2e83"),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"账单详情","mask-closable":!1,width:"950"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Divider",{attrs:{dashed:""}}),a("Form",{ref:"formValidate",staticClass:"tabform",attrs:{"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{type:"flex",gutter:24}},[a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"选择店员：","label-for":"status1"}},[a("Select",{attrs:{placeholder:"请选择",clearable:"","element-id":"status1"},on:{"on-change":t.searchs},model:{value:t.formValidate.staff_id,callback:function(e){t.$set(t.formValidate,"staff_id",e)},expression:"formValidate.staff_id"}},t._l(t.staff,(function(e,n){return a("Option",{attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"订单搜索：","label-for":"status1"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入交易单号/交易人"},model:{value:t.formValidate.keywork,callback:function(e){t.$set(t.formValidate,"keywork",e)},expression:"formValidate.keywork"}})],1)],1),a("Col",[a("div",{staticClass:"search",on:{click:t.searchs}},[t._v("搜索")])]),a("Col",[a("div",{staticClass:"reset",on:{click:t.reset}},[t._v("重置")])])],1)],1),a("Table",{ref:"table",staticClass:"table",attrs:{columns:t.columns,data:t.tabList,loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"number",fn:function(e){var n=e.row;e.index;return[0==n.pm?a("span",{staticClass:"colorgreen"},[t._v("- "+t._s(n.number))]):t._e(),1==n.pm?a("span",{staticClass:"colorred"},[t._v("+ "+t._s(n.number))]):t._e()]}},{key:"nickname",fn:function(e){var n=e.row;e.index;return[a("span",[t._v(t._s(n.uid?n.user_nickname:"游客"))])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},c=[],u=a("8745"),d=a("2f62");function f(t,e,a,n,i,r,o){try{var s=t[r](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(n,i)}function m(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function o(t){f(r,n,i,o,s,"next",t)}function s(t){f(r,n,i,o,s,"throw",t)}o(void 0)}))}}function h(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?h(a,!0).forEach((function(e){b(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):h(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function b(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var g={name:"commissionDetails",data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},modals:!1,detailsData:{},Ids:0,loading:!1,staff:[],ids:"",formValidate:{ids:"",staff_id:"",keywork:"",data:"",page:1,limit:10},total:0,columns:[{title:"交易单号",key:"order_id",minWidth:80},{title:"关联订单",key:"link_id",minWidth:80},{title:"交易时间",key:"trade_time",minWidth:150},{title:"交易金额",slot:"number",minWidth:80},{title:"交易人",slot:"nickname",ellipsis:!0,minWidth:80},{title:"关联店员",key:"staff_name",minWidth:80},{title:"交易类型",key:"type_name",minWidth:80},{title:"支付方式",key:"pay_type_name",minWidth:80},{title:"备注",key:"mark",minWidth:120}],tabList:[]}},computed:p({},Object(d["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),mounted:function(){this.staffApi()},methods:{staffApi:function(){var t=this;Object(u["k"])().then((function(e){t.staff=e.data}))},searchs:function(){this.formValidate.page=1,this.getList(this.ids)},onchangeTime:function(t){this.formValidate.start_time=t[0],this.formValidate.end_time=t[1]},getList:function(t){var e=this;this.ids=t,this.formValidate.ids=t,this.loading=!0,Object(u["h"])(this.formValidate).then(function(){var t=m(o.a.mark((function t(a){var n;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=a.data,e.tabList=n.list,e.total=n.count,e.loading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList(this.ids)},reset:function(){this.formValidate={ids:this.ids,staff_id:"",keywork:"",data:"",page:1,limit:10},this.getList(this.ids)},cancel:function(){this.formValidate={ids:"",staff_id:"",keywork:"",data:"",page:1,limit:10}}}},v=g,w=(a("e4d3"),a("2877")),D=Object(w["a"])(v,l,c,!1,null,"08c9e881",null),_=D.exports,y=a("aad2");function k(t,e,a,n,i,r,o){try{var s=t[r](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(n,i)}function x(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function o(t){k(r,n,i,o,s,"next",t)}function s(t){k(r,n,i,o,s,"throw",t)}o(void 0)}))}}var O={name:"bill",components:{commissionDetails:_},data:function(){return{total:0,loading:!1,tab:"day",options:y["a"],columns:[{title:"ID",key:"id",width:60},{title:"标题",key:"title",minWidth:80},{title:"日期",key:"add_time",minWidth:150},{title:"收入金额",slot:"income_num",minWidth:80},{title:"支出金额",slot:"exp_num",minWidth:80},{title:"门店应入账金额",slot:"entry_num",minWidth:80},{title:"操作",slot:"action",fixed:"right",minWidth:120,align:"center"}],orderList:[{id:"1",order_id:"200",pay_price:"200",status:1,phone:"13000000000",address:"100"}],formValidate:{data:"",page:1,limit:20},timeVal:[],fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"全部",val:""},{text:"昨天",val:"yesterday"},{text:"今天",val:"today"},{text:"本周",val:"week"},{text:"本月",val:"month"},{text:"本季度",val:"quarter"},{text:"本年",val:"year"}]}}},computed:{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}},mounted:function(){this.onClickTab(this.tab)},methods:{onClickTab:function(t){this.tab=t,this.getList()},getList:function(){var t=this;this.loading=!0;var e={timeType:this.tab,data:this.formValidate.data,page:this.formValidate.page,limit:this.formValidate.limit};Object(u["g"])(e).then((function(e){t.orderList=e.data.list,t.loading=!1,t.total=e.data.count}))},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.getList()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",this.formValidate.page=1,this.getList()},pageChange:function(t){this.formValidate.page=t,this.getList()},Info:function(t){this.$refs.commission.modals=!0,this.$refs.commission.getList(t.ids)},download:function(){var t=x(o.a.mark((function t(e){var a,n,i,r,l,c;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=[],n=[],i=[],r="",l={ids:e.ids,page:1},t.next=4,this.getExcelData(l);case 4:return c=t.sent,r||(r=c.filename),n.length||(n=c.filekey),a.length||(a=c.header),i=i.concat(c.export),Object(s["a"])(a,n,r,i),t.abrupt("return");case 11:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),getExcelData:function(t){return new Promise((function(e,a){Object(u["a"])(t).then((function(t){return e(t.data)}))}))}}},V=O,C=(a("523a"),Object(w["a"])(V,n,i,!1,null,"fc1d4b24",null));e["default"]=C.exports},e4d3:function(t,e,a){"use strict";var n=a("4d03"),i=a.n(n);i.a}}]);