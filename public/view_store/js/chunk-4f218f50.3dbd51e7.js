(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4f218f50"],{"1a85":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{attrs:{bordered:!1,"dis-hover":""}},[n("Row",[n("Col",{attrs:{span:"20"}},[n("Form",{ref:"form",attrs:{model:t.formData,"label-width":110}},[n("FormItem",{attrs:{label:"桌码开关："}},[n("i-switch",{attrs:{"true-value":"1","false-value":"0",size:"large"},model:{value:t.formData.store_code_switch,callback:function(e){t.$set(t.formData,"store_code_switch",e)},expression:"formData.store_code_switch"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),n("FormItem",{attrs:{label:"结账方式："}},[n("RadioGroup",{model:{value:t.formData.store_checkout_method,callback:function(e){t.$set(t.formData,"store_checkout_method",e)},expression:"formData.store_checkout_method"}},[n("Radio",{staticClass:"mr20",attrs:{label:"1"}},[t._v("合并结账")]),n("Radio",{attrs:{label:"2"}},[t._v("单独结账")])],1),n("div",{staticStyle:{"font-size":"12px",color:"#999999"}},[t._v("多个人扫同一桌码，合并结账是指多人中的其中一个人结账即可，购物车共用一个，适用于好友聚餐；单独结账是指每个人单独进行结账，购物车都是独立的，适用于快餐店每个人单独点餐")])],1),n("FormItem",{directives:[{name:"show",rawName:"v-show",value:1==t.formData.store_checkout_method,expression:"formData.store_checkout_method == 1"}],attrs:{label:"用餐人数弹窗："}},[n("i-switch",{attrs:{"true-value":"1","false-value":"0",size:"large"},model:{value:t.formData.store_number_diners_window,callback:function(e){t.$set(t.formData,"store_number_diners_window",e)},expression:"formData.store_number_diners_window"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1),n("FormItem",{attrs:{label:"餐桌座位："}},[n("Table",{staticClass:"mb15",attrs:{columns:t.columns,data:t.tableSeatsList},scopedSlots:t._u([{key:"action",fn:function(e){var r=e.row;return[n("a",{on:{click:function(e){return t.onEdit(r)}}},[t._v("编辑")]),n("Divider",{attrs:{type:"vertical"}}),n("a",{on:{click:function(e){return t.onDelete(r.id)}}},[t._v("删除")])]}}])}),n("Button",{attrs:{type:"primary"},on:{click:t.modalToggle}},[t._v("添加")])],1)],1)],1)],1),n("div",{staticClass:"footer acea-row row-center-wrapper"},[n("Button",{staticClass:"ml20",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("提交")])],1)],1),n("Modal",{attrs:{"mask-closable":!1,title:"餐桌座位",width:"540","class-name":"add-modal-wrap","footer-hide":""},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[n("Form",{attrs:{"label-width":107}},[n("FormItem",{attrs:{label:"餐桌座位数："}},[n("InputNumber",{attrs:{min:1},model:{value:t.number,callback:function(e){t.number=e},expression:"number"}})],1)],1),n("div",{staticClass:"footer"},[n("Button",{staticClass:"mr10",on:{click:t.modalToggle}},[t._v("取消")]),n("Button",{attrs:{type:"primary"},on:{click:t.addSeats}},[t._v("确认")])],1)],1)],1)},o=[],a=n("90e7"),c={data:function(){return{columns:[{title:"餐桌座位数",key:"number"},{title:"创建时间",key:"add_time"},{title:"操作",slot:"action",align:"center"}],tableSeatsList:[],modal:!1,formData:{store_code_switch:"1",store_checkout_method:"1",store_number_diners_window:"1"},id:0,number:1}},created:function(){this.getConfig(),this.getTableSeatsList()},methods:{getConfig:function(){var t=this;Object(a["m"])("store_table_code").then((function(e){"Array"!==e.data.constructor.name&&(t.formData=e.data)}))},getTableSeatsList:function(){var t=this;Object(a["s"])().then((function(e){t.tableSeatsList=e.data}))},onEdit:function(t){this.modal=!0,this.id=t.id,this.number=t.number},onDelete:function(t){var e=this;this.$modalSure({title:"删除餐桌座位数",url:"/table/del/seats/".concat(t),method:"delete",ids:""}).then((function(t){e.$Message.success(t.msg),e.getTableSeatsList()}))},modalToggle:function(){this.id=0,this.number=1,this.modal=!this.modal},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){t&&Object(a["F"])("store_table_code",e.formData).then((function(t){e.$Message.success(t.msg)}))}))},addSeats:function(){var t=this;Object(a["a"])(this.id,{number:this.number}).then((function(e){t.$Message.success(e.msg),t.modal=!1,t.getTableSeatsList()}))}}},u=c,s=(n("34db"),n("2877")),i=Object(s["a"])(u,r,o,!1,null,"3ad74058",null);e["default"]=i.exports},"34db":function(t,e,n){"use strict";var r=n("44fc"),o=n.n(r);o.a},"44fc":function(t,e,n){},"90e7":function(t,e,n){"use strict";n.d(e,"I",(function(){return o})),n.d(e,"A",(function(){return a})),n.d(e,"B",(function(){return c})),n.d(e,"u",(function(){return u})),n.d(e,"G",(function(){return s})),n.d(e,"f",(function(){return i})),n.d(e,"d",(function(){return d})),n.d(e,"e",(function(){return l})),n.d(e,"t",(function(){return m})),n.d(e,"D",(function(){return f})),n.d(e,"E",(function(){return b})),n.d(e,"h",(function(){return h})),n.d(e,"J",(function(){return p})),n.d(e,"K",(function(){return g})),n.d(e,"C",(function(){return _})),n.d(e,"i",(function(){return v})),n.d(e,"k",(function(){return j})),n.d(e,"j",(function(){return O})),n.d(e,"l",(function(){return y})),n.d(e,"m",(function(){return w})),n.d(e,"F",(function(){return k})),n.d(e,"s",(function(){return D})),n.d(e,"a",(function(){return S})),n.d(e,"q",(function(){return C})),n.d(e,"b",(function(){return F})),n.d(e,"r",(function(){return L})),n.d(e,"c",(function(){return x})),n.d(e,"g",(function(){return $})),n.d(e,"L",(function(){return T})),n.d(e,"H",(function(){return I})),n.d(e,"n",(function(){return B})),n.d(e,"o",(function(){return M})),n.d(e,"x",(function(){return z})),n.d(e,"v",(function(){return E})),n.d(e,"w",(function(){return R})),n.d(e,"p",(function(){return q})),n.d(e,"y",(function(){return J})),n.d(e,"z",(function(){return A}));var r=n("b6bd");function o(){return Object(r["a"])({url:"system/role",method:"get"})}function a(t){return Object(r["a"])({url:"system/role/".concat(t.id),method:"post",data:t})}function c(t){return Object(r["a"])({url:"system/role/".concat(t,"/edit"),method:"get"})}function u(){return Object(r["a"])({url:"system/menusList",method:"get"})}function s(t){return Object(r["a"])({url:"system/admin",method:"get",params:t})}function i(t,e){return Object(r["a"])({url:"system/admin/set_status/".concat(t,"/").concat(e),method:"put"})}function d(){return Object(r["a"])({url:"system/admin/create",method:"get"})}function l(t){return Object(r["a"])({url:"system/admin/".concat(t,"/edit"),method:"get"})}function m(){return Object(r["a"])({url:"config",method:"get"})}function f(){return Object(r["a"])({url:"system/store/info",method:"get"})}function b(t){return Object(r["a"])({url:"system/store/update",method:"put",data:t})}function h(t){return Object(r["a"])({url:"city",method:"get",params:t})}function p(t){return Object(r["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function g(t,e){return Object(r["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function _(t){return Object(r["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function v(t){return Object(r["a"])({url:"city",method:"get",params:t})}function j(t){return Object(r["a"])({url:"/order/delivery_order/list",method:"get",params:t})}function O(t){return Object(r["a"])({url:"/order/delivery_order/cancelForm/".concat(t),method:"get"})}function y(t){return Object(r["a"])({url:"/system/store/getBusiness/".concat(t),method:"get"})}function w(t){return Object(r["a"])({url:"/system/config/".concat(t),method:"get"})}function k(t,e){return Object(r["a"])({url:"/system/config/".concat(t),method:"post",data:e})}function D(){return Object(r["a"])({url:"/table/seats/list",method:"get"})}function S(t,e){return Object(r["a"])({url:"/table/add/seats/".concat(t),method:"get",params:e})}function C(t){return Object(r["a"])({url:"/table/cate/list",method:"get",params:t})}function F(t,e){return Object(r["a"])({url:"/table/add/cate/".concat(t),method:"get",params:e})}function L(t){return Object(r["a"])({url:"/table/qrcodes/list",method:"get",params:t})}function x(t,e){return Object(r["a"])({url:"/table/add/qrcode/".concat(t),method:"post",data:e})}function $(){return Object(r["a"])({url:"/system/cashierMenusList",method:"get"})}function T(t,e){return Object(r["a"])({url:"/table/update/using/".concat(t),method:"get",params:e})}function I(t,e){return Object(r["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function B(t){return Object(r["a"])({url:"system/config/edit_new_build/"+t,method:"get"})}function M(t){return Object(r["a"])({url:"/system/printer/list",method:"get",params:t})}function z(t){return Object(r["a"])({url:"/system/printer/status/".concat(t.id,"/").concat(t.status),method:"post"})}function E(t,e){return Object(r["a"])({url:"/system/printer/".concat(t),method:"post",data:e})}function R(t){return Object(r["a"])({url:"/system/printer/info/".concat(t),method:"get"})}function q(t){return Object(r["a"])({url:"resolve/city",method:"get",params:t})}function J(t,e){return Object(r["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function A(t,e){return Object(r["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}}}]);