(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-175ff32b"],{"29ec":function(t,e,a){"use strict";var n=a("ff3a"),r=a.n(n);r.a},4382:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",[a("Col",{staticClass:"mr"},[a("FormItem",{attrs:{label:"创建时间："}},[a("DatePicker",{staticStyle:{width:"200px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1),a("Col",{staticClass:"mr"},[a("FormItem",{attrs:{label:"选择店员：","label-for":"status1"}},[a("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择",clearable:"","element-id":"status1"},on:{"on-change":t.search},model:{value:t.formValidate.staff_id,callback:function(e){t.$set(t.formValidate,"staff_id",e)},expression:"formValidate.staff_id"}},t._l(t.staff,(function(e,n){return a("Option",{attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),a("Col",{staticClass:"mr"},[a("FormItem",{attrs:{label:"订单搜索：","label-for":"status1"}},[a("Input",{staticClass:"input",staticStyle:{width:"200px"},attrs:{placeholder:"请输入交易单号/交易人"},model:{value:t.formValidate.keywork,callback:function(e){t.$set(t.formValidate,"keywork",e)},expression:"formValidate.keywork"}})],1)],1),a("Col",{staticClass:"mr"},[a("FormItem",{attrs:{label:"支付方式：","label-for":"pay_type"}},[a("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择",clearable:"","element-id":"pay_type"},on:{"on-change":t.search},model:{value:t.formValidate.pay_type,callback:function(e){t.$set(t.formValidate,"pay_type",e)},expression:"formValidate.pay_type"}},[a("Option",{attrs:{value:"weixin"}},[t._v("微信支付")]),a("Option",{attrs:{value:"yue"}},[t._v("余额支付")]),a("Option",{attrs:{value:"offline"}},[t._v("线下支付")]),a("Option",{attrs:{value:"alipay"}},[t._v("支付宝支付")]),a("Option",{attrs:{value:"cash"}},[t._v("现金支付")]),a("Option",{attrs:{value:"integral"}},[t._v("积分支付")])],1)],1)],1),a("Col",{staticClass:"mr"},[a("div",{staticClass:"search",on:{click:t.search}},[t._v("搜索")])]),a("Col",[a("div",{staticClass:"reset",on:{click:t.reset}},[t._v("重置")])])],1)],1)],1),a("Card",{staticClass:"ive-mt tablebox",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"btnbox"}),a("div",{staticClass:"table"},[a("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"number",fn:function(e){var n=e.row;e.index;return[0==n.pm?a("span",{staticClass:"colorgreen"},[t._v("- "+t._s(n.number))]):t._e(),1==n.pm?a("span",{staticClass:"colorred"},[t._v("+ "+t._s(n.number))]):t._e()]}},{key:"user_nickname",fn:function(e){var n=e.row;e.index;return[a("span",[t._v(t._s(n.uid?n.user_nickname:"游客"))])]}},{key:"action",fn:function(e){var n=e.row;e.index;return[a("a",{on:{click:function(e){return t.remark(n)}}},[t._v("备注")])]}},{key:"mark",fn:function(e){var n=e.row;e.index;return[a("Tooltip",{attrs:{"max-width":"300",placement:"bottom"}},[a("span",{staticClass:"line2"},[t._v(t._s(n.mark))]),a("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(n.mark))])])]}}])})],1),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)]),a("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"请修改内容",closable:!1,"mask-closable":!1},model:{value:t.modalmark,callback:function(e){t.modalmark=e},expression:"modalmark"}},[a("Form",{ref:"remarks",attrs:{model:t.remarks,"label-width":80},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"备注："}},[a("Input",{staticStyle:{width:"100%"},attrs:{maxlength:"200","show-word-limit":"",type:"textarea",placeholder:"请填写备注~"},model:{value:t.remarks.mark,callback:function(e){t.$set(t.remarks,"mark",e)},expression:"remarks.mark"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary"},on:{click:function(e){return t.putRemark()}}},[t._v("提交")]),a("Button",{on:{click:function(e){return t.cancel()}}},[t._v("取消")])],1)],1)],1)},r=[],i=a("2f62"),o=a("8745"),s=a("aad2");function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(a,!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var m={name:"order",data:function(){return{modalmark:!1,remarks:{mark:""},staff:[],total:0,grid:{xl:7,lg:7,md:12,sm:24,xs:24},options:s["a"],loading:!1,columns:[{title:"交易单号",key:"order_id",minWidth:180},{title:"关联订单",key:"link_id",minWidth:180},{title:"交易时间",key:"trade_time",minWidth:150},{title:"交易金额",slot:"number",minWidth:80},{title:"交易人",slot:"user_nickname",ellipsis:!0,minWidth:80},{title:"关联店员",key:"staff_name",minWidth:80},{title:"交易类型",key:"type_name",minWidth:80},{title:"支付方式",key:"pay_type_name",minWidth:80},{title:"备注",slot:"mark",minWidth:120},{title:"操作",slot:"action",fixed:"right",minWidth:80,align:"center"}],orderList:[],formValidate:{staff_id:"",keywork:"",data:"",pay_type:"",page:1,limit:20},timeVal:[],fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"全部",val:""},{text:"昨天",val:"yesterday"},{text:"今天",val:"today"},{text:"本周",val:"week"},{text:"本月",val:"month"},{text:"本季度",val:"quarter"},{text:"本年",val:"year"}]}}},computed:c({},Object(i["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),mounted:function(){this.getList(),this.staffApi()},methods:{getList:function(){var t=this;this.loading=!0,Object(o["i"])(this.formValidate).then((function(e){t.orderList=e.data.list,t.total=e.data.count,t.loading=!1}))},staffApi:function(){var t=this;Object(o["k"])().then((function(e){t.staff=e.data}))},search:function(){this.formValidate.page=1,this.getList()},reset:function(){this.formValidate={staff_id:"",keywork:"",data:"",pay_type:"",page:1,limit:15},this.timeVal=[],this.getList()},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.getList()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",this.formValidate.page=1,this.getList()},pageChange:function(t){this.formValidate.page=t,this.getList()},remark:function(t){this.remarkId=t.id,this.remarks.mark=t.mark,this.modalmark=!0},putRemark:function(){var t=this;this.modalmark=!1,Object(o["j"])(this.remarkId,this.remarks).then((function(e){t.$Message.success(e.msg),t.remarks={mark:""},t.modalmark=!1,t.getList()})).catch((function(e){t.$Message.error(e.msg),t.modalmark=!1}))},cancel:function(){this.remarks={mark:""},this.modalmark=!1}}},f=m,d=(a("29ec"),a("2877")),h=Object(d["a"])(f,n,r,!1,null,"3875ac2f",null);e["default"]=h.exports},8745:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"f",(function(){return l})),a.d(e,"i",(function(){return c})),a.d(e,"j",(function(){return u})),a.d(e,"k",(function(){return m})),a.d(e,"g",(function(){return f})),a.d(e,"h",(function(){return d})),a.d(e,"a",(function(){return h}));var n=a("b6bd");function r(){return Object(n["a"])({url:"finance/info",method:"get"})}function i(t){return Object(n["a"])({url:"finance/info",method:"post",data:t})}function o(t){return Object(n["a"])({url:"finance/storeExtract/list",method:"get",params:t})}function s(t){return Object(n["a"])({url:"finance/storeExtract/cash",method:"post",data:t})}function l(t,e){return Object(n["a"])({url:"finance/storeExtract/mark/".concat(t),method:"post",data:e})}function c(t){return Object(n["a"])({url:"finance/store_finance_flow/list",method:"get",params:t})}function u(t,e){return Object(n["a"])({url:"finance/store_finance_flow/mark/".concat(t),method:"post",data:e})}function m(){return Object(n["a"])({url:"finance/store_finance_flow/staff",method:"get"})}function f(t){return Object(n["a"])({url:"finance/store_finance_flow/fund_record",method:"get",params:t})}function d(t){return Object(n["a"])({url:"finance/store_finance_flow/fund_record_info",method:"get",params:t})}function h(t){return Object(n["a"])({url:"/export/financeRecord",method:"get",params:t})}},aad2:function(t,e,a){"use strict";var n=a("c1df"),r=a.n(n);e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本季度",value:function(){var t=r()(r()().quarter(r()().quarter()).startOf("quarter").valueOf()).format("YYYY-MM-DD"),e=r()(r()().quarter(r()().quarter()).endOf("quarter").valueOf()).format("YYYY-MM-DD");return[t,e]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},ff3a:function(t,e,a){}}]);