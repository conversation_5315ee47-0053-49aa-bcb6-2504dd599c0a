(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-070674a9"],{"0b65":function(t,e,n){"use strict";e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,n=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,n))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},"273a":function(t,e,n){"use strict";var r=n("54ed"),a=n.n(r);a.a},"54ed":function(t,e,n){},"90e7":function(t,e,n){"use strict";n.d(e,"I",(function(){return a})),n.d(e,"A",(function(){return o})),n.d(e,"B",(function(){return i})),n.d(e,"u",(function(){return u})),n.d(e,"G",(function(){return s})),n.d(e,"f",(function(){return c})),n.d(e,"d",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"t",(function(){return m})),n.d(e,"D",(function(){return f})),n.d(e,"E",(function(){return h})),n.d(e,"h",(function(){return g})),n.d(e,"J",(function(){return p})),n.d(e,"K",(function(){return b})),n.d(e,"C",(function(){return v})),n.d(e,"i",(function(){return w})),n.d(e,"k",(function(){return D})),n.d(e,"j",(function(){return O})),n.d(e,"l",(function(){return y})),n.d(e,"m",(function(){return j})),n.d(e,"F",(function(){return _})),n.d(e,"s",(function(){return k})),n.d(e,"a",(function(){return T})),n.d(e,"q",(function(){return F})),n.d(e,"b",(function(){return C})),n.d(e,"r",(function(){return M})),n.d(e,"c",(function(){return x})),n.d(e,"g",(function(){return P})),n.d(e,"L",(function(){return W})),n.d(e,"H",(function(){return z})),n.d(e,"n",(function(){return I})),n.d(e,"o",(function(){return L})),n.d(e,"x",(function(){return Y})),n.d(e,"v",(function(){return S})),n.d(e,"w",(function(){return V})),n.d(e,"p",(function(){return $})),n.d(e,"y",(function(){return B})),n.d(e,"z",(function(){return E}));var r=n("b6bd");function a(){return Object(r["a"])({url:"system/role",method:"get"})}function o(t){return Object(r["a"])({url:"system/role/".concat(t.id),method:"post",data:t})}function i(t){return Object(r["a"])({url:"system/role/".concat(t,"/edit"),method:"get"})}function u(){return Object(r["a"])({url:"system/menusList",method:"get"})}function s(t){return Object(r["a"])({url:"system/admin",method:"get",params:t})}function c(t,e){return Object(r["a"])({url:"system/admin/set_status/".concat(t,"/").concat(e),method:"put"})}function l(){return Object(r["a"])({url:"system/admin/create",method:"get"})}function d(t){return Object(r["a"])({url:"system/admin/".concat(t,"/edit"),method:"get"})}function m(){return Object(r["a"])({url:"config",method:"get"})}function f(){return Object(r["a"])({url:"system/store/info",method:"get"})}function h(t){return Object(r["a"])({url:"system/store/update",method:"put",data:t})}function g(t){return Object(r["a"])({url:"city",method:"get",params:t})}function p(t){return Object(r["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function b(t,e){return Object(r["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function v(t){return Object(r["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function w(t){return Object(r["a"])({url:"city",method:"get",params:t})}function D(t){return Object(r["a"])({url:"/order/delivery_order/list",method:"get",params:t})}function O(t){return Object(r["a"])({url:"/order/delivery_order/cancelForm/".concat(t),method:"get"})}function y(t){return Object(r["a"])({url:"/system/store/getBusiness/".concat(t),method:"get"})}function j(t){return Object(r["a"])({url:"/system/config/".concat(t),method:"get"})}function _(t,e){return Object(r["a"])({url:"/system/config/".concat(t),method:"post",data:e})}function k(){return Object(r["a"])({url:"/table/seats/list",method:"get"})}function T(t,e){return Object(r["a"])({url:"/table/add/seats/".concat(t),method:"get",params:e})}function F(t){return Object(r["a"])({url:"/table/cate/list",method:"get",params:t})}function C(t,e){return Object(r["a"])({url:"/table/add/cate/".concat(t),method:"get",params:e})}function M(t){return Object(r["a"])({url:"/table/qrcodes/list",method:"get",params:t})}function x(t,e){return Object(r["a"])({url:"/table/add/qrcode/".concat(t),method:"post",data:e})}function P(){return Object(r["a"])({url:"/system/cashierMenusList",method:"get"})}function W(t,e){return Object(r["a"])({url:"/table/update/using/".concat(t),method:"get",params:e})}function z(t,e){return Object(r["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function I(t){return Object(r["a"])({url:"system/config/edit_new_build/"+t,method:"get"})}function L(t){return Object(r["a"])({url:"/system/printer/list",method:"get",params:t})}function Y(t){return Object(r["a"])({url:"/system/printer/status/".concat(t.id,"/").concat(t.status),method:"post"})}function S(t,e){return Object(r["a"])({url:"/system/printer/".concat(t),method:"post",data:e})}function V(t){return Object(r["a"])({url:"/system/printer/info/".concat(t),method:"get"})}function $(t){return Object(r["a"])({url:"resolve/city",method:"get",params:t})}function B(t,e){return Object(r["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function E(t,e){return Object(r["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},c4c5:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{staticClass:"mt15 ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[n("div",{staticClass:"new_card_pd"},[n("Form",{ref:"orderData",staticClass:"tabform",attrs:{model:t.formData,"label-width":t.labelWidth,"label-position":t.labelPosition,inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[n("FormItem",{attrs:{label:"时间选择："}},[n("DatePicker",{staticClass:"input-add",attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1),n("FormItem",{attrs:{label:"配送状态："}},[n("Select",{staticClass:"input-add",attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.orderSearch},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}},[n("Option",{attrs:{value:""}},[t._v("全部")]),n("Option",{attrs:{value:"2"}},[t._v("待取货")]),n("Option",{attrs:{value:"3"}},[t._v("配送中")]),n("Option",{attrs:{value:"4"}},[t._v("已完成")]),n("Option",{attrs:{value:"-1"}},[t._v("已取消")]),n("Option",{attrs:{value:"9"}},[t._v("物品返回中")]),n("Option",{attrs:{value:"10"}},[t._v("物品返回完成")]),n("Option",{attrs:{value:"100"}},[t._v("骑士到店")])],1)],1),n("FormItem",{attrs:{label:"订单搜索：","label-for":"nickname"}},[n("Input",{staticClass:"input-add",attrs:{placeholder:"请输入配送订单号/原订单号"},model:{value:t.formData.keyword,callback:function(e){t.$set(t.formData,"keyword",e)},expression:"formData.keyword"}})],1),n("FormItem",[n("Button",{staticClass:"ml75",attrs:{type:"primary"},on:{click:t.orderSearch}},[t._v("查询")]),n("Button",{staticClass:"ml20",on:{click:t.orderReset}},[t._v("重置")])],1)],1)],1)]),n("Card",{staticClass:"mt15 ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Table",{ref:"table",attrs:{columns:t.columns,data:t.data,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"yOrderId",fn:function(e){var r=e.row;return[n("div",[t._v(t._s(r.orderInfo.order_id))])]}},{key:"status",fn:function(e){var r=e.row;return[2==r.status?n("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("待取货")]):t._e(),3==r.status?n("Tag",{attrs:{color:"blue",size:"medium"}},[t._v("配送中")]):t._e(),4==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("已完成")]):t._e(),-1==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("已取消")]):t._e(),9==r.status?n("Tag",{attrs:{color:"red",size:"medium"}},[t._v("物品返回中")]):t._e(),10==r.status?n("Tag",{attrs:{color:"default",size:"medium"}},[t._v("物品返回完成")]):t._e(),100==r.status?n("Tag",{attrs:{color:"blue",size:"medium"}},[t._v("骑士到店")]):t._e()]}},{key:"distance",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(n.distance)+"km\n            ")]}},{key:"mark",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(n.mark||"-")+"\n            ")]}},{key:"action",fn:function(e){var r=e.row;return[n("a",{on:{click:function(e){return t.cancelOrder(r)}}},[t._v("取消发单")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,current:t.formData.page,"show-elevator":"","show-total":"","page-size":t.formData.limit,"show-sizer":""},on:{"on-change":t.pageChange,"on-page-size-change":t.limitChange}})],1)],1)],1)},a=[],o=n("2f62"),i=n("0b65"),u=n("90e7");function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(n,!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var d={name:"record",components:{},data:function(){return{options:i["a"],timeVal:[],formData:{data:"",status:"",keyword:"",page:1,limit:20},loading:!1,columns:[{title:"ID",key:"id",width:60},{title:"配送订单号",key:"delivery_no",minWidth:110},{title:"原订单号",slot:"yOrderId",minWidth:110},{title:"配送起点",key:"from_address",minWidth:130},{title:"配送终点",key:"to_address",minWidth:130},{title:"配送状态",slot:"status",minWidth:80},{title:"配送距离",slot:"distance",minWidth:80},{title:"配送费用",key:"fee",minWidth:80},{title:"消费时间",key:"add_time",minWidth:110},{title:"备注",slot:"mark",minWidth:90},{title:"操作",slot:"action",fixed:"right",minWidth:85}],data:[],total:0}},computed:c({},Object(o["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getList()},methods:{cancelOrder:function(t){var e=this;this.$modalForm(Object(u["j"])(t.id)).then((function(){return e.getList()}))},orderReset:function(){this.formData={data:"",status:"",keyword:"",page:1,limit:20},this.timeVal=[],this.getList()},orderSearch:function(){this.formData.page=1,this.getList()},pageChange:function(t){this.formData.page=t,this.getList()},limitChange:function(t){this.formData.limit=t,this.getList()},getList:function(){var t=this;this.loading=!0,Object(u["k"])(this.formData).then((function(e){t.loading=!1,t.data=e.data.list,t.total=e.data.count})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},onchangeTime:function(t){this.timeVal=t,this.formData.data=this.timeVal.join("-"),t[0]||(this.formData.data=""),this.orderSearch()}}},m=d,f=(n("273a"),n("2877")),h=Object(f["a"])(m,r,a,!1,null,"71cbbfd7",null);e["default"]=h.exports}}]);