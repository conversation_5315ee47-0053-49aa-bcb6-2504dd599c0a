(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e104bbca"],{"0e5c":function(t,e,i){"use strict";i.d(e,"a",(function(){return l}));var r=i("a34a"),n=i.n(r),a=i("8237"),s=i.n(a),o=i("91b6");function c(t,e,i,r,n,a,s){try{var o=t[a](s),c=o.value}catch(u){return void i(u)}o.done?e(c):Promise.resolve(c).then(r,n)}function u(t){return function(){var e=this,i=arguments;return new Promise((function(r,n){var a=t.apply(e,i);function s(t){c(a,r,n,s,o,"next",t)}function o(t){c(a,r,n,s,o,"throw",t)}s(void 0)}))}}var l=function(t){t.randoms;var e=t.file,i=t.pieceSize,r=void 0===i?2:i,a=t.pid,c=(t.progress,t.success),l=t.error,d=t.uploading;if(e){var h="",p=1024*r*1024,f=Math.ceil(e.size/p),m=function(){var t=new FileReader;t.readAsBinaryString(e),t.addEventListener("load",(function(t){var e=t.target.result;h=s()(e),_()}))},g=function(t,e,i){var r=e*i,n=Math.min(t.size,r+i),a=t.slice(r,n);return{start:r,end:n,chunk:a}},_=function(){var t=u(n.a.mark((function t(){var i,r,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=0;case 1:if(!(i<f)){t.next=10;break}return r=g(e,i,p),a=r.chunk,console.log("总片数"+f),console.log("分片后的数据---测试："+i),t.next=7,v({chunk:a,currentChunk:i,chunkCount:f});case 7:i++,t.next=1;break;case 10:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),v=function(t){return new Promise((function(i,r){var n={headers:{"Content-Type":"multipart/form-data"}},s=new FormData;s.append("chunkNumber",t.currentChunk+1),s.append("chunkSize",p),s.append("currentChunkSize",t.chunk.size),s.append("file",t.chunk),s.append("filename",e.name),s.append("totalChunks",t.chunkCount),s.append("md5",h),s.append("pid",a),Object(o["f"])(s,n).then((function(e){console.log("分片上传返回信息：",e),1==e.data.code?(d(t.currentChunk+1,t.chunkCount),i(!0)):2==e.data.code&&(t.currentChunk<t.chunkCount-1?console.log("分片上传成功"):t.currentChunk+1==t.chunkCount&&(console.log("文件开始------合并成功"),c(e.data)))})).catch((function(t){l&&l(t)}))}))};m()}}},"23ef":function(t,e,i){"use strict";var r=i("e362"),n=i.n(r);n.a},"3c35":function(t,e){(function(e){t.exports=e}).call(this,{})},7774:function(t,e,i){"use strict";var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"mt20 ml20"},[i("Form",[i("FormItem",{attrs:{label:"视频类型："}},[i("RadioGroup",{on:{"on-change":t.changeVideo},model:{value:t.seletVideo,callback:function(e){t.seletVideo=e},expression:"seletVideo"}},[i("Radio",{staticClass:"radio",attrs:{label:0}},[t._v("本地视频")]),i("Radio",{attrs:{label:1}},[t._v("视频链接")])],1)],1),1==t.seletVideo?i("FormItem",{attrs:{label:"视频链接"}},[i("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入视频链接"},model:{value:t.videoLink,callback:function(e){t.videoLink=e},expression:"videoLink"}}),i("Button",{attrs:{type:"primary"},on:{click:t.zh_uploadFile}},[t._v("确认添加")])],1):t._e(),0==t.seletVideo?i("FormItem",{attrs:{label:"本地上传："}},[i("input",{ref:"refid",staticClass:"display-add",attrs:{type:"file"},on:{change:t.zh_uploadFile_change}}),"1"!==t.upload_type||t.videoLink?i("Button",{staticClass:"ml10",attrs:{type:"primary",icon:"ios-cloud-upload-outline"},on:{click:t.zh_uploadFile}},[t._v("上传视频")]):t._e(),"1"!==t.upload_type||t.videoLink?t._e():i("Upload",{staticClass:"ml10",staticStyle:{display:"inline-block"},attrs:{"show-upload-list":!1,action:t.fileUrl2,"before-upload":t.beforeUpload,data:t.uploadData,headers:t.header,multiple:!0,"on-success":t.handleSuccess}},[i("Button",{attrs:{type:"primary",icon:"ios-cloud-upload-outline"}},[t._v("上传视频")])],1),t.upload.videoIng?i("Progress",{attrs:{percent:t.progress,"stroke-width":5}}):t._e()],1):t._e()],1),t.formValidate.video_link?i("div",{staticClass:"iview-video-style"},[i("video",{staticClass:"video-style",attrs:{src:t.formValidate.video_link,controls:"controls"}},[t._v("\n                您的浏览器不支持 video 标签。\n            ")]),i("div",{staticClass:"mark"}),i("Icon",{staticClass:"iconv",attrs:{type:"ios-trash-outline"},on:{click:t.delVideo}})],1):t._e()],1),i("div",{staticClass:"mt50 ml20"},[i("Button",{attrs:{type:"primary"},on:{click:t.uploads}},[t._v("确认")])],1)])},n=[],a=i("c4c8"),s=i("0e5c"),o=i("d708"),c=i("c276"),u={name:"vide11o",props:{pid:{type:Number,default:0}},data:function(){return{fileUrl:o["a"].apiBaseURL+"/file/upload",fileUrl2:o["a"].apiBaseURL+"/file/video_upload",upload:{videoIng:!1},progress:0,videoLink:"",formValidate:{video_link:""},upload_type:"",uploadData:{},header:{},seletVideo:0}},watch:{seletVideo:{handler:function(t,e){this.videoLink="",this.formValidate.video_link=""}}},created:function(){this.uploadType(),this.getToken()},methods:{delVideo:function(){var t=this;t.$set(t.formValidate,"video_link",""),t.$refs.refid.value=""},handleSuccess:function(t,e,i){200===t.status?(this.formValidate.video_link=t.data.src,this.$Message.success(t.msg)):this.$Message.error(t.msg)},getToken:function(){this.header["Authori-zation"]="Bearer "+c["a"].cookies.get("token")},uploadType:function(){var t=this;Object(a["I"])().then((function(e){t.upload_type=e.data.upload_type}))},beforeUpload:function(t){var e=this,i=["video/mp4"],r=-1!==i.indexOf(t.type);return r?(Object(s["a"])({randoms:"",file:t,pieceSize:3,pid:this.pid,success:function(t){e.formValidate.video_link=t.file_path,e.progress=100},error:function(t){e.$Message.error(t.msg)},uploading:function(t,i){e.videoIng=!0;var r=Math.floor(t/i*100);e.progress=r}}),!1):this.$Message.warning({content:"文件  "+t.name+"  格式不正确, 请选择格式正确的视频",duration:5})},zh_uploadFile:function(){if(this.videoLink&&"video"==this.$getFileType(this.videoLink))this.formValidate.video_link=this.videoLink;else{if(this.videoLink&&"video"!==this.$getFileType(this.videoLink)&&1==this.seletVideo)return this.$Message.error("请输入正确的视频链接");this.$refs.refid.click()}},zh_uploadFile_change:function(t){var e=this;if("video/mp4"!==t.target.files[0].type)return e.$Message.error("只能上传mp4文件");var i={key:t.target.files[0].name,contentType:t.target.files[0].type};Object(a["p"])(i).then((function(i){e.$videoCloud.videoUpload({type:i.data.type,evfile:t,res:i,uploading:function(t,r){e.upload.videoIng=t,200==i.status&&(e.progress=100)}}).then((function(t){e.formValidate.video_link=t.url,e.$Message.success("视频上传成功"),e.upload.videoIng=!1})).catch((function(t){e.$Message.error(t)}))})).catch((function(t){e.$Message.error(t.msg)}))},uploads:function(){var t=this;0==this.seletVideo&&this.formValidate.video_link||0==this.seletVideo&&""==this.formValidate.video_link&&(this.$Message.error("请上传视频"),nowEditor.dialog.close(!0)),1==this.seletVideo&&""==this.videoLink&&(this.$Message.error("请上传视频"),nowEditor.dialog.close(!0)),this.videoLink&&"video"!==this.$getFileType(this.videoLink)&&1==this.seletVideo?this.$Message.error("请输入正确的视频链接"):1==this.seletVideo||0==this.seletVideo&&1!=this.upload_type?Object(a["J"])({path:this.formValidate.video_link,pid:this.pid,upload_type:1==this.seletVideo?1:this.upload_type}).then((function(e){t.$emit("getVideo",t.formValidate.video_link),t.formValidate.video_link="",t.$refs.refid.value=""})):(this.$emit("getVideo",this.formValidate.video_link),this.formValidate.video_link="",this.$refs.refid.value="")},changeVideo:function(t){this.videoLink=""}}},l=u,d=(i("23ef"),i("2877")),h=Object(d["a"])(l,r,n,!1,null,"459fee66",null);e["a"]=h.exports},8237:function(module,exports,__webpack_require__){(function(process,global){var __WEBPACK_AMD_DEFINE_RESULT__;
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.7.3
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */(function(){"use strict";var ERROR="input is invalid type",WINDOW="object"===typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===typeof process&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===typeof module&&module.exports,AMD=__webpack_require__("3c35"),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!==typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-**********],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"===typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var createOutputMethod=function(t){return function(e){return new Md5(!0).update(e)[t]()}},createMethod=function(){var t=createOutputMethod("hex");NODE_JS&&(t=nodeWrap(t)),t.create=function(){return new Md5},t.update=function(e){return t.create().update(e)};for(var e=0;e<OUTPUT_TYPES.length;++e){var i=OUTPUT_TYPES[e];t[i]=createOutputMethod(i)}return t},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(t){if("string"===typeof t)return crypto.createHash("md5").update(t,"utf8").digest("hex");if(null===t||void 0===t)throw ERROR;return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===Buffer?crypto.createHash("md5").update(new Buffer(t)).digest("hex"):method(t)};return nodeMethod};function Md5(t){if(t)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var e=new ArrayBuffer(68);this.buffer8=new Uint8Array(e),this.blocks=new Uint32Array(e)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(t){if(!this.finalized){var e,i=typeof t;if("string"!==i){if("object"!==i)throw ERROR;if(null===t)throw ERROR;if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(t)))throw ERROR;e=!0}var r,n,a=0,s=t.length,o=this.blocks,c=this.buffer8;while(a<s){if(this.hashed&&(this.hashed=!1,o[0]=o[16],o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),e)if(ARRAY_BUFFER)for(n=this.start;a<s&&n<64;++a)c[n++]=t[a];else for(n=this.start;a<s&&n<64;++a)o[n>>2]|=t[a]<<SHIFT[3&n++];else if(ARRAY_BUFFER)for(n=this.start;a<s&&n<64;++a)r=t.charCodeAt(a),r<128?c[n++]=r:r<2048?(c[n++]=192|r>>6,c[n++]=128|63&r):r<55296||r>=57344?(c[n++]=224|r>>12,c[n++]=128|r>>6&63,c[n++]=128|63&r):(r=65536+((1023&r)<<10|1023&t.charCodeAt(++a)),c[n++]=240|r>>18,c[n++]=128|r>>12&63,c[n++]=128|r>>6&63,c[n++]=128|63&r);else for(n=this.start;a<s&&n<64;++a)r=t.charCodeAt(a),r<128?o[n>>2]|=r<<SHIFT[3&n++]:r<2048?(o[n>>2]|=(192|r>>6)<<SHIFT[3&n++],o[n>>2]|=(128|63&r)<<SHIFT[3&n++]):r<55296||r>=57344?(o[n>>2]|=(224|r>>12)<<SHIFT[3&n++],o[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],o[n>>2]|=(128|63&r)<<SHIFT[3&n++]):(r=65536+((1023&r)<<10|1023&t.charCodeAt(++a)),o[n>>2]|=(240|r>>18)<<SHIFT[3&n++],o[n>>2]|=(128|r>>12&63)<<SHIFT[3&n++],o[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],o[n>>2]|=(128|63&r)<<SHIFT[3&n++]);this.lastByteIndex=n,this.bytes+=n-this.start,n>=64?(this.start=n-64,this.hash(),this.hashed=!0):this.start=n}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[e>>2]|=EXTRA[3&e],e>=56&&(this.hashed||this.hash(),t[0]=t[16],t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.bytes<<3,t[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var t,e,i,r,n,a,s=this.blocks;this.first?(t=s[0]-680876937,t=(t<<7|t>>>25)-271733879<<0,r=(-1732584194^2004318071&t)+s[1]-117830708,r=(r<<12|r>>>20)+t<<0,i=(-271733879^r&(-271733879^t))+s[2]-1126478375,i=(i<<17|i>>>15)+r<<0,e=(t^i&(r^t))+s[3]-1316259209,e=(e<<22|e>>>10)+i<<0):(t=this.h0,e=this.h1,i=this.h2,r=this.h3,t+=(r^e&(i^r))+s[0]-680876936,t=(t<<7|t>>>25)+e<<0,r+=(i^t&(e^i))+s[1]-389564586,r=(r<<12|r>>>20)+t<<0,i+=(e^r&(t^e))+s[2]+606105819,i=(i<<17|i>>>15)+r<<0,e+=(t^i&(r^t))+s[3]-1044525330,e=(e<<22|e>>>10)+i<<0),t+=(r^e&(i^r))+s[4]-176418897,t=(t<<7|t>>>25)+e<<0,r+=(i^t&(e^i))+s[5]+1200080426,r=(r<<12|r>>>20)+t<<0,i+=(e^r&(t^e))+s[6]-1473231341,i=(i<<17|i>>>15)+r<<0,e+=(t^i&(r^t))+s[7]-45705983,e=(e<<22|e>>>10)+i<<0,t+=(r^e&(i^r))+s[8]+1770035416,t=(t<<7|t>>>25)+e<<0,r+=(i^t&(e^i))+s[9]-1958414417,r=(r<<12|r>>>20)+t<<0,i+=(e^r&(t^e))+s[10]-42063,i=(i<<17|i>>>15)+r<<0,e+=(t^i&(r^t))+s[11]-1990404162,e=(e<<22|e>>>10)+i<<0,t+=(r^e&(i^r))+s[12]+1804603682,t=(t<<7|t>>>25)+e<<0,r+=(i^t&(e^i))+s[13]-40341101,r=(r<<12|r>>>20)+t<<0,i+=(e^r&(t^e))+s[14]-1502002290,i=(i<<17|i>>>15)+r<<0,e+=(t^i&(r^t))+s[15]+1236535329,e=(e<<22|e>>>10)+i<<0,t+=(i^r&(e^i))+s[1]-165796510,t=(t<<5|t>>>27)+e<<0,r+=(e^i&(t^e))+s[6]-1069501632,r=(r<<9|r>>>23)+t<<0,i+=(t^e&(r^t))+s[11]+643717713,i=(i<<14|i>>>18)+r<<0,e+=(r^t&(i^r))+s[0]-373897302,e=(e<<20|e>>>12)+i<<0,t+=(i^r&(e^i))+s[5]-701558691,t=(t<<5|t>>>27)+e<<0,r+=(e^i&(t^e))+s[10]+38016083,r=(r<<9|r>>>23)+t<<0,i+=(t^e&(r^t))+s[15]-660478335,i=(i<<14|i>>>18)+r<<0,e+=(r^t&(i^r))+s[4]-405537848,e=(e<<20|e>>>12)+i<<0,t+=(i^r&(e^i))+s[9]+568446438,t=(t<<5|t>>>27)+e<<0,r+=(e^i&(t^e))+s[14]-1019803690,r=(r<<9|r>>>23)+t<<0,i+=(t^e&(r^t))+s[3]-187363961,i=(i<<14|i>>>18)+r<<0,e+=(r^t&(i^r))+s[8]+1163531501,e=(e<<20|e>>>12)+i<<0,t+=(i^r&(e^i))+s[13]-1444681467,t=(t<<5|t>>>27)+e<<0,r+=(e^i&(t^e))+s[2]-51403784,r=(r<<9|r>>>23)+t<<0,i+=(t^e&(r^t))+s[7]+1735328473,i=(i<<14|i>>>18)+r<<0,e+=(r^t&(i^r))+s[12]-1926607734,e=(e<<20|e>>>12)+i<<0,n=e^i,t+=(n^r)+s[5]-378558,t=(t<<4|t>>>28)+e<<0,r+=(n^t)+s[8]-2022574463,r=(r<<11|r>>>21)+t<<0,a=r^t,i+=(a^e)+s[11]+1839030562,i=(i<<16|i>>>16)+r<<0,e+=(a^i)+s[14]-35309556,e=(e<<23|e>>>9)+i<<0,n=e^i,t+=(n^r)+s[1]-1530992060,t=(t<<4|t>>>28)+e<<0,r+=(n^t)+s[4]+1272893353,r=(r<<11|r>>>21)+t<<0,a=r^t,i+=(a^e)+s[7]-155497632,i=(i<<16|i>>>16)+r<<0,e+=(a^i)+s[10]-1094730640,e=(e<<23|e>>>9)+i<<0,n=e^i,t+=(n^r)+s[13]+681279174,t=(t<<4|t>>>28)+e<<0,r+=(n^t)+s[0]-358537222,r=(r<<11|r>>>21)+t<<0,a=r^t,i+=(a^e)+s[3]-722521979,i=(i<<16|i>>>16)+r<<0,e+=(a^i)+s[6]+76029189,e=(e<<23|e>>>9)+i<<0,n=e^i,t+=(n^r)+s[9]-640364487,t=(t<<4|t>>>28)+e<<0,r+=(n^t)+s[12]-421815835,r=(r<<11|r>>>21)+t<<0,a=r^t,i+=(a^e)+s[15]+530742520,i=(i<<16|i>>>16)+r<<0,e+=(a^i)+s[2]-995338651,e=(e<<23|e>>>9)+i<<0,t+=(i^(e|~r))+s[0]-198630844,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~i))+s[7]+1126891415,r=(r<<10|r>>>22)+t<<0,i+=(t^(r|~e))+s[14]-1416354905,i=(i<<15|i>>>17)+r<<0,e+=(r^(i|~t))+s[5]-57434055,e=(e<<21|e>>>11)+i<<0,t+=(i^(e|~r))+s[12]+1700485571,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~i))+s[3]-1894986606,r=(r<<10|r>>>22)+t<<0,i+=(t^(r|~e))+s[10]-1051523,i=(i<<15|i>>>17)+r<<0,e+=(r^(i|~t))+s[1]-2054922799,e=(e<<21|e>>>11)+i<<0,t+=(i^(e|~r))+s[8]+1873313359,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~i))+s[15]-30611744,r=(r<<10|r>>>22)+t<<0,i+=(t^(r|~e))+s[6]-1560198380,i=(i<<15|i>>>17)+r<<0,e+=(r^(i|~t))+s[13]+1309151649,e=(e<<21|e>>>11)+i<<0,t+=(i^(e|~r))+s[4]-145523070,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~i))+s[11]-1120210379,r=(r<<10|r>>>22)+t<<0,i+=(t^(r|~e))+s[2]+718787259,i=(i<<15|i>>>17)+r<<0,e+=(r^(i|~t))+s[9]-343485551,e=(e<<21|e>>>11)+i<<0,this.first?(this.h0=t+1732584193<<0,this.h1=e-271733879<<0,this.h2=i-1732584194<<0,this.h3=r+271733878<<0,this.first=!1):(this.h0=this.h0+t<<0,this.h1=this.h1+e<<0,this.h2=this.h2+i<<0,this.h3=this.h3+r<<0)},Md5.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,i=this.h2,r=this.h3;return HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[i>>4&15]+HEX_CHARS[15&i]+HEX_CHARS[i>>12&15]+HEX_CHARS[i>>8&15]+HEX_CHARS[i>>20&15]+HEX_CHARS[i>>16&15]+HEX_CHARS[i>>28&15]+HEX_CHARS[i>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,i=this.h2,r=this.h3;return[255&t,t>>8&255,t>>16&255,t>>24&255,255&e,e>>8&255,e>>16&255,e>>24&255,255&i,i>>8&255,i>>16&255,i>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(16),e=new Uint32Array(t);return e[0]=this.h0,e[1]=this.h1,e[2]=this.h2,e[3]=this.h3,t},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var t,e,i,r="",n=this.array(),a=0;a<15;)t=n[a++],e=n[a++],i=n[a++],r+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[63&(t<<4|e>>>4)]+BASE64_ENCODE_CHAR[63&(e<<2|i>>>6)]+BASE64_ENCODE_CHAR[63&i];return t=n[a],r+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[t<<4&63]+"==",r};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__webpack_require__("f28c"),__webpack_require__("c8ba"))},b0e7:function(t,e,i){"use strict";var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"Modal"},[i("Row",{attrs:{type:"flex",justify:"start"}},[i("Col",{staticClass:"Navs"},[i("div",{staticClass:"trees"},[i("Tree",{ref:"tree",staticClass:"treeBox",attrs:{data:t.treeData,render:t.renderContent,"load-data":t.loadData},on:{"on-toggle-expand":t.toggle}}),t.searchClass&&t.treeData.length<=1?i("div",{staticClass:"searchNo"},[t._v("\n\t          此分类暂无数据\n\t        ")]):t._e()],1)]),i("Col",{staticClass:"flex-1"},[i("div",{staticClass:"right-container"},[i("div",{staticClass:"header",staticStyle:{"padding-left":"0"}},[0!==t.isShow?i("Button",{staticClass:"mr10",staticStyle:{width:"100px"},attrs:{disabled:0===t.checkPicList.length},on:{click:t.checkPics}},[t._v(t._s(1==t.uploadName.file_type?"使用选中图片":"使用选中视频"))]):t._e(),1==t.uploadName.file_type?i("Button",{staticClass:"mr10",on:{click:t.openUpload}},[t._v("上传图片")]):i("Button",{staticClass:"mr10",on:{click:t.uploadVideo}},[t._v("上传视频")]),i("Button",{staticClass:"mr10",attrs:{disabled:0===t.checkPicList.length},on:{click:function(e){return e.stopPropagation(),t.editPicList("图片")}}},[t._v(t._s(1==t.uploadName.file_type?"删除图片":"删除视频"))]),i("div",{staticClass:"select-wrapper mr10"},[i("Cascader",{attrs:{placeholder:1==t.uploadName.file_type?"图片移动至":"视频移动至",data:t.cascaderData,"load-data":t.loadData,"change-on-select":""},on:{"on-visible-change":t.visibleChange},model:{value:t.cascaderValue,callback:function(e){t.cascaderValue=e},expression:"cascaderValue"}})],1),i("div",{staticClass:"input-wrapper"},[i("Input",{attrs:{search:"",placeholder:"搜索内容"},on:{"on-search":t.changePage},model:{value:t.uploadName.name,callback:function(e){t.$set(t.uploadName,"name",e)},expression:"uploadName.name"}})],1)],1),i("div",{staticClass:"conter"},[i("div",{staticClass:"pictrueList acea-row"},[i("Row",{staticClass:"conter",attrs:{gutter:24}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowPic,expression:"isShowPic"}],staticClass:"imagesNo"},[i("Icon",{attrs:{type:"ios-images",size:"60",color:"#dbdbdb"}}),i("span",{staticClass:"imagesNo_sp"},[t._v(t._s(1==t.uploadName.file_type?"图片库为空":"视频库为空"))])],1),i("div",{staticClass:"acea-row"},t._l(t.pictrueList,(function(e,r){return i("div",{key:r,staticClass:"pictrueList_pic",on:{mouseenter:function(i){return t.enterLeave(e)},mouseleave:function(i){return t.enterLeave(e)}}},[e.num>0?i("p",{staticClass:"number"},[i("Badge",{attrs:{count:e.num,type:"error",offset:[11,12]}},[i("a",{staticClass:"demo-badge",attrs:{href:"#"}})])],1):t._e(),i("div",{staticClass:"picimage"},[1===e.file_type?i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.poster||e.satt_dir,expression:"item.poster || item.satt_dir"}],class:e.isSelect?"on":"",on:{click:function(i){return i.stopPropagation(),t.changImage(e,r,t.pictrueList)}}}):t._e(),2===e.file_type?i("video",{class:e.isSelect?"on":"",attrs:{src:e.att_dir},on:{click:function(i){return i.stopPropagation(),t.changImage(e,r,t.pictrueList)}}}):t._e()]),i("div",{staticClass:"picName"},[e.isEdit?i("Input",{attrs:{size:"small",type:"text"},on:{"on-blur":function(i){return t.bindTxt(e)}},model:{value:e.real_name,callback:function(i){t.$set(e,"real_name",i)},expression:"item.real_name"}}):i("p",[t._v("\n\t\t\t                "+t._s(e.editName)+"\n\t\t\t              ")]),i("div",{staticClass:"picMenu"},[i("ButtonGroup",[i("Button",{attrs:{size:"small"},on:{click:function(t){e.isEdit=!e.isEdit}}},[i("Icon",{attrs:{type:"ios-create"}})],1),i("Button",{staticClass:"preview",attrs:{size:"small"},on:{click:function(i){return t.openView(e)}}},[i("Icon",{attrs:{type:"ios-eye"}})],1),i("Button",{attrs:{size:"small"},on:{click:function(i){return t.editPicList(e.att_id)}}},[i("Icon",{attrs:{type:"ios-trash"}})],1)],1)],1)],1)])})),0)])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:t.openImgShow,expression:"openImgShow"}],staticClass:"nameStyle"},[1==t.uploadName.file_type?i("img",{attrs:{src:t.viewImg}}):2==t.uploadName.file_type?i("div",{attrs:{id:"player"}}):t._e()]),i("div",{staticClass:"footer acea-row row-right"},[i("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.fileData.page,"page-size":t.fileData.limit},on:{"on-change":t.pageChange}})],1)])])])],1),i("Modal",{attrs:{width:"1024px",scrollable:"","footer-hide":"",closable:"",title:"上传视频","mask-closable":!1,"z-index":9},model:{value:t.modalVideo,callback:function(e){t.modalVideo=e},expression:"modalVideo"}},[i("uploadVideo",{attrs:{pid:t.fileData.pid},on:{getVideo:t.getvideo}})],1)],1)},n=[],a=i("a34a"),s=i.n(a),o=i("12e0"),c=i("d708"),u=i("c276"),l=i("7774");function d(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function h(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?d(i,!0).forEach((function(e){p(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):d(i).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function p(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function f(t){return _(t)||g(t)||m()}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function g(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function _(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}function v(t,e,i,r,n,a,s){try{var o=t[a](s),c=o.value}catch(u){return void i(u)}o.done?e(c):Promise.resolve(c).then(r,n)}function y(t){return function(){var e=this,i=arguments;return new Promise((function(r,n){var a=t.apply(e,i);function s(t){v(a,r,n,s,o,"next",t)}function o(t){v(a,r,n,s,o,"throw",t)}s(void 0)}))}}var b={name:"uploadPictures",components:{uploadVideo:l["a"]},props:{isChoice:{type:String,default:""},gridBtn:{type:Object,default:null},gridPic:{type:Object,default:null},isShow:{type:Number,default:1},isType:{type:Number|String,default:1}},data:function(){return{searchClass:!1,spinShow:!1,fileUrl:c["a"].apiBaseURL+"/file/upload",modalPic:!1,treeData:[],treeData2:[],pictrueList:[],uploadData:{},checkPicList:[],uploadName:{name:"",file_type:this.isType},FromData:null,treeId:0,isJudge:!1,buttonProps:{type:"default",size:"small"},fileData:{pid:0,page:1,limit:18},total:0,pids:0,list:[],modalTitleSs:"",isShowPic:!1,header:{},ids:[],viewImg:"",openImgShow:!1,headTab:[{title:"图片",name:"1"},{title:"视频",name:"2"}],modalVideo:!1,cascaderData:[],cascaderValue:[],currentTreeId:0}},watch:{pictrueList:function(){this.checkPicList=this.ids=[]}},mounted:function(){var t=this,e=this;this.getToken(),this.getList(),1==this.isType?e.getFileList():setTimeout((function(){e.getFileList()}),300),document.addEventListener("click",(function(e){document.querySelector(".nameStyle").contains(e.target)||e.target.classList.contains("ivu-icon-ios-eye")||e.target.classList.contains("preview")||(t.openImgShow=!1,t.viewImg="",t.player&&(t.player.dispose(),t.player=null))}),!0)},methods:{visibleChange:function(t){var e=this;this.$nextTick((function(){t||e.cascaderValue.length&&(e.ids.length?(e.pids=e.cascaderValue[e.cascaderValue.length-1],e.getMove()):(e.$Message.warning("请先选择图片"),e.cascaderValue=[]))}))},createPoster:function(t){new Promise((function(e,i){var r=document.createElement("video");r.setAttribute("src",t.att_dir),r.setAttribute("crossOrigin","anonymous"),r.setAttribute("width",100),r.setAttribute("height",100),r.setAttribute("preload","auto"),r.addEventListener("canplay",(function(){var t=document.createElement("canvas"),i=t.getContext("2d"),n=r.width,a=r.height;t.width=n,t.height=a,i.drawImage(r,0,0,n,a),e(t.toDataURL("image/jpeg"))}))})).then((function(e){t.poster=e}))},createPlayer:function(t){this.player&&(this.player.dispose(),this.player=null),this.player=new Aliplayer({id:"player",width:"100%",height:"100%",autoplay:!0,source:t.att_dir})},uploadVideo:function(){this.modalVideo=!0},getvideo:function(){var t=this;t.modalVideo=!1,setTimeout((function(){t.changePage()}),300)},onhangeTab:function(){this.treeId=0,this.getList(),this.getFileList(),this.checkPicList=[]},enterMouse:function(t){t.realName=!t.realName},enterLeave:function(t){t.isShowEdit=!t.isShowEdit},getToken:function(){this.header["Authori-zation"]="Bearer "+u["a"].cookies.get("token")},renderContent:function(t,e){var i=this,r=e.root,n=e.node,a=e.data,s=[];return 0==a.pid&&s.push(t("DropdownItem",{props:{name:"1"},style:{paddingLeft:"20px"}},"添加")),a.id&&s.push(t("DropdownItem",{props:{name:"2"},style:{paddingLeft:"20px"}},"编辑"),t("DropdownItem",{props:{name:"3"},style:{paddingLeft:"20px"}},"删除")),t("span",{style:{position:"relative",display:"inline-block",width:"100%"},attrs:{id:"tree"+a.id},on:{mouseover:function(){a.flag=!0,i.$refs.tree.$el.querySelector("#tree".concat(a.id)).parentNode.parentNode.classList.add("hovering")},mouseout:function(){a.flag=!1,i.$refs.tree.$el.querySelector("#tree".concat(a.id)).parentNode.parentNode.classList.remove("hovering")},click:function(){i.appendBtn(r,n,a)}}},[t("span",[t("Icon",{props:{type:"ios-folder"},style:{color:"rgba(255, 202, 40, 1)",marginRight:a.pid?"0":"8px",visibility:a.pid?"hidden":"visible",verticalAlign:"baseline"}}),t("span",a.title)]),t("Dropdown",{props:{transfer:!0},style:{position:"absolute",top:0,right:0},on:{"on-click":function(t){switch(t){case"1":i.append(r,n,a);break;case"2":i.editPic(r,n,a);break;case"3":i.remove(r,n,a,"分类");break;default:break}}}},[t("Icon",{props:{type:"ios-more"},style:{display:a.flag?"inline-block":"none",marginRight:"8px",fontSize:"20px"}}),t("DropdownMenu",{slot:"list"},s)])])},renderContentSel:function(t,e){var i=this,r=e.root,n=e.node,a=e.data;return t("div",{style:{display:"inline-block",width:"90%"}},[t("span",[t("span",{style:{cursor:"pointer"},class:["ivu-tree-title"],on:{click:function(t){i.handleCheckChange(r,n,a,t)}}},a.title)])])},handleCheckChange:function(t,e,i,r){this.list=[];var n=i.id,a=i.title;this.list.push({value:n,title:a}),this.ids.length?(this.pids=n,this.getMove()):this.$Message.warning("请先选择图片");for(var s=this.$refs.reference.$el.querySelectorAll(".ivu-tree-title-selected"),o=0;o<s.length;o++)s[o].className="ivu-tree-title";r.path[0].className="ivu-tree-title  ivu-tree-title-selected"},getMove:function(){var t=this,e={pid:this.pids,images:this.ids.toString()};Object(o["g"])(e).then(function(){var e=y(s.a.mark((function e(i){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$Message.success(i.msg),t.getFileList(),t.pids=0,t.checkPicList=[],t.ids=[],t.cascaderValue=[];case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg),t.cascaderValue=[]}))},editPicList:function(t){var e=this,i={ids:this.ids.toString()};"number"==typeof t&&(i={ids:t.toString()});var r={title:1==this.uploadName.file_type?"删除选中图片":"删除选中视频",url:"file/file/delete",method:"POST",ids:i};this.$modalSure(r).then((function(t){e.$Message.success(t.msg),e.getFileList(),e.checkPicList=[]})).catch((function(t){e.$Message.error(t.msg)}))},onMouseOver:function(t,e,i){event.preventDefault(),i.flag=!i.flag,i.flag2&&(i.flag2=!1)},onClick:function(t,e,i){i.flag2=!i.flag2},toggle:function(t){var e=this;this.$nextTick((function(){e.$refs.tree.$el.querySelector("#tree".concat(e.currentTreeId)).parentNode.parentNode.classList.add("selected")}))},appendBtn:function(t,e,i){var r=this.$refs.tree.$el,n=i.id||0;r.querySelector(".selected")&&r.querySelector(".selected").classList.remove("selected"),r.querySelector("#tree".concat(i.id)).parentNode.parentNode.classList.add("selected"),this.treeId=n,this.currentTreeId=n,this.fileData.page=1,this.getFileList()},append:function(t,e,i){this.treeId=i.id,this.getFrom(1)},remove:function(t,e,i,r){var n=this;this.tits=r;var a={title:"删除 [ "+i.title+" ] 分类",url:"file/category/".concat(i.id),method:"DELETE",ids:""};this.$modalSure(a).then((function(t){n.$Message.success(t.msg);var e=i.selected||i.id==n.treeId?1:0;if(e){var r=n.$refs.tree.$el;r.querySelector(".selected")&&r.querySelector(".selected").classList.remove("selected")}n.getList(!e),n.getFileList(e),n.checkPicList=[]})).catch((function(t){n.$Message.error(t.msg)}))},editPic:function(t,e,i){var r=this;this.$modalForm(Object(o["a"])(i.id,{file_type:this.uploadName.file_type})).then((function(){return r.getList(1)}))},changePage:function(){this.fileData.page=1,this.getFileList(),this.checkPicList=[]},getList:function(t){var e=this,i={title:1==this.uploadName.file_type?"全部图片":"全部视频",id:"",pid:0};Object(o["f"])(this.uploadName).then(function(){var r=y(s.a.mark((function r(n){var a,o;return s.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:a=n.data.list,o=[i].concat(f(a)),o.forEach((function(t,e){t.flag=!1,t.selected=!e,t.label=t.title,t.value=t.id})),e.treeData=o,t||e.$nextTick((function(){e.$refs.tree.$el.querySelector("#tree".concat(o[0].id)).parentNode.parentNode.classList.add("selected")})),e.cascaderData=JSON.parse(JSON.stringify(o)),e.cascaderData.shift(),"search"!==type?e.treeData2=f(e.treeData):e.searchClass=!0,e.addFlag(e.treeData);case 9:case"end":return r.stop()}}),r)})));return function(t){return r.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},loadData:function(t,e){t.loading=!0,Object(o["f"])({pid:t.id,file_type:this.uploadName.file_type}).then(function(){var i=y(s.a.mark((function i(r){var n,a;return s.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:n=r.data.list,a=n.map((function(t){return h({},t,{label:t.title,value:t.id,flag:!1})})),t.loading=!1,Object.hasOwnProperty.call(t,"nodeKey")?e(a):(t.children=a,e());case 4:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()).catch((function(t){}))},addFlag:function(t){var e=this;t.map((function(t){e.$set(t,"flag",!1),e.$set(t,"flag2",!1),t.children&&e.addFlag(t.children)}))},add:function(){this.treeId=0,this.getFrom()},getFileList:function(t){var e=this;this.fileData.pid=t?0:this.treeId,this.fileData.file_type=this.uploadName.file_type,this.fileData.name=this.uploadName.name,Object(o["d"])(this.fileData).then(function(){var t=y(s.a.mark((function t(i){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:i.data.list.forEach((function(t){t.isSelect=!1,t.isEdit=!1,t.isShowEdit=!1,t.realName=!1,t.num=0,e.editName(t)})),e.pictrueList=i.data.list,e.pictrueList.length?e.isShowPic=!1:e.isShowPic=!0,e.total=i.data.count;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},pageChange:function(t){this.fileData.page=t,this.getFileList(),this.checkPicList=[]},getFrom:function(t){var e=this;this.$modalForm(Object(o["b"])({id:this.treeId,file_type:this.uploadName.file_type})).then((function(i){e.getList(t)}))},beforeUpload:function(t){var e=this,i=["image/png","image/jpg","image/jpeg","image/gif"],r=-1!==i.indexOf(t.type);if(!r)return this.$Message.warning({content:"文件  "+t.name+"  格式不正确, 请选择格式正确的图片",duration:5}),!1;var n=this.$cache.local.getJSON("file_size_max"),a=t.size<n,s=n/1024/1024;if(!a)return this.$Message.warning({content:"文件体积过大,图片大小不能超过"+s+"M",duration:5}),!1;this.uploadData={pid:this.treeId};var o=new Promise((function(t){e.$nextTick((function(){t(!0)}))}));return o},handleSuccess:function(t,e,i){200===t.status?(this.fileData.page=1,this.$Message.success(t.msg),this.getFileList()):this.$Message.error(t.msg)},cancel:function(){this.$emit("changeCancel")},changImage:function(t,e,i){var r=this,n=0;"单选"===this.isChoice&&this.checkPicList.length>=1?this.checkPicList[0].att_id==t.att_id?(t.isSelect=!1,this.pictrueList.forEach((function(t){t.att_id==r.checkPicList[0].att_id&&(t.num=0)})),this.checkPicList=[]):(this.checkPicList[0].num=0,this.checkPicList[0].isSelect=!1,this.checkPicList.splice(0,1),this.checkPicList.push(t),t.isSelect=!0,t.num=1):(t.isSelect?(t.isSelect=!1,this.checkPicList.map((function(e,i){e.att_id==t.att_id&&(n=i)})),this.checkPicList.splice(n,1)):(t.isSelect=!0,this.checkPicList.push(t)),this.ids=[],this.checkPicList.map((function(t,e){r.ids.push(t.att_id)})),this.pictrueList.map((function(t,e){t.isSelect?r.checkPicList.filter((function(e,i){t.att_id==e.att_id&&(t.num=i+1)})):t.num=0})))},checkPics:function(){if("单选"===this.isChoice){if(this.checkPicList.length>1)return this.$Message.warning("最多只能选一张图片");this.$emit("getPic",this.checkPicList[0])}else{var t=this.$route.query.maxLength;if(void 0!=t&&this.checkPicList.length>Number(t))return this.$Message.warning("最多只能选"+t+"张图片");this.$emit("getPicD",this.checkPicList)}},editName:function(t){var e=t.real_name.split("."),i=void 0==e[1]?[]:e[1];e[0].length,i.length;t.editName=t.real_name},bindTxt:function(t){var e=this;""==t.real_name&&this.$Message.error("请填写内容"),Object(o["e"])(t.att_id,{real_name:t.real_name}).then((function(i){e.editName(t),t.isEdit=!1,e.$Message.success(i.msg)})).catch((function(t){e.$Message.error(t.msg)}))},openView:function(t){this.viewImg==t.satt_dir?(this.openImgShow=!1,this.viewImg=""):(this.openImgShow=!0,1==t.file_type?this.viewImg=t.satt_dir:2==t.file_type&&this.createPlayer(t))},openUpload:function(){var t=this;this.$uploadImg({categories:this.treeData,categoryId:this.treeId,onClose:function(){t.fileData.page=1,t.getFileList()}})}}},k=b,w=(i("dd53"),i("2877")),S=Object(w["a"])(k,r,n,!1,null,"7ebe7b90",null);e["a"]=S.exports},c4c8:function(t,e,i){"use strict";i.d(e,"v",(function(){return n})),i.d(e,"s",(function(){return a})),i.d(e,"r",(function(){return s})),i.d(e,"z",(function(){return o})),i.d(e,"w",(function(){return c})),i.d(e,"F",(function(){return u})),i.d(e,"G",(function(){return l})),i.d(e,"k",(function(){return d})),i.d(e,"x",(function(){return h})),i.d(e,"u",(function(){return p})),i.d(e,"e",(function(){return f})),i.d(e,"g",(function(){return m})),i.d(e,"o",(function(){return g})),i.d(e,"q",(function(){return _})),i.d(e,"p",(function(){return v})),i.d(e,"d",(function(){return y})),i.d(e,"c",(function(){return b})),i.d(e,"j",(function(){return k})),i.d(e,"I",(function(){return w})),i.d(e,"h",(function(){return S})),i.d(e,"t",(function(){return C})),i.d(e,"i",(function(){return E})),i.d(e,"D",(function(){return A})),i.d(e,"B",(function(){return O})),i.d(e,"C",(function(){return R})),i.d(e,"J",(function(){return L})),i.d(e,"a",(function(){return P})),i.d(e,"H",(function(){return H})),i.d(e,"y",(function(){return M})),i.d(e,"A",(function(){return N})),i.d(e,"b",(function(){return D})),i.d(e,"l",(function(){return j})),i.d(e,"f",(function(){return I})),i.d(e,"n",(function(){return $})),i.d(e,"m",(function(){return F})),i.d(e,"E",(function(){return B})),i.d(e,"K",(function(){return x}));var r=i("b6bd");function n(t){return Object(r["a"])({url:"product/product",method:"get",params:t})}function a(t){return Object(r["a"])({url:"product/product/".concat(t),method:"get"})}function s(t){return Object(r["a"])({url:"product/type_header",method:"get",params:t})}function o(){return Object(r["a"])({url:"product/product_label",method:"get"})}function c(t){return Object(r["a"])({url:"product/reply",method:"get",params:t})}function u(t,e){return Object(r["a"])({url:"product/reply/set_reply/".concat(e),method:"PUT",data:t})}function l(t,e){return Object(r["a"])({url:"product/product/set_show/".concat(t,"/").concat(e),method:"PUT"})}function d(t){return Object(r["a"])({url:"product/product/attrs/".concat(t),method:"get"})}function h(t,e){return Object(r["a"])({url:"product/product/saveStocks/".concat(e),method:"PUT",data:t})}function p(t){return Object(r["a"])({url:"/product/product/list",method:"get",params:t})}function f(t){return Object(r["a"])({url:"product/category/cascader_list/".concat(t),method:"get"})}function m(t){return Object(r["a"])({url:"product/product/".concat(t.id),method:"POST",data:t})}function g(){return Object(r["a"])({url:"product/product/get_rule",method:"get"})}function _(){return Object(r["a"])({url:"product/product/get_template",method:"get"})}function v(t){return Object(r["a"])({url:"product/product/get_temp_keys",method:"get",params:t})}function y(){return Object(r["a"])({url:"product/cache",method:"delete"})}function b(){return Object(r["a"])({url:"product/brand/cascader_list/2",method:"get"})}function k(t){return Object(r["a"])({url:"product/get_all_unit",method:"get"})}function w(){return Object(r["a"])({url:"file/upload_type",method:"get"})}function S(){return Object(r["a"])({url:"product/all_ensure",method:"get"})}function C(){return Object(r["a"])({url:"product/label/form",method:"get"})}function E(){return Object(r["a"])({url:"product/all_specs",method:"get"})}function A(t){return Object(r["a"])({url:"product/product/rule",method:"GET",params:t})}function O(t,e){return Object(r["a"])({url:"product/product/rule/".concat(e),method:"POST",data:t})}function R(t){return Object(r["a"])({url:"product/product/rule/".concat(t),method:"get"})}function L(t){return Object(r["a"])({url:"file/video_attachment",method:"post",data:t})}function P(){return Object(r["a"])({url:"system/form/all_system_form",method:"get"})}function H(t,e){return Object(r["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function M(t){return Object(r["a"])({url:"product/product/product_show",method:"put",data:t})}function N(t){return Object(r["a"])({url:"product/product/product_unshow",method:"put",data:t})}function D(t){return Object(r["a"])({url:"product/batch_process",method:"post",data:t})}function j(t){return Object(r["a"])({url:"product/category",method:"get",params:t})}function I(t,e){return Object(r["a"])({url:"/product/category/set_show/".concat(t,"/").concat(e),method:"PUT"})}function $(t){return Object(r["a"])({url:"/product/category/".concat(t),method:"get"})}function F(){return Object(r["a"])({url:"/product/category/create",method:"get"})}function B(t,e){return Object(r["a"])({url:"product/product/cate/".concat(t),method:"post",data:e})}function x(){return Object(r["a"])({url:"/user/wechat/card",method:"get"})}},cb78:function(t,e,i){},dd53:function(t,e,i){"use strict";var r=i("cb78"),n=i.n(r);n.a},e362:function(t,e,i){}}]);