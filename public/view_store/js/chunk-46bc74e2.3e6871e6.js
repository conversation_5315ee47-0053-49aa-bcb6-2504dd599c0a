(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46bc74e2"],{"1de2":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{staticClass:"mart"},[a("Col",{staticClass:"mr"},[a("FormItem",{attrs:{label:"用户搜索：",labelWidth:80,"label-for":"nickname"}},[a("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入ID或者手机号","element-id":"nickname",clearable:""},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}},[a("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.userFrom.field_key,callback:function(e){t.$set(t.userFrom,"field_key",e)},expression:"userFrom.field_key"}},[a("Option",{attrs:{value:"all"}},[t._v("全部")]),a("Option",{attrs:{value:"uid"}},[t._v("ID")]),a("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1),a("Col",{staticClass:"ivu-text-right userFrom "},[a("FormItem",[a("Button",{staticClass:"mr15 search",attrs:{type:"primary",label:"default"},on:{click:t.userSearchs}},[t._v("搜索")]),a("Button",{staticClass:"ResetSearch search",on:{click:function(e){return t.reset("userFrom")}}},[t._v("重置")])],1)],1)],1)],1)],1),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Row",{attrs:{type:"flex",justify:"space-between"}},[a("Col",{attrs:{span:"24"}},[a("Button",{staticClass:"button",attrs:{disabled:t.datanew.length<=0},on:{click:t.setLabel}},[t._v("批量设置标签")])],1),a("Col",{attrs:{span:"24"}},[a("Table",{ref:"selection",staticClass:"ivu-mt",attrs:{columns:t.columns,data:t.dataList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"on-select-all":t.selectall,"on-select-all-cancel":t.selectall,"on-sort-change":t.sortChanged,"on-selection-change":t.select},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"nickname",fn:function(e){var s=e.row;e.index;return[a("div",{staticClass:"acea-row"},[a("Icon",{directives:[{name:"show",rawName:"v-show",value:"男"===s.sex,expression:"row.sex === '男'"}],staticClass:"mr5",attrs:{type:"md-male",color:"#2db7f5",size:"15"}}),a("Icon",{directives:[{name:"show",rawName:"v-show",value:"女"===s.sex,expression:"row.sex === '女'"}],staticClass:"mr5",attrs:{type:"md-female",color:"#ed4014",size:"15"}}),a("div",[t._v(t._s(s.nickname)),null!=s.delete_time?a("span",{staticStyle:{color:"#ed4014"}},[t._v(" (已注销)")]):t._e()])],1)]}},{key:"isMember",fn:function(e){var s=e.row;e.index;return[a("div",[t._v(t._s(s.isMember?"是":"否"))])]}},{key:"action",fn:function(e){var s=e.row;e.index;return[a("a",{on:{click:function(e){return t.detail(s)}}},[t._v("详情")]),null==s.delete_time?a("Divider",{attrs:{type:"vertical"}}):t._e(),null==s.delete_time?a("a",{on:{click:function(e){return t.recharge(s)}}},[t._v("充值")]):t._e(),null==s.delete_time?a("Divider",{attrs:{type:"vertical"}}):t._e(),null==s.delete_time?a("a",{on:{click:function(e){return t.paying(s)}}},[t._v("付费会员")]):t._e(),null==s.delete_time?a("Divider",{attrs:{type:"vertical"}}):t._e(),null==s.delete_time?a("a",{on:{click:function(e){return t.openLabel(s)}}},[t._v("设置标签")]):t._e()]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{staticClass:"box",attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1),a("Recharges",{ref:"recharges"}),a("Paying",{ref:"payings"}),a("Setusers",{ref:"setusers"}),a("user-details",{ref:"userDetails",attrs:{"group-list":t.groupList}}),a("Modal",{attrs:{scrollable:"",title:"选择用户标签",closable:!0,width:"540","footer-hide":!0},model:{value:t.labelShow,callback:function(e){t.labelShow=e},expression:"labelShow"}},[a("userLabel",{attrs:{uid:t.labelActive.uid},on:{close:t.labelClose}})],1)],1)},i=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"修改店员","mask-closable":!1,width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.tableList},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"status",fn:function(e){var s=e.row;e.index;return[1==s.status?a("span",[t._v("开启")]):t._e(),0==s.status?a("span",[t._v("关闭")]):t._e()]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.formValidate.page,"page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},r=[],o=a("b4ea"),l=a("c24f"),c={name:"setusers",data:function(){var t=this;return{modals:!1,total:0,formValidate:{page:1,limit:15},loading:!1,tableList:[],currentChoose:"",id:"",columns:[{title:"选择",key:"id",width:70,align:"center",render:function(e,a){var s=a.row.id,i=!1;t.currentChoose===s?(t.current(a.row),i=!0):i=!1;var n=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){n.currentChoose=s,t.$parent.getList()}}})])}},{title:"头像",slot:"avatars",Width:60},{title:"微信昵称",key:"staff_name",minWidth:80},{title:"手机号",key:"phone",minWidth:100},{title:"账号状态",slot:"status",minWidth:80}]}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Object(o["n"])(this.formValidate).then((function(e){t.tableList=e.data.list,t.total=e.data.count}))},getId:function(t){this.id=t},cancel:function(){this.$Message.info("已取消"),this.currentChoose=""},pageChange:function(t){this.formValidate.page=t,this.getList()},current:function(t){var e=this,a={uid:this.id,staff_id:t.id};Object(l["g"])(a).then((function(t){e.modals=!1,e.$Message.success(t.msg),e.currentChoose="",e.$parent.getList()})).catch((function(t){e.modals=!1,e.$Message.error(t.msg),e.currentChoose=""}))}}},d=c,u=(a("8195"),a("2877")),m=Object(u["a"])(d,n,r,!1,null,"1218f014",null),v=m.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户充值","mask-closable":!1,width:"583"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"header_tab left",class:1==t.tabs?"on":"",on:{click:function(e){return t.tab(1)}}},[t._v("优惠充值")]),a("div",{staticClass:"header_tab",class:2==t.tabs?"on":"",on:{click:function(e){return t.tab(2)}}},[t._v("自定义充值")])]),1==t.tabs?a("div",{staticClass:"content"},t._l(t.data,(function(e,s){return a("div",{staticClass:"content_box",class:t.contTabs==s?"content_tab":"",on:{click:function(a){return t.contTab(e,s)}}},[a("div",{staticClass:"top"},[t._v("¥"+t._s(e.price))]),a("div",{staticClass:"bottom"},[t._v("额外赠送：¥ "+t._s(e.give_money))])])})),0):a("div",{staticClass:"contents"},[a("div",[a("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"0.00",type:"number"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("div",{staticClass:"title"},[t._v("自定义充值的金额无赠送优惠")])],1)]),a("div",{staticClass:"footer"},[a("Button",{staticStyle:{"margin-right":"14px"},attrs:{type:"default"},on:{click:t.close}},[t._v("取消")]),a("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1)]),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"付款码详情","mask-closable":!1,width:"400"},on:{"on-cancel":t.cancel},model:{value:t.qr,callback:function(e){t.qr=e},expression:"qr"}},[a("div",{staticClass:"qrbox"},[a("div",{ref:"qrCodeUrl",staticClass:"qrcode"}),t.qrtip?t._e():a("div",[t._v("微信扫码充值，将于 "+t._s(t.$moment(1e3*t.codeKey.invalid).format("YYYY-MM-DD H:mm:ss"))+" 过期")]),t.qrtip?a("div",{staticClass:"tips"},[t._v("若已充值，联系客服")]):t._e(),t.qrtip?a("div",{staticClass:"qrtips"},[t._v("已失效，请重新刷新")]):t._e()])])],1)},h=[],p=a("d044"),b=a.n(p),_={name:"userDetails",data:function(){return{codeKey:"",value:"",modals:!1,tabs:1,contTabs:0,data:"",tabdata:"",id:"",qr:!1,timeNum:0,qrtip:!1}},mounted:function(){},methods:{creatQrCode:function(){var t=this.codeKey.code_url;new b.a(this.$refs.qrCodeUrl,{text:t,width:160,height:160,colorDark:"#000000",colorLight:"#ffffff",correctLevel:b.a.CorrectLevel.H})},tab:function(t){this.tabs=t},contTab:function(t,e){this.contTabs=e,this.tabdata=t},getList:function(t){var e=this;Object(l["n"])().then((function(a){e.data=a.data.recharge_quota,e.id=t,e.contTab(e.data[0],0)}))},save:function(){var t=this,e={uid:t.id,rechar_id:t.tabdata.id,price:t.tabdata.price};2==t.tabs&&(e.price=t.value,e.rechar_id=0),Object(l["o"])(e).then((function(e){t.modals=!1,t.codeKey=e.data.data.jsConfig,t.qr=!0,t.creatQrCode(),t.scanTime=setInterval((function(){if(t.timeNum++,t.timeNum>=60){t.timeNum=0,window.clearInterval(t.scanTime);var a=Date.parse(new Date)/1e3;a>=t.codeKey.invalid&&(t.qrtip=!0)}else t.getScanStatus(e.data.data.order_id)}),1e3),t.tabs=1,t.value=""})).catch((function(e){t.timeNum=0,window.clearInterval(t.scanTime),t.$Message.success(e.msg)}))},close:function(){this.modals=!1},getScanStatus:function(t){var e=this,a=this;Object(l["b"])(1,{order_id:t,end_time:a.codeKey.invalid}).then((function(t){t.data.status&&(a.$parent.getList(),a.timeNum=0,a.qr=!1,a.qrtip=!1,window.clearInterval(a.scanTime),a.$Message.success("充值成功"),e.$refs.qrCodeUrl.innerHTML="")}))},cancel:function(){this.tabs=1,this.qr=!1,this.qrtip=!1,this.$refs.qrCodeUrl.innerHTML="",this.timeNum=0,window.clearInterval(this.scanTime)}},beforeDestroy:function(){this.timeNum=0,this.$refs.qrCodeUrl.innerHTML="",window.clearInterval(this.scanTime)}},g=_,w=(a("54ba"),Object(u["a"])(g,f,h,!1,null,"c34d5fa0",null)),C=w.exports,k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户充值","mask-closable":!1,width:"583"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"header_tab left",class:1==t.tabs?"on":"",on:{click:function(e){return t.tab(1)}}},[t._v("充值会员")])]),1==t.tabs?a("div",{staticClass:"content"},t._l(t.data,(function(e,s){return a("div",{staticClass:"content_box",class:t.contTabs==s?"content_tab":"",on:{click:function(a){return t.contTab(e,s)}}},[a("div",{staticClass:"top"},[t._v("¥"+t._s(e.pre_price))]),a("div",{staticClass:"bottom"},[t._v(t._s(e.title)+"原价：¥ "+t._s(e.price))])])})),0):t._e(),a("div",{staticClass:"footer"},[a("Button",{staticStyle:{"margin-right":"14px"},attrs:{type:"default"},on:{click:t.close}},[t._v("取消")]),a("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1)]),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"付款码详情","mask-closable":!1,width:"400"},on:{"on-cancel":t.cancel},model:{value:t.qr,callback:function(e){t.qr=e},expression:"qr"}},[a("div",{staticClass:"qrbox"},[a("div",{ref:"qrCodeUrl",staticClass:"qrcode"}),t.qrtip?t._e():a("div",[t._v("微信扫码充值，将于 "+t._s(t.$moment(1e3*t.codeKey.invalid).format("YYYY-MM-DD H:mm:ss"))+" 过期")]),t.qrtip?a("div",{staticClass:"tips"},[t._v("若已充值，联系客服")]):t._e(),t.qrtip?a("div",{staticClass:"qrtips"},[t._v("已失效，请重新刷新")]):t._e()])])],1)},y=[],I={name:"userDetails",data:function(){return{codeKey:"",value:"",modals:!1,tabs:1,contTabs:0,data:"",tabdata:"",id:"",qr:!1,timeNum:0,qrtip:!1}},mounted:function(){},methods:{creatQrCode:function(){var t=this.codeKey.code_url;new b.a(this.$refs.qrCodeUrl,{text:t,width:160,height:160,colorDark:"#000000",colorLight:"#ffffff",correctLevel:b.a.CorrectLevel.H})},tab:function(t){this.tabs=t},contTab:function(t,e){this.contTabs=e,this.tabdata=t},getList:function(t){var e=this;Object(l["t"])().then((function(a){e.data=a.data,e.id=t,e.contTab(e.data[0],0)}))},save:function(){var t=this,e=this,a={uid:this.id,member_id:this.tabdata.id};Object(l["s"])(a).then((function(a){"SUCCESS"==a.data.status?(t.$Message.success(a.msg),t.$parent.getList(),t.modals=!1):(t.modals=!1,t.codeKey=a.data.result.jsConfig,t.qr=!0,t.creatQrCode(),t.scanTime=setInterval((function(){if(t.timeNum++,t.timeNum>=60){t.timeNum=0,window.clearInterval(t.scanTime);var s=Date.parse(new Date)/1e3;s>=t.codeKey.invalid&&(e.qrtip=!0)}else t.getScanStatus(a.data.result.order_id)}),1e3),t.tabs=1,t.value="")})).catch((function(e){t.timeNum=0,window.clearInterval(t.scanTime),t.$Message.error(e.msg)}))},close:function(){this.modals=!1},getScanStatus:function(t){var e=this;Object(l["b"])(2,{order_id:t,end_time:this.codeKey.invalid}).then((function(t){t.data.status&&(e.$parent.getList(),e.timeNum=0,window.clearInterval(e.scanTime),e.qr=!1,e.qrtip=!1,e.$Message.success("充值成功"),e.$refs.qrCodeUrl.innerHTML="")}))},cancel:function(){this.tabs=1,this.qr=!1,this.qrtip=!1,this.$refs.qrCodeUrl.innerHTML="",this.timeNum=0,window.clearInterval(this.scanTime)}},beforeDestroy:function(){this.timeNum=0,this.$refs.qrCodeUrl.innerHTML="",window.clearInterval(this.scanTime)}},D=I,x=(a("f28f"),Object(u["a"])(D,k,y,!1,null,"ad78eca6",null)),L=x.exports,O=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"label-wrapper"},[t.labelList.length?a("div",{staticClass:"list-box"},t._l(t.labelList,(function(e,s){return a("div",{key:s,staticClass:"label-box"},[a("div",{staticClass:"title"},[t._v(t._s(e.name))]),a("div",{staticClass:"list"},t._l(e.label,(function(e,s){return a("div",{key:s,staticClass:"label-item",class:{on:e.disabled},on:{click:function(a){return t.selectLabel(e)}}},[t._v(t._s(e.label_name))])})),0)])})),0):a("div",[t._v("暂无标签")]),a("div",{staticClass:"footer"},[a("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确定")]),a("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:t.cancel}},[t._v("取消")])],1)])},S=[],$={name:"userLabel",props:{uid:{type:String|Number,default:""}},data:function(){return{labelList:[],activeIds:[],isUser:!1}},watch:{uid:{handler:function(t,e){t!=e&&this.getList()},deep:!0}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Object(l["d"])(this.uid).then((function(e){e.data.map((function(e){e.label.map((function(e){e.disabled&&t.activeIds.push(e.id)}))})),t.labelList=e.data}))},selectLabel:function(t){if(t.disabled){var e=this.activeIds.indexOf(t.id);this.activeIds.splice(e,1),t.disabled=!1}else this.activeIds.push(t.id),t.disabled=!0},subBtn:function(){var t=this,e=[];this.labelList.map((function(t){t.label.map((function(t){0==t.disabled&&e.push(t.id)}))})),this.$emit("close"),Object(l["f"])(this.uid,{label_ids:this.activeIds,un_label_ids:e}).then((function(e){t.activeIds=[],t.labelList=[],t.$Message.success(e.msg),t.$emit("close")})).catch((function(e){t.$Message.error(e.msg)}))},cancel:function(){this.activeIds=[],this.labelList=[],this.$emit("close")}}},F=$,j=(a("6d33"),Object(u["a"])(F,O,S,!1,null,"77d93035",null)),M=j.exports,W=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Drawer",{attrs:{closable:!1,width:"1000","class-name":"order_box",styles:{padding:0}},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[s("div",{staticClass:"acea-row user-row"},[s("div",{staticClass:"avatar mr15"},[s("img",{attrs:{src:t.psInfo.avatar}})]),s("div",{staticClass:"user-row-text"},[s("div",[s("span",{staticClass:"nickname"},[t._v(t._s(t.psInfo.nickname||"-")+t._s(null!=t.psInfo.delete_time?" (已注销)":""))]),s("i",{staticClass:"iconfont",class:{iconxiaochengxu:"routine"===t.psInfo.user_type,icongongzhonghao:"wechat"===t.psInfo.user_type,iconPC:"pc"===t.psInfo.user_type,iconh5:"h5"===t.psInfo.user_type,iconapp:"app"===t.psInfo.user_type}})]),s("div",{staticClass:"level"},[t.psInfo.is_money_level?s("img",{attrs:{src:a("30a5")}}):t._e(),t.psInfo.level?s("span",{staticClass:"vip"},[t._v("V"+t._s(t.psInfo.level))]):t._e()])])]),s("div",{staticClass:"acea-row info-row"},t._l(t.detailsData,(function(e,a){return s("div",{key:a,staticClass:"info-row-item"},[s("div",{staticClass:"info-row-item-title"},[t._v(t._s(e.title))]),s("div",[t._v(t._s(e.value)+t._s(e.key))])])})),0),s("Tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.list,(function(e,a){return s("TabPane",{key:a,attrs:{label:e.label,name:e.val}},["info"===e.val?[s("user-form",{directives:[{name:"show",rawName:"v-show",value:t.isEdit,expression:"isEdit"}],ref:"userForm",refInFor:!0,attrs:{"ps-info":t.psInfo},on:{"change-menu":t.changeMenu}}),s("user-info",{directives:[{name:"show",rawName:"v-show",value:!t.isEdit,expression:"!isEdit"}],attrs:{"ps-info":t.psInfo,workMemberInfo:t.workMemberInfo,workClientInfo:t.workClientInfo}})]:[s("Table",{ref:"table",refInFor:!0,attrs:{columns:t.columns,data:t.userLists,loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"coupon_price",fn:function(e){var a=e.row;return[1==a.coupon_type?s("span",[t._v(t._s(a.coupon_price)+"元")]):t._e(),2==a.coupon_type?s("span",[t._v(t._s(parseFloat(a.coupon_price)/10)+"折（"+t._s(a.coupon_price.toString().split(".")[0])+"%）")]):t._e()]}},{key:"product",fn:function(e){var a=e.row;return[s("div",{staticClass:"product"},[s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a.image,expression:"row.image"}]})]),s("div",{staticClass:"title"},[t._v(t._s(a.store_name))])])]}}],null,!0)}),s("div",{staticClass:"acea-row row-right page"},[s("Page",{attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"update:current":function(e){return t.$set(t.userFrom,"page",e)},"on-change":t.pageChange}})],1)]],2)})),1)],1)},q=[],T=a("a34a"),N=a.n(T),P=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Form",{attrs:{model:t.formData,"label-width":76}},[a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("基本信息")]),a("div",{staticClass:"section-bd"},[a("FormItem",{attrs:{label:"用户编号："}},[a("Input",{attrs:{disabled:""},model:{value:t.formData.uid,callback:function(e){t.$set(t.formData,"uid",e)},expression:"formData.uid"}})],1),a("FormItem",{attrs:{label:"真实姓名："}},[a("Input",{attrs:{placeholder:"请输入"},model:{value:t.formData.real_name,callback:function(e){t.$set(t.formData,"real_name",e)},expression:"formData.real_name"}})],1),a("FormItem",{attrs:{label:"手机号码："}},[a("Input",{attrs:{placeholder:"请输入"},model:{value:t.formData.phone,callback:function(e){t.$set(t.formData,"phone",e)},expression:"formData.phone"}})],1),a("FormItem",{attrs:{label:"生日："}},[a("DatePicker",{attrs:{value:t.formData.birthday},on:{"on-change":t.dateChange}})],1),a("FormItem",{attrs:{label:"性别："}},[a("Select",{model:{value:t.formData.sex,callback:function(e){t.$set(t.formData,"sex",e)},expression:"formData.sex"}},[a("Option",{attrs:{value:0}},[t._v("保密")]),a("Option",{attrs:{value:1}},[t._v("男")]),a("Option",{attrs:{value:2}},[t._v("女")])],1)],1),a("FormItem",{attrs:{label:"省市区："}},[a("Cascader",{attrs:{data:t.addresData,"load-data":t.loadData},on:{"on-change":t.addchack},model:{value:t.addressSelect,callback:function(e){t.addressSelect=e},expression:"addressSelect"}})],1),a("FormItem",{attrs:{label:"身份证号："}},[a("Input",{attrs:{placeholder:"请输入"},model:{value:t.formData.card_id,callback:function(e){t.$set(t.formData,"card_id",e)},expression:"formData.card_id"}})],1),a("FormItem",{attrs:{label:"详细地址："}},[a("Input",{attrs:{placeholder:"请输入"},model:{value:t.formData.addres,callback:function(e){t.$set(t.formData,"addres",e)},expression:"formData.addres"}})],1)],1)]),a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("密码设置")]),a("div",{staticClass:"section-bd"},[a("FormItem",{attrs:{label:"登录密码："}},[a("Input",{attrs:{type:"password",password:"",placeholder:"请输入"},model:{value:t.formData.pwd,callback:function(e){t.$set(t.formData,"pwd",e)},expression:"formData.pwd"}})],1),a("FormItem",{attrs:{label:"确认密码："}},[a("Input",{attrs:{type:"password",password:"",placeholder:"请输入"},model:{value:t.formData.true_pwd,callback:function(e){t.$set(t.formData,"true_pwd",e)},expression:"formData.true_pwd"}})],1)],1)]),a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("用户设置")]),a("div",{staticClass:"section-bd"},[a("FormItem",{attrs:{label:"推广资格："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formData.spread_open,callback:function(e){t.$set(t.formData,"spread_open",e)},expression:"formData.spread_open"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("启用")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("禁用")])])],1),a("FormItem",{attrs:{label:"用户状态："}},[a("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("锁定")])])],1),a("FormItem",{attrs:{label:"用户标签："}},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"labelInput acea-row row-between-wrapper",on:{click:t.openLabel}},[a("div",{staticStyle:{width:"90%"}},[t.dataLabel.length?a("div",t._l(t.dataLabel,(function(e,s){return a("Tag",{attrs:{closable:""},on:{"on-close":function(a){return t.closeLabel(e)}}},[t._v(t._s(e.label_name))])})),1):a("span",{staticClass:"span"},[t._v("选择用户关联标签")])]),a("div",{staticClass:"iconfont iconxiayi"})]),a("Button",{attrs:{type:"text"},on:{click:function(e){return t.add(1)}}},[t._v("添加标签")])],1)]),a("FormItem",{attrs:{label:"用户分组："}},[a("div",{staticStyle:{display:"flex"}},[a("Select",{attrs:{placeholder:"请选择",transfer:!0},model:{value:t.formData.group_id,callback:function(e){t.$set(t.formData,"group_id",e)},expression:"formData.group_id"}},t._l(t.groupList,(function(e){return a("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.group_name))])})),1),a("Button",{attrs:{type:"text"},on:{click:function(e){return t.add(2)}}},[t._v("添加分组")])],1)]),a("FormItem",{attrs:{label:"用户等级："}},[a("Select",{model:{value:t.formData.level,callback:function(e){t.$set(t.formData,"level",e)},expression:"formData.level"}},t._l(t.levelOptions,(function(e){return a("Option",{key:e.id,attrs:{value:e.grade}},[t._v(t._s(e.name))])})),1)],1),a("FormItem",{attrs:{label:"推广人："}},[a("Input",{attrs:{clearable:"",placeholder:"请选择",icon:"ios-arrow-down"},on:{"on-clear":t.clearSpread,"on-focus":t.editSpread},model:{value:t.formData.spread_uid_nickname,callback:function(e){t.$set(t.formData,"spread_uid_nickname",e)},expression:"formData.spread_uid_nickname"}})],1)],1)])]),a("Modal",{attrs:{scrollable:"",title:"请选择商城用户",closable:!1,width:"50%"},model:{value:t.customerShow,callback:function(e){t.customerShow=e},expression:"customerShow"}},[t.customerShow?a("customerInfo",{on:{imageObject:t.imageObject}}):t._e()],1),a("Modal",{attrs:{scrollable:"",title:"选择用户标签",closable:!0,width:"540","footer-hide":!0,"mask-closable":!1},model:{value:t.labelShow,callback:function(e){t.labelShow=e},expression:"labelShow"}})],1)},E=[],z={name:"userForm",props:{psInfo:Object},data:function(){return{labelShow:!1,customerShow:!1,formData:{uid:this.psInfo.uid,real_name:this.psInfo.real_name,phone:this.psInfo.phone,birthday:this.psInfo.birthday,sex:this.psInfo.sex,card_id:this.psInfo.card_id,addres:this.psInfo.addres,pwd:"",true_pwd:"",spread_open:this.psInfo.spread_open,status:this.psInfo.status,group_id:0,label_id:[],level:this.psInfo.level,provincials:"",province:0,city:0,area:0,street:0,spread_uid:0,spread_uid_nickname:""},dataLabel:[],addressSelect:[],levelOptions:[],labelOptions:[],addresData:[],groupList:[]}},watch:{psInfo:function(t){this.formData.uid=t.uid,this.formData.real_name=t.real_name,this.formData.phone=t.phone,this.formData.birthday=t.birthday,this.formData.sex=t.sex,this.formData.card_id=t.card_id,this.formData.addres=t.addres,this.formData.spread_open=t.spread_open,this.formData.status=t.status,this.dataLabel=t.label_id,this.formData.level=t.level,this.formData.provincials=t.provincials,this.formData.province=t.province,this.formData.city=t.city,this.formData.area=t.area,this.formData.street=t.street,this.formData.spread_uid_nickname=t.spread_uid_nickname,this.formData.spread_uid=t.spread_uid,this.formData.group_id=t.group_id}},created:function(){this.levelList(),this.groupLists(),this.labelList(),this.psInfo.province&&this.addressSelect.push(this.psInfo.province),this.psInfo.city&&this.addressSelect.push(this.psInfo.city),this.psInfo.area&&this.addressSelect.push(this.psInfo.area),this.psInfo.street&&this.addressSelect.push(this.psInfo.street)},methods:{clearSpread:function(){this.formData.spread_uid=0,this.formData.spread_uid_nickname=""},closeLabel:function(t){var e=this.dataLabel.indexOf(this.dataLabel.filter((function(e){return e.id==t.id}))[0]);this.dataLabel.splice(e,1)},activeData:function(t){this.labelShow=!1,this.dataLabel=t},openLabel:function(){this.labelShow=!0,this.$refs.userLabel.userLabel(JSON.parse(JSON.stringify(this.dataLabel)))},labelClose:function(){this.labelShow=!1},editSpread:function(){this.customerShow=!0},imageObject:function(t){this.customerShow=!1,this.formData.spread_uid=t.uid,this.formData.spread_uid_nickname=t.name},changeMenu:function(t){this.$emit("change-menu",t)},add:function(t){var e=this;switch(t){case 1:this.$modalForm(userLabelAddApi(0)).then((function(){}));break;case 2:this.$modalForm(groupAddApi(0)).then((function(){e.groupLists()}));break}},levelList:function(){},groupLists:function(){},labelList:function(){},loadData:function(t,e){t.loading=!0,cityApi({pid:t.value}).then((function(a){t.children=a.data,t.loading=!1,e()}))},addchack:function(t,e){var a=this;t.forEach((function(t,e){0==e?a.formData.province=t:1==e?a.formData.city=t:2==e?a.formData.area=t:a.formData.street=t})),this.formData.provincials=e.map((function(t){return t.label})).join("/")},dateChange:function(t){this.formData.birthday=t},detailsPut:function(){var t=this,e=[];if(this.dataLabel.forEach((function(t){e.push(t.id)})),this.formData.label_id=e,this.formData.phone&&!/^1(3|4|5|7|8|9|6)\d{9}$/.test(this.formData.phone))return this.$Message.error("请填写正确的手机号");putUserApi(this.formData).then((function(e){t.$Message.success("修改成功"),t.$emit("change-menu","99")})).catch((function(e){t.$Message.error(e.msg)}))}}},B=z,U=(a("aa9f"),Object(u["a"])(B,P,E,!1,null,"6e0aa0f0",null)),A=U.exports,H=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"user-info"},[a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("基本信息")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("用户编号：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.uid))])]),a("div",{staticClass:"item"},[a("div",[t._v("真实姓名：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.real_name||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("手机号码：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.phone||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("生日：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.birthday||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("性别：")]),t.psInfo.sex?a("div",{staticClass:"value"},[t._v(t._s(1==t.psInfo.sex?"男":"女"))]):a("div",{staticClass:"value"},[t._v("保密")])]),a("div",{staticClass:"item"},[a("div",[t._v("身份证号：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.card_id||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("用户地址：")]),a("div",{staticClass:"value"},[t._v(t._s(""+t.psInfo.provincials+t.psInfo.addres||"-"))])])])]),t._m(0),a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("用户概况")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("推广资格：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.spread_open?"启用":"禁用"))])]),a("div",{staticClass:"item"},[a("div",[t._v("用户状态：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.status?"开启":"锁定"))])]),a("div",{staticClass:"item"},[a("div",[t._v("用户等级：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.level))])]),a("div",{staticClass:"item"},[a("div",[t._v("用户标签：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.label_list))])]),a("div",{staticClass:"item"},[a("div",[t._v("用户分组：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.group_name||"无"))])]),a("div",{staticClass:"item"},[a("div",[t._v("推广人：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.spread_uid_nickname||"无"))])]),a("div",{staticClass:"item"},[a("div",[t._v("注册时间：")]),a("div",{staticClass:"value"},[t._v(t._s(t._f("timeFormat")(t.psInfo.add_time)))])]),a("div",{staticClass:"item"},[a("div",[t._v("登录时间：")]),a("div",{staticClass:"value"},[t._v(t._s(t._f("timeFormat")(t.psInfo.last_time)))])])])]),a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("用户备注")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("备注：")]),a("div",{staticClass:"value"},[t._v(t._s(t.psInfo.mark||"-"))])])])]),t.workMemberInfo?a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("企业成员信息")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.workMemberInfo.qr_code,alt:""}})])])]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("姓名：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.name||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("职务信息：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.position||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("手机号码：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.mobile||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("性别：")]),a("div",{staticClass:"value"},[t._v(t._s(t._f("gender")(t.workMemberInfo.gender)))])]),a("div",{staticClass:"item"},[a("div",{staticStyle:{width:"40px"}},[t._v("邮箱：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.biz_mail||"-"))])]),a("div",{staticClass:"item",staticStyle:{"margin-right":"30px"}},[a("div",[t._v("地址：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.address||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("备注：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.remark||"-"))])])])]):t._e(),t.workClientInfo?a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("企业客户信息")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("姓名：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.name||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("职务信息：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.position||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("备注：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.remark||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("性别：")]),a("div",{staticClass:"value"},[t._v(t._s(t._f("gender")(t.workClientInfo.gender)))])]),a("div",{staticClass:"item"},[a("div",[t._v("企业主体名称：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.corp_full_name||"-"))])]),a("div",{staticClass:"item"},[a("div",[t._v("企业主体简称：")]),a("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.corp_name||"-"))])])])]):t._e()])},K=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[t._v("密码")]),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[t._v("登录密码：")]),a("div",{staticClass:"value"},[t._v("********")])])])])}],R=a("5a0c"),Y=a.n(R),V={name:"userInfo",props:{psInfo:Object,workMemberInfo:Object,workClientInfo:Object},filters:{timeFormat:function(t){return t?Y()(1e3*t).format("YYYY-MM-DD HH:mm:ss"):"-"},gender:function(t){return 1==t?"男":2==t?"女":"未知"}}},J=V,Q=(a("dc6f"),Object(u["a"])(J,H,K,!1,null,"29d37a65",null)),G=Q.exports;function X(t,e,a,s,i,n,r){try{var o=t[n](r),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(s,i)}function Z(t){return function(){var e=this,a=arguments;return new Promise((function(s,i){var n=t.apply(e,a);function r(t){X(n,s,i,r,o,"next",t)}function o(t){X(n,s,i,r,o,"throw",t)}r(void 0)}))}}var tt={name:"userDetails",components:{userForm:A,userInfo:G},props:["levelList","labelList","groupList","fromType"],data:function(){return{theme2:"light",list:[{val:"info",label:"用户信息"},{val:"order",label:"消费记录"},{val:"integral",label:"积分明细"},{val:"sign",label:"签到记录"},{val:"coupon",label:"持有优惠券"},{val:"balance_change",label:"余额变动"},{val:"spread",label:"好友关系"},{val:"visit",label:"浏览足迹"},{val:"spread_change",label:"推荐人变更记录"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"info",page:1,limit:12},total:0,columns:[],userLists:[],psInfo:{},workMemberInfo:{},workClientInfo:{},activeName:"info",isEdit:!1,groupOptions:[],labelOptions:[]}},watch:{activeName:function(t){this.userFrom.page=1,"info"!=t&&(this.isEdit=!1,this.changeType(t))},modals:function(t){t&&(this.isEdit=!1)}},created:function(){},methods:{changeMenu:function(t){if("99"===t)return this.getDetails(this.userId),this.$parent.getList(),void(this.isEdit=!1);this.$parent.changeMenu(this.psInfo,t)},finish:function(){this.$refs.userForm[0].detailsPut()},getDetails:function(t){var e=this;this.userId=t,this.spinShow=!0,Object(l["c"])(t).then(function(){var t=Z(N.a.mark((function t(a){var s,i;return N.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===a.status?(s=a.data,e.detailsData=s.headerList,"order"!==e.fromType&&(i=e.groupList.find((function(t){return t.id==s.ps_info.group_id})),i&&(s.ps_info.group_name=i.group_name)),e.psInfo=s.ps_info,e.workMemberInfo=s.workMemberInfo,e.workClientInfo=s.workClientInfo,e.spinShow=!1):(e.spinShow=!1,e.$Message.error(a.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},pageChange:function(t){switch(this.userFrom.page=t,this.activeName){case"visit":this.changeType(this.userFrom.type);break;case"spread_change":this.changeType(this.userFrom.type);break;default:console.log(this.userFrom.type),this.changeType(this.userFrom.type);break}},changeType:function(t){var e=this;this.loading=!0,this.userFrom.type=t,console.log(this.userFrom),this.activeName=t;var a={id:this.userId,datas:this.userFrom};Object(l["e"])(a).then(function(){var t=Z(N.a.mark((function t(a){var s;return N.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(200!==a.status){t.next=25;break}s=a.data,e.userLists=s.list,e.total=s.count,t.t0=e.userFrom.type,t.next="order"===t.t0?7:"integral"===t.t0?9:"sign"===t.t0?11:"coupon"===t.t0?13:"balance_change"===t.t0?15:"visit"===t.t0?17:"spread_change"===t.t0?19:21;break;case 7:return e.columns=[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"商品总价",key:"total_price",minWidth:110},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],t.abrupt("break",22);case 9:return e.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"number",minWidth:120},{title:"变化前积分",key:"balance",minWidth:120},{title:"日期",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",22);case 11:return e.columns=[{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",22);case 13:return e.columns=[{title:"优惠券名称",key:"coupon_title",minWidth:120},{title:"面值",slot:"coupon_price",minWidth:120},{title:"有效期(天)",key:"coupon_time",minWidth:120},{title:"兑换时间",key:"_add_time",minWidth:120}],t.abrupt("break",22);case 15:return e.columns=[{title:"动作",key:"title",minWidth:120},{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",22);case 17:return e.columns=[{title:"商品信息",slot:"product",minWidth:400},{title:"价格",key:"product_price",minWidth:120,render:function(t,e){return t("div","¥".concat(e.row.product_price))}},{title:"浏览时间",key:"add_time",minWidth:120}],t.abrupt("break",22);case 19:return e.columns=[{title:"推荐人ID",key:"spread_uid",minWidth:120},{title:"推荐人",key:"nickname",minWidth:120,render:function(t,e){return t("div",[t("img",{style:{borderRadius:"50%",marginRight:"10px",verticalAlign:"middle"},attrs:{with:38,height:38},directives:[{name:"lazy",value:e.row.avatar},{name:"viewer"}]}),t("span",{style:{verticalAlign:"middle"}},e.row.nickname)])}},{title:"变更方式",key:"type",minWidth:120},{title:"变更时间",key:"spread_time",minWidth:120}],t.abrupt("break",22);case 21:e.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"type",minWidth:120},{title:"加入时间",key:"add_time",minWidth:120}];case 22:e.loading=!1,t.next=27;break;case 25:e.loading=!1,e.$Message.error(a.msg);case 27:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))}}},et=tt,at=(a("c637"),a("933bc"),Object(u["a"])(et,W,q,!1,null,"2dd17b32",null)),st=at.exports,it=a("2f62");function nt(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,s)}return a}function rt(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?nt(a,!0).forEach((function(e){ot(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):nt(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function ot(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var lt={name:"user",components:{userDetails:st,userLabel:M,Recharges:C,Setusers:v,Paying:L},data:function(){return{total:0,loading:!1,groupList:[],columns:[{type:"selection",width:60,align:"center"},{title:"ID",key:"uid",width:60},{title:"头像",slot:"avatars",minWidth:60},{title:"昵称",slot:"nickname",minWidth:150},{title:"付费会员",slot:"isMember",minWidth:90},{title:"用户等级",key:"level",minWidth:90},{title:"标签",key:"labels",minWidth:90},{title:"手机号",key:"phone",minWidth:100},{title:"用户类型",key:"user_type",minWidth:100},{title:"余额",key:"now_money",sortable:"custom",minWidth:100},{title:"关联店员",key:"staff_name",minWidth:100},{title:"操作",slot:"action",fixed:"right",minWidth:290,align:"center"}],dataList:[],datanew:[],dataid:[],userFrom:{keyword:"",page:1,limit:15,field_key:"all"},labelShow:!1,labelActive:{uid:0}}},computed:rt({},Object(it["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),mounted:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(l["m"])(this.userFrom).then((function(e){t.loading=!1,t.total=e.data.count,t.dataList=e.data.list})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},userSearchs:function(){var t=this;this.userFrom.page=1,""==this.userFrom.keyword?this.getList():(this.loading=!0,Object(l["u"])(this.userFrom).then((function(e){t.dataList=e.data.list,t.total=e.data.count,t.loading=!1})))},reset:function(t){this.userFrom={keyword:"",field_key:"all",page:1,limit:15},this.getList()},select:function(t){this.datanew=t;var e=[];this.datanew.map((function(t){e.push(t.uid)})),this.dataid=e},selectall:function(t){if(0==t.length)this.dataid=[];else{this.datanew=t;var e=[];this.datanew.map((function(t){e.push(t.uid)})),this.dataid=e}},setLabel:function(){var t=this;if(0==this.datanew.length)this.$Message.warning("请选择要设置标签的用户");else{this.dataid.join(",");var e={all:0};e.uids=this.dataid,this.$modalForm(Object(l["p"])(e)).then((function(){return t.getList()}))}},detail:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(t.uid)},recharge:function(t){this.$refs.recharges.modals=!0,this.$refs.recharges.getList(t.uid)},paying:function(t){this.$refs.payings.modals=!0,this.$refs.payings.getList(t.uid)},setUser:function(t){this.$refs.setusers.modals=!0,this.$refs.setusers.getId(t.uid)},openLabel:function(t){this.labelShow=!0,this.labelActive.uid=t.uid},labelClose:function(){this.labelShow=!1,this.labelActive.uid=0,this.getList()},pageChange:function(t){this.userFrom.page=t,this.getList()},sortChanged:function(t){this.userFrom[t.key]=t.order,this.getList()}}},ct=lt,dt=(a("50d1"),Object(u["a"])(ct,s,i,!1,null,"5df59026",null));e["default"]=dt.exports},"20aa":function(t,e,a){},"30a5":function(t,e,a){t.exports=a.p+"view_store/img/svip-user.b89bb400.png"},"50d1":function(t,e,a){"use strict";var s=a("20aa"),i=a.n(s);i.a},"54af":function(t,e,a){},"54ba":function(t,e,a){"use strict";var s=a("bba9"),i=a.n(s);i.a},"612f":function(t,e,a){},"66fe":function(t,e,a){},"6d33":function(t,e,a){"use strict";var s=a("66fe"),i=a.n(s);i.a},"6e3f":function(t,e,a){},8195:function(t,e,a){"use strict";var s=a("6e3f"),i=a.n(s);i.a},"933bc":function(t,e,a){"use strict";var s=a("f7e6"),i=a.n(s);i.a},aa9f:function(t,e,a){"use strict";var s=a("612f"),i=a.n(s);i.a},bba9:function(t,e,a){},c24f:function(t,e,a){"use strict";a.d(e,"r",(function(){return i})),a.d(e,"k",(function(){return n})),a.d(e,"l",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"q",(function(){return l})),a.d(e,"j",(function(){return c})),a.d(e,"m",(function(){return d})),a.d(e,"u",(function(){return u})),a.d(e,"d",(function(){return m})),a.d(e,"f",(function(){return v})),a.d(e,"c",(function(){return f})),a.d(e,"e",(function(){return h})),a.d(e,"p",(function(){return p})),a.d(e,"n",(function(){return b})),a.d(e,"t",(function(){return _})),a.d(e,"o",(function(){return g})),a.d(e,"s",(function(){return w})),a.d(e,"g",(function(){return C})),a.d(e,"i",(function(){return k})),a.d(e,"b",(function(){return y})),a.d(e,"h",(function(){return I}));var s=a("b6bd");function i(){return Object(s["a"])({url:"user/user_label_cate",method:"get"})}function n(){return Object(s["a"])({url:"user/user_label_cate/create",method:"get"})}function r(t){return Object(s["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function o(t){return Object(s["a"])({url:"user/user_label",method:"get",params:t})}function l(){return Object(s["a"])({url:"user/user_label/create",method:"get"})}function c(t){return Object(s["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function d(t){return Object(s["a"])({url:"user/user",method:"get",params:t})}function u(t){return Object(s["a"])({url:"user/search",method:"get",params:t})}function m(t){return Object(s["a"])({url:"user/label/".concat(t),method:"get"})}function v(t,e){return Object(s["a"])({url:"user/label/".concat(t),method:"post",data:e})}function f(t){return Object(s["a"])({url:"user/user/".concat(t),method:"get"})}function h(t){return Object(s["a"])({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function p(t){return Object(s["a"])({url:"user/set_label",method:"post",data:t})}function b(){return Object(s["a"])({url:"user/recharge/meal",method:"get"})}function _(){return Object(s["a"])({url:"user/member/ship",method:"get"})}function g(t){return Object(s["a"])({url:"user/recharge",method:"post",data:t})}function w(t){return Object(s["a"])({url:"/user/member",method:"post",data:t})}function C(t){return Object(s["a"])({url:"staff/binding/user",method:"post",data:t})}function k(t){return Object(s["a"])({url:"updatePwd",method:"PUT",data:t})}function y(t,e){return Object(s["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function I(){return Object(s["a"])({url:"staff/staff_info",method:"get"})}},c637:function(t,e,a){"use strict";var s=a("54af"),i=a.n(s);i.a},d283:function(t,e,a){},dc6f:function(t,e,a){"use strict";var s=a("f4db"),i=a.n(s);i.a},f28f:function(t,e,a){"use strict";var s=a("d283"),i=a.n(s);i.a},f4db:function(t,e,a){},f7e6:function(t,e,a){}}]);