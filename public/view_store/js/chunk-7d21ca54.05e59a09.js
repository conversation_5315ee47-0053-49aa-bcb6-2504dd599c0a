(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d21ca54"],{"037e":function(t,e,a){},3707:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition}},[a("Row",{attrs:{type:"flex",gutter:24}},[a("Col",[a("FormItem",{attrs:{label:"配送员搜索："}},[a("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入ID/手机号",clearable:""},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}},[a("Select",{staticStyle:{width:"70px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.formValidate.field_key,callback:function(e){t.$set(t.formValidate,"field_key",e)},expression:"formValidate.field_key"}},[a("Option",{attrs:{value:"all"}},[t._v("全部")]),a("Option",{attrs:{value:"id"}},[t._v("ID")]),a("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1),a("Col",[a("div",{staticClass:"search",on:{click:t.search}},[t._v("搜索")])]),a("Col",[a("div",{staticClass:"reset",on:{click:t.reset}},[t._v("重置")])])],1)],1)],1),a("Card",{staticClass:"ive-mt tablebox",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"btnbox"},[a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["staff-delivery-create"],expression:"['staff-delivery-create']"}],attrs:{type:"primary"},on:{click:t.add}},[t._v("添加配送员")])],1),a("div",{staticClass:"table"},[a("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"status",fn:function(e){var i=e.row;e.index;return[a("i-switch",{attrs:{value:i.status,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.changeSwitch(i)}},model:{value:i.status,callback:function(e){t.$set(i,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"avatar",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"action",fn:function(e){var i=e.row,s=e.index;return[a("a",{on:{click:function(e){return t.edit(i.id)}}},[t._v("编辑")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.del(i.id,"删除该配送员",s)}}},[t._v("删除")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.details(i)}}},[t._v("查看详情")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)]),a("Details",{ref:"Details"})],1)},s=[],r=a("b4ea"),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"配送员详情","mask-closable":!1,width:"900"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e(),a("div",{staticClass:"acea-row"},[a("div",{staticClass:"avatar mr15"},[a("img",{attrs:{src:t.psInfo.avatar}})]),a("div",{staticClass:"dashboard-workplace-header-tip"},[a("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:t._s(t.psInfo.nickname||"-")}}),a("div",{staticClass:"dashboard-workplace-header-tip-desc"},t._l(t.detailsData,(function(e,i){return a("span",{key:i,staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v(t._s(e.title+"："+e.value+" "+e.key))])})),0)])]),a("Row",{staticClass:"mt25",attrs:{type:"flex",justify:"space-between"}},[a("Col",{attrs:{span:"24"}},[a("Table",{ref:"table",attrs:{columns:t.columns,data:t.userLists,"max-height":"400",loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1)],1)},o=[],l=a("a34a"),c=a.n(l);function d(t,e,a,i,s,r,n){try{var o=t[r](n),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(i,s)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var r=t.apply(e,a);function n(t){d(r,i,s,n,o,"next",t)}function o(t){d(r,i,s,n,o,"throw",t)}n(void 0)}))}}var h={name:"userDetails",data:function(){return{theme2:"light",modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"cashier_order",page:1,limit:10},total:0,columns:[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"商品总价",key:"total_price",minWidth:110},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],userLists:[],psInfo:{}}},created:function(){},methods:{getDetails:function(t){var e=this;this.userId=t,this.spinShow=!0,this.loading=!0,Object(r["c"])(t,this.userFrom).then(function(){var t=u(c.a.mark((function t(a){var i;return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===a.status?(i=a.data,e.detailsData=i.data,e.psInfo=i.info,e.userLists=i.list.list,e.total=i.list.count,e.spinShow=!1,e.loading=!1):(e.spinShow=!1,e.loading=!1,e.$Message.error(a.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.loading=!1,e.$Message.error(t.msg)}))},pageChange:function(t){this.userFrom.page=t,this.getDetails(this.userId)}}},f=h,m=(a("b328"),a("aa41"),a("2877")),p=Object(m["a"])(f,n,o,!1,null,"66554472",null),v=p.exports,g={name:"deliveryClerk",components:{Details:v},data:function(){return{total:0,a:12,grid:{xl:8,lg:7,md:12,sm:24,xs:24},loading:!1,columns:[{title:"ID",key:"id",width:60},{title:"头像",slot:"avatar",minWidth:80},{title:"昵称",key:"wx_name",minWidth:80},{title:"账号状态",slot:"status",minWidth:80},{title:"手机号",key:"phone",minWidth:100},{title:"操作",slot:"action",fixed:"right",minWidth:180,align:"center"}],orderList:[{phone:13e8,id:6,staff_name:"哈哈"}],formValidate:{field_key:"all",keyword:"",page:1,limit:15}}},computed:{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(r["d"])(this.formValidate).then((function(e){t.total=e.data.count,t.orderList=e.data.list,t.loading=!1})).catch((function(e){t.$Message.error(e.msg),t.loading=!1}))},add:function(){var t=this;this.$modalForm(Object(r["f"])()).then((function(){return t.getList()}))},edit:function(t){var e=this;this.$modalForm(Object(r["b"])(t)).then((function(){return e.getList()}))},del:function(t,e,a){var i=this,s={title:e,num:a,url:"staff/delivery/".concat(t),method:"DELETE",ids:""};this.$modalSure(s).then((function(t){i.$Message.success(t.msg),i.orderList.splice(a,1),i.orderList.length||(i.formValidate.page=1==i.formValidate.page?1:i.formValidate.page-1),i.getList()})).catch((function(t){i.$Message.error(t.msg)}))},search:function(){this.getList()},reset:function(){this.formValidate.field_key="all",this.formValidate.keyword="",this.getList()},changeSwitch:function(t){var e=this;Object(r["g"])(t.id,t.status).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(res.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList()},details:function(t){this.$refs.Details.modals=!0,this.$refs.Details.getDetails(t.id)}}},w=g,b=(a("6bad"),Object(m["a"])(w,i,s,!1,null,"1296820a",null));e["default"]=b.exports},"6bad":function(t,e,a){"use strict";var i=a("037e"),s=a.n(i);s.a},aa41:function(t,e,a){"use strict";var i=a("d3ee"),s=a.n(i);s.a},b328:function(t,e,a){"use strict";var i=a("ccba"),s=a.n(i);s.a},ccba:function(t,e,a){},d3ee:function(t,e,a){}}]);