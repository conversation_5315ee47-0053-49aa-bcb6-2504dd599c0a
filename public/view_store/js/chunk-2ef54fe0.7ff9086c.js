(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ef54fe0"],{"64e9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.addCategory}},[t._v("添加分类")]),r("vxe-table",{ref:"xTable",staticClass:"ivu-mt",attrs:{data:t.tableData,"highlight-hover-row":"",loading:t.loading,"header-row-class-name":"false","row-id":"id","tree-config":{lazy:!0,children:"children",hasChild:"children",loadMethod:t.loadChildrenMethod,reserve:!0}}},[r("vxe-table-column",{attrs:{field:"id",title:"ID",tooltip:"",width:"80"}}),r("vxe-table-column",{attrs:{field:"cate_name","tree-node":"",title:"分类名称","min-width":"250"}}),r("vxe-table-column",{attrs:{field:"pic",title:"分类图标","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;return[r("viewer",[r("div",{staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.pic,expression:"row.pic"}]})])])]}}])}),r("vxe-table-column",{attrs:{field:"sort",title:"排序","min-width":"100",tooltip:"true"}}),r("vxe-table-column",{attrs:{field:"is_show",title:"状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("i-switch",{attrs:{value:n.is_show,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.onchangeIsShow(n)}},model:{value:n.is_show,callback:function(e){t.$set(n,"is_show",e)},expression:"row.is_show"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("显示")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("隐藏")])])]}}])}),r("vxe-table-column",{attrs:{field:"date",title:"操作",width:"250",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row,o=e.index;return[r("a",{on:{click:function(e){return t.edit(n)}}},[t._v("编辑")]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.del(n,"删除商品分类",o)}}},[t._v("删除")])]}}])})],1)],1)],1)},o=[],u=r("a34a"),c=r.n(u),a=r("c4c8");function d(t,e,r,n,o,u,c){try{var a=t[u](c),d=a.value}catch(i){return void r(i)}a.done?e(d):Promise.resolve(d).then(n,o)}function i(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var u=t.apply(e,r);function c(t){d(u,n,o,c,a,"next",t)}function a(t){d(u,n,o,c,a,"throw",t)}c(void 0)}))}}var l={data:function(){return{tableData:[],loading:!1}},created:function(){this.productCategory()},methods:{productCategory:function(){var t=this;Object(a["l"])({pid:0}).then((function(e){t.tableData=e.data.list}))},addCategory:function(){var t=this;this.$modalForm(Object(a["m"])()).then((function(){t.productCategory()}))},edit:function(t){var e=this;this.$modalForm(Object(a["n"])(t.id)).then((function(){e.productCategory()}))},del:function(t,e,r){var n=this,o={title:e,num:r,url:"product/category/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(o).then((function(t){n.$Message.success(t.msg),n.productCategory()})).catch((function(t){n.$Message.error(t.msg)}))},loadChildrenMethod:function(t){var e=t.row;return new Promise((function(t,r){Object(a["l"])({pid:e.id}).then((function(e){var r=e.data.list;t(r)}))}))},onchangeIsShow:function(t){var e=this;Object(a["f"])(t.id,t.is_show).then(function(){var t=i(c.a.mark((function t(r){return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$Message.success(r.msg),e.productCategory();case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}}},s=l,f=(r("db26"),r("2877")),p=Object(f["a"])(s,n,o,!1,null,"412b1312",null);e["default"]=p.exports},b6d0:function(t,e,r){},c4c8:function(t,e,r){"use strict";r.d(e,"v",(function(){return o})),r.d(e,"s",(function(){return u})),r.d(e,"r",(function(){return c})),r.d(e,"z",(function(){return a})),r.d(e,"w",(function(){return d})),r.d(e,"F",(function(){return i})),r.d(e,"G",(function(){return l})),r.d(e,"k",(function(){return s})),r.d(e,"x",(function(){return f})),r.d(e,"u",(function(){return p})),r.d(e,"e",(function(){return h})),r.d(e,"g",(function(){return m})),r.d(e,"o",(function(){return b})),r.d(e,"q",(function(){return g})),r.d(e,"p",(function(){return v})),r.d(e,"d",(function(){return O})),r.d(e,"c",(function(){return j})),r.d(e,"j",(function(){return w})),r.d(e,"I",(function(){return _})),r.d(e,"h",(function(){return y})),r.d(e,"t",(function(){return C})),r.d(e,"i",(function(){return x})),r.d(e,"D",(function(){return k})),r.d(e,"B",(function(){return P})),r.d(e,"C",(function(){return S})),r.d(e,"J",(function(){return T})),r.d(e,"a",(function(){return $})),r.d(e,"H",(function(){return D})),r.d(e,"y",(function(){return M})),r.d(e,"A",(function(){return E})),r.d(e,"b",(function(){return z})),r.d(e,"l",(function(){return I})),r.d(e,"f",(function(){return U})),r.d(e,"n",(function(){return B})),r.d(e,"m",(function(){return F})),r.d(e,"E",(function(){return J})),r.d(e,"K",(function(){return G}));var n=r("b6bd");function o(t){return Object(n["a"])({url:"product/product",method:"get",params:t})}function u(t){return Object(n["a"])({url:"product/product/".concat(t),method:"get"})}function c(t){return Object(n["a"])({url:"product/type_header",method:"get",params:t})}function a(){return Object(n["a"])({url:"product/product_label",method:"get"})}function d(t){return Object(n["a"])({url:"product/reply",method:"get",params:t})}function i(t,e){return Object(n["a"])({url:"product/reply/set_reply/".concat(e),method:"PUT",data:t})}function l(t,e){return Object(n["a"])({url:"product/product/set_show/".concat(t,"/").concat(e),method:"PUT"})}function s(t){return Object(n["a"])({url:"product/product/attrs/".concat(t),method:"get"})}function f(t,e){return Object(n["a"])({url:"product/product/saveStocks/".concat(e),method:"PUT",data:t})}function p(t){return Object(n["a"])({url:"/product/product/list",method:"get",params:t})}function h(t){return Object(n["a"])({url:"product/category/cascader_list/".concat(t),method:"get"})}function m(t){return Object(n["a"])({url:"product/product/".concat(t.id),method:"POST",data:t})}function b(){return Object(n["a"])({url:"product/product/get_rule",method:"get"})}function g(){return Object(n["a"])({url:"product/product/get_template",method:"get"})}function v(t){return Object(n["a"])({url:"product/product/get_temp_keys",method:"get",params:t})}function O(){return Object(n["a"])({url:"product/cache",method:"delete"})}function j(){return Object(n["a"])({url:"product/brand/cascader_list/2",method:"get"})}function w(t){return Object(n["a"])({url:"product/get_all_unit",method:"get"})}function _(){return Object(n["a"])({url:"file/upload_type",method:"get"})}function y(){return Object(n["a"])({url:"product/all_ensure",method:"get"})}function C(){return Object(n["a"])({url:"product/label/form",method:"get"})}function x(){return Object(n["a"])({url:"product/all_specs",method:"get"})}function k(t){return Object(n["a"])({url:"product/product/rule",method:"GET",params:t})}function P(t,e){return Object(n["a"])({url:"product/product/rule/".concat(e),method:"POST",data:t})}function S(t){return Object(n["a"])({url:"product/product/rule/".concat(t),method:"get"})}function T(t){return Object(n["a"])({url:"file/video_attachment",method:"post",data:t})}function $(){return Object(n["a"])({url:"system/form/all_system_form",method:"get"})}function D(t,e){return Object(n["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function M(t){return Object(n["a"])({url:"product/product/product_show",method:"put",data:t})}function E(t){return Object(n["a"])({url:"product/product/product_unshow",method:"put",data:t})}function z(t){return Object(n["a"])({url:"product/batch_process",method:"post",data:t})}function I(t){return Object(n["a"])({url:"product/category",method:"get",params:t})}function U(t,e){return Object(n["a"])({url:"/product/category/set_show/".concat(t,"/").concat(e),method:"PUT"})}function B(t){return Object(n["a"])({url:"/product/category/".concat(t),method:"get"})}function F(){return Object(n["a"])({url:"/product/category/create",method:"get"})}function J(t,e){return Object(n["a"])({url:"product/product/cate/".concat(t),method:"post",data:e})}function G(){return Object(n["a"])({url:"/user/wechat/card",method:"get"})}},db26:function(t,e,r){"use strict";var n=r("b6d0"),o=r.n(n);o.a}}]);