(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0a4477"],{"0683":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":"",padding:0}},[i("div",{staticClass:"new_card_pd"},[i("Form",{ref:"levelFrom",attrs:{model:e.levelFrom,inline:"","label-width":e.labelWidth,"label-position":e.labelPosition},nativeOn:{submit:function(e){e.preventDefault()}}},[i("FormItem",{attrs:{label:"搜索：","label-for":"keyword"}},[i("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入模板名称"},model:{value:e.levelFrom.name,callback:function(t){e.$set(e.levelFrom,"name",t)},expression:"levelFrom.name"}}),i("Button",{staticClass:"btnOn",attrs:{type:"primary"},on:{click:e.userSearchs}},[e._v("查询")])],1)],1)],1)]),i("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[i("Button",{attrs:{type:"primary"},on:{click:e.freight}},[e._v("添加运费模板")]),i("Table",{ref:"table",staticClass:"mt25",attrs:{columns:e.columns1,data:e.levelLists,loading:e.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:e._u([{key:"icons",fn:function(e){var t=e.row;return[i("viewer",[i("div",{staticClass:"tabBox_img"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.icon,expression:"row.icon"}]})])])]}},{key:"action",fn:function(t){var n=t.row,r=t.index;return[i("a",{on:{click:function(t){return e.edit(n.id)}}},[e._v("修改")]),1!==n.id?i("Divider",{attrs:{type:"vertical"}}):e._e(),1!==n.id?i("a",{on:{click:function(t){return e.del(n,"删除运费模板",r)}}},[e._v("删除")]):e._e()]}}])}),i("div",{staticClass:"acea-row row-right page"},[i("Page",{attrs:{total:e.total,current:e.levelFrom.page,"show-elevator":"","show-total":"","page-size":e.levelFrom.limit},on:{"on-change":e.pageChange}})],1)],1),i("freight-template",{ref:"template"})],1)},r=[],a=i("a34a"),o=i.n(a),l=i("2f62"),s=i("90e7"),c=i("5334");function u(e,t,i,n,r,a,o){try{var l=e[a](o),s=l.value}catch(c){return void i(c)}l.done?t(s):Promise.resolve(s).then(n,r)}function d(e){return function(){var t=this,i=arguments;return new Promise((function(n,r){var a=e.apply(t,i);function o(e){u(a,n,r,o,l,"next",e)}function l(e){u(a,n,r,o,l,"throw",e)}o(void 0)}))}}function m(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function p(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?m(i,!0).forEach((function(t){h(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):m(i).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function h(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var f={name:"setting_templates",components:{freightTemplate:c["a"]},data:function(){return{grid:{xl:7,lg:7,md:12,sm:24,xs:24},loading:!1,columns1:[{title:"ID",key:"id",width:80},{title:"模板名称",key:"name",minWidth:100},{title:"计费方式",key:"type",minWidth:120},{title:"指定包邮",key:"appoint",minWidth:120},{title:"排序",key:"sort",minWidth:120},{title:"添加时间",key:"add_time",minWidth:120},{title:"操作",slot:"action",fixed:"right",minWidth:120}],levelFrom:{name:"",page:1,limit:15},levelLists:[],total:0,FromData:null}},created:function(){this.getList()},computed:p({},Object(l["e"])("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:75},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:{freight:function(){this.$refs.template.id=0,this.$refs.template.isTemplate=!0},del:function(e,t,i){var n=this,r={title:t,num:i,url:"setting/shipping_templates/del/".concat(e.id),method:"DELETE",ids:""};this.$modalSure(r).then((function(e){n.$Message.success(e.msg),n.levelLists.splice(i,1),n.levelLists.length||(n.levelFrom.page=1==n.levelFrom.page?1:n.levelFrom.page-1),n.getList()})).catch((function(e){n.$Message.error(e.msg)}))},getList:function(){var e=this;this.loading=!0,Object(s["J"])(this.levelFrom).then(function(){var t=d(o.a.mark((function t(i){var n;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=i.data,e.levelLists=n.data,e.total=n.count,e.loading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},pageChange:function(e){this.levelFrom.page=e,this.getList()},edit:function(e){this.$refs.template.isTemplate=!0,this.$refs.template.editFrom(e)},userSearchs:function(){this.levelFrom.page=1,this.getList()}}},v=f,g=i("2877"),b=Object(g["a"])(v,n,r,!1,null,null,null);t["default"]=b.exports}}]);