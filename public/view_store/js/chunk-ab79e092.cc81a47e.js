(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ab79e092"],{a401:function(t,e,i){},bd3a:function(t,e,i){"use strict";var a=i("a401"),s=i.n(a);s.a},f617:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"resize",rawName:"v-resize",value:t.handleResize,expression:"handleResize"}]},[i("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[i("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition}},[i("Row",[i("Col",{staticClass:"mr"},[i("FormItem",{attrs:{label:"选择配送员："}},[i("Select",{staticStyle:{width:"250px"},attrs:{placeholder:"请选择",clearable:""},on:{"on-change":t.searchs},model:{value:t.formValidate.delivery_uid,callback:function(e){t.$set(t.formValidate,"delivery_uid",e)},expression:"formValidate.delivery_uid"}},t._l(t.select,(function(e,a){return i("Option",{attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),i("Col",[i("FormItem",{attrs:{label:"订单时间："}},[i("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1)],1)],1),i("Row",{staticClass:"ivu-mt Box",attrs:{gutter:24}},[i("Col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[i("div",{staticClass:"ivu-pl-8 fonts"},[t._v("配送订单趋势")]),t.infoList?i("echarts-from",{ref:"visitChart",attrs:{series:t.series,echartsTitle:t.inlie,infoList:t.infoList,yAxisData:t.yAxisData}}):t._e()],1)],1),i("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[i("div",{staticClass:"fonts"},[t._v("配送数据")]),i("Table",{ref:"selection",attrs:{columns:t.columns4,data:t.tabList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"uid",fn:function(e){var a=e.row;return[i("span",[t._v(t._s(a.nickname)+" / "+t._s(a.uid))])]}}])}),i("div",{staticClass:"acea-row row-right page"},[i("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},s=[],n=i("a34a"),o=i.n(n),l=i("b4ea"),r=i("9901"),d=i("0b65"),c=i("61f7");function h(t,e,i,a,s,n,o){try{var l=t[n](o),r=l.value}catch(d){return void i(d)}l.done?e(r):Promise.resolve(r).then(a,s)}function m(t){return function(){var e=this,i=arguments;return new Promise((function(a,s){var n=t.apply(e,i);function o(t){h(n,a,s,o,l,"next",t)}function l(t){h(n,a,s,o,l,"throw",t)}o(void 0)}))}}var u={name:"bill",components:{echartsFrom:r["a"]},data:function(){return{total:0,select:[],grid:{xl:7,lg:10,md:12,sm:24,xs:24},grids:{xl:19,lg:7,md:12,sm:24,xs:24},loading:!1,optionData:{},formValidate:{delivery_uid:"0",data:"yesterday",page:1,limit:15},options:d["a"],timeVal:[],fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"昨天",val:"yesterday"},{text:"今天",val:"today"},{text:"最近7天",val:"sevenday"},{text:"近30天",val:"thirtyday"},{text:"本月",val:"month"},{text:"本年",val:"year"}]},extractStatistics:{price:"11",brokerage_count:"23",priced:"34"},series:[],yAxisData:[],infoList:{},infoLists:{},circle:"circle",inlie:"inlie",columns4:[{title:"订单号",key:"order_id",width:200},{title:"用户信息",width:120,slot:"uid"},{title:"实际支付",key:"pay_price",minWidth:100},{title:"订单类型",key:"pink_name",minWidth:100},{title:"支付方式",key:"pay_type_name",minWidth:100},{title:"配送员名称",key:"delivery_name",minWidth:100},{title:"下单时间",key:"add_time",minWidth:120}],tabList:[]}},computed:{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}},created:function(){var t=new Date,e=new Date;e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),this.timeVal=[e,t],this.formValidate.data=Object(c["a"])(e,"yyyy/MM/dd")+"-"+Object(c["a"])(t,"yyyy/MM/dd"),this.getStatistics(),this.storeList(),this.getList()},methods:{storeList:function(){var t=this;Object(l["l"])().then((function(e){t.select=e.data}))},getList:function(){var t=this;this.loading=!0,Object(l["e"])(this.formValidate).then((function(e){t.tabList=e.data.list,t.total=e.data.count,t.loading=!1}))},searchs:function(){this.getList(),this.getStatistics()},getStatistics:function(){var t=this;this.formValidate.delivery_uid||(this.formValidate.delivery_uid=0),Object(l["k"])(this.formValidate).then(function(){var e=m(o.a.mark((function e(i){return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.infoList=i.data||{},t.series=t.infoList.series||[],t.yAxisData=[{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}];case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.getList(),this.getStatistics()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",""==t[0]&&(this.formValidate.data="yesterday"),this.formValidate.page=1,this.getList(),this.getStatistics()},handleResize:function(){this.infoList&&this.$refs.visitChart.handleResize()},pageChange:function(t){this.formValidate.page=t,this.getList()}}},f=u,v=(i("bd3a"),i("2877")),y=Object(v["a"])(f,a,s,!1,null,"0e900240",null);e["default"]=y.exports}}]);