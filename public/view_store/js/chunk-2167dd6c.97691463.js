(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2167dd6c"],{"1ccf8":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{attrs:{bordered:!1,"dis-hover":""}},[n("div",{staticClass:"mb15"},[n("Button",{staticClass:"mr10",attrs:{type:"primary"},on:{click:t.modalToggle}},[t._v("添加桌码")]),n("Button",{attrs:{disabled:!t.QRCodeSelected.length,type:"primary"},on:{click:t.onBatchDownload}},[t._v("批量下载")])],1),n("Table",{attrs:{columns:t.columns,data:t.tableQRCodeList,loading:t.loading},on:{"on-selection-change":t.onSelectionChange},scopedSlots:t._u([{key:"qrcode",fn:function(t){var e=t.row;return[n("viewer",[n("div",{staticClass:"tabBox_img"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.qrcode,expression:"row.qrcode"}]})])])]}},{key:"is_using",fn:function(e){var r=e.row;return[n("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.isUsingChange(r.id,e)}},model:{value:r.is_using,callback:function(e){t.$set(r,"is_using",e)},expression:"row.is_using"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("启用")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("停用")])])]}},{key:"category",fn:function(e){var r=e.row;return[n("div",[t._v(t._s(r.category.name))])]}},{key:"action",fn:function(e){var r=e.row;return[n("a",{on:{click:function(e){return t.onEdit(r)}}},[t._v("编辑")]),n("Divider",{attrs:{type:"vertical"}}),n("a",{on:{click:function(e){return t.onDownload(r)}}},[t._v("下载")]),n("Divider",{attrs:{type:"vertical"}}),n("a",{on:{click:function(e){return t.onDelete(r.id)}}},[t._v("删除")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,current:t.page,"page-size":t.limit,"show-total":""},on:{"on-change":t.onChange}})],1)],1),n("Modal",{attrs:{"mask-closable":!1,title:(t.id?"编辑":"新增")+"桌号",width:"540","class-name":"add-modal-wrap","footer-hide":""},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[n("Form",{attrs:{model:t.formValidate,"label-width":96}},[n("FormItem",{attrs:{label:"桌台类型："}},[n("Select",{model:{value:t.formValidate.cate_id,callback:function(e){t.$set(t.formValidate,"cate_id",e)},expression:"formValidate.cate_id"}},t._l(t.tableCateList,(function(e){return n("Option",{key:e.id,attrs:{value:e.id}},[t._v(t._s(e.name))])})),1)],1),n("FormItem",{attrs:{label:"餐桌座位："}},[n("Select",{model:{value:t.formValidate.seat_num,callback:function(e){t.$set(t.formValidate,"seat_num",e)},expression:"formValidate.seat_num"}},t._l(t.tableSeatsList,(function(e){return n("Option",{key:e.id,attrs:{value:e.number}},[t._v(t._s(e.number))])})),1)],1),n("FormItem",{attrs:{label:"桌号："}},[n("div",[t._l(t.formValidate.number,(function(e,r){return n("Tag",{key:r,staticClass:"tag",attrs:{size:"large"}},[t._v(t._s(e))])})),n("div",{staticClass:"number-wrap"},[n("InputNumber",{staticClass:"input",attrs:{min:1,precision:0},model:{value:t.number,callback:function(e){t.number=e},expression:"number"}}),n("Button",{staticClass:"btn",attrs:{type:"primary"},on:{click:t.addNumber}},[t._v("添加")])],1)],2),n("div",{staticStyle:{"font-size":"12px",color:"#999999"}},[t._v("\n          输入桌号后，请点击“添加”按钮确认\n        ")])]),n("FormItem",{attrs:{label:"备注："}},[n("Input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:5},maxlength:"120","show-word-limit":""},model:{value:t.formValidate.remarks,callback:function(e){t.$set(t.formValidate,"remarks",e)},expression:"formValidate.remarks"}})],1)],1),n("div",{staticClass:"footer"},[n("Button",{staticClass:"mr10",on:{click:t.modalToggle}},[t._v("取消")]),n("Button",{staticClass:"mr10",on:{click:function(e){return t.addTableQRCode(1)}}},[t._v("保存并启用")]),n("Button",{attrs:{type:"primary"},on:{click:function(e){return t.addTableQRCode(0)}}},[t._v("仅保存")])],1)],1)],1)},o=[],a=(n("21a6"),n("90e7"));function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(n,!0).forEach((function(e){c(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function c(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var u={data:function(){return{columns:[{type:"selection",width:60,align:"center"},{title:"桌码名称",key:"table_number"},{title:"二维码",slot:"qrcode"},{title:"关联分类",slot:"category"},{title:"创建时间",key:"add_time"},{title:"备注",key:"remarks"},{title:"状态",slot:"is_using"},{title:"操作",slot:"action",align:"center"}],tableQRCodeList:[],total:0,limit:10,page:1,modal:!1,tableCateList:[],tableSeatsList:[],formValidate:{cate_id:0,seat_num:0,number:[],is_using:0,remarks:""},number:1,id:0,loading:!1,QRCodeSelected:[]}},created:function(){this.getTableQRCodeList(),this.getTableCateList(),this.getTableSeatsList()},methods:{onSelectionChange:function(t){this.QRCodeSelected=t},getTableQRCodeList:function(){var t=this;this.loading=!0,Object(a["r"])({page:this.page,limit:this.limit}).then((function(e){t.loading=!1,t.tableQRCodeList=e.data.list,t.total=e.data.count}))},getTableCateList:function(){var t=this;Object(a["q"])().then((function(e){t.tableCateList=e.data.data}))},getTableSeatsList:function(){var t=this;Object(a["s"])().then((function(e){t.tableSeatsList=e.data}))},onChange:function(t){this.page=t,this.getTableQRCodeList()},onEdit:function(t){var e=t.cate_id,n=t.seat_num,r=t.is_using,o=t.remarks,a=t.table_number,i=t.id;this.modal=!0,this.formValidate={cate_id:e,seat_num:n,is_using:r,remarks:o,number:[]},this.number=a,this.id=i},onDownload:function(t){if("string"!=typeof t.qrcode||!t.qrcode)return this.$Message.error("无二维码图片链接");new Promise((function(e,n){var r=new Image;r.crossOrigin="anonymous",r.src=t.qrcode,r.onload=function(){e(r)}})).then((function(e){var n=document.createElement("canvas"),r=n.getContext("2d");n.width=e.width,n.height=e.height,r.drawImage(e,0,0,e.width,e.height);var o=n.toDataURL("image/jpeg"),a=document.createElement("a"),i=new MouseEvent("click");a.download=t.table_number,a.href=o,a.dispatchEvent(i)}))},onDelete:function(t){var e=this;this.$modalSure({title:"删除桌码",url:"/table/del/qrcode/".concat(t),method:"delete",ids:""}).then((function(t){e.$Message.success(t.msg),e.page>1&&1==e.tableQRCodeList.length&&e.page--,e.getTableQRCodeList()}))},onBatchDownload:function(){if(!this.QRCodeSelected[0].qrcode)return this.$Message.error("无二维码图片链接");this.QRCodeSelected.forEach(this.onDownload)},modalToggle:function(){this.modal=!this.modal,this.formValidate={cate_id:0,seat_num:0,number:[],is_using:0,remarks:""},this.number=1,this.id=0},addTableQRCode:function(t){var e=this;if(!this.formValidate.cate_id)return this.$Message.error("请选择桌台类型");if(!this.formValidate.seat_num)return this.$Message.error("请选择餐桌座位");if(!this.formValidate.number.length){if(!this.id)return this.$Message.error("请添加桌码");var n=this.tableQRCodeList.find((function(t){return t.id==e.id}));this.formValidate.number=[n.table_number]}var r=s({},this.formValidate);r.is_using=t,Object(a["c"])(this.id,r).then((function(t){e.$Message.success(t.msg),e.modal=!1,e.getTableQRCodeList()})).catch((function(t){e.$Message.error(t.msg)}))},addNumber:function(){this.formValidate.number.push(this.number),this.formValidate.number=this.formValidate.number.filter((function(t,e,n){return n.indexOf(t)===e}))},isUsingChange:function(t,e){var n=this;Object(a["L"])(t,{is_using:e}).then((function(t){n.$Message.success(t.msg),n.getTableQRCodeList()})).catch((function(t){n.$Message.error(t.msg)}))}}},l=u,d=(n("a2fe"),n("2877")),f=Object(d["a"])(l,r,o,!1,null,"e9309812",null);e["default"]=f.exports},"21a6":function(t,e,n){(function(n){var r,o,a;(function(n,i){o=[],r=i,a="function"===typeof r?r.apply(e,o):r,void 0===a||(t.exports=a)})(0,(function(){"use strict";function e(t,e){return"undefined"==typeof e?e={autoBom:!1}:"object"!=typeof e&&(console.warn("Deprecated: Expected third argument to be a object"),e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob(["\ufeff",t],{type:t.type}):t}function r(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){c(r.response,e,n)},r.onerror=function(){console.error("could not download file")},r.send()}function o(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(t){}return 200<=e.status&&299>=e.status}function a(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(r){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var i="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof n&&n.global===n?n:void 0,s=i.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),c=i.saveAs||("object"!=typeof window||window!==i?function(){}:"download"in HTMLAnchorElement.prototype&&!s?function(t,e,n){var s=i.URL||i.webkitURL,c=document.createElement("a");e=e||t.name||"download",c.download=e,c.rel="noopener","string"==typeof t?(c.href=t,c.origin===location.origin?a(c):o(c.href)?r(t,e,n):a(c,c.target="_blank")):(c.href=s.createObjectURL(t),setTimeout((function(){s.revokeObjectURL(c.href)}),4e4),setTimeout((function(){a(c)}),0))}:"msSaveOrOpenBlob"in navigator?function(t,n,i){if(n=n||t.name||"download","string"!=typeof t)navigator.msSaveOrOpenBlob(e(t,i),n);else if(o(t))r(t,n,i);else{var s=document.createElement("a");s.href=t,s.target="_blank",setTimeout((function(){a(s)}))}}:function(t,e,n,o){if(o=o||open("","_blank"),o&&(o.document.title=o.document.body.innerText="downloading..."),"string"==typeof t)return r(t,e,n);var a="application/octet-stream"===t.type,c=/constructor/i.test(i.HTMLElement)||i.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||a&&c||s)&&"undefined"!=typeof FileReader){var l=new FileReader;l.onloadend=function(){var t=l.result;t=u?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=t:location=t,o=null},l.readAsDataURL(t)}else{var d=i.URL||i.webkitURL,f=d.createObjectURL(t);o?o.location=f:location.href=f,o=null,setTimeout((function(){d.revokeObjectURL(f)}),4e4)}});i.saveAs=c.saveAs=c,t.exports=c}))}).call(this,n("c8ba"))},"90e7":function(t,e,n){"use strict";n.d(e,"I",(function(){return o})),n.d(e,"A",(function(){return a})),n.d(e,"B",(function(){return i})),n.d(e,"u",(function(){return s})),n.d(e,"G",(function(){return c})),n.d(e,"f",(function(){return u})),n.d(e,"d",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"t",(function(){return f})),n.d(e,"D",(function(){return m})),n.d(e,"E",(function(){return b})),n.d(e,"h",(function(){return g})),n.d(e,"J",(function(){return h})),n.d(e,"K",(function(){return p})),n.d(e,"C",(function(){return v})),n.d(e,"i",(function(){return y})),n.d(e,"k",(function(){return w})),n.d(e,"j",(function(){return O})),n.d(e,"l",(function(){return _})),n.d(e,"m",(function(){return j})),n.d(e,"F",(function(){return k})),n.d(e,"s",(function(){return C})),n.d(e,"a",(function(){return L})),n.d(e,"q",(function(){return R})),n.d(e,"b",(function(){return T})),n.d(e,"r",(function(){return S})),n.d(e,"c",(function(){return E})),n.d(e,"g",(function(){return x})),n.d(e,"L",(function(){return V})),n.d(e,"H",(function(){return M})),n.d(e,"n",(function(){return Q})),n.d(e,"o",(function(){return D})),n.d(e,"x",(function(){return B})),n.d(e,"v",(function(){return $})),n.d(e,"w",(function(){return q})),n.d(e,"p",(function(){return A})),n.d(e,"y",(function(){return U})),n.d(e,"z",(function(){return P}));var r=n("b6bd");function o(){return Object(r["a"])({url:"system/role",method:"get"})}function a(t){return Object(r["a"])({url:"system/role/".concat(t.id),method:"post",data:t})}function i(t){return Object(r["a"])({url:"system/role/".concat(t,"/edit"),method:"get"})}function s(){return Object(r["a"])({url:"system/menusList",method:"get"})}function c(t){return Object(r["a"])({url:"system/admin",method:"get",params:t})}function u(t,e){return Object(r["a"])({url:"system/admin/set_status/".concat(t,"/").concat(e),method:"put"})}function l(){return Object(r["a"])({url:"system/admin/create",method:"get"})}function d(t){return Object(r["a"])({url:"system/admin/".concat(t,"/edit"),method:"get"})}function f(){return Object(r["a"])({url:"config",method:"get"})}function m(){return Object(r["a"])({url:"system/store/info",method:"get"})}function b(t){return Object(r["a"])({url:"system/store/update",method:"put",data:t})}function g(t){return Object(r["a"])({url:"city",method:"get",params:t})}function h(t){return Object(r["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function p(t,e){return Object(r["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function v(t){return Object(r["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function y(t){return Object(r["a"])({url:"city",method:"get",params:t})}function w(t){return Object(r["a"])({url:"/order/delivery_order/list",method:"get",params:t})}function O(t){return Object(r["a"])({url:"/order/delivery_order/cancelForm/".concat(t),method:"get"})}function _(t){return Object(r["a"])({url:"/system/store/getBusiness/".concat(t),method:"get"})}function j(t){return Object(r["a"])({url:"/system/config/".concat(t),method:"get"})}function k(t,e){return Object(r["a"])({url:"/system/config/".concat(t),method:"post",data:e})}function C(){return Object(r["a"])({url:"/table/seats/list",method:"get"})}function L(t,e){return Object(r["a"])({url:"/table/add/seats/".concat(t),method:"get",params:e})}function R(t){return Object(r["a"])({url:"/table/cate/list",method:"get",params:t})}function T(t,e){return Object(r["a"])({url:"/table/add/cate/".concat(t),method:"get",params:e})}function S(t){return Object(r["a"])({url:"/table/qrcodes/list",method:"get",params:t})}function E(t,e){return Object(r["a"])({url:"/table/add/qrcode/".concat(t),method:"post",data:e})}function x(){return Object(r["a"])({url:"/system/cashierMenusList",method:"get"})}function V(t,e){return Object(r["a"])({url:"/table/update/using/".concat(t),method:"get",params:e})}function M(t,e){return Object(r["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function Q(t){return Object(r["a"])({url:"system/config/edit_new_build/"+t,method:"get"})}function D(t){return Object(r["a"])({url:"/system/printer/list",method:"get",params:t})}function B(t){return Object(r["a"])({url:"/system/printer/status/".concat(t.id,"/").concat(t.status),method:"post"})}function $(t,e){return Object(r["a"])({url:"/system/printer/".concat(t),method:"post",data:e})}function q(t){return Object(r["a"])({url:"/system/printer/info/".concat(t),method:"get"})}function A(t){return Object(r["a"])({url:"resolve/city",method:"get",params:t})}function U(t,e){return Object(r["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function P(t,e){return Object(r["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},"97c8":function(t,e,n){},a2fe:function(t,e,n){"use strict";var r=n("97c8"),o=n.n(r);o.a}}]);