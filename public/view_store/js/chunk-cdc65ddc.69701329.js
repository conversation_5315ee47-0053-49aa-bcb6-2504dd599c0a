(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cdc65ddc"],{"0b65":function(t,e,a){"use strict";e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},"2aa7":function(t,e,a){},"5c3ac":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[r("Form",{ref:"orderData",staticClass:"tabform",attrs:{model:t.orderData,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",[r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"商品类型："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.orderData.product_type,callback:function(e){t.$set(t.orderData,"product_type",e)},expression:"orderData.product_type"}},[r("Option",{attrs:{value:"0"}},[t._v("普通商品")]),r("Option",{attrs:{value:"6"}},[t._v("预约商品")]),r("Option",{attrs:{value:"4"}},[t._v("次卡商品")]),r("Option",{attrs:{value:"5"}},[t._v("卡项商品")])],1)],1)],1),r("Col",{staticClass:"ivu-text-left mr"},[r("FormItem",{attrs:{label:"创建时间："}},[r("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1),r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"活动类型："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.orderData.type,callback:function(e){t.$set(t.orderData,"type",e)},expression:"orderData.type"}},[r("Option",{attrs:{value:"0"}},[t._v("普通订单")]),r("Option",{attrs:{value:"1"}},[t._v("秒杀订单")]),r("Option",{attrs:{value:"2"}},[t._v("砍价订单")]),r("Option",{attrs:{value:"3"}},[t._v("拼团订单")]),r("Option",{attrs:{value:"4"}},[t._v("积分订单")]),r("Option",{attrs:{value:"5"}},[t._v("套餐订单")]),r("Option",{attrs:{value:"6"}},[t._v("预售订单")]),r("Option",{attrs:{value:"7"}},[t._v("新人订单")]),r("Option",{attrs:{value:"8"}},[t._v("抽奖订单")]),r("Option",{attrs:{value:"9"}},[t._v("拼单订单")]),r("Option",{attrs:{value:"10"}},[t._v("桌码订单")]),r("Option",{attrs:{value:"11"}},[t._v("卡项订单")]),r("Option",{attrs:{value:"12"}},[t._v("预约订单")])],1)],1)],1),r("Col",{staticClass:"ivu-text-left mr"},[r("FormItem",{attrs:{label:"选择店员："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.orderData.staff_id,callback:function(e){t.$set(t.orderData,"staff_id",e)},expression:"orderData.staff_id"}},t._l(t.staffData,(function(e){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label)+"\n                ")])})),1)],1)],1),r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"订单来源："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:""},on:{"on-change":t.userSearchs},model:{value:t.orderData.order_type,callback:function(e){t.$set(t.orderData,"order_type",e)},expression:"orderData.order_type"}},[r("Option",{attrs:{value:"106"}},[t._v("收银台订单")]),r("Option",{attrs:{value:"108"}},[t._v("线上订单")])],1)],1)],1),r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"支付方式："}},[r("Select",{staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.userSearchs},model:{value:t.orderData.pay_type,callback:function(e){t.$set(t.orderData,"pay_type",e)},expression:"orderData.pay_type"}},t._l(t.payList,(function(e){return r("Option",{key:e.id,attrs:{value:e.val}},[t._v(t._s(e.label))])})),1)],1)],1),r("Col",{staticClass:"mr"},[r("FormItem",{attrs:{label:"订单搜索：","label-for":"real_name"}},[r("Input",{staticStyle:{width:"250px"},attrs:{"enter-button":"",placeholder:"订单号/商品名称"},model:{value:t.orderData.real_name,callback:function(e){t.$set(t.orderData,"real_name",e)},expression:"orderData.real_name"}}),r("Button",{staticClass:"ml20 search",attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("搜索")])],1)],1)],1)],1)],1),r("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[Object.keys(t.tablists).length?r("div",{staticClass:"product_tabs tabbox mb20"},[r("Tabs",{on:{"on-click":t.userSearchs},model:{value:t.orderData.status,callback:function(e){t.$set(t.orderData,"status",e)},expression:"orderData.status"}},[r("TabPane",{attrs:{label:"全部",name:" "}}),r("TabPane",{attrs:{label:"待支付",name:"0"}}),r("TabPane",{attrs:{label:"待发货("+(t.tablists.unshipped||0)+")",name:"1"}}),r("TabPane",{attrs:{label:"待核销("+(t.tablists.write_off||0)+")",name:"5"}}),r("TabPane",{attrs:{label:"待收货",name:"2"}}),r("TabPane",{attrs:{label:"待评价",name:"3"}}),r("TabPane",{attrs:{label:"已核销",name:"6"}}),r("TabPane",{attrs:{label:"已完成",name:"4"}}),r("TabPane",{attrs:{label:"已退款",name:"-2"}})],1)],1):t._e(),r("Row",{attrs:{type:"flex"}},[106==t.orderData.order_type?r("Col",[r("Button",{directives:[{name:"auth",rawName:"v-auth",value:["order-cashier-cashier_scan"],expression:"['order-cashier-cashier_scan']"}],staticClass:"mr",attrs:{type:"primary"},on:{click:t.qrcodeShow}},[t._v("下载收银码")])],1):t._e(),r("Col",[r("Button",{directives:[{name:"auth",rawName:"v-auth",value:["order-export"],expression:"['order-export']"}],attrs:{icon:"ios-share-outline"},on:{click:t.exports}},[t._v("导出")])],1),r("Col",[r("Tooltip",{attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[r("Button",{staticClass:"ml15",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.printOreder}},[t._v("打印配货单")])],1)],1)],1),r("vxe-table",{ref:"xTable",staticClass:"ivu-mt",attrs:{loading:t.loading,"row-id":"id","expand-config":{accordion:!0},"checkbox-config":{reserve:!0},data:t.tableList},on:{"checkbox-all":t.checkboxAll,"checkbox-change":t.checkboxItem}},[r("vxe-column",{attrs:{type:"",width:"0"}}),r("vxe-column",{attrs:{type:"expand",width:"35"},scopedSlots:t._u([{key:"content",fn:function(e){var a=e.row;return[r("div",{staticClass:"tdinfo"},[r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商品总价：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.total_price)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("下单时间：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.add_time)}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("推广人：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.spread_nickname?a.spread_nickname:"无")}})])],1),r("Row",{staticClass:"expand-row"},[r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("用户备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.mark?a.mark:"无")}})]),r("Col",{attrs:{span:"8"}},[r("span",{staticClass:"expand-key"},[t._v("商家备注：")]),r("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.remark?a.remark:"无")}})])],1)],1)]}}])}),r("vxe-column",{attrs:{type:"checkbox",width:"100"},scopedSlots:t._u([{key:"header",fn:function(){return[r("div",[r("Dropdown",{attrs:{transfer:""},on:{"on-click":t.allPages},scopedSlots:t._u([{key:"list",fn:function(){return[r("DropdownMenu",[r("DropdownItem",{attrs:{name:"0"}},[t._v("当前页")]),r("DropdownItem",{attrs:{name:"1"}},[t._v("所有页")])],1)]},proxy:!0}])},[r("a",{staticClass:"acea-row row-middle",attrs:{href:"javascript:void(0)"}},[r("span",[t._v("全选("+t._s(1==t.isAll?t.total-t.checkUidList.length:t.checkUidList.length)+")")]),r("Icon",{attrs:{type:"ios-arrow-down"}})],1)])],1)]},proxy:!0}])}),r("vxe-column",{attrs:{field:"order_id",title:"订单号","min-width":"175"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("span",{staticStyle:{display:"block"},domProps:{textContent:t._s(a.order_id)}}),r("span",{directives:[{name:"show",rawName:"v-show",value:1===a.is_del&&null==a.delete_time,expression:"row.is_del === 1 && row.delete_time == null"}],staticStyle:{color:"#ed4014",display:"block"}},[t._v("用户已删除")])]}}])}),r("vxe-column",{attrs:{field:"nickname",title:"用户信息","min-width":"210"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[r("div",{staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[i.uid?r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:i.avatar,expression:"row.avatar"}]}):r("img",{attrs:{src:a("586c")}})]),i.uid?r("span",{staticClass:"tabBox_tit"},[r("a",{on:{click:function(e){return t.showUserInfo(i)}}},[t._v(t._s(i.nickname)),r("span",{staticStyle:{"margin-left":"5px"}},[t._v("/"+t._s(i.uid))])])]):r("span",{staticClass:"tabBox_tit"},[t._v("\n\t            游客"),r("span",{staticStyle:{"margin-left":"5px"}},[t._v("/"+t._s(i.uid))])]),null!=i.delete_time?r("span",{staticStyle:{color:"#ed4014"}},[t._v("\n\t            (已注销)")]):t._e()])]}}])}),r("vxe-column",{attrs:{field:"info",title:"商品信息","min-width":"330"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return t._l(a._info,(function(e,a){return r("div",{key:a,staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.image:e.cart_info.productInfo.image,expression:"\n\t                val.cart_info.productInfo.attrInfo\n\t                  ? val.cart_info.productInfo.attrInfo.image\n\t                  : val.cart_info.productInfo.image\n\t              "}]})]),r("span",{staticClass:"tabBox_tit"},[e.cart_info.is_gift?r("span",{staticClass:"font-color-red"},[t._v("[赠品]")]):t._e(),t._v(t._s(e.cart_info.productInfo.store_name+" | ")+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:"")+"\n\t          ")]),r("span",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.cart_info.truePrice+" x "+e.cart_info.cart_num))])])}))}}])}),r("vxe-column",{attrs:{field:"pay_price",title:"实际支付",align:"center","min-width":"70"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("span",[t._v(t._s(a.paid>0?a.pay_price:0))])]}}])}),r("vxe-column",{attrs:{field:"pay_type_name",title:"支付方式","min-width":"100"}}),r("vxe-column",{attrs:{field:"clerk_name",title:"店员","min-width":"100"}}),r("vxe-column",{attrs:{field:"add_time",title:"下单时间","min-width":"130"}}),r("vxe-column",{attrs:{field:"statusName",title:"订单状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.status_name?r("div",{staticClass:"pt5",domProps:{innerHTML:t._s(a.status_name.status_name)}}):t._e(),!a.is_all_refund&&a.refund.length?r("div",{staticClass:"trip"},[t._v("\n\t          部分退款中\n\t        ")]):t._e(),a.is_all_refund&&a.refund.length&&6!=a.refund_type?r("div",{staticClass:"trip"},[t._v("\n\t          退款中\n\t        ")]):t._e(),a.status_name?r("div",{staticClass:"pictrue-box"},t._l(a.status_name.pics||[],(function(e,i){return a.status_name.pics?r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:i},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e,expression:"item"}],staticClass:"pictrue mr10",attrs:{src:e}})]):t._e()})),0):t._e()]}}])}),r("vxe-column",{attrs:{field:"action",title:"操作",align:"center",width:"140",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[r("a",[2!==a._status&&8!==a._status&&4!==a.status||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time?t._e():r("a",{on:{click:function(e){return t.sendOrder(a)}}},[t._v("发送货")]),2!=a.shipping_type||0!=a.status&&5!=a.status||1!=a.paid||0!==a.refund_status||null!=a.delete_time?t._e():r("a",{on:{click:function(e){return t.bindWrite(a)}}},[t._v("立即核销")]),(2!==a._status&&!a.split.length||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time)&&(2!=a.shipping_type||0!=a.status&&5!=a.status||1!=a.paid||null!=a.delete_time||0!==a.refund_status)?t._e():r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.changeMenu(a,"2")}}},[t._v("订单详情")])],1)]}}])})],1),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,current:t.orderData.page,"show-elevator":"","show-total":"","page-size":t.orderData.limit},on:{"on-change":t.pageChange}})],1)],1),r("orderWriteOff",{ref:"writeOff",attrs:{orderNumId:t.orderNumId},on:{submitSuccess:t.submitSuccess}}),r("user-details",{ref:"userDetails"}),r("edit-from",{ref:"edits",attrs:{FromData:t.FromData},on:{submitFail:t.submitFail}}),r("details-from",{ref:"detailss",attrs:{orderDatalist:t.orderDatalist,orderId:t.orderId,"row-active":t.rowActive,formType:1}}),r("order-remark",{ref:"remarks",attrs:{orderId:t.orderId,currentTab:t.orderData.order_type},on:{submitFail:t.submitFail}}),r("order-record",{ref:"record"}),r("order-send",{ref:"send",attrs:{orderId:t.orderId,status:t.status,pay_type:t.pay_type},on:{submitFail:t.submitFail}}),r("Modal",{attrs:{title:"收款码","footer-hide":""},model:{value:t.modalCode,callback:function(e){t.modalCode=e},expression:"modalCode"}},[r("div",[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"acea-row row-around code"},[t.spin?r("Spin",{attrs:{fix:""}}):t._e(),r("div",{staticClass:"acea-row row-column-around row-between-wrapper"},[r("div",{staticClass:"QRpic"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.qrcode&&t.qrcode.wechat,expression:"qrcode && qrcode.wechat"}]})]),r("span",{staticClass:"mt10"},[t._v("公众号收款码")])]),r("div",{staticClass:"acea-row row-column-around row-between-wrapper"},[r("div",{staticClass:"QRpic"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.qrcode&&t.qrcode.routine,expression:"qrcode && qrcode.routine"}]})]),r("span",{staticClass:"mt10"},[t._v("小程序收款码")])])],1)])]),r("Modal",{attrs:{title:"手动退款",width:"960","class-name":"refund-modal"},on:{"on-visible-change":t.visibleChange},model:{value:t.refundModal,callback:function(e){t.refundModal=e},expression:"refundModal"}},[r("Form",{attrs:{"label-width":100}},[11==t.rowActive.type&&t.refundProduct[0]?r("FormItem",{attrs:{label:"卡项情况："}},[r("Card",{attrs:{"dis-hover":""}},[r("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[r("div",{staticClass:"flex-1"},[t._v(t._s(t.refundProduct[0].productInfo.store_name))]),1==t.refundProduct[0].productInfo.attrInfo.write_valid?r("div",[t._v("永久有效")]):2==t.refundProduct[0].productInfo.attrInfo.write_valid?r("div",[t._v("购买后"+t._s(t.refundProduct[0].productInfo.attrInfo.write_days)+"天有效")]):3==t.refundProduct[0].productInfo.attrInfo.write_valid?r("div",[t._v(t._s(t._f("timeFormat")(t.refundProduct[0].productInfo.attrInfo.write_start))+" - "+t._s(t._f("timeFormat")(t.refundProduct[0].productInfo.attrInfo.write_end)))]):t._e()]),r("div",{staticClass:"acea-row flex-wrap"},[r("div",{staticClass:"flex-33"},[t._v("购卡实付金额：￥"+t._s(t.rowActive.pay_price))]),r("div",{staticClass:"flex-33"},[t._v("数量：1")]),r("div",{staticClass:"flex-33"},[t._v("剩余金额：￥"+t._s(t.remainingPrice))]),r("div",{staticClass:"flex-33"},[t._v("已核销："+t._s(t.writeTimes-t.writeSurplusTimes)+"/"+t._s(t.writeTimes))]),r("div",{staticClass:"flex-33"},[t._v("\n                卡项权益：\n                "),r("Poptip",{attrs:{placement:"bottom",width:"300"}},[r("div",{staticClass:"cup"},[t._v("查看")]),r("div",{attrs:{slot:"content"},slot:"content"},t._l(t.cardBenefits,(function(e){return r("div",{key:e.id,staticClass:"acea-row row-middle pt-4 pb-4 fs-12"},[r("div",{staticClass:"flex-1 min-w-0 pr-8 white-space-normal line2"},[t._v(t._s(e.cart_info.productInfo.store_name)+t._s(e.cart_info.productInfo.attrInfo.suk))]),r("div",[t._v(t._s(e.write_times)+"次（已使用"+t._s(e.write_times-e.write_surplus_times)+"次）")])])})),0)])],1)])])],1):t._e(),r("FormItem",{attrs:{label:"退款金额：",required:""}},[r("InputNumber",{staticClass:"w-408",model:{value:t.refundMoney,callback:function(e){t.refundMoney=e},expression:"refundMoney"}})],1),r("FormItem",{attrs:{label:"退款说明："}},[r("Input",{staticClass:"w-408",attrs:{placeholder:"请输入退款说明"},model:{value:t.refund_explain,callback:function(e){t.refund_explain=e},expression:"refund_explain"}})],1),this.refundProductNum>1?r("FormItem",{attrs:{label:"分单退款："}},[r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.is_split_order,callback:function(e){t.is_split_order=e},expression:"is_split_order"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"tips"},[t._v("可选择表格中的商品单独退款，退款后且不能撤回，请谨慎操作！")]),r("Table",{directives:[{name:"show",rawName:"v-show",value:t.is_split_order,expression:"is_split_order"}],ref:"refundTable",attrs:{"max-height":"500",columns:t.refundColumns,data:t.refundProduct},on:{"on-selection-change":t.refundSelectionChange},scopedSlots:t._u([{key:"product",fn:function(e){var a=e.row;return[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image-wrap"},[r("img",{staticClass:"image",attrs:{src:a.productInfo.attrInfo.image}})]),r("div",{staticClass:"title"},[t._v(t._s(a.productInfo.store_name))])]}},{key:"action",fn:function(e){var a=e.row;return[r("InputNumber",{attrs:{max:a.cart_num-a.refund_num,min:1,precision:0,"controls-outside":""},on:{"on-change":function(e){return t.refundNumChange(a)}},model:{value:a.refundNum,callback:function(e){t.$set(a,"refundNum",e)},expression:"row.refundNum"}})]}}],null,!1,1307172905)})],1):t._e()],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancelRefundModal}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.putOpenRefund}},[t._v("提交")])],1)],1)],1)},i=[],s=a("a34a"),n=a.n(s),o=a("2f62"),l=a("d708"),c=a("a464"),d=a("759d"),u=a("31b4"),f=a("1a84"),m=a("91b4"),h=a("5465"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单核销",width:"1000","footer-hide":""},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Card",{attrs:{bordered:!1,"dis-hover":""}},[a("Table",{ref:"selection",attrs:{columns:t.columns,border:"",data:t.writeOffData,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var r=e.row;e.index;return[a("div",{staticClass:"product-data"},[a("img",{staticClass:"image",attrs:{src:r.cart_info.productInfo.image}}),a("div",{staticClass:"name line2"},[t._v("\n\t\t\t\t\t\t\t\t\t \t\t\t"+t._s(r.cart_info.productInfo.store_name)+"\n\t\t\t\t\t\t\t\t\t \t\t")])])]}},{key:"value",fn:function(e){var r=e.row;e.index;return[a("div",[t._v(t._s(r.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var r=e.row;e.index;return[a("div",[t._v(t._s(r.cart_info.productInfo.attrInfo?r.cart_info.productInfo.attrInfo.price:r.cart_info.productInfo.price))])]}},{key:"price",fn:function(e){var r=e.row;e.index;return[a("div",[t._v(t._s(r.cart_info.truePrice))])]}},{key:"writeOff",fn:function(e){var r=e.row;e.index;return[r.is_writeoff?a("div",[t._v("已核销")]):a("div",{staticStyle:{color:"#F5222D"}},[t._v("待核销")])]}},{key:"cartNum",fn:function(e){var r=e.row;e.index;return[a("div",[t._v(t._s(r.write_times))])]}}])}),a("Button",{attrs:{type:"primary"},on:{click:t.submitWriteOff}},[t._v("确认核销")])],1)],1)},v=[],_=a("f8b7"),g={name:"orderRecord",props:{orderNumId:String},data:function(){var t=this;return{modals:!1,loading:!1,writeOffData:[],writeOffItem:[],columns:[{type:"selection",width:50,align:"center"},{title:"商品信息",slot:"image",align:"center",minWidth:150},{title:"规格",slot:"value",align:"center",minWidth:90},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:60},{title:"商品优惠价",slot:"price",align:"center",minWidth:60},{title:"状态",slot:"writeOff",align:"center",minWidth:30},{title:"总数",slot:"cartNum",align:"center",minWidth:30},{title:"已核销",key:"writeoffed_num",align:"center",minWidth:30},{title:"待核销数量",key:"surplus_num",align:"center",minWidth:130,render:function(e,a){return e("div",[e("InputNumber",{props:{min:1,max:a.row.numShow,value:a.row.write_surplus_times||0,disabled:!(a.row.write_surplus_times&&!a.row.is_writeoff)},on:{"on-change":function(e){a.row.surplus_num=e||1,t.writeOffItem.length?t.writeOffItem.forEach((function(e){e.id==a.row.id?e.surplus_num=a.row.surplus_num:t.writeOffItem.push(a.row)})):t.writeOffItem.push(a.row),t.selectData.forEach((function(e,r){e.cart_id===a.row.cart_id&&t.selectData.splice(r,1,a.row)}))}}})])}}],selectData:[]}},methods:{cancel:function(){this.$refs.selection.selectAll(!1)},selectOne:function(t){var e=this;t.forEach((function(t){e.writeOffItem.forEach((function(e){t.id==e.id&&(t.surplus_num=e.surplus_num)}))})),this.selectData=t},getWriteOff:function(t){var e=this;Object(_["jb"])(t).then((function(t){var a={title:"待服务",key:"unservice_num",align:"center",minWidth:30},r=e.columns.findIndex((function(t){return t.key===a.key}));if(-1!==r&&e.columns.splice(r,1),6==t.data.product_type){var i=e.columns.length-1;e.columns.splice(i,0,a)}var s=t.data.cart_info;s.forEach((function(t){t.numShow=t.surplus_num,t.is_writeoff&&(t._disabled=!0)})),e.writeOffData=s,e.writeOffItem=[]})).catch((function(t){e.$Message.error(res.msg)}))},submitWriteOff:function(){var t=this;if(!this.selectData.length)return this.$Message.error("请选择要核销的商品");var e=[];this.selectData.forEach((function(t){e.push({cart_id:t.cart_id,cart_num:t.surplus_num})})),Object(_["Z"])(this.orderNumId,{cart_ids:e}).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$refs.selection.selectAll(!1),t.$emit("submitSuccess")})).catch((function(e){t.$Message.error(e.msg)}))}}},w=g,b=(a("e469"),a("2877")),y=Object(b["a"])(w,p,v,!1,null,"30b063ca",null),x=y.exports,k=a("2e83"),D=a("0b65"),C=a("7dc5"),I=a("b4ea"),O=a("add5"),$=a.n(O),S=a("5a0c"),M=a.n(S);function T(t,e,a,r,i,s,n){try{var o=t[s](n),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(r,i)}function P(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var s=t.apply(e,a);function n(t){T(s,r,i,n,o,"next",t)}function o(t){T(s,r,i,n,o,"throw",t)}n(void 0)}))}}function L(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function N(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?L(a,!0).forEach((function(e){F(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):L(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function F(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var j={name:"index",components:{userDetails:d["a"],editFrom:u["a"],detailsFrom:m["a"],orderRecord:h["a"],orderWriteOff:x,orderSend:f["a"],expandRow:c["a"],orderRemark:C["a"]},filters:{timeFormat:function(t){return M()(1e3*t).format("YYYY-MM-DD HH:mm")}},data:function(){return{isCheckBox:!1,checkUidList:[],isAll:0,qrcode:{},spin:!1,modalCode:!1,delfromData:{},pay_type:"",status:0,FromData:null,orderDatalist:null,orderId:0,orderNumId:"",options:D["a"],payList:[{label:"微信支付",val:"1"},{label:"支付宝支付",val:"4"},{label:"余额支付",val:"2"},{label:"线下支付",val:"3"}],statusList:[{value:"",label:"全部"},{value:"0",label:"未支付"},{value:"1",label:"待配送"},{value:"2",label:"配送中"},{value:"5",label:"待核销"},{value:"6",label:"已核销"},{value:"3",label:"待评价"},{value:"4",label:"已完成"},{value:"-4",label:"已删除"}],staffData:[],tablists:{},orderChartType:{},timeVal:[],orderData:{page:1,limit:10,type:"",status:"",time:"",real_name:"",staff_id:"",order_type:"",product_type:"",pay_type:""},tableList:[],total:0,loading:!1,columns:[{type:"expand",width:30,render:function(t,e){return t(c["a"],{props:{row:e.row}})}},{type:"selection",width:60,align:"center"},{title:"订单号",slot:"order_id",minWidth:160},{title:"用户信息",slot:"userInfo",minWidth:250},{title:"商品信息",slot:"info",minWidth:350},{title:"实际支付",slot:"pay_price",minWidth:120},{title:"支付方式",key:"pay_type_name",minWidth:120},{title:"收银店员",key:"clerk_name",minWidth:120},{title:"下单时间",key:"add_time",minWidth:130},{title:"订单状态",slot:"statusName",minWidth:120},{title:"操作",slot:"action",fixed:"right",minWidth:200,align:"center"}],rowActive:{},refundModal:!1,refundColumns:[{type:"selection",width:60,align:"center"},{title:"商品信息",width:210,slot:"product"},{title:"规格",render:function(t,e){return t("div",e.row.productInfo.attrInfo.suk)}},{title:"售价",render:function(t,e){return t("div",e.row.productInfo.attrInfo.price)}},{title:"优惠价",key:"refundPrice"},{title:"总数",key:"cart_num"},{title:"退款数量",slot:"action",width:160}],refundProduct:[],refundSelection:[],refundMoney:0,is_split_order:0,refund_explain:"",writeSurplusTimes:0,writeTimes:0,cardBenefits:[],remainingPrice:0}},watch:{refundSelection:{handler:function(t){var e=this;this.refundMoney=t.reduce((function(t,a){var r=a.refundPrice,i=a.refundNum;return e.$computes.Add(t,e.$computes.Mul(r,i))}),0)},deep:!0},is_split_order:function(t){var e=this;this.$nextTick((function(){e.$refs.refundTable.selectAll(!!t)}))},refundMoney:function(t){var e=this;this.$nextTick((function(){"number"==typeof t&&parseFloat(t)!=parseInt(t)&&t.toString().length-(t.toString().indexOf(".")+1)>2&&(e.refundMoney=Number(t.toFixed(2)))}))}},computed:N({},Object(o["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"right"},refundProductNum:function(){return this.refundProduct.reduce((function(t,e){return t+e.refundNum}),0)}}),created:function(){this.orderData.status=this.$route.query.status||"",this.staffList(),this.getList()},mounted:function(){},methods:{visibleChange:function(t){this.is_split_order=0,t||(this.refundSelection=[])},cancelRefundModal:function(){this.refundModal=!1},putOpenRefund:function(){var t=this,e={id:this.orderId,refund_price:this.refundMoney,type:1,is_split_order:this.is_split_order,refund_explain:this.refund_explain};if(this.is_split_order){if(!this.refundSelection.length)return this.$Message.error("请选择需要退款的商品");e.cart_ids=this.refundSelection.map((function(t){var e=t.id,a=t.refundNum;return{cart_id:e,cart_num:a}}))}Object(_["V"])(e).then((function(e){t.$Message.success(e.msg),t.refundModal=!1,t.getData(t.orderId),t.getList()})).catch((function(e){t.$Message.error(e.msg)}))},refundSelectionChange:function(t){this.refundSelection=t},refundNumChange:function(t){var e=t.id,a=t.refundNum,r=this.refundSelection.find((function(t){return t.id===e}));r&&(r.refundNum=a)},allReset:function(){this.isAll=0,this.isCheckBox=!1,this.$refs.xTable.setAllCheckboxRow(!1),this.checkUidList=[]},allPages:function(t){this.isAll=t,0==t?this.$refs.xTable.toggleAllCheckboxRow():(this.isCheckBox?(this.$refs.xTable.setAllCheckboxRow(!1),this.isCheckBox=!1,this.isAll=0):(this.$refs.xTable.setAllCheckboxRow(!0),this.isCheckBox=!0,this.isAll=1),this.checkUidList=[])},checkboxItem:function(t){var e=parseInt(t.rowid),a=this.checkUidList.indexOf(e);-1!==a?this.checkUidList=this.checkUidList.filter((function(t){return t!==e})):this.checkUidList.push(e)},checkboxAll:function(){var t=this.$refs.xTable.getCheckboxRecords(!0),e=this.$refs.xTable.getCheckboxReserveRecords(!0);0==this.isAll&&this.checkUidList.length<=e.length&&!this.isCheckBox&&(e=[]),e=e.concat(t);var a=[];e.forEach((function(t){a.push(parseInt(t.id))})),this.checkUidList=a,t.length||(this.isCheckBox=!1)},printOreder:function(){if(this.checkUidList.length>10||1==this.isAll&&this.total>10)return this.$Message.error("最多批量打印10个订单");var t=[];1==this.isAll&&this.total<=10&&this.tableList.forEach((function(e){t.push(parseInt(e.id))}));var e=this.$router.resolve({path:"".concat(l["a"].routePre,"/order/distribution"),query:{id:1==this.isAll?t.join(","):this.checkUidList.join(",")}});window.open(e.href,"_blank")},qrcodeShow:function(){var t=this;this.spin=!0,this.modalCode=!0,Object(_["n"])().then((function(e){t.spin=!1,t.qrcode=e.data})).catch((function(e){t.spin=!1,t.$Message.error(e.msg)}))},splitOrderDetail:function(t){this.$router.push({path:"split_list",query:{id:t.id,orderChartType:this.orderData.status}})},changeMenu:function(t,e){var a=this;switch(this.orderId=t.id,e){case"1":this.delfromData={title:"修改立即支付",url:"/order/pay_offline/".concat(t.id),method:"post",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.getData(t.id,1),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"2":this.rowActive=t,this.getData(t.id);break;case"3":this.$refs.record.modals=!0,this.$refs.record.getList(t.id);break;case"4":this.$refs.remarks.modals=!0,this.$refs.remarks.formValidate.remark=t.remark;break;case"5":11==t.type&&this.getOrderBenefits(),this.getOnlyRefundData(t.id,t.refund_type);break;case"55":this.getRefundData(t.id,t.refund_type);break;case"8":this.delfromData={title:"修改确认收货",url:"/order/take/".concat(t.id),method:"put",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.getList(),a.getData(t.id,1)})).catch((function(t){a.$Message.error(t.msg)}));break;case"10":this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"11":this.delfromData={title:"立即打印电子面单",info:"您确认打印此电子面单吗?",url:"/order/order_dump/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"12":var r=this.$router.resolve({path:"".concat(l["a"].routePre,"/order/distribution"),query:{id:t.id}});window.open(r.href,"_blank");break;case"13":this.printImg(t.kuaidi_label);break;default:this.delfromData={title:"删除订单",url:"/order/del/".concat(t.id),method:"DELETE",ids:""},this.delOrder(t,this.delfromData)}},getOnlyRefundData:function(t,e){var a=this,r=this.rowActive._info,i=Object.keys(r).map((function(t){return r[t].cart_info})).filter((function(t){return!t.is_gift}));i.forEach((function(t){t.refundPrice=a.$computes.Div(t.refund_price,t.cart_num),t.refundNum=t.cart_num-t.refund_num,t._disabled=!t.refundNum})),this.refundProduct=i,1===this.refundProductNum&&(this.refundSelection=i),this.refundModal=!0},getRefundData:function(t,e){var a=this;this.orderData.status;this.delfromData={title:"是否立即退货",url:"/refund/agree/".concat(t),method:"get"},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}))},delOrder:function(t,e){var a=this;if(1===t.is_del)this.$modalSure(e).then((function(t){a.$Message.success(t.msg),a.getList(),a.$refs.detailss.modals=!1})).catch((function(t){a.$Message.error(t.msg)}));else{var r="错误！",i="<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>";this.$Modal.error({title:r,content:i})}},getData:function(t,e){var a=this;Object(_["r"])(t).then(function(){var t=P(n.a.mark((function t(r){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e||(a.$refs.detailss.modals=!0),a.$refs.detailss.activeName="detail",11==r.data.orderInfo.type&&(r.data.orderInfo.cartInfo[0]._loading=!1,r.data.orderInfo.cartInfo[0].children=[]),a.orderDatalist=r.data,a.orderDatalist.orderInfo.refund_reason_wap_img)try{a.orderDatalist.orderInfo.refund_reason_wap_img=JSON.parse(a.orderDatalist.orderInfo.refund_reason_wap_img)}catch(i){a.orderDatalist.orderInfo.refund_reason_wap_img=[]}case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){a.$Message.error(t.msg)}))},submitSuccess:function(){this.getList(),this.$refs.detailss.modals&&this.getData(this.orderId,1)},singleWrite:function(t){var e=this;this.$Modal.confirm({title:"提示",content:"确定要核销该订单吗？",cancelText:"取消",closable:!0,maskClosable:!0,onOk:function(){Object(_["kb"])(t.order_id).then((function(t){e.$Message.success(t.msg),e.getList(),e.$refs.detailss.modals&&e.getData(e.orderId,1)})).catch((function(t){e.$Message.error(t.msg)}))},onCancel:function(){}})},bindWrite:function(t){var e=this;t.total_num>1?(this.orderNumId=t.order_id,this.$refs.writeOff.modals=!0,this.$refs.writeOff.getWriteOff({oid:t.id})):4==t.product_type?this.$modalForm(Object(_["P"])(t.id)).then((function(t){e.$Message.success(t.msg),e.getList(),e.$refs.detailss.modals&&e.getData(e.orderId,1)})):this.singleWrite(t)},delivery:function(t){var e=this;Object(_["s"])(t.id).then(function(){var t=P(n.a.mark((function t(a){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.FromData=a.data,e.$refs.edits.modals=!0,e.getData(e.orderId,1);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},edit:function(t){this.getOrderData(t.id)},getOrderData:function(t){var e=this;Object(_["y"])(t).then(function(){var t=P(n.a.mark((function t(a){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!1!==a.data.status){t.next=2;break}return t.abrupt("return",e.$authLapse(a.data));case 2:e.$authLapse(a.data),e.FromData=a.data,e.$refs.edits.modals=!0;case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},submitFail:function(){this.status=0,this.getList(),this.getData(this.orderId,1)},sendOrder:function(t){var e=this;this.$store.commit("store/order/setSplitOrder",t.total_num),this.$refs.send.modals=!0,this.orderId=t.id,this.status=t._status,this.pay_type=t.pay_type,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(a){e.$refs.send.getCartInfo(t._status,t.id)}))},showUserInfo:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(t.uid)},printImg:function(t){$()({printable:t,type:"image",documentTitle:"快递信息",style:"img{\n\t      width: 100%;\n\t      height: 476px;\n\t    }"})},staffList:function(){var t=this;Object(I["r"])().then((function(e){t.staffData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},getChart:function(){var t=this;Object(_["G"])(this.orderData).then((function(e){t.tablists=e.data,t.orderChartType=e.data})).catch((function(e){t.$Message.error(e.msg)}))},userSearchs:function(){this.allReset(),this.orderData.page=1,this.getList()},getList:function(){var t=this;this.getChart(),this.loading=!0,Object(_["K"])(this.orderData).then((function(e){var a=e.data;a.data.forEach((function(e){e.id==t.orderId&&(t.rowActive=e)})),t.tableList=a.data,t.total=a.count,t.loading=!1,t.$nextTick((function(){if(1==this.isAll)this.isCheckBox?this.$refs.xTable.setAllCheckboxRow(!0):this.$refs.xTable.setAllCheckboxRow(!1);else{var t=this.$refs.xTable.getCheckboxReserveRecords(!0);(!this.checkUidList.length||this.checkUidList.length<=t.length)&&this.$refs.xTable.setAllCheckboxRow(!1)}}))})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.orderData.page=t,this.getList()},onchangeTime:function(t){this.timeVal=t,this.orderData.time=this.timeVal.join("-"),this.orderData.page=1,t[0]||(this.orderData.time=""),this.allReset(),this.getList()},selectChange:function(){this.orderData.page=1,this.timeVal=[],this.getList()},exports:function(){var t=P(n.a.mark((function t(){var e,a,r,i,s,o,l,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=N({},this.orderData,{ids:this.checkUidList.join(",")}),a=[],r=[],i=[],s="",o=JSON.parse(JSON.stringify(e)),o.page=1,l=0;case 5:if(!(l<o.page+1)){t.next=22;break}return t.next=8,this.getExcelData(o);case 8:if(c=t.sent,s||(s=c.filename),r.length||(r=c.filekey),a.length||(a=c.header),!c.export.length){t.next=17;break}i=i.concat(c.export),o.page++,t.next=19;break;case 17:return Object(k["a"])(a,r,s,i),t.abrupt("return");case 19:l++,t.next=5;break;case 22:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getExcelData:function(t){return new Promise((function(e,a){Object(_["I"])(t,1).then((function(t){return e(t.data)}))}))},getOrderBenefits:function(){var t=this;Object(_["E"])(this.orderId).then((function(e){t.writeSurplusTimes=e.data.write_surplus_times,t.writeTimes=e.data.write_times,t.remainingPrice=e.data.remaining_price,t.refundMoney=e.data.remaining_price,t.cardBenefits=e.data.cart_info}))}}},A=j,R=(a("b6e1"),Object(b["a"])(A,r,i,!1,null,"b1e02172",null));e["default"]=R.exports},"89ed":function(t,e,a){},b6e1:function(t,e,a){"use strict";var r=a("2aa7"),i=a.n(r);i.a},e469:function(t,e,a){"use strict";var r=a("89ed"),i=a.n(r);i.a}}]);