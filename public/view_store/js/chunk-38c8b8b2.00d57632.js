(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-38c8b8b2"],{"5abd":function(e,r,t){},"7bcf":function(e,r,t){"use strict";t.r(r);var o=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"box"},["dialog"===this.$route.query.fodder||"many"===this.$route.query.type?t("upload-from",{attrs:{isChoice:e.isChoiceD,gridPic:e.gridPic,gridBtn:e.gridBtn},on:{getPicD:e.getPicD}}):t("upload-from",{attrs:{isChoice:e.isChoice,gridPic:e.gridPic,gridBtn:e.gridBtn},on:{getPic:e.getPic}})],1)},i=[],c=t("b0e7"),d={name:"widgetImg",components:{uploadFrom:c["a"]},data:function(){return{isChoice:"单选",isChoiceD:"多选",gridPic:{xl:4,lg:4,md:8,sm:12,xs:12},gridBtn:{xl:4,lg:4,md:4,sm:8,xs:8}}},mounted:function(){console.log(this.$route.query.fodder)},methods:{getPicD:function(e){if("dialog"===this.$route.query.fodder){for(var r=0;r<e.length;r++)nowEditor.editor.execCommand("insertimage",{src:e[r].att_dir});nowEditor.dialog.close(!0)}else{var t=window.form_create_helper.get(this.$route.query.fodder)||[];e=e.map((function(e){return e.att_dir}));var o=t.concat(e),i=Array.from(new Set(o));form_create_helper.set(this.$route.query.fodder,i),form_create_helper.close(this.$route.query.fodder)}},getPic:function(e){form_create_helper.set(this.$route.query.fodder,e.satt_dir),form_create_helper.close(this.$route.query.fodder)}}},n=d,s=(t("cb36"),t("2877")),a=Object(s["a"])(n,o,i,!1,null,"7b56e7df",null);r["default"]=a.exports},cb36:function(e,r,t){"use strict";var o=t("5abd"),i=t.n(o);i.a}}]);