(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-004ae8a9"],{"0d85":function(t,e,i){"use strict";var a=i("fa7e"),r=i.n(a);r.a},"1e0c":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"i-layout-page-header"},[i("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[i("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[i("router-link",{attrs:{to:{path:t.routePre+"/marketing/short_video/index"}}},[i("div",{staticClass:"font-sm after-line"},[i("span",{staticClass:"iconfont iconfanhui"}),i("span",{staticClass:"pl10"},[t._v("返回")])])]),i("span",{staticClass:"mr20 ml16",domProps:{textContent:t._s(t.$route.params.id?"编辑短视频":"添加短视频")}})],1)])],1),i("Card",{staticClass:"ivu-mt mb79",attrs:{bordered:!1,"dis-hover":""}},[i("div",{staticClass:"new_tab"},[i("Tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.headeNum,(function(t,e){return i("TabPane",{key:e,attrs:{label:t.name,name:t.type}})})),1)],1),"2"===t.currentTab?i("div",{staticClass:"Button"},[i("Button",{staticClass:"bnt mr15",attrs:{type:"primary"},on:{click:t.addGoods}},[t._v("添加商品")]),i("Tooltip",{attrs:{content:"本页至少选中一项",disabled:!!t.formSelection.length}},[i("Button",{staticClass:"bnt mr15",attrs:{disabled:!t.formSelection.length},on:{click:t.batchDel}},[t._v("批量删除")])],1)],1):t._e(),i("Form",{ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[i("Row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab,expression:"currentTab === '1'"}],attrs:{gutter:24,type:"flex"}},[i("Col",{attrs:{span:"24"}},[i("FormItem",{attrs:{label:"视频简介：",prop:"desc"}},[i("Input",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{type:"textarea",rows:3,placeholder:"请输入视频简介",maxlength:"220","show-word-limit":""},model:{value:t.formValidate.desc,callback:function(e){t.$set(t.formValidate,"desc",e)},expression:"formValidate.desc"}})],1)],1),i("Col",{attrs:{span:"24"}},[i("FormItem",{attrs:{label:"上传视频：",prop:"video_url"}},[i("Button",{on:{click:function(e){return t.modalPicTap("video")}}},[t._v("上传视频")]),i("div",{staticClass:"tips"},[t._v("建议时长：9～30秒，视频宽高比9:16（不建议本地储存）")]),t.formValidate.video_url?i("div",{staticClass:"iview-video-style"},[i("video",{staticClass:"video-style",attrs:{src:t.formValidate.video_url,controls:"controls"}},[t._v("\n                                    您的浏览器不支持 video 标签。\n                                ")]),i("div",{staticClass:"mark"}),i("Icon",{staticClass:"iconv",attrs:{type:"ios-trash-outline"},on:{click:t.delVideo}})],1):t._e()],1)],1),i("Col",{attrs:{span:"24"}},[i("FormItem",{attrs:{label:"封面图：",prop:"image"}},[i("div",{staticClass:"pictrueBox"},[t.formValidate.image?i("div",{staticClass:"pictrue"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.image,expression:"formValidate.image"}]}),i("Button",{staticClass:"btndel",attrs:{shape:"circle",icon:"md-close"},nativeOn:{click:function(e){return t.handleRemove(t.index)}}})],1):i("div",{staticClass:"upLoad acea-row row-center-wrapper",on:{click:function(e){return t.modalPicTap("image")}}},[i("Input",{staticClass:"input-display",model:{value:t.formValidate.image,callback:function(e){t.$set(t.formValidate,"image",e)},expression:"formValidate.image"}}),i("Icon",{attrs:{type:"ios-add",size:"26"}})],1)]),i("div",{staticClass:"tips"},[t._v("建议尺寸：226 * 300px")])])],1),i("Col",{attrs:{span:"24"}},[i("FormItem",{attrs:{label:"排序："}},[i("InputNumber",{directives:[{name:"width",rawName:"v-width",value:"50%",expression:"'50%'"}],attrs:{min:0,max:99999999},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1)],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}]},[i("Table",{staticClass:"ivu-mt",attrs:{columns:t.columns,data:t.tableData,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"on-selection-change":t.selectChange},scopedSlots:t._u([{key:"info",fn:function(e){var a=e.row;return[i("div",{staticClass:"imgPic acea-row row-middle"},[i("viewer",[i("div",{staticClass:"pictrue"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a.image,expression:"row.image"}]})])]),i("div",{staticClass:"info"},[i("Tooltip",{attrs:{"max-width":"200",placement:"bottom",transfer:""}},[i("span",{staticClass:"line2"},[t._v(t._s(a.store_name)+t._s(a.suk))]),i("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(a.store_name)+t._s(a.suk))])])],1)],1)]}},{key:"action",fn:function(e){var a=e.row;e.index;return[i("a",{on:{click:function(e){return t.del(a)}}},[t._v("删除")])]}}])})],1),i("div",{staticClass:"footer acea-row row-center-wrapper"},["1"!==t.currentTab?i("Button",{staticStyle:{"margin-right":"10px"},on:{click:t.upTab}},[t._v("上一步")]):t._e(),"2"!==t.currentTab?i("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.downTab("formValidate")}}},[t._v("下一步")]):i("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")])],1)],1)],1),i("Modal",{attrs:{width:"960px",scrollable:"","footer-hide":"",closable:"",title:"video"==t.typeTit?"上传视频":"上传商品图","mask-closable":!1,"z-index":10},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?i("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic,isType:"video"==t.typeTit?2:1},on:{getPic:t.getPic}}):t._e()],1),i("Modal",{staticClass:"paymentFooter",attrs:{title:"商品列表",footerHide:"",scrollable:"",width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?i("goods-list",{ref:"goodslist",attrs:{ischeckbox:!0,isdiy:!0},on:{getProductId:t.getProductId}}):t._e()],1)],1)},r=[],o=i("2f62"),n=i("d708"),s=i("b0e7"),c=i("b7be"),l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"goodList"},[i("Form",{ref:"formValidate",staticClass:"tabform",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition,inline:""}},[t.liveStatus?t._e():i("FormItem",{attrs:{label:"商品分类：","label-for":"pid"}},[i("Cascader",{staticClass:"input-add",attrs:{data:t.treeSelect,placeholder:"请选择商品分类","change-on-select":"",filterable:""},on:{"on-change":t.treeSearchs}})],1),i("FormItem",{attrs:{label:"商品搜索：","label-for":"store_name"}},[i("Input",{staticClass:"input-add mr14",attrs:{placeholder:"请输入商品名称,关键字,编号"},model:{value:t.formValidate.store_name,callback:function(e){t.$set(t.formValidate,"store_name",e)},expression:"formValidate.store_name"}}),i("Button",{attrs:{type:"primary"},on:{click:function(e){return t.userSearchs()}}},[t._v("查询")])],1)],1),i("Table",{ref:"table",staticClass:"mr-20",attrs:{"no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果",columns:0==t.liveStatus?t.columns4:t.columns5,data:t.tableList,loading:t.loading},on:{"on-select-all":t.selectAll,"on-select-all-cancel":t.cancelAll,"on-select":t.TableSelectRow,"on-select-cancel":t.TableSelectCancelRow},scopedSlots:t._u([{key:"store_name",fn:function(e){var a=e.row;return[i("Tooltip",{attrs:{"max-width":"200",placement:"bottom"}},[i("span",{staticClass:"line2"},[t._v(t._s(a.store_name)+t._s(a.suk))]),i("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(a.store_name)+t._s(a.suk))])])]}},{key:"image",fn:function(t){var e=t.row;return[i("viewer",[i("div",{staticClass:"tabBox_img"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.image,expression:"row.image"}]})])])]}},{key:"product_type",fn:function(e){var a=e.row;return a.hasOwnProperty("product_type")?[0==a.product_type?i("span",[t._v("普通商品")]):t._e(),1==a.product_type?i("span",[t._v("卡密商品")]):t._e(),3==a.product_type?i("span",[t._v("虚拟商品")]):t._e()]:void 0}}],null,!0)}),i("div",{staticClass:"acea-row row-right page"},[i("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1),"many"!==t.many||t.diy?t._e():i("div",{staticClass:"footer",attrs:{slot:"footer"},slot:"footer"},[i("Button",{attrs:{type:"primary",size:"large",loading:t.modal_loading,long:""},on:{click:t.ok}},[t._v("提交")])],1)],1)},d=[],u=i("a34a"),m=i.n(u),f=i("c4c8");function h(t,e,i,a,r,o,n){try{var s=t[o](n),c=s.value}catch(l){return void i(l)}s.done?e(c):Promise.resolve(c).then(a,r)}function p(t){return function(){var e=this,i=arguments;return new Promise((function(a,r){var o=t.apply(e,i);function n(t){h(o,a,r,n,s,"next",t)}function s(t){h(o,a,r,n,s,"throw",t)}n(void 0)}))}}function g(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function v(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?g(i,!0).forEach((function(e){b(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):g(i).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function b(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var y={name:"index",props:{goodsType:{type:Number,default:0},diy:{type:Boolean,default:!1},isdiy:{type:Boolean,default:!1},ischeckbox:{type:Boolean,default:!1},liveStatus:{type:Boolean,default:!1},isLive:{type:Boolean,default:!1},datas:{type:Object,default:function(){return{}}}},data:function(){return{selectEquips:[],selectEquipsIds:[],cateIds:[],modal_loading:!1,treeSelect:[],formValidate:{page:1,limit:10,cate_id:"",store_name:""},total:0,modals:!1,loading:!1,grid:{xl:10,lg:10,md:12,sm:24,xs:24},tableList:[],currentid:0,productRow:{},columns4:[{title:"商品ID",key:"id"},{title:"图片",slot:"image",width:60},{title:"商品名称",slot:"store_name",minWidth:200},{title:"商品类型",slot:"product_type",minWidth:100},{title:"商品分类",key:"cate_name",minWidth:150}],columns5:[{title:"商品ID",key:"id"},{title:"图片",slot:"image"},{title:"商品名称",key:"name",minWidth:250}],images:[],many:""}},computed:v({},Object(o["e"])("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){var t=this,e={width:60,align:"center",render:function(e,i){var a=i.row.id,r=!1;r=t.currentid===a;var o=t;return e("div",[e("Radio",{props:{value:r},on:{"on-change":function(){if(o.currentid=a,t.productRow=i.row,t.$emit("getProductId",t.productRow),t.productRow.id){if("image"===t.$route.query.fodder){var e={image:t.productRow.image,product_id:t.productRow.id,name:t.productRow.name};form_create_helper.set("image",e),form_create_helper.close("image")}}else t.$Message.warning("请先选择商品")}}})])}},i={type:"selection",width:60,align:"center"},a="";a=this.ischeckbox?"many":this.$route.query.type,this.many=a,"many"===a?(this.columns4.unshift(i),this.columns5.unshift(i)):(this.columns4.unshift(e),this.columns5.unshift(e))},mounted:function(){this.goodsCategory(),this.getList()},methods:{sortData:function(){var t=this;this.selectEquipsIds.length&&this.tableList.forEach((function(e){t.selectEquipsIds.includes(e.id)&&(e._checked=!0)}))},TableSelectRow:function(t,e){this.selectEquipsIds.includes(e.id)||(this.selectEquipsIds.push(e.id),this.selectEquips.push(e))},TableSelectCancelRow:function(t,e){var i=this.selectEquipsIds.indexOf(e.id);-1!=i&&(this.selectEquipsIds.splice(i,1),this.selectEquips.splice(i,1))},selectAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectRow(null,this.tableList[t])},cancelAll:function(){for(var t=this.tableList.length-1;t>=0;t--)this.TableSelectCancelRow(null,this.tableList[t])},goodsCategory:function(){var t=this;Object(f["e"])(0).then((function(e){t.treeSelect=e.data})).catch((function(e){t.$Message.error(e.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList()},getList:function(){var t=this;this.loading=!0,this.liveStatus||(this.isLive&&(this.formValidate.is_live=1),this.goodsType&&(this.formValidate.is_presale_product=0,this.formValidate.is_vip_product=0),this.formValidate.cate_id=this.cateIds[this.cateIds.length-1],Object(f["u"])(this.formValidate).then(function(){var e=p(m.a.mark((function e(i){var a;return m.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=i.data,t.tableList=a.list,t.total=i.data.count,t.sortData(),t.loading=!1;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)})))},changeCheckbox:function(t){var e=[];t.forEach((function(t){var i={image:t.image,product_id:t.id,store_name:t.store_name,temp_id:t.temp_id};e.push(i)})),this.images=e,this.$emit("getProductDiy",t)},ok:function(){var t=[];if(this.selectEquips.forEach((function(e){var i={image:e.image,product_id:e.id,store_name:e.store_name,temp_id:e.temp_id};t.push(i)})),t.length>0)if("image"===this.$route.query.fodder){var e=form_create_helper.get("image");form_create_helper.set("image",e.concat(t)),form_create_helper.close("image")}else this.isdiy?this.$emit("getProductId",this.selectEquips):this.$emit("getProductId",t);else this.$Message.warning("请先选择商品")},treeSearchs:function(t){this.cateIds=t,this.formValidate.page=1,this.getList()},userSearchs:function(){this.formValidate.page=1,this.getList()},clear:function(){this.productRow.id="",this.currentid=""}}},_=y,w=(i("0d85"),i("2877")),k=Object(w["a"])(_,l,d,!1,null,"3ba27c12",null),C=k.exports;function P(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function O(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?P(i,!0).forEach((function(e){x(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):P(i).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function x(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var V={name:"create",components:{uploadPictures:s["a"],goodsList:C},data:function(){var t=function(t,e,i){if(!e)return i(new Error("请上传视频封面图"));i()},e=function(t,e,i){if(!e)return i(new Error("请上传视频"));i()};return{routePre:n["a"].routePre,modals:!1,gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},headeNum:[{type:"1",name:"基础设置"},{type:"2",name:"关联商品"}],currentTab:"1",formValidate:{desc:"",image:"",sort:0,video_url:"",product_id:[]},modalPic:!1,isChoice:"单选",ruleValidate:{desc:[{required:!0,message:"请输入视频简介",trigger:"blur"}],video_url:[{required:!0,message:"请上传视频",validator:e,trigger:"change"}],image:[{required:!0,validator:t,trigger:"change"}]},columns:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"info",minWidth:180},{title:"商品分类",key:"cate_name",minWidth:180},{title:"售价",key:"price",minWidth:180},{title:"库存",key:"stock",minWidth:180},{title:"操作",slot:"action",fixed:"right",width:100}],tableData:[],id:0,formSelection:[],typeTit:""}},computed:O({},Object(o["e"])("store/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:90},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.id=this.$route.params.id||0,this.id&&this.getInfo()},methods:{delVideo:function(){var t=this;t.$set(t.formValidate,"video_url",""),this.$refs.formValidate.validateField("video_url")},del:function(t){var e=this;this.tableData.forEach((function(i,a){if(t.id===i.id)return e.tableData.splice(a,1)}))},batchDel:function(){for(var t=0;t<this.formSelection.length;t++)for(var e=0;e<this.tableData.length;e++)this.tableData[e].id===this.formSelection[t].id&&(this.tableData.splice(e,1),e--)},selectChange:function(t){this.formSelection=t},addGoods:function(){this.modals=!0},cancel:function(){this.modals=!1},unique:function(t){var e=new Map;return t.filter((function(t){return!e.has(t.id)&&e.set(t.id,1)}))},getProductId:function(t){this.modals=!1;var e=this.tableData.concat(t);this.tableData=this.unique(e)},getInfo:function(){var t=this;Object(c["d"])(this.id).then((function(e){t.formValidate=e.data,t.tableData=e.data.productInfo})).catch((function(e){t.$Message.error(e.msg)}))},upTab:function(){this.currentTab="1"},downTab:function(t){var e=this;this.$refs[t].validate((function(t){t?e.currentTab="2":e.$Message.warning("请完善数据")}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(t){var i=[];e.tableData.forEach((function(t){i.push(t.id)})),e.formValidate.product_id=i,Object(c["g"])(e.formValidate,e.id).then((function(t){e.$router.push({path:"".concat(n["a"].routePre,"/marketing/short_video/index")}),e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))}else e.$Message.warning("请完善数据")}))},modalPicTap:function(t){this.typeTit=t,this.modalPic=!0},getPic:function(t){this.modalPic=!1,"image"==this.typeTit?(this.formValidate.image=t.att_dir,this.$refs.formValidate.validateField("image")):(this.formValidate.video_url=t.att_dir,this.$refs.formValidate.validateField("video_url"))},handleRemove:function(){this.formValidate.image="",this.$refs.formValidate.validateField("image")}}},j=V,T=(i("5114"),Object(w["a"])(j,a,r,!1,null,"36b5a7e9",null));e["default"]=T.exports},5114:function(t,e,i){"use strict";var a=i("c560"),r=i.n(a);r.a},b7be:function(t,e,i){"use strict";i.d(e,"e",(function(){return r})),i.d(e,"d",(function(){return o})),i.d(e,"g",(function(){return n})),i.d(e,"i",(function(){return s})),i.d(e,"h",(function(){return c})),i.d(e,"k",(function(){return l})),i.d(e,"j",(function(){return d})),i.d(e,"b",(function(){return u})),i.d(e,"f",(function(){return m})),i.d(e,"c",(function(){return f})),i.d(e,"a",(function(){return h}));var a=i("b6bd");function r(t){return Object(a["a"])({url:"/marketing/video/index",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/marketing/video/info/".concat(t),method:"get"})}function n(t,e){return Object(a["a"])({url:"/marketing/video/save/".concat(e),method:"post",data:t})}function s(t){return Object(a["a"])({url:"/marketing/video/set_status/".concat(t.id,"/").concat(t.status),method:"get"})}function c(t){return Object(a["a"])({url:"/marketing/video/set_recommend/".concat(t.id,"/").concat(t.recommend),method:"get"})}function l(t){return Object(a["a"])({url:"/marketing/video/verify/".concat(t.id,"/").concat(t.verify),method:"get"})}function d(t){return Object(a["a"])({url:"/marketing/video/take_down/".concat(t),method:"get"})}function u(t){return Object(a["a"])({url:"/marketing/video/comment",method:"get",params:t})}function m(t,e){return Object(a["a"])({url:"/marketing/video/comment/reply/".concat(e),method:"post",data:t})}function f(t,e){return Object(a["a"])({url:"/marketing/video/comment/reply/".concat(e),method:"get",params:t})}function h(t){return Object(a["a"])({url:"/marketing/video/comment/fictitious/".concat(t),method:"get"})}},c560:function(t,e,i){},fa7e:function(t,e,i){}}]);