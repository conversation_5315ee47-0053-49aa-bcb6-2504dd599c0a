(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b09b4da"],{"26a0":function(t,e,a){},"2b60":function(t,e,a){"use strict";var i=a("26a0"),r=a.n(i);r.a},"57d3":function(t,e,a){"use strict";var i=a("e3c4"),r=a.n(i);r.a},"699d":function(t,e,a){},"9c31":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition}},[a("Row",{attrs:{type:"flex",gutter:24}},[a("Col",[a("FormItem",{attrs:{label:"店员搜索：",labelWidth:80}},[a("Input",{attrs:{placeholder:"请输入ID/手机号",clearable:""},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}},[a("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.formValidate.field_key,callback:function(e){t.$set(t.formValidate,"field_key",e)},expression:"formValidate.field_key"}},[a("Option",{attrs:{value:"all"}},[t._v("全部")]),a("Option",{attrs:{value:"id"}},[t._v("ID")]),a("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1),a("Col",[a("div",{staticClass:"search",on:{click:t.search}},[t._v("搜索")])]),a("Col",[a("div",{staticClass:"reset",on:{click:t.reset}},[t._v("重置")])])],1)],1)],1),a("Card",{staticClass:"ive-mt tablebox",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"btnbox"},[a("Button",{directives:[{name:"auth",rawName:"v-auth",value:["staff-staff-create"],expression:"['staff-staff-create']"}],attrs:{type:"primary"},on:{click:t.add}},[t._v("添加店员")])],1),a("div",{staticClass:"table"},[a("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"staff_name",fn:function(e){var i=e.row;e.index;return[a("div",[t._v(t._s(i.staff_name)),null!=i.delete_time?a("span",{staticStyle:{color:"#ed4014"}},[t._v(" (已注销)")]):t._e()])]}},{key:"label",fn:function(e){var i=e.row;e.index;return[a("div",[t._v(t._s(i.workMember?i.workMember.name:""))])]}},{key:"status",fn:function(e){var i=e.row;e.index;return[a("i-switch",{attrs:{value:i.status,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.changeSwitch(i)}},model:{value:i.status,callback:function(e){t.$set(i,"status",e)},expression:"row.status"}},[a("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),a("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"action",fn:function(e){var i=e.row,r=e.index;return[1==i.status&&null==i.delete_time?a("a",{on:{click:function(e){return t.goCashier(i)}}},[t._v("进入收银台")]):t._e(),1==i.status&&null==i.delete_time?a("Divider",{attrs:{type:"vertical"}}):t._e(),null==i.delete_time?a("a",{on:{click:function(e){return t.edit(i.id)}}},[t._v("编辑")]):t._e(),null==i.delete_time?a("Divider",{attrs:{type:"vertical"}}):t._e(),i.level>0?a("a",{on:{click:function(e){return t.del(i.id,"删除该店员",r)}}},[t._v("删除")]):t._e(),i.level>0?a("Divider",{attrs:{type:"vertical"}}):t._e(),a("a",{on:{click:function(e){return t.details(i)}}},[t._v("查看详情")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)]),a("Details",{ref:"userDetails",on:{edit:t.handleEdit}}),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"修改","mask-closable":!1,width:"550"},model:{value:t.editModal,callback:function(e){t.editModal=e},expression:"editModal"}},[a("Form",{attrs:{model:t.editForm,"label-width":80}},[a("FormItem",{attrs:{label:"业绩金额："}},[t._v("¥"+t._s(t.editRow.pay_price))]),a("FormItem",{attrs:{label:"业绩订单："}},[t._v(t._s(t.editRow.order_id))]),a("FormItem",{attrs:{label:"业绩归属："}},[a("Select",{model:{value:t.editForm.staff_id,callback:function(e){t.$set(t.editForm,"staff_id",e)},expression:"editForm.staff_id"}},t._l(t.staffAll,(function(e){return a("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),a("div",{staticClass:"acea-row row-right"},[a("Button",{staticClass:"mr10",on:{click:t.cancelEditModal}},[t._v("取消")]),a("Button",{attrs:{type:"primary"},on:{click:t.saveOrderStaff}},[t._v("确认")])],1)],1)],1)},r=[],s=a("2f62"),n=(a("c276"),a("a78e")),o=a.n(n),l=a("b4ea"),c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"店员详情","mask-closable":!1,width:"900"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e(),a("div",{staticClass:"acea-row"},[a("div",{staticClass:"avatar mr15"},[a("img",{attrs:{src:t.psInfo.avatar}})]),a("div",{staticClass:"dashboard-workplace-header-tip"},[a("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:t._s(t.psInfo.staff_name||"-")}}),a("div",{staticClass:"dashboard-workplace-header-tip-desc"},t._l(t.detailsData,(function(e,i){return a("span",{key:i,staticClass:"dashboard-workplace-header-tip-desc-sp"},["元"!=e.key?a("span",[t._v(t._s(e.title+"："+e.value+e.key))]):a("span",[t._v(t._s(e.title+"：￥"+e.value))])])})),0)])]),a("Row",{staticClass:"mt25",attrs:{type:"flex",justify:"space-between"}},[a("Col",{staticClass:"user_menu",attrs:{span:"4"}},[a("Menu",{attrs:{theme:t.theme2,"active-name":t.activeName},on:{"on-select":t.changeType}},t._l(t.list,(function(e,i){return a("MenuItem",{key:i,attrs:{name:e.val}},[t._v("\n            "+t._s(e.label)+"\n          ")])})),1)],1),a("Col",{attrs:{span:"20"}},[a("Table",{ref:"table",attrs:{columns:t.columns,data:t.userLists,"max-height":"400",loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"avatar",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"real_name",fn:function(e){var i=e.row;e.index;return[a("div",[t._v(t._s(i.uid?i.real_name:"游客"))])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1)],1)},d=[],u=a("a34a"),m=a.n(u);function f(t,e,a,i,r,s,n){try{var o=t[s](n),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(i,r)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var s=t.apply(e,a);function n(t){f(s,i,r,n,o,"next",t)}function o(t){f(s,i,r,n,o,"throw",t)}n(void 0)}))}}var p={name:"userDetails",data:function(){return{theme2:"light",list:[{val:"cashier_order",label:"收银订单"},{val:"self_order",label:"配送订单"},{val:"writeoff_order",label:"核销订单"},{val:"recharge",label:"充值订单"},{val:"customer",label:"专属客户"},{val:"card",label:"激活会员卡"},{val:"svip",label:"付费会员"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"cashier_order",page:1,limit:10},total:0,columns:[],userLists:[],psInfo:{},activeName:"cashier_order"}},created:function(){},methods:{getDetails:function(t){var e=this;this.activeName="cashier_order",this.userId=t,this.spinShow=!0,Object(l["h"])(t).then(function(){var t=h(m.a.mark((function t(a){var i;return m.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===a.status?(i=a.data,e.detailsData=i.headerList,e.psInfo=i.ps_info,e.changeType(e.activeName,e.userFrom.page),e.spinShow=!1):(e.spinShow=!1,e.$Message.error(a.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},pageChange:function(t){this.userFrom.page=t,this.changeType(this.userFrom.type,t)},changeType:function(t,e){var a=this;this.loading=!0,this.userFrom.type=t,this.activeName=t,e||(e=1),this.userFrom.page=e;var i={id:this.userId,datas:this.userFrom};Object(l["i"])(i).then(function(){var t=h(m.a.mark((function t(e){var i;return m.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a.loading=!1,200!==e.status){t.next=24;break}i=e.data,a.userLists=i.list,a.total=i.count,t.t0=a.userFrom.type,t.next="cashier_order"===t.t0?8:"writeoff_order"===t.t0?8:"self_order"===t.t0?8:"recharge"===t.t0?10:"spread"===t.t0?12:"svip"===t.t0?14:"card"===t.t0?16:"customer"===t.t0?18:20;break;case 8:return a.columns=[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",slot:"real_name",minWidth:80},{title:"商品数量",key:"total_num",minWidth:60},{title:"商品总价",key:"total_price",minWidth:100},{title:"实付金额",key:"pay_price",minWidth:100},{title:"交易完成时间",key:"pay_time",minWidth:100},{title:"操作",key:"action",width:50,render:function(t,e){return t("a",{on:{click:function(){a.$emit("edit",e.row)}}},["修改"])}}],t.abrupt("break",21);case 10:return a.columns=[{title:"订单号",key:"order_id",minWidth:180},{title:"用户昵称",key:"nickname",minWidth:100},{title:"充值金额",key:"price",minWidth:80},{title:"充值时间",key:"_pay_time",minWidth:120},{title:"备注",key:"remarks",minWidth:120}],t.abrupt("break",21);case 12:return a.columns=[{title:"ID",key:"uid",minWidth:80},{title:"头像",slot:"avatar",minWidth:80},{title:"用户昵称",key:"nickname",minWidth:80},{title:"推广时间",key:"spread_time",minWidth:120}],t.abrupt("break",21);case 14:return a.columns=[{title:"订单号",key:"order_id",minWidth:180},{title:"支付金额",key:"pay_price",minWidth:80},{title:"会员类型",key:"member_type",minWidth:80},{title:"支付时间",key:"pay_time",minWidth:120},{title:"备注",key:"remarks",minWidth:120}],t.abrupt("break",21);case 16:return a.columns=[{title:"ID",key:"uid",minWidth:80},{title:"card_id",key:"card_id",minWidth:80},{title:"会员卡号",key:"code",minWidth:120},{title:"激活时间",key:"submit_time",minWidth:120},{title:"添加时间",key:"add_time",minWidth:120}],t.abrupt("break",21);case 18:return a.columns=[{title:"用户ID",key:"uid",minWidth:80},{title:"头像",slot:"avatar",minWidth:80},{title:"用户昵称",key:"nickname",minWidth:120},{title:"绑定时间",key:"salesman_time",minWidth:120},{title:"业绩金额",key:"performance_price",minWidth:120},{title:"订单笔数",key:"order_num",minWidth:120}],t.abrupt("break",21);case 20:a.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"type",minWidth:120},{title:"加入时间",key:"add_time",minWidth:120}];case 21:a.loading=!1,t.next=26;break;case 24:a.loading=!1,a.$Message.error(e.msg);case 26:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){a.loading=!1,a.$Message.error(t.msg)}))},refreshOrder:function(){this.changeType(this.userFrom.type,this.userFrom.page)}}},v=p,g=(a("57d3"),a("fb2f"),a("2877")),b=Object(g["a"])(v,c,d,!1,null,"3f6c7200",null),_=b.exports;function y(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function k(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?y(a,!0).forEach((function(e){w(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):y(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function w(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var W={name:"clerkList",components:{Details:_},data:function(){return{total:0,a:12,loading:!1,columns:[{title:"ID",key:"id",width:60},{title:"头像",slot:"avatars",minWidth:80},{title:"昵称",slot:"staff_name",minWidth:120},{title:"店员身份",key:"roles",minWidth:120},{title:"企微员工",slot:"label",minWidth:120},{title:"手机号",key:"phone",minWidth:150},{title:"专属客户",key:"customer_num",minWidth:150},{title:"账号状态",slot:"status",minWidth:80},{title:"操作",slot:"action",fixed:"right",minWidth:250}],orderList:[],formValidate:{field_key:"all",keyword:"",page:1,limit:15},staffRow:{},editForm:{order_id:"",staff_id:0},editRow:{},editModal:!1,staffAll:[]}},computed:k({},Object(s["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),mounted:function(){this.getList()},methods:{goCashier:function(t){var e=this;Object(l["a"])(t.id).then((function(t){o.a.set("cashierData",JSON.stringify(t)),window.open(window.location.protocol+"//"+window.location.host+"/"+t.data.prefix+"/login")})).catch((function(t){e.$Message.error(t.msg)}))},getList:function(){var t=this;this.loading=!0,Object(l["n"])(this.formValidate).then((function(e){t.total=e.data.count,t.orderList=e.data.list,t.loading=!1})).catch((function(e){t.$Message.error(e.msg),t.loading=!1}))},add:function(){var t=this;this.$modalForm(Object(l["s"])()).then((function(){return t.getList()}))},edit:function(t){var e=this;this.$modalForm(Object(l["m"])(t)).then((function(){return e.getList()}))},del:function(t,e,a){var i=this,r={title:e,num:a,url:"/staff/staff/".concat(t),method:"DELETE",ids:""};this.$modalSure(r).then((function(t){i.$Message.success(t.msg),i.orderList.splice(a,1),i.orderList.length||(i.formValidate.page=1==i.formValidate.page?1:i.formValidate.page-1),i.getList()})).catch((function(t){i.$Message.error(t.msg)}))},search:function(){this.getList()},reset:function(){this.formValidate.field_key="all",this.formValidate.keyword="",this.getList()},changeSwitch:function(t){var e=this;Object(l["t"])(t.id,t.status).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList()},details:function(t){this.staffRow=t,this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(t.id)},handleEdit:function(t){this.editRow=t,this.editForm.order_id=t.order_id,this.editForm.staff_id=0,this.editModal=!0,this.getStaffAll()},getStaffAll:function(){var t=this;Object(l["r"])().then((function(e){t.staffAll=e.data.filter((function(e){return e.value!=t.staffRow.id}))})).catch((function(e){t.$Message.error(e.msg)}))},saveOrderStaff:function(){var t=this;if(!this.editForm.staff_id)return this.$Message.warning("请选择业绩归属店员");Object(l["j"])(this.editForm).then((function(e){t.$Message.success(e.msg),t.editModal=!1,t.$refs.userDetails.refreshOrder()})).catch((function(e){t.$Message.error(e.msg)}))},cancelEditModal:function(){this.editModal=!1}}},x=W,O=(a("2b60"),Object(g["a"])(x,i,r,!1,null,"6dd274d3",null));e["default"]=O.exports},e3c4:function(t,e,a){},fb2f:function(t,e,a){"use strict";var i=a("699d"),r=a.n(i);r.a}}]);