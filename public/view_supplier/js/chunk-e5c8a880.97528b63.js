(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e5c8a880"],{"0583":function(e,t,r){},"90e7":function(e,t,r){"use strict";r.d(t,"n",(function(){return s})),r.d(t,"k",(function(){return o})),r.d(t,"d",(function(){return i})),r.d(t,"c",(function(){return n})),r.d(t,"b",(function(){return c})),r.d(t,"a",(function(){return m})),r.d(t,"l",(function(){return l})),r.d(t,"p",(function(){return d})),r.d(t,"q",(function(){return u})),r.d(t,"m",(function(){return v})),r.d(t,"e",(function(){return f})),r.d(t,"o",(function(){return p})),r.d(t,"f",(function(){return h})),r.d(t,"j",(function(){return b})),r.d(t,"g",(function(){return w})),r.d(t,"h",(function(){return I})),r.d(t,"i",(function(){return _}));var a=r("b6bd");function s(){return Object(a["a"])({url:"/supplier",method:"get"})}function o(e){return Object(a["a"])({url:"/supplier",method:"put",data:e})}function i(e){return Object(a["a"])({url:"city",method:"get",params:e})}function n(e){return Object(a["a"])({url:"admin",method:"get",params:e})}function c(){return Object(a["a"])({url:"admin/create",method:"get"})}function m(e){return Object(a["a"])({url:"admin/".concat(e,"/edit"),method:"get"})}function l(e){return Object(a["a"])({url:"admin/set_status/".concat(e.id,"/").concat(e.status),method:"put"})}function d(e){return Object(a["a"])({url:"setting/shipping_templates/list",method:"get",params:e})}function u(e,t){return Object(a["a"])({url:"setting/shipping_templates/save/".concat(e),method:"post",data:t})}function v(e){return Object(a["a"])({url:"setting/shipping_templates/".concat(e,"/edit"),method:"get"})}function f(e){return Object(a["a"])({url:"city",method:"get",params:e})}function p(e,t){return Object(a["a"])({url:"/system/form/info/".concat(e),method:"get",params:t})}function h(e){return Object(a["a"])({url:"/print/list",method:"get",params:e})}function b(e){var t=e.id,r=e.status;return Object(a["a"])({url:"/print/set_status/".concat(t,"/").concat(r),method:"get"})}function w(e,t){return Object(a["a"])({url:"/print/save/".concat(e),method:"post",data:t})}function I(e,t){return Object(a["a"])({url:"/print/content/".concat(e),method:"get",params:t})}function _(e,t){return Object(a["a"])({url:"/print/save_content/".concat(e),method:"post",data:t})}},"9cb2":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"article-manager video-icon form-submit",attrs:{id:"shopp-manager"}},[a("div",{staticClass:"i-layout-page-header"},[a("PageHeader",{staticClass:"product_tabs",attrs:{"hidden-breadcrumb":""}},[a("div",{staticClass:"acea-row row-middle",attrs:{slot:"title"},slot:"title"},[a("router-link",{attrs:{to:{path:e.roterPre+"/setting/ticket"}}},[a("div",{staticClass:"font-sm after-line"},[a("span",{staticClass:"iconfont iconfanhui"}),a("span",{staticClass:"pl10"},[e._v("返回")])])]),a("span",{staticClass:"mr20 ml16"},[e._v("小票配置")])],1)])],1),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"acea-row row-between warpper"},[a("Form",{attrs:{model:e.formItem,"label-width":120}},[a("FormItem",{attrs:{label:"小票头部："}},[a("CheckboxGroup",{model:{value:e.formItem.header,callback:function(t){e.$set(e.formItem,"header",t)},expression:"formItem.header"}},[a("Checkbox",{attrs:{label:0}},[e._v("供应商名称")])],1)],1),a("FormItem",{attrs:{label:"配送信息："}},[a("CheckboxGroup",{model:{value:e.formItem.delivery,callback:function(t){e.$set(e.formItem,"delivery",t)},expression:"formItem.delivery"}},[a("Checkbox",{attrs:{label:0}},[e._v("配送方式")]),a("Checkbox",{attrs:{label:1}},[e._v("客户姓名")]),a("Checkbox",{attrs:{label:2}},[e._v("客户电话")]),a("Checkbox",{attrs:{label:3}},[e._v("收货地址")])],1)],1),a("FormItem",{attrs:{label:"买家备注："}},[a("Checkbox",{attrs:{"true-value":1,"false-value":0},model:{value:e.formItem.buyer_remarks,callback:function(t){e.$set(e.formItem,"buyer_remarks",t)},expression:"formItem.buyer_remarks"}},[e._v("买家备注")])],1),a("FormItem",{attrs:{label:"商品信息："}},[a("CheckboxGroup",{model:{value:e.formItem.goods,callback:function(t){e.$set(e.formItem,"goods",t)},expression:"formItem.goods"}},[a("Checkbox",{attrs:{label:0}},[e._v("商品基础信息")]),a("Checkbox",{attrs:{label:1}},[e._v("规格编码")])],1)],1),a("FormItem",{attrs:{label:"运费税费信息："}},[a("Checkbox",{attrs:{"true-value":1,"false-value":0},model:{value:e.formItem.freight,callback:function(t){e.$set(e.formItem,"freight",t)},expression:"formItem.freight"}},[e._v("运费")])],1),a("FormItem",{attrs:{label:"优惠信息："}},[a("Checkbox",{attrs:{"true-value":1,"false-value":0},model:{value:e.formItem.preferential,callback:function(t){e.$set(e.formItem,"preferential",t)},expression:"formItem.preferential"}},[e._v("优惠总计")])],1),a("FormItem",{attrs:{label:"支付信息："}},[a("CheckboxGroup",{model:{value:e.formItem.pay,callback:function(t){e.$set(e.formItem,"pay",t)},expression:"formItem.pay"}},[a("Checkbox",{attrs:{label:0}},[e._v("支付方式")]),a("Checkbox",{attrs:{label:1}},[e._v("实收金额")])],1)],1),a("FormItem",{attrs:{label:"其他订单信息："}},[a("CheckboxGroup",{model:{value:e.formItem.order,callback:function(t){e.$set(e.formItem,"order",t)},expression:"formItem.order"}},[a("Checkbox",{attrs:{label:0}},[e._v("订单编号")]),a("Checkbox",{attrs:{label:1}},[e._v("下单时间")]),a("Checkbox",{attrs:{label:2}},[e._v("支付时间")]),a("Checkbox",{attrs:{label:3}},[e._v("打印时间")])],1)],1),a("FormItem",{attrs:{label:"底部公告："}},[a("Checkbox",{attrs:{"true-value":1,"false-value":0},model:{value:e.formItem.show_notice,callback:function(t){e.$set(e.formItem,"show_notice",t)},expression:"formItem.show_notice"}},[e._v("底部公告")]),e.formItem.show_notice?a("div",[a("Input",{staticStyle:{width:"500px"},attrs:{maxlength:"50","show-word-limit":"",type:"textarea",placeholder:"请输入公告内容"},model:{value:e.formItem.notice_content,callback:function(t){e.$set(e.formItem,"notice_content",t)},expression:"formItem.notice_content"}})],1):e._e()],1)],1),a("div",{staticClass:"ticket-preview"},[a("div",{staticClass:"out-line"}),a("div",{staticClass:"ticket-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.header.includes(0),expression:"formItem.header.includes(0)"}],staticClass:"acea-row row-center-wrapper fs-18 fw-500 mt-6"},[e._v("\n            CRMEB供应商名称\n          ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.delivery.length,expression:"formItem.delivery.length"}],staticClass:"btn-line mt-10 pt-10 pb-10"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.delivery.includes(0),expression:"formItem.delivery.includes(0)"}],staticClass:"acea-row"},[a("div",[e._v("配送方式：")]),a("div",[e._v("商家配送")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.delivery.includes(1),expression:"formItem.delivery.includes(1)"}],staticClass:"acea-row mt-10"},[a("div",[e._v("客户姓名：")]),a("div",[e._v("王一梅")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.delivery.includes(2),expression:"formItem.delivery.includes(2)"}],staticClass:"acea-row mt-10"},[a("div",[e._v("客户电话：")]),a("div",[e._v("13033445566")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.delivery.includes(3),expression:"formItem.delivery.includes(3)"}],staticClass:"acea-row mt-10"},[a("div",[e._v("收货信息：")]),a("div",{staticClass:"flex-1"},[e._v("上海市浦东新区世界大道25号B座309室")])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.buyer_remarks,expression:"formItem.buyer_remarks"}],staticClass:"btn-line pt-10 pb-10"},[a("div",{staticClass:"acea-row"},[a("div",[e._v("买家备注：")]),a("div",{staticClass:"flex-1"},[e._v("\n                买家备注信息买家备注信息买家备注信息买家备注信息买家备注信息\n              ")])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(0),expression:"formItem.goods.includes(0)"}],staticClass:"btn-line pt-10 pb-10"},[a("div",[e._v("商品")]),a("div",{staticClass:"acea-row row-between mt-10"},[a("span",[e._v("单价")]),a("span",[e._v("数量")]),a("span",[e._v("小计")])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(0),expression:"formItem.goods.includes(0)"}],staticClass:"btn-line pt-10 pb-10"},[a("div",[a("div",[e._v("\n                商品名称商品名称商品名称商品名称商品名称商品名称（规格1/规格2）\n              ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(1),expression:"formItem.goods.includes(1)"}],staticClass:"acea-row row-between mt-10"},[a("div",[e._v("规格编码")]),a("div",[e._v("1234567899")])]),a("div",{staticClass:"acea-row row-between mt-10"},[a("span",[e._v("100.0")]),a("span",[e._v("2")]),a("span",[e._v("200.0")])])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(0),expression:"formItem.goods.includes(0)"}],staticClass:"btn-line pt-10 pb-10"},[a("div",[a("div",[e._v("【会员价】商品名称商品名称商品名称商品名称商品名称")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(1),expression:"formItem.goods.includes(1)"}],staticClass:"acea-row row-between mt-10"},[a("div",[e._v("规格编码")]),a("div",[e._v("1234567899")])]),a("div",{staticClass:"acea-row row-between mt-10"},[a("span",[e._v("100.0")]),a("span",[e._v("2")]),a("span",[e._v("200.0")])])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.goods.includes(0),expression:"formItem.goods.includes(0)"}],staticClass:"btn-line acea-row row-between pt-10 pb-10"},[a("div",[e._v("共3件")]),a("div",{staticClass:"fw-500"},[e._v("合计：¥300.00")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.freight,expression:"formItem.freight"}],staticClass:"btn-line acea-row row-between pt-10 pb-10"},[[a("div",[e._v("运费")]),a("div",{staticClass:"fw-500"},[e._v("¥10.00")])]],2),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.preferential,expression:"formItem.preferential"}],staticClass:"btn-line acea-row row-between pt-10 pb-10"},[[a("div",[e._v("优惠")]),a("div",{staticClass:"fw-500"},[e._v("总计：-¥24.00")])]],2),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.pay.length,expression:"formItem.pay.length"}],staticClass:"btn-line pt-10 pb-10"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.pay.includes(0),expression:"formItem.pay.includes(0)"}]},[e._v("支付方式：微信支付")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.pay.includes(1),expression:"formItem.pay.includes(1)"}],staticClass:"mt-10 fw-500"},[e._v("\n              实收：¥276.00\n            ")])]),a("div",{staticClass:"pt-10 pb-10"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.order.includes(0),expression:"formItem.order.includes(0)"}]},[e._v("\n              订单编号：12223378900\n            ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.order.includes(1),expression:"formItem.order.includes(1)"}],staticClass:"mt-10"},[e._v("\n              下单时间：2024/09/23 12:00:00\n            ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.order.includes(2),expression:"formItem.order.includes(2)"}],staticClass:"mt-10"},[e._v("\n              支付时间：2024/09/23 12:00:00\n            ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.formItem.order.includes(3),expression:"formItem.order.includes(3)"}],staticClass:"mt-10"},[e._v("\n              打印时间：2024/09/23 12:00:00\n            ")])]),e.formItem.show_notice?a("div",{staticClass:"mt-20 acea-row row-center-wrapper"},[e._v("\n            "+e._s(e.formItem.notice_content)+"\n          ")]):e._e()]),a("div",{staticClass:"bottom-notice"},[a("img",{staticClass:"image",attrs:{src:r("e9ed"),alt:""}})])])],1)]),a("Card",{staticClass:"fixed-card",attrs:{bordered:!1,"dis-hover":""}},[a("Button",{staticClass:"submission",attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1)],1)},s=[],o=r("2f62"),i=r("90e7"),n=r("d708");function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(r,!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var d={name:"content",data:function(){return{formItem:{scene:1,header:[0],delivery:[0,1,2,3],buyer_remarks:1,goods:[0],freight:1,preferential:1,pay:[0,1],order:[0,1,2,3],code:0,code_url:"",show_notice:1,notice_content:"感谢您的购物，欢迎再次光临！"},id:0,roterPre:n["a"].roterPre}},computed:m({},Object(o["e"])("admin/layout",["isMobile","menuCollapse"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"},labelBottom:function(){return this.isMobile?void 0:15}}),created:function(){this.id=this.$route.params.id,this.getPrintContent()},methods:{getPrintContent:function(){var e=this;Object(i["h"])(this.id,{scene:this.formItem.scene}).then((function(t){Array.isArray(t.data)||(t.data.preferential=t.data.preferential[0],e.formItem=t.data)}))},save:function(){var e=this;Object(i["i"])(this.id,this.formItem).then((function(t){e.$Message.success("保存成功")})).catch((function(t){e.$Message.error("保存失败")}))}}},u=d,v=(r("bf07"),r("2877")),f=Object(v["a"])(u,a,s,!1,null,"4299e45e",null);t["default"]=f.exports},bf07:function(e,t,r){"use strict";var a=r("0583"),s=r.n(a);s.a},e9ed:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgoAAAAaBAMAAAAkpmnUAAAAAXNSR0IArs4c6QAAAA9QTFRFAAAA////////////////j0LeaAAAAAR0Uk5TAECAv6NU3QwAAAB9SURBVFjD7dSxDYAwDETRAzEAUhZAiAFYgeD9Z6JAxESipLvv7l/5CmsLrgqDiEABBRRQQAEFFFBAAQUUUEABhR8UVg3FtFJhkaTiWU2hSpK0OVYqLPc+OlZTOKUXj1elwvHsk1+lwv7so1+lwtztXvWhIL+KaP+h270q6gUS+pJSUmHA9AAAAABJRU5ErkJggg=="}}]);