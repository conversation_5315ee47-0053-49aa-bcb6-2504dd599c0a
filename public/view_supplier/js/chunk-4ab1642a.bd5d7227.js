(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ab1642a"],{"7bcf":function(e,r,t){"use strict";t.r(r);var i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"box"},["dialog"===this.$route.query.fodder||"many"===this.$route.query.type?t("upload-from",{attrs:{isChoice:e.isChoiceD,gridPic:e.gridPic,gridBtn:e.gridBtn},on:{getPicD:e.getPicD}}):t("upload-from",{attrs:{isChoice:e.isChoice,gridPic:e.gridPic,gridBtn:e.gridBtn},on:{getPic:e.getPic}})],1)},o=[],c=t("b0e7"),d={name:"widgetImg",components:{uploadFrom:c["a"]},data:function(){return{isChoice:"单选",isChoiceD:"多选",gridPic:{xl:4,lg:4,md:8,sm:12,xs:12},gridBtn:{xl:4,lg:4,md:4,sm:8,xs:8}}},mounted:function(){console.log(this.$route.query.fodder)},methods:{getPicD:function(e){if("dialog"===this.$route.query.fodder);else{var r=window.form_create_helper.get(this.$route.query.fodder)||[];e=e.map((function(e){return e.att_dir}));var t=r.concat(e),i=Array.from(new Set(t));form_create_helper.set(this.$route.query.fodder,i),form_create_helper.close(this.$route.query.fodder)}},getPic:function(e){form_create_helper.set(this.$route.query.fodder,e.satt_dir),form_create_helper.close(this.$route.query.fodder)}}},n=d,s=(t("dfda"),t("2877")),u=Object(s["a"])(n,i,o,!1,null,"2e1c93f4",null);r["default"]=u.exports},ccf5:function(e,r,t){},dfda:function(e,r,t){"use strict";var i=t("ccf5"),o=t.n(i);o.a}}]);