(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d0b6542"],{3964:function(t,e,a){},5019:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt box",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("FormItem",{attrs:{label:"时间选择："}},[a("DatePicker",{staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1)],1)],1),a("Card",{staticClass:"ive-mt tablebox",attrs:{bordered:!1,"dis-hover":""}},[a("div",{staticClass:"product_tabs tabbox"},[a("Tabs",{on:{"on-click":t.onClickTab}},[a("TabPane",{attrs:{label:"日账单",name:"day"}}),a("TabPane",{attrs:{label:"周账单",name:"week"}}),a("TabPane",{attrs:{label:"月账单",name:"month"}})],1)],1),a("div",{staticClass:"btnbox"}),a("Alert",{staticClass:"ml20 mr20",attrs:{"show-icon":""}},[t._v("本页面账单按照订单完成时间入账")]),a("div",{staticClass:"table"},[a("Table",{ref:"table",staticClass:"mt25",attrs:{columns:t.columns,data:t.orderList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"income_num",fn:function(e){var n=e.row;e.index;return[a("span",{staticStyle:{color:"#f5222d"}},[t._v("￥"+t._s(n.income_num))])]}},{key:"exp_num",fn:function(e){var n=e.row;e.index;return[a("span",{staticStyle:{color:"#00c050"}},[t._v("￥"+t._s(n.exp_num))])]}},{key:"entry_num",fn:function(e){var n=e.row;e.index;return[a("span",[t._v("￥"+t._s(n.entry_num))])]}},{key:"action",fn:function(e){var n=e.row;e.index;return[a("a",{on:{click:function(e){return t.Info(n)}}},[t._v("账单详情")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.download(n)}}},[t._v("下载")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"update:current":function(e){return t.$set(t.formValidate,"page",e)},"on-change":t.pageChange}})],1)],1)],1),a("commission-details",{ref:"commission",attrs:{takeTime:""}})],1)},i=[],r=a("a34a"),o=a.n(r),s=a("2e83"),l=a("78af"),c=a("8745"),u=a("aad2");function d(t){return h(t)||m(t)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function m(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function h(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}function p(t,e,a,n,i,r,o){try{var s=t[r](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(n,i)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function o(t){p(r,n,i,o,s,"next",t)}function s(t){p(r,n,i,o,s,"throw",t)}o(void 0)}))}}var b={name:"bill",components:{commissionDetails:l["a"]},data:function(){return{total:0,loading:!1,tab:"day",options:u["a"],columns:[{title:"ID",key:"id",width:60},{title:"标题",key:"title",minWidth:80},{title:"交易时间",key:"add_time",minWidth:150},{title:"完成时间",key:"take_time",minWidth:150},{title:"收入金额",slot:"income_num",minWidth:80},{title:"支出金额",slot:"exp_num",minWidth:80},{title:"供应商应入账金额",slot:"entry_num",minWidth:80},{title:"操作",slot:"action",fixed:"right",minWidth:120,align:"center"}],orderList:[{id:"1",order_id:"200",pay_price:"200",status:1,phone:"13000000000",address:"100"}],formValidate:{data:"",page:1,limit:20},timeVal:[],fromList:{title:"选择时间",custom:!0,fromTxt:[{text:"全部",val:""},{text:"昨天",val:"yesterday"},{text:"今天",val:"today"},{text:"本周",val:"week"},{text:"本月",val:"month"},{text:"本季度",val:"quarter"},{text:"本年",val:"year"}]}}},computed:{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}},watch:{tab:{handler:function(){this.getList()},immediate:!0}},methods:{onClickTab:function(t){this.tab=t},getList:function(){var t=this;this.loading=!0;var e={timeType:this.tab,data:this.formValidate.data,page:this.formValidate.page,limit:this.formValidate.limit,is_take:1};Object(c["f"])(e).then((function(e){t.orderList=e.data.list,t.loading=!1,t.total=e.data.count}))},selectChange:function(t){this.formValidate.page=1,this.formValidate.data=t,this.timeVal=[],this.getList()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",this.formValidate.page=1,this.getList()},pageChange:function(t){this.formValidate.page=t,this.getList()},Info:function(t){this.$refs.commission.modals=!0,this.$refs.commission.getList(t.ids)},download:function(){var t=g(o.a.mark((function t(e){var a,n,i,r,l,c;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=[],n=[],i=[],r="",l={ids:e.ids,page:1},t.next=4,this.getExcelData(l);case 4:c=t.sent,r||(r=c.filename),n.length||(n=c.filekey),a.length||(a=c.header),i=[].concat(d(i),d(c.export)),Object(s["a"])(a,n,r,i);case 10:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),getExcelData:function(t){return new Promise((function(e){return Object(c["a"])(t).then((function(t){return e(t.data)}))}))}}},w=b,v=(a("ca0e"),a("2877")),y=Object(v["a"])(w,n,i,!1,null,"a17e9652",null);e["default"]=y.exports},"72bd":function(t,e,a){"use strict";var n=a("b89b"),i=a.n(n);i.a},"78af":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"账单详情","mask-closable":!1,width:"950"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Form",{ref:"formValidate",staticClass:"tabform",attrs:{"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{attrs:{type:"flex",gutter:24}},[a("Col",t._b({},"Col",t.grid,!1),[a("FormItem",{attrs:{label:"订单搜索：","label-for":"status1"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入交易单号/交易人"},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1)],1),a("Col",[a("div",{staticClass:"search",on:{click:t.searchs}},[t._v("搜索")])]),a("Col",[a("div",{staticClass:"reset",on:{click:t.reset}},[t._v("重置")])])],1)],1),a("Table",{ref:"table",staticClass:"table",attrs:{columns:t.columns,data:t.tabList,loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"number",fn:function(e){var n=e.row;e.index;return[0==n.pm?a("span",{staticClass:"colorgreen"},[t._v("- "+t._s(n.number))]):t._e(),1==n.pm?a("span",{staticClass:"colorred"},[t._v("+ "+t._s(n.number))]):t._e()]}},{key:"nickname",fn:function(e){var n=e.row;e.index;return[a("span",[t._v(t._s(n.uid?n.user_nickname:"游客"))])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},i=[],r=a("a34a"),o=a.n(r),s=a("8745"),l=a("2f62");function c(t,e,a,n,i,r,o){try{var s=t[r](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(n,i)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function o(t){c(r,n,i,o,s,"next",t)}function s(t){c(r,n,i,o,s,"throw",t)}o(void 0)}))}}function d(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function f(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?d(a,!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):d(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={name:"commissionDetails",props:{takeTime:{type:Boolean,default:!1}},data:function(){var t=[{title:"交易单号",key:"order_id",minWidth:80},{title:"关联订单",key:"link_id",minWidth:80},{title:"交易时间",key:"trade_time",minWidth:150},{title:"完成时间",key:"take_time",minWidth:150},{title:"交易金额",slot:"number",minWidth:80},{title:"交易人",slot:"nickname",ellipsis:!0,minWidth:80},{title:"交易类型",key:"type_name",minWidth:80},{title:"支付方式",key:"pay_type_name",minWidth:80},{title:"备注",key:"mark",minWidth:120}];return this.takeTime||t.forEach((function(e,a){"take_time"==e.key&&t.splice(a,1)})),{grid:{xl:7,lg:7,md:12,sm:24,xs:24},modals:!1,detailsData:{},Ids:0,loading:!1,financeTypeList:[],ids:"",formValidate:{ids:"",staff_id:"",keyword:"",data:"",page:1,limit:10},total:0,columns:t,tabList:[]}},computed:f({},Object(l["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"left"}}),mounted:function(){this.staffApi()},methods:{staffApi:function(){var t=this;Object(s["j"])().then((function(e){var a=e.data;t.financeTypeList=Object.keys(a).map((function(t){return{label:a[t],value:t}}))}))},searchs:function(){this.formValidate.page=1,this.getList(this.ids)},onchangeTime:function(t){this.formValidate.start_time=t[0],this.formValidate.end_time=t[1]},getList:function(t){var e=this;this.ids=t,this.formValidate.ids=t,this.loading=!0,Object(s["g"])(this.formValidate).then(function(){var t=u(o.a.mark((function t(a){var n;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=a.data,e.tabList=n.list,e.total=n.count,e.loading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList(this.ids)},reset:function(){this.formValidate={ids:this.ids,staff_id:"",keyword:"",data:"",page:1,limit:10},this.getList(this.ids)},cancel:function(){this.formValidate={ids:"",staff_id:"",keyword:"",data:"",page:1,limit:10}}}},p=h,g=(a("72bd"),a("2877")),b=Object(g["a"])(p,n,i,!1,null,"2b9802c8",null);e["a"]=b.exports},8745:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"d",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"e",(function(){return s})),a.d(e,"h",(function(){return l})),a.d(e,"i",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"g",(function(){return d})),a.d(e,"a",(function(){return f})),a.d(e,"j",(function(){return m}));var n=a("b6bd");function i(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"get";return Object(n["a"])({url:"/finance/info",method:e,data:t})}function r(t){return Object(n["a"])({url:"/finance/supplier_extract/list",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/finance/supplier_extract/cash",method:"post",data:t})}function s(t,e){return Object(n["a"])({url:"/finance/supplier_extract/mark/".concat(t),method:"post",data:e})}function l(t){return Object(n["a"])({url:"finance/supplier_flowing_water/list",method:"get",params:t})}function c(t,e){return Object(n["a"])({url:"/finance/supplier_flowing_water/mark/".concat(t),method:"post",data:e})}function u(t){return Object(n["a"])({url:"/finance/supplier_flowing_water/fund_record",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/finance/supplier_flowing_water/fund_record_info",method:"get",params:t})}function f(t){return Object(n["a"])({url:"/export/financeRecord",method:"get",params:t})}function m(t){return Object(n["a"])({url:"/finance/supplier_flowing_water/type",method:"get",params:t})}},aad2:function(t,e,a){"use strict";var n=a("c1df"),i=a.n(n);e["a"]={shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,a))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本季度",value:function(){var t=i()(i()().quarter(i()().quarter()).startOf("quarter").valueOf()).format("YYYY-MM-DD"),e=i()(i()().quarter(i()().quarter()).endOf("quarter").valueOf()).format("YYYY-MM-DD");return[t,e]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]}},b89b:function(t,e,a){},ca0e:function(t,e,a){"use strict";var n=a("3964"),i=a.n(n);i.a}}]);