(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4cbde848"],{"3ea6":function(t,e,r){"use strict";var n=r("d42b"),a=r.n(n);a.a},"7f08":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{staticClass:"ivu-mts",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"new_card_pd"},[r("Form",{ref:"artFrom",staticClass:"tabform",attrs:{inline:"",model:t.artFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{attrs:{gutter:24,type:"flex",justify:"end"}},[r("Col",{staticClass:"ivu-text-left",attrs:{span:"24"}},[r("FormItem",{attrs:{label:"规格搜索："}},[r("Input",{staticClass:"input-add",attrs:{placeholder:"请输入规格分类名称"},model:{value:t.artFrom.rule_name,callback:function(e){t.$set(t.artFrom,"rule_name",e)},expression:"artFrom.rule_name"}}),r("Button",{attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("查询")])],1)],1)],1)],1)],1)]),r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("Button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.addAttr}},[t._v("添加商品规格")]),r("Table",{ref:"selection",staticClass:"mt25",attrs:{columns:t.columns4,data:t.tableList,loading:t.loading,"highlight-row":"","no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"attr_value",fn:function(e){var n=e.row;return t._l(n.attr_value,(function(e,n){return r("span",{key:n,staticStyle:{display:"block"},domProps:{textContent:t._s(e)}})}))}},{key:"action",fn:function(e){var n=e.row,a=e.index;return[r("a",{on:{click:function(e){return t.edit(n)}}},[t._v("编辑")]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.del(n,"删除规格",a)}}},[t._v("删除")])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,current:t.artFrom.page,"show-elevator":"","show-total":"","page-size":t.artFrom.limit},on:{"on-change":t.pageChange}})],1)],1),r("add-attr",{ref:"addattr",on:{getList:t.userSearchs}})],1)},a=[],o=r("2f62"),i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{attrs:{scrollable:"",title:t.title,"class-name":"vertical-center-modal",width:"950"},on:{"on-cancel":t.onCancel},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[r("Form",{ref:"formDynamic",staticClass:"attrFrom",attrs:{model:t.formDynamic,rules:t.rules,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[r("Row",{attrs:{gutter:24}},[r("Col",{attrs:{span:"24"}},[r("Col",{staticClass:"mb15",attrs:{span:"8"}},[r("FormItem",{attrs:{label:"分类名称：",prop:"rule_name"}},[r("Input",{attrs:{placeholder:"请输入分类名称",maxlength:20},model:{value:t.formDynamic.rule_name,callback:function(e){t.$set(t.formDynamic,"rule_name",e)},expression:"formDynamic.rule_name"}})],1)],1)],1),t._l(t.formDynamic.spec,(function(e,n){return r("Col",{key:n,staticClass:"noForm",attrs:{span:"23"}},[r("FormItem",[r("div",{staticClass:"acea-row row-middle"},[r("span",{staticClass:"mr5"},[t._v(t._s(e.value))]),r("Icon",{attrs:{type:"ios-close-circle"},on:{click:function(e){return t.handleRemove(n)}}})],1),r("div",{staticClass:"rulesBox"},[t._l(e.detail,(function(n,a){return r("Tag",{key:a,attrs:{type:"dot",closable:"",color:"primary",name:n},on:{"on-close":function(r){return t.handleRemove2(e.detail,a)}}},[t._v(t._s(n))])})),r("Input",{staticClass:"width20",attrs:{maxlength:"30","show-word-limit":"",search:"","enter-button":"添加",placeholder:"请输入属性名称"},on:{"on-search":function(r){return t.createAttr(e.detail.attrsVal,n)}},model:{value:e.detail.attrsVal,callback:function(r){t.$set(e.detail,"attrsVal",r)},expression:"item.detail.attrsVal"}})],2)])],1)})),t.isBtn?r("Col",{staticClass:"mt10",attrs:{span:"24"}},[r("Col",{staticClass:"mr15",attrs:{span:"8"}},[r("FormItem",{attrs:{label:"规格名称："}},[r("Input",{attrs:{placeholder:"请输入规格名称",maxlength:"30","show-word-limit":""},model:{value:t.attrsName,callback:function(e){t.attrsName=e},expression:"attrsName"}})],1)],1),r("Col",{staticClass:"mt10 mr20",attrs:{span:"8"}},[r("FormItem",{attrs:{label:"规格值："}},[r("Input",{attrs:{maxlength:"30","show-word-limit":"",placeholder:"请输入规格值"},model:{value:t.attrsVal,callback:function(e){t.attrsVal=e},expression:"attrsVal"}})],1)],1),r("Col",{staticClass:"mr20",attrs:{span:"8"}},[r("div",{staticClass:"sub"},[r("Button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),r("Button",{on:{click:t.offAttrName}},[t._v("取消")])],1)])],1):t._e(),t.spinShow?r("Spin",{attrs:{size:"large",fix:""}}):t._e()],2),t.isBtn?t._e():r("Button",{staticClass:"ml95 mt10",attrs:{type:"primary"},on:{click:t.addBtn}},[t._v("添加新规格")])],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancle}},[t._v("取消")]),r("Button",{attrs:{type:"primary",loading:t.modal_loading},on:{click:function(e){return t.handleSubmit("formDynamic")}}},[t._v("确定")])],1)],1)},c=[],s=r("c4c8");function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(r,!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var m={name:"addAttr",data:function(){return{spinShow:!1,modal_loading:!1,grid:{xl:3,lg:3,md:12,sm:24,xs:24},modal:!1,index:1,rules:{rule_name:[{required:!0,message:"请输入分类名称",trigger:"blur"}]},formDynamic:{rule_name:"",spec:[]},attrsName:"",attrsVal:"",formDynamicNameData:[],isBtn:!1,formDynamicName:[],results:[],result:[],ids:0,title:"添加商品规格"}},computed:l({},Object(o["e"])("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:110},labelPosition:function(){return this.isMobile?"top":"right"}}),methods:{onCancel:function(){this.clear()},addBtn:function(){if(""==this.formDynamic.rule_name.trim())return this.$Message.error("请输入分类名称");this.isBtn=!0},getIofo:function(t){var e=this;this.spinShow=!0,this.ids=t.id,this.title="编辑商品规格",Object(s["x"])(t.id).then((function(t){e.formDynamic=t.data.info,e.spinShow=!1})).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;if(0===e.formDynamic.spec.length)return e.$Message.warning("请至少添加一条商品规格！");for(var r=0;r<e.formDynamic.spec.length;r++){if(!e.formDynamic.spec[r].value.trim())return e.$Message.warning("请添加规格名称！");if(!e.formDynamic.spec[r].detail.length)return e.$Message.warning("请添加规格值！")}e.modal_loading=!0,setTimeout((function(){Object(s["w"])(e.formDynamic,e.ids).then((function(t){e.$Message.success(t.msg),setTimeout((function(){e.modal=!1,e.modal_loading=!1}),500),setTimeout((function(){e.$emit("getList"),e.clear()}),600)})).catch((function(t){e.modal_loading=!1,e.$Message.error(t.msg)}))}),1200)}))},clear:function(){this.$refs["formDynamic"].resetFields(),this.formDynamic.spec=[],this.isBtn=!1,this.ids=0,this.title="添加商品规格",this.attrsName="",this.attrsVal=""},offAttrName:function(){this.isBtn=!1},cancle:function(){this.modal=!1,this.clear()},handleRemove:function(t){this.formDynamic.spec.splice(t,1)},handleRemove2:function(t,e){t.splice(e,1)},createAttrName:function(){if(this.attrsName&&this.attrsVal){var t={value:this.attrsName,detail:[this.attrsVal]};this.formDynamic.spec.push(t);var e={};this.formDynamic.spec=this.formDynamic.spec.reduce((function(t,r){return!e[r.value]&&(e[r.value]=t.push(r)),t}),[]),this.attrsName="",this.attrsVal="",this.isBtn=!1}else this.$Message.warning("请添加规格名称或规格值")},createAttr:function(t,e){if(t){this.formDynamic.spec[e].detail.push(t);var r={};this.formDynamic.spec[e].detail=this.formDynamic.spec[e].detail.reduce((function(t,e){return!r[e]&&(r[e]=t.push(e)),t}),[])}else this.$Message.warning("请添加属性")}}},f=m,p=(r("3ea6"),r("2877")),h=Object(p["a"])(f,i,c,!1,null,"53265fb2",null),b=h.exports;function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(r,!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var O={name:"productAttr",components:{addAttr:b},data:function(){return{loading:!1,artFrom:{page:1,limit:10,rule_name:""},columns4:[{title:"ID",key:"id",width:80},{title:"分类",key:"rule_name",minWidth:150},{title:"规格名",key:"attr_name",minWidth:250},{title:"规格值",slot:"attr_value",minWidth:300},{title:"操作",slot:"action",fixed:"right",width:120}],tableList:[],total:0}},computed:y({},Object(o["e"])("admin/layout",["isMobile"]),{},Object(o["e"])("admin/order",["orderChartType"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getDataList()},methods:{del:function(t,e,r){var n=this,a={title:e,num:r,url:"product/product/rule/delete/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(a).then((function(t){n.$Message.success(t.msg),n.tableList.splice(r,1),n.tableList.length||(n.artFrom.page=1==n.artFrom.page?1:n.artFrom.page-1),n.getDataList()})).catch((function(t){n.$Message.error(t.msg)}))},addAttr:function(){this.$refs.addattr.ids=0,this.$refs.addattr.modal=!0},edit:function(t){this.$refs.addattr.modal=!0,this.$refs.addattr.getIofo(t)},getDataList:function(){var t=this;this.loading=!0,Object(s["y"])(this.artFrom).then((function(e){var r=e.data;t.tableList=r.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.artFrom.page=t,this.getDataList()},userSearchs:function(){this.artFrom.page=1,this.getDataList()}}},_=O,j=(r("99a9"),Object(p["a"])(_,n,a,!1,null,"c68b2ab6",null));e["default"]=j.exports},"99a9":function(t,e,r){"use strict";var n=r("e624"),a=r.n(n);a.a},c4c8:function(t,e,r){"use strict";r.d(e,"y",(function(){return a})),r.d(e,"w",(function(){return o})),r.d(e,"x",(function(){return i})),r.d(e,"r",(function(){return c})),r.d(e,"z",(function(){return s})),r.d(e,"q",(function(){return u})),r.d(e,"n",(function(){return l})),r.d(e,"e",(function(){return d})),r.d(e,"u",(function(){return m})),r.d(e,"A",(function(){return f})),r.d(e,"t",(function(){return p})),r.d(e,"v",(function(){return h})),r.d(e,"m",(function(){return b})),r.d(e,"D",(function(){return g})),r.d(e,"a",(function(){return y})),r.d(e,"b",(function(){return v})),r.d(e,"o",(function(){return O})),r.d(e,"f",(function(){return _})),r.d(e,"k",(function(){return j})),r.d(e,"l",(function(){return w})),r.d(e,"d",(function(){return D})),r.d(e,"c",(function(){return C})),r.d(e,"i",(function(){return k})),r.d(e,"C",(function(){return P})),r.d(e,"g",(function(){return x})),r.d(e,"p",(function(){return $})),r.d(e,"h",(function(){return F})),r.d(e,"B",(function(){return S})),r.d(e,"j",(function(){return M})),r.d(e,"s",(function(){return B}));var n=r("b6bd");function a(t){return Object(n["a"])({url:"product/product/rule",method:"GET",params:t})}function o(t,e){return Object(n["a"])({url:"product/product/rule/".concat(e),method:"POST",data:t})}function i(t){return Object(n["a"])({url:"product/product/rule/".concat(t),method:"get"})}function c(t){return Object(n["a"])({url:"product/reply",method:"get",params:t})}function s(t,e){return Object(n["a"])({url:"product/reply/set_reply/".concat(e),method:"PUT",data:t})}function u(t){return Object(n["a"])({url:"product/product",method:"get",params:t})}function l(t){return Object(n["a"])({url:"product/type_header",method:"get",params:t})}function d(t){return Object(n["a"])({url:"product/category/cascader_list/".concat(t),method:"get"})}function m(){return Object(n["a"])({url:"product/product_label",method:"get"})}function f(t,e){return Object(n["a"])({url:"product/product/set_show/".concat(t,"/").concat(e),method:"PUT"})}function p(t){return Object(n["a"])({url:"product/product/product_show",method:"put",data:t})}function h(t){return Object(n["a"])({url:"product/product/product_unshow",method:"put",data:t})}function b(){return Object(n["a"])({url:"product/product/get_template",method:"get"})}function g(t){return Object(n["a"])({url:"file/video_attachment",method:"post",data:t})}function y(){return Object(n["a"])({url:"system/form/all_system_form",method:"get"})}function v(t){return Object(n["a"])({url:"product/batch_process",method:"post",data:t})}function O(t){return Object(n["a"])({url:"product/product/".concat(t),method:"get"})}function _(t){return Object(n["a"])({url:"product/product/".concat(t.id),method:"POST",data:t})}function j(){return Object(n["a"])({url:"product/product/get_rule",method:"get"})}function w(t){return Object(n["a"])({url:"product/product/get_temp_keys",method:"get",params:t})}function D(){return Object(n["a"])({url:"product/cache",method:"delete"})}function C(){return Object(n["a"])({url:"product/brand/cascader_list/2",method:"get"})}function k(t){return Object(n["a"])({url:"product/get_all_unit",method:"get"})}function P(){return Object(n["a"])({url:"file/upload_type",method:"get"})}function x(){return Object(n["a"])({url:"product/all_ensure",method:"get"})}function $(){return Object(n["a"])({url:"product/label/form",method:"get"})}function F(){return Object(n["a"])({url:"product/all_specs",method:"get"})}function S(t,e){return Object(n["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function M(t){return Object(n["a"])({url:"product/product/attrs/".concat(t),method:"get"})}function B(t,e){return Object(n["a"])({url:"product/product/saveStocks/".concat(e),method:"PUT",data:t})}},d42b:function(t,e,r){},e624:function(t,e,r){}}]);