(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-28fa7a0e"],{"4faa":function(t,e,i){"use strict";var a=i("b5c7"),s=i.n(a);s.a},7195:function(t,e,i){"use strict";var a=i("bb4b"),s=i.n(a);s.a},b0e7:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"Modal"},[i("Row",{staticClass:"colLeft"},[i("Col",{staticClass:"colLeft left",attrs:{xl:6,lg:6,md:6,sm:6,xs:24}},[i("div",{staticClass:"Nav"},[i("div",{staticClass:"input"},[i("Input",{staticStyle:{width:"90%"},attrs:{search:"","enter-button":"",placeholder:"请输入分类名称"},on:{"on-search":t.changePage},model:{value:t.uploadName.name,callback:function(e){t.$set(t.uploadName,"name",e)},expression:"uploadName.name"}})],1),i("div",{staticClass:"trees-coadd"},[i("div",{staticClass:"scollhide"},[i("div",{staticClass:"trees"},[i("Tree",{ref:"tree",staticClass:"treeBox",attrs:{data:t.treeData,render:t.renderContent,"load-data":t.loadData}}),t.searchClass&&t.treeData.length<=1?i("div",{staticClass:"searchNo"},[t._v("此分类暂无数据")]):t._e()],1)])])])]),i("Col",{staticClass:"colLeft right",attrs:{xl:18,lg:18,md:18,sm:18,xs:24}},[i("div",{staticClass:"conter"},[i("div",{staticClass:"bnt acea-row row-middle"},[i("Col",{attrs:{span:"24"}},[0!==t.isShow?i("Button",{staticClass:"mr10",staticStyle:{width:"100px"},attrs:{type:"primary",disabled:0===t.checkPicList.length},on:{click:t.checkPics}},[t._v("使用选中图片")]):t._e(),i("Button",{staticClass:"mr10",attrs:{type:"primary"},on:{click:t.openUpload}},[t._v("上传图片")]),i("Button",{staticClass:"mr10",attrs:{type:"error",disabled:0===t.checkPicList.length},on:{click:function(e){return e.stopPropagation(),t.editPicList("图片")}}},[t._v("删除图片")]),i("i-select",{staticClass:"treeSel",staticStyle:{width:"250px"},attrs:{value:t.pids,placeholder:"图片移动至"}},[t._l(t.list,(function(e,a){return i("i-option",{key:a,staticStyle:{display:"none"},attrs:{value:e.value}},[t._v("\n            "+t._s(e.title)+"\n          ")])})),i("Tree",{ref:"reference",staticClass:"treeBox",attrs:{data:t.treeData2,render:t.renderContentSel,"load-data":t.loadData}})],2)],1)],1),i("div",{staticClass:"pictrueList acea-row"},[i("Row",{staticClass:"conter",attrs:{gutter:24}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowPic,expression:"isShowPic"}],staticClass:"imagesNo"},[i("Icon",{attrs:{type:"ios-images",size:"60",color:"#dbdbdb"}}),i("span",{staticClass:"imagesNo_sp"},[t._v("图片库为空")])],1),i("div",{staticClass:"acea-row mb10"},t._l(t.pictrueList,(function(e,a){return i("div",{key:a,staticClass:"pictrueList_pic mr10 mb10",on:{mouseenter:function(i){return t.enterMouse(e)},mouseleave:function(i){return t.enterMouse(e)}}},[e.num>0?i("p",{staticClass:"number"},[i("Badge",{attrs:{count:e.num,type:"error",offset:[11,12]}},[i("a",{staticClass:"demo-badge",attrs:{href:"#"}})])],1):t._e(),i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.satt_dir,expression:"item.satt_dir"}],class:e.isSelect?"on":"",on:{click:function(i){return i.stopPropagation(),t.changImage(e,a,t.pictrueList)}}}),i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"},on:{mouseenter:function(i){return t.enterLeave(e)},mouseleave:function(i){return t.enterLeave(e)}}},[e.isEdit?i("Input",{staticStyle:{width:"80%"},attrs:{size:"small",type:"text"},on:{"on-blur":function(i){return t.bindTxt(e)}},model:{value:e.real_name,callback:function(i){t.$set(e,"real_name",i)},expression:"item.real_name"}}):i("p",[t._v("\n                  "+t._s(e.editName)+"\n                ")]),e.isShowEdit?i("span",{staticClass:"iconfont iconbianji1",on:{click:function(t){e.isEdit=!e.isEdit}}}):t._e()],1),i("div",{directives:[{name:"show",rawName:"v-show",value:e.realName&&e.real_name,expression:"item.realName && item.real_name"}],staticClass:"nameStyle"},[t._v("\n                "+t._s(e.real_name)+"\n              ")])])})),0)])],1),i("div",{staticClass:"footer acea-row row-right"},[i("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.fileData.page,"page-size":t.fileData.limit},on:{"on-change":t.pageChange}})],1)])])],1),i("uploadImg",{attrs:{"category-list":t.treeData,"category-id":t.treeId},on:{uploadSuccess:t.uploadSuccess},model:{value:t.uploadVisible,callback:function(e){t.uploadVisible=e},expression:"uploadVisible"}})],1)},s=[],n=i("a34a"),r=i.n(n),o=i("b6bd");function c(t){return Object(o["a"])({url:"file/category",method:"get",params:t})}function l(t){return Object(o["a"])({url:"file/category/create",method:"get",params:t})}function u(t){return Object(o["a"])({url:"file/category/".concat(t,"/edit"),method:"get"})}function d(t){return Object(o["a"])({url:"file/file",method:"get",params:t})}function h(t){return Object(o["a"])({url:"file/file/do_move",method:"put",data:t})}function f(t){return Object(o["a"])({url:"file/file/delete",method:"post",data:t})}function m(t,e){return Object(o["a"])({url:"file/file/update/"+t,method:"put",data:e})}var p=i("d708"),g=i("c276"),v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("Modal",{attrs:{value:t.visible,title:"上传图片",width:"960","class-name":"upload-img-modal"},on:{"on-cancel":t.cancelModal,"on-visible-change":t.visibleChange}},[t.spinShow?i("Spin",{attrs:{size:"large",fix:""}}):t._e(),i("Form",{attrs:{model:t.formData,"label-width":100}},[i("FormItem",{attrs:{label:"上传方式："}},[i("RadioGroup",{on:{"on-change":t.typeChange},model:{value:t.formData.type,callback:function(e){t.$set(t.formData,"type",e)},expression:"formData.type"}},[i("Radio",{attrs:{label:0}},[t._v("本地上传")]),i("Radio",{attrs:{label:1}},[t._v("网络图片")]),i("Radio",{attrs:{label:2}},[t._v("扫码上传")])],1)],1),i("FormItem",{attrs:{label:"上传至分组："}},[i("Cascader",{staticStyle:{width:"426px"},attrs:{data:t.cascaderData,"load-data":t.loadData,"change-on-select":""},on:{"on-change":t.regionChange},model:{value:t.formData.region,callback:function(e){t.$set(t.formData,"region",e)},expression:"formData.region"}})],1),t.formData.type?t._e():i("FormItem",{attrs:{label:"上传图片："}},[i("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"picture-list"},[t._l(t.previewImgList,(function(e,a){return i("div",{key:e.url,staticClass:"item"},[i("img",{staticClass:"image",attrs:{src:e.url},on:{dragstart:function(e){return t.dragstart(a)},dragenter:function(e){return t.dragenter(a,e)}}}),i("Icon",{attrs:{type:"ios-close-circle",size:"16",color:"#999999"},on:{click:function(e){return t.handleRemove(a)}}})],1)})),i("Upload",{ref:"upload",staticClass:"item",attrs:{action:"",multiple:"",accept:"image/png,image/jpeg",format:["jpeg","png"],"show-upload-list":!1,"before-upload":t.beforeUpload}},[i("Icon",{attrs:{type:"ios-add",size:"62",color:"#CCCCCC"}})],1)],2),i("div",{staticClass:"tips"},[t._v("\n        建议上传图片最大宽度750px，不超过"+t._s(t.maxFileSize/1024/1024)+"MB；仅支持jpeg、png格式，可拖拽调整上传顺序\n      ")])]),1===t.formData.type?i("FormItem",{attrs:{label:"网络图片："}},[i("div",{staticClass:"input-wrapper"},[i("Input",{staticStyle:{width:"426px"},attrs:{placeholder:"请网络图片地址"},model:{value:t.imgLink,callback:function(e){t.imgLink=e},expression:"imgLink"}}),i("Button",{attrs:{type:"text"},on:{click:t.pullImage}},[t._v("提取照片")])],1),t.formData.imgList.length?i("div",{staticClass:"picture-list"},t._l(t.formData.imgList,(function(e,a){return i("div",{key:e.url,staticClass:"item"},[i("img",{staticClass:"image",attrs:{src:e.url},on:{dragstart:function(e){return t.dragstart(a)},dragenter:function(e){return t.dragenter(a,e)}}}),i("Icon",{attrs:{type:"ios-close-circle",size:"16",color:"#999999"},on:{click:function(e){return t.handleRemove(a)}}})],1)})),0):t._e(),t.formData.imgList.length>1?i("div",{staticClass:"tips"},[t._v("\n        鼠标拖拽图片可调整图层顺序\n      ")]):t._e()]):t._e(),2===t.formData.type?i("FormItem",{attrs:{label:"二维码："}},[i("div",{staticClass:"scan-code-upload"},[i("div",{staticClass:"qrcode-wrapper"},[i("div",{ref:"qrcode",staticClass:"qrcode"}),i("div",{staticClass:"tips large"},[t._v("扫描二维码，快速上传手机图片")]),i("div",{staticClass:"tips"},[t._v("建议使用手机浏览器")])]),i("div",{staticClass:"picture-wrapper"},[i("Button",{on:{click:t.scanQRCodeImageList}},[t._v("刷新图库")]),i("div",{staticClass:"tips"},[t._v("刷新图库按钮，可显示移动端上传成功的图片")]),i("div",{staticClass:"picture-list"},t._l(t.formData.imgList,(function(e,a){return i("div",{key:e.att_id,staticClass:"item"},[i("img",{staticClass:"image",attrs:{src:e.att_dir,alt:""}}),i("Icon",{attrs:{type:"ios-close-circle",size:"16",color:"#999999"},on:{click:function(i){return t.handleRemove(e.att_id)}}})],1)})),0)],1)])]):t._e()],1),i("div",{staticClass:"modal-footer",attrs:{slot:"footer"},slot:"footer"},[i("Button",{on:{click:t.cancelModal}},[t._v("取消")]),i("Button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确认")])],1)],1)},y=[],w=i("91b6"),C=i("d044"),b=i.n(C);function L(t){return new Promise((function(e,i){var a=new FileReader;a.readAsDataURL(t),a.onload=function(){var i=new Image;i.src=a.result,i.onload=function(){var a=i.width,s=i.height,n=document.createElement("canvas");n.width=a,n.height=s;var r=n.getContext("2d");r.fillStyle="#fff",r.fillRect(0,0,n.width,n.height),r.drawImage(i,0,0,a,s);var o=n.toDataURL("image/jpeg",.8),c=k(o,t.name);e(c)}}}))}function k(t,e){var i=t.split(","),a=i[0].match(/:(.*?);/)[1],s=atob(i[1]),n=s.length,r=new Uint8Array(n);while(n--)r[n]=s.charCodeAt(n);return new File([r],e,{type:a})}function D(t,e,i,a,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void i(l)}o.done?e(c):Promise.resolve(c).then(a,s)}function _(t){return function(){var e=this,i=arguments;return new Promise((function(a,s){var n=t.apply(e,i);function r(t){D(n,a,s,r,o,"next",t)}function o(t){D(n,a,s,r,o,"throw",t)}r(void 0)}))}}var x={model:{prop:"visible",event:"cancel"},props:{visible:{type:Boolean,default:!1},categoryList:{type:Array,default:function(){return[]}},categoryId:{type:Number,default:0}},data:function(){return{formData:{type:0,region:[],imgList:[]},imgLink:"",spinShow:!1,categoryData:[],maxFileSize:0}},computed:{cascaderData:function(){return this.mapCategoryList(this.categoryData)},previewImgList:function(){return this.formData.imgList.map((function(t){return{url:URL.createObjectURL(t)}}))},selectedCategoryId:function(){return this.formData.region[this.formData.region.length-1]||0}},watch:{categoryList:function(t){this.categoryData=t,this.findCategoryId(t)},categoryId:{handler:function(){this.findCategoryId(this.categoryList)},immediate:!0}},methods:{visibleChange:function(t){var e=this;t?(this.findCategoryId(this.categoryList),Object(w["a"])().then((function(t){e.formData.type=t.data.is_way,e.maxFileSize=t.data.upload_file_size_max}))):(this.formData.imgList=[],this.imgLink="",Object(w["f"])(this.formData.type))},findCategoryId:function(t){var e=this,i=[],a=this.categoryId||"",s=t.find((function(t){return t.id===a||!!t.children&&e.findCategoryId(t.children)}));s&&(s.pid&&i.push(s.pid),i.push(s.id),this.formData.region=i)},mapCategoryList:function(t){var e=this;return t.map((function(t){return t.value=t.id,t.label=t.title,t.children&&(t.children=e.mapCategoryList(t.children)),t}))},loadData:function(t,e){var i=this;t.loading=!0,c({pid:t.id,file_type:1}).then((function(a){t.children=a.data.list,t.loading=!1,i.$nextTick(e)}))},typeChange:function(t){this.formData.imgList=[],this.imgLink="",this.timer&&(clearTimeout(this.timer),this.timer=null),2===t?this.scanQRCodeText():this.qrcode&&(this.qrcode=null,Object(w["c"])())},regionChange:function(){2===this.formData.type&&this.scanQRCodeText()},cancelModal:function(){this.formData.imgList=[],this.imgLink="",this.timer&&(clearTimeout(this.timer),this.timer=null),Object(w["c"])(),this.$emit("cancel",!1)},dragstart:function(t){this.dragIndex=t},dragenter:function(t,e){if(e.preventDefault(),t!==this.dragIndex){var i=this.formData.imgList[this.dragIndex];this.formData.imgList.splice(this.dragIndex,1),this.formData.imgList.splice(t,0,i),this.dragIndex=t}},beforeUpload:function(){var t=_(r.a.mark((function t(e){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!(e.size>=this.maxFileSize)){t.next=4;break}return t.next=3,L(e);case 3:e=t.sent;case 4:return this.formData.imgList.push(e),t.abrupt("return",!1);case 6:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleRemove:function(t){var e=this;2===this.formData.type?f({ids:t}).then((function(){e.scanQRCodeImageList()})):this.formData.imgList.splice(t,1)},scanQRCodeImageList:function(){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=null),Object(w["d"])(this.token).then((function(e){t.formData.imgList=e.data,t.timer=setTimeout(t.scanQRCodeImageList,2e3)}))},uploadFile:function(t){var e=this;return new Promise((function(i){var a=new FormData;a.append("file",t),a.append("pid",e.selectedCategoryId),Object(w["h"])(a).then(i)}))},scanQRCodeText:function(){var t=this;Object(w["e"])({pid:this.selectedCategoryId}).then((function(e){t.createQRCode(e.data.url)}))},createQRCode:function(t){this.qrcode?this.qrcode.makeCode(t):this.qrcode=new b.a(this.$refs.qrcode,{text:t,width:160,height:160,colorDark:"#000000",colorLight:"#ffffff",correctLevel:b.a.CorrectLevel.H}),this.token=t.split("token=")[1],this.scanQRCodeImageList()},uploadOnlineFile:function(){var t=this;this.spinShow=!0,Object(w["i"])({pid:this.selectedCategoryId,images:this.formData.imgList.map((function(t){return t.url}))}).then((function(){t.spinShow=!1,t.$Message.success("上传成功"),t.$emit("uploadSuccess")})).catch((function(e){t.spinShow=!1,t.$Message.error(e.msg)}))},submitForm:function(){switch(this.formData.type){case 1:this.uploadOnlineFile();break;case 2:this.moveApi();break;default:this.batchUpload();break}},batchUpload:function(){var t=_(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.spinShow=!0,e=0;case 2:if(!(e<this.formData.imgList.length)){t.next=9;break}return t.next=5,this.uploadFile(this.formData.imgList[e]);case 5:e===this.formData.imgList.length-1&&(this.spinShow=!1,this.$Message.success("上传成功"),this.$emit("uploadSuccess"));case 6:e++,t.next=2;break;case 9:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),moveApi:function(){var t=this.formData.imgList.map((function(t){return t.att_id}));this.selectedCategoryId,t.join();this.spinShow=!1,this.$Message.success("上传成功"),Object(w["c"])(),this.$emit("uploadSuccess")},pullImage:function(){this.imgLink&&this.formData.imgList.push({url:this.imgLink})}}},S=x,I=(i("4faa"),i("2877")),$=Object(I["a"])(S,v,y,!1,null,"6ea892e0",null),P=$.exports;function M(t){return N(t)||j(t)||F()}function F(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function j(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function N(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}function O(t,e,i,a,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void i(l)}o.done?e(c):Promise.resolve(c).then(a,s)}function R(t){return function(){var e=this,i=arguments;return new Promise((function(a,s){var n=t.apply(e,i);function r(t){O(n,a,s,r,o,"next",t)}function o(t){O(n,a,s,r,o,"throw",t)}r(void 0)}))}}var z={name:"uploadPictures",components:{uploadImg:P},props:{isChoice:{type:String,default:""},gridBtn:{type:Object,default:null},gridPic:{type:Object,default:null},isShow:{type:Number,default:1}},data:function(){return{searchClass:!1,spinShow:!1,fileUrl:p["a"].apiBaseURL+"/file/upload",modalPic:!1,treeData:[],treeData2:[],pictrueList:[],uploadData:{},checkPicList:[],uploadName:{name:""},FromData:null,treeId:0,isJudge:!1,buttonProps:{type:"default",size:"small"},fileData:{pid:0,page:1,limit:24},total:0,pids:0,list:[],modalTitleSs:"",isShowPic:!1,header:{},ids:[],uploadList:[],uploadVisible:!1}},mounted:function(){this.getToken(),this.getList(),this.getFileList()},methods:{enterMouse:function(t){t.realName=!t.realName},enterLeave:function(t){t.isShowEdit=!t.isShowEdit},getToken:function(){this.header["Authori-zation"]="Bearer "+g["a"].cookies.get("token")},renderContent:function(t,e){var i=this,a=e.root,s=e.node,n=e.data,r=[];return 0==n.pid&&r.push(t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.append(a,s,n)}}},"添加分类")),""!==n.id&&r.push(t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.editPic(a,s,n)}}},"编辑分类"),t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.remove(a,s,n,"分类")}}},"删除分类")),t("span",{class:["ivu-span"],style:{display:"inline-block",width:"88%",height:"32px",lineHeight:"32px",position:"relative",color:"rgba(0,0,0,0.6)",cursor:"pointer"},on:{mouseenter:function(){i.onMouseOver(a,s,n)},mouseleave:function(){i.onMouseOver(a,s,n)}}},[t("span",{on:{click:function(t){i.checkPicList=[],i.appendBtn(a,s,n,t)}}},n.title),t("div",{style:{display:"inline-block",float:"right"}},[t("Icon",{props:{type:"ios-more"},style:{marginRight:"8px",fontSize:"20px",display:n.flag?"inline":"none"},on:{click:function(){i.onClick(a,s,n)}}}),t("div",{class:["right-menu ivu-poptip-inner"],style:{width:"80px",position:"absolute",zIndex:"9",top:"0",right:"0",display:n.flag2?"block":"none"}},r)])])},renderContentSel:function(t,e){var i=this,a=e.root,s=e.node,n=e.data;return t("div",{style:{display:"inline-block",width:"90%"}},[t("span",[t("span",{style:{cursor:"pointer"},class:["ivu-tree-title"],on:{click:function(t){i.handleCheckChange(a,s,n,t)}}},n.title)])])},handleCheckChange:function(t,e,i,a){this.list=[];var s=i.id,n=i.title;this.list.push({value:s,title:n}),this.ids.length?(this.pids=s,this.getMove()):this.$Message.warning("请先选择图片");for(var r=this.$refs.reference.$el.querySelectorAll(".ivu-tree-title-selected"),o=0;o<r.length;o++)r[o].className="ivu-tree-title";a.path[0].className="ivu-tree-title  ivu-tree-title-selected"},getMove:function(){var t=this,e={pid:this.pids,images:this.ids.toString()};h(e).then(function(){var e=R(r.a.mark((function e(i){return r.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$Message.success(i.msg),t.getFileList(),t.pids=0,t.checkPicList=[],t.ids=[];case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},editPicList:function(t){var e=this;this.tits=t;var i={ids:this.ids.toString()},a={title:"删除选中图片",url:"file/file/delete",method:"POST",ids:i};this.$modalSure(a).then((function(t){e.$Message.success(t.msg),e.getFileList(),e.checkPicList=[]})).catch((function(t){e.$Message.error(t.msg)}))},onMouseOver:function(t,e,i){event.preventDefault(),i.flag=!i.flag,i.flag2&&(i.flag2=!1)},onClick:function(t,e,i){i.flag2=!i.flag2},appendBtn:function(t,e,i,a){this.treeId=i.id,this.fileData.page=1,this.getFileList();for(var s=this.$refs.tree.$el.querySelectorAll(".ivu-tree-title-selected"),n=0;n<s.length;n++)s[n].className="ivu-tree-title";a.path[0].className="ivu-tree-title  ivu-tree-title-selected"},append:function(t,e,i){this.treeId=i.id,this.getFrom()},remove:function(t,e,i,a){var s=this;this.tits=a;var n={title:"删除 [ "+i.title+" ] 分类",url:"file/category/".concat(i.id),method:"DELETE",ids:""};this.$modalSure(n).then((function(t){s.$Message.success(t.msg),s.getList(),s.checkPicList=[]})).catch((function(t){s.$Message.error(t.msg)}))},editPic:function(t,e,i){var a=this;this.$modalForm(u(i.id)).then((function(){return a.getList()}))},changePage:function(){this.getList("search")},getList:function(t){var e=this,i={title:"全部图片",id:"",pid:0};c(this.uploadName).then(function(){var a=R(r.a.mark((function a(s){return r.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.treeData=s.data.list,e.treeData.unshift(i),"search"!==t?e.treeData2=M(e.treeData):e.searchClass=!0,e.addFlag(e.treeData);case 4:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},loadData:function(t,e){c({pid:t.id}).then(function(){var t=R(r.a.mark((function t(i){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=i.data.list,e(a);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){}))},addFlag:function(t){var e=this;t.map((function(t){e.$set(t,"flag",!1),e.$set(t,"flag2",!1),t.children&&e.addFlag(t.children)}))},add:function(){this.treeId=0,this.getFrom()},getFileList:function(){var t=this;this.fileData.pid=this.treeId,d(this.fileData).then(function(){var e=R(r.a.mark((function e(i){return r.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.data.list.forEach((function(e){e.isSelect=!1,e.isEdit=!1,e.isShowEdit=!1,e.realName=!1,e.num=0,t.editName(e)})),t.pictrueList=i.data.list,t.pictrueList.length?t.isShowPic=!1:t.isShowPic=!0,t.total=i.data.count;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},pageChange:function(t){this.fileData.page=t,this.getFileList(),this.checkPicList=[]},getFrom:function(){var t=this;this.$modalForm(l({id:this.treeId})).then((function(e){console.log(e),t.getList()}))},beforeUpload:function(t){var e=this;if(this.uploadList.length>4)return!1;var i=["image/png","image/jpg","image/jpeg","image/gif"],a=-1!==i.indexOf(t.type);if(!a)return this.$Message.warning({content:"文件  "+t.name+"  格式不正确, 请选择格式正确的图片",duration:5}),!1;console.log(t.size,"文件大小");var s=localStorage.getItem("file_size_max"),n=t.size<s,r=s/1024/1024;if(!n)return this.$Message.warning({content:"文件体积过大,图片大小不能超过"+r+"M",duration:5}),!1;this.uploadList.push(t),this.uploadData={pid:this.treeId};var o=new Promise((function(t){e.$nextTick((function(){t(!0)}))}));return o},handleSuccess:function(t,e,i){200===t.status?(this.uploadList=[],this.fileData.page=1,this.$Message.success(t.msg),this.getFileList()):this.$Message.error(t.msg)},cancel:function(){this.$emit("changeCancel")},changImage:function(t,e,i){var a=this,s=0;t.isSelect?(t.isSelect=!1,this.checkPicList.map((function(e,i){e.att_id==t.att_id&&(s=i)})),this.checkPicList.splice(s,1)):(t.isSelect=!0,this.checkPicList.push(t)),this.ids=[],this.checkPicList.map((function(t,e){a.ids.push(t.att_id)})),this.pictrueList.map((function(t,e){t.isSelect?a.checkPicList.filter((function(e,i){t.att_id==e.att_id&&(t.num=i+1)})):t.num=0}))},checkPics:function(){if("单选"===this.isChoice){if(this.checkPicList.length>1)return this.$Message.warning("最多只能选一张图片");this.$emit("getPic",this.checkPicList[0])}else{var t=this.$route.query.maxLength;if(void 0!=t&&this.checkPicList.length>Number(t))return this.$Message.warning("最多只能选"+t+"张图片");this.$emit("getPicD",this.checkPicList),console.log(this.checkPicList)}},editName:function(t){var e=t.real_name.split("."),i=void 0==e[1]?[]:e[1];e[0].length,i.length;t.editName=t.real_name},bindTxt:function(t){var e=this;""==t.real_name&&this.$Message.error("请填写内容"),m(t.att_id,{real_name:t.real_name}).then((function(i){e.editName(t),t.isEdit=!1,e.$Message.success(i.msg)})).catch((function(t){e.$Message.error(t.msg)}))},openUpload:function(){this.uploadVisible=!0},uploadSuccess:function(){this.uploadVisible=!1,this.fileData.page=1,this.getFileList()}}},T=z,B=(i("7195"),Object(I["a"])(T,a,s,!1,null,"266178fc",null));e["a"]=B.exports},b5c7:function(t,e,i){},bb4b:function(t,e,i){}}]);