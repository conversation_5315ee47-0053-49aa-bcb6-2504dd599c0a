(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-115997fc"],{5706:function(t,e,a){"use strict";var r=a("ddc3"),n=a.n(r);n.a},"622c":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt pt10",attrs:{bordered:!1,"dis-hover":""}},[[a("Form",{ref:"formValidate",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":150}},[a("FormItem",{attrs:{label:"名称:",prop:"supplier_name"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入名称"},model:{value:t.formValidate.supplier_name,callback:function(e){t.$set(t.formValidate,"supplier_name",e)},expression:"formValidate.supplier_name "}})],1),a("FormItem",{attrs:{label:"联系人姓名:",prop:"name"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入联系人姓名"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),a("FormItem",{attrs:{label:"联系电话:",prop:"phone"}},[a("Input",{staticClass:"input",attrs:{type:"text",maxlength:"11",placeholder:"请输入联系电话"},model:{value:t.formValidate.phone,callback:function(e){t.$set(t.formValidate,"phone",e)},expression:"formValidate.phone"}})],1),a("FormItem",{attrs:{label:"邮箱:",prop:"email"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入邮箱"},model:{value:t.formValidate.email,callback:function(e){t.$set(t.formValidate,"email",e)},expression:"formValidate.email"}})],1),a("FormItem",{attrs:{label:"省市区:","label-for":"address",prop:"address"}},[a("Cascader",{staticClass:"input",attrs:{data:t.addresData,"load-data":t.loadData},on:{"on-change":t.addchack},model:{value:t.formValidate.addressSelect,callback:function(e){t.$set(t.formValidate,"addressSelect",e)},expression:"formValidate.addressSelect"}})],1),a("FormItem",{attrs:{label:"详细地址:",prop:"detailed_address"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入详细地址"},model:{value:t.formValidate.detailed_address,callback:function(e){t.$set(t.formValidate,"detailed_address",e)},expression:"formValidate.detailed_address"}})],1),a("FormItem",{attrs:{label:"登录用户名:",prop:"account"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入登录名称"},model:{value:t.formValidate.account,callback:function(e){t.$set(t.formValidate,"account",e)},expression:"formValidate.account"}})],1),a("FormItem",{attrs:{label:"登录密码:",prop:"pwd"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请输入登录密码"},model:{value:t.formValidate.pwd,callback:function(e){t.$set(t.formValidate,"pwd",e)},expression:"formValidate.pwd"}})],1),a("FormItem",{attrs:{label:"确认登录密码:",prop:"conf_pwd"}},[a("Input",{staticClass:"input",attrs:{placeholder:"请确认登录密码"},model:{value:t.formValidate.conf_pwd,callback:function(e){t.$set(t.formValidate,"conf_pwd",e)},expression:"formValidate.conf_pwd"}})],1)],1)]],2),a("Card",{staticClass:"fixed-card",attrs:{bordered:!1,"dis-hover":""}},[a("Form",[a("FormItem",[a("Button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("保存")])],1)],1)],1)],1)},n=[],o=a("90e7"),i=a("2f62");function c(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function s(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?c(a,!0).forEach((function(e){d(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):c(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function d(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var l={name:"index",components:{},data:function(){var t=this,e=function(e,a,r){""===a?r(new Error("请输入联系电话")):""!==t.value&&(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(a)?r():r(new Error("请输入正确的联系电话")))};return{formValidate:{supplier_name:"",name:"",email:"",phone:"",detailed_address:"",province:0,city:0,area:0,street:0,addressSelect:[],address:""},addresData:[],ruleValidate:{supplier_name:[{required:!0,message:"请输入名称",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{validator:e,trigger:"blur"}],account:[{required:!0,message:"请输入用户名",trigger:"blur"}]}}},created:function(){this.getInfo();var t={pid:0};this.cityInfo(t)},mounted:function(){this.setCopyrightShow({value:!1})},destroyed:function(){this.setCopyrightShow({value:!0})},methods:s({},Object(i["d"])("store/layout",["setCopyrightShow"]),{loadData:function(t,e){t.loading=!0,Object(o["d"])({pid:t.value}).then((function(a){t.children=a.data,t.loading=!1,e()}))},addchack:function(t,e){var a=this;t.forEach((function(t,e){0==e?a.formValidate.province=t:1==e?a.formValidate.city=t:2==e?a.formValidate.area=t:a.formValidate.street=t})),this.formValidate.address=e.map((function(t){return t.label})).join("/")},cityInfo:function(t){var e=this;Object(o["d"])(t).then((function(t){e.addresData=t.data}))},getInfo:function(){var t=this;Object(o["n"])().then((function(e){t.formValidate=e.data;var a=[];e.data.province&&a.push(e.data.province),e.data.city&&a.push(e.data.city),e.data.area&&a.push(e.data.area),e.data.street&&a.push(e.data.street),t.formValidate.addressSelect=a})).catch((function(e){t.$Message.error(e.msg)}))},handleSubmit:function(t){var e=this;if(""!==this.formValidate.email&&!this.validateEmail(this.formValidate.email))return this.$Message.error("请输入正确的邮箱");this.$refs[t].validate((function(t){t&&Object(o["k"])(e.formValidate).then((function(t){e.$Message.success(t.msg),e.getInfo()})).catch((function(t){e.$Message.error(t.msg)}))}))},validateEmail:function(t){var e=/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/i;return e.test(t)}})},u=l,p=(a("5706"),a("2877")),f=Object(p["a"])(u,r,n,!1,null,"122f8115",null);e["default"]=f.exports},"90e7":function(t,e,a){"use strict";a.d(e,"n",(function(){return n})),a.d(e,"k",(function(){return o})),a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return c})),a.d(e,"b",(function(){return s})),a.d(e,"a",(function(){return d})),a.d(e,"l",(function(){return l})),a.d(e,"p",(function(){return u})),a.d(e,"q",(function(){return p})),a.d(e,"m",(function(){return f})),a.d(e,"e",(function(){return m})),a.d(e,"o",(function(){return h})),a.d(e,"f",(function(){return b})),a.d(e,"j",(function(){return g})),a.d(e,"g",(function(){return v})),a.d(e,"h",(function(){return V})),a.d(e,"i",(function(){return O}));var r=a("b6bd");function n(){return Object(r["a"])({url:"/supplier",method:"get"})}function o(t){return Object(r["a"])({url:"/supplier",method:"put",data:t})}function i(t){return Object(r["a"])({url:"city",method:"get",params:t})}function c(t){return Object(r["a"])({url:"admin",method:"get",params:t})}function s(){return Object(r["a"])({url:"admin/create",method:"get"})}function d(t){return Object(r["a"])({url:"admin/".concat(t,"/edit"),method:"get"})}function l(t){return Object(r["a"])({url:"admin/set_status/".concat(t.id,"/").concat(t.status),method:"put"})}function u(t){return Object(r["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function p(t,e){return Object(r["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function f(t){return Object(r["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function m(t){return Object(r["a"])({url:"city",method:"get",params:t})}function h(t,e){return Object(r["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function b(t){return Object(r["a"])({url:"/print/list",method:"get",params:t})}function g(t){var e=t.id,a=t.status;return Object(r["a"])({url:"/print/set_status/".concat(e,"/").concat(a),method:"get"})}function v(t,e){return Object(r["a"])({url:"/print/save/".concat(t),method:"post",data:e})}function V(t,e){return Object(r["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function O(t,e){return Object(r["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},ddc3:function(t,e,a){}}]);