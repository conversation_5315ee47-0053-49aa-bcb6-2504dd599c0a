(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-537ea75c"],{"21c4":function(t,e,r){"use strict";var n=r("9048"),a=r.n(n);a.a},"2d28":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{"background-color":"#fff"}},t._l(t.newArrayData,(function(e,n){return r("div",{key:n,staticClass:"putSupplier"},[r("div",{staticClass:"header acea-row row-between-wrapper"},[r("div",{staticClass:"left acea-row row-middle"},[r("div",{staticClass:"pictrue",attrs:{id:"qrCodeUrl"+n}}),r("div",{staticClass:"info"},[r("div",[r("span",{staticClass:"name"},[t._v("收货人：")]),t._v(t._s(e.real_name))]),r("div",[r("span",{staticClass:"name"},[t._v("收货地址：")]),t._v(t._s(e.user_address))]),r("div",[r("span",{staticClass:"name"},[t._v("手机号：")]),r("span",[t._v(t._s(e.user_phone))])])])]),r("div",{staticClass:"info"},[r("div",[r("span",{staticClass:"name"},[t._v("订单编号：")]),t._v(t._s(e.order_id))]),r("div",[r("span",{staticClass:"name"},[t._v("支付时间：")]),t._v(t._s(e.pay_time))]),r("div",[r("span",{staticClass:"name"},[t._v("支付方式：")]),t._v(t._s(e.pay_type_name))])])]),r("div",{staticClass:"mt20"},[r("Table",{attrs:{border:"",columns:t.columns,data:e.goodsList,"disabled-hover":!0},scopedSlots:t._u([{key:"store_name",fn:function(e){var n=e.row;return[r("div",{staticClass:"line1"},[t._v(t._s(n.store_name))])]}},{key:"suk",fn:function(e){var n=e.row;return[r("div",{staticClass:"line1"},[t._v(t._s(n.suk))])]}}],null,!0)})],1),r("div",{staticClass:"bottom acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"item"},[r("span",{staticClass:"name"},[t._v("运费：")]),t._v(t._s(e.freight_price))]),r("div",{staticClass:"item"},[r("span",{staticClass:"name"},[t._v("优惠：")]),t._v(t._s(e.coupon_price))]),r("div",{staticClass:"item"},[r("span",{staticClass:"name"},[t._v("会员折扣：")]),t._v(t._s(e.vip_true_price))]),r("div",{staticClass:"item"},[r("span",{staticClass:"name"},[t._v("积分抵扣：")]),t._v(t._s(e.deduction_price))])]),r("div",{staticClass:"pricePay"},[t._v("实付金额：￥"+t._s(e.pay_price))])]),r("div",{staticClass:"bottom acea-row"},[r("div",{staticClass:"name"},[t._v("用户备注："),r("span",{staticClass:"con"},[t._v(t._s(e.mark||"-"))])])]),r("div",{staticClass:"h50"},[t.data.site_name||t.data.refund_phone||t.data.refund_address?r("div",{staticClass:"delivery"},[t.data.site_name?r("div",[t._v("店铺信息："+t._s(t.data.site_name))]):t._e(),t.data.refund_address?r("div",[t._v("地址："+t._s(t.data.refund_address))]):t._e(),t.data.refund_phone?r("div",[t._v("联系方式："+t._s(t.data.refund_phone))]):t._e()]):t._e()])])})),0)},a=[],u=r("f8b7"),i=r("d044"),c=r.n(i),o=r("d708"),d={data:function(){return{columns:[{title:"商品编号",key:"index",align:"center",width:60},{title:"商品名称",slot:"store_name",align:"center",width:253},{title:"商品规格",slot:"suk",align:"center",width:219},{title:"单价",key:"truePrice",align:"center",width:100},{title:"数量",key:"cart_num",align:"center",width:60},{title:"金额",key:"subtotal",align:"center",width:100}],data:{},goods:[],BaseURL:o["a"].apiBaseURL.replace(/supplierapi/,""),newArrayData:[]}},created:function(){this.getDistribution()},mounted:function(){this.$nextTick((function(){var t=this;setTimeout((function(){t.creatQrCode()}),200)}))},methods:{creatQrCode:function(){var t=this.BaseURL;this.newArrayData.forEach((function(e,r){var n=document.getElementById("qrCodeUrl"+r);new c.a(n,{text:t,width:90,height:90,colorDark:"#000000",colorLight:"#ffffff",correctLevel:c.a.CorrectLevel.H})}))},getDistribution:function(){var t=this;Object(u["f"])(this.$route.query.id).then((function(e){t.data=e.data,t.goods=e.data.list;var r=e.data,n=[];r.list.forEach((function(t,e){for(var r=[],a=t.list,u=Math.ceil(a.length/6),i=0;i<u;i++){var c=a.slice(6*i,6*i+6);c.length&&r.push(c)}var o=6-r[r.length-1].length;if(o)for(var d=0;d<o;d++)r[r.length-1].push({cart_num:"",index:"",store_name:"",subtotal:"",suk:"",truePrice:""});r.forEach((function(e){n.push({real_name:t.real_name,user_address:t.user_address,user_phone:t.user_phone,freight_price:t.freight_price,coupon_price:t.coupon_price,vip_true_price:t.vip_true_price,deduction_price:t.deduction_price,use_integral:t.use_integral,pay_price:t.pay_price,mark:t.mark,order_id:t.order_id,pay_time:t.pay_time,pay_type_name:t.pay_type_name,goodsList:e})}))})),t.newArrayData=n})).catch((function(e){t.$Message.error(e.msg)}))}}},s=d,l=(r("21c4"),r("2877")),f=Object(l["a"])(s,n,a,!1,null,"47fe3266",null);e["default"]=f.exports},9048:function(t,e,r){},f8b7:function(t,e,r){"use strict";r.d(e,"r",(function(){return a})),r.d(e,"h",(function(){return u})),r.d(e,"l",(function(){return i})),r.d(e,"v",(function(){return c})),r.d(e,"G",(function(){return o})),r.d(e,"q",(function(){return d})),r.d(e,"p",(function(){return s})),r.d(e,"t",(function(){return l})),r.d(e,"F",(function(){return f})),r.d(e,"m",(function(){return _})),r.d(e,"D",(function(){return p})),r.d(e,"n",(function(){return m})),r.d(e,"d",(function(){return h})),r.d(e,"g",(function(){return v})),r.d(e,"j",(function(){return b})),r.d(e,"H",(function(){return g})),r.d(e,"i",(function(){return O})),r.d(e,"e",(function(){return j})),r.d(e,"f",(function(){return w})),r.d(e,"K",(function(){return C})),r.d(e,"o",(function(){return y})),r.d(e,"c",(function(){return k})),r.d(e,"z",(function(){return x})),r.d(e,"J",(function(){return q})),r.d(e,"u",(function(){return D})),r.d(e,"C",(function(){return L})),r.d(e,"b",(function(){return E})),r.d(e,"A",(function(){return A})),r.d(e,"B",(function(){return B})),r.d(e,"a",(function(){return U})),r.d(e,"I",(function(){return $})),r.d(e,"y",(function(){return J})),r.d(e,"x",(function(){return P})),r.d(e,"s",(function(){return R})),r.d(e,"k",(function(){return S})),r.d(e,"w",(function(){return T})),r.d(e,"E",(function(){return H}));var n=r("b6bd");function a(t){return Object(n["a"])({url:"order/list",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/order/express_list?status="+t,method:"get"})}function i(t){return Object(n["a"])({url:"/refund/express/".concat(t),method:"get"})}function c(t){return Object(n["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function o(t){return Object(n["a"])({url:"/order/split_delivery/".concat(t.id),method:"put",data:t.datas})}function d(t){return Object(n["a"])({url:"/order/express/temp",method:"get",params:t})}function s(){return Object(n["a"])({url:"/order/delivery/list",method:"get"})}function l(){return Object(n["a"])({url:"/order/sheet_info",method:"get"})}function f(t){return Object(n["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function _(t){return Object(n["a"])({url:"/order/refund/".concat(t),method:"get"})}function p(t){return Object(n["a"])({url:"/order/refund_integral/".concat(t),method:"get"})}function m(t){return Object(n["a"])({url:"/order/no_refund/".concat(t),method:"get"})}function h(t){return Object(n["a"])({url:"/order/info/".concat(t),method:"get"})}function v(t){return Object(n["a"])({url:"/order/express/".concat(t),method:"get"})}function b(t){return Object(n["a"])({url:"/order/status/".concat(t.id),method:"get",params:t.datas})}function g(t,e){return Object(n["a"])({url:"order/split_order/"+t,method:"get",params:e})}function O(t){return Object(n["a"])({url:"/order/edit/".concat(t),method:"get"})}function j(t){return Object(n["a"])({url:"/order/distribution/".concat(t),method:"get"})}function w(t){return Object(n["a"])({url:"/order/distribution_info",method:"get",params:{ids:t}})}function C(t){return Object(n["a"])({url:"/order/write_update/".concat(t),method:"put"})}function y(t){return Object(n["a"])({url:"order/hand/batch_delivery",method:"get",params:t})}function k(t){return Object(n["a"])({url:"export/expressList",method:"get"})}function x(t){return Object(n["a"])({url:"/order/write",method:"post",data:t})}function q(t){return Object(n["a"])({url:"export/storeOrder",method:"get",params:t})}function D(t){return Object(n["a"])({url:"order/other/batch_delivery",method:"post",data:t})}function L(t){return Object(n["a"])({url:"queue/index",method:"get",params:t})}function E(t,e,r){return Object(n["a"])({url:"queue/delivery/log/".concat(t,"/").concat(e),method:"get",params:r})}function A(t,e){return Object(n["a"])({url:"queue/again/do_queue/".concat(t,"/").concat(e),method:"get"})}function B(t,e){return Object(n["a"])({url:"queue/del/wrong_queue/".concat(t,"/").concat(e),method:"get"})}function U(t,e,r){return Object(n["a"])({url:"export/batchOrderDelivery/".concat(t,"/").concat(e,"/").concat(r),method:"get"})}function $(t){return Object(n["a"])({url:"queue/stop/wrong_queue/".concat(t),method:"get"})}function J(t){return Object(n["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function P(t){return Object(n["a"])({url:"/refund/remark/".concat(t.id),method:"put",data:t.remark})}function R(t){return Object(n["a"])({url:"refund/list",method:"get",params:t})}function S(t){return Object(n["a"])({url:"/refund/detail/".concat(t),method:"get"})}function T(t){return Object(n["a"])({url:"/refund/refund/".concat(t.id),method:"put",data:t})}function H(){return Object(n["a"])({url:"/refund/reason",method:"get"})}}}]);