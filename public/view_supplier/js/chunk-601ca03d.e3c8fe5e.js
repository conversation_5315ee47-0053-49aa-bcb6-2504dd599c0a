(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-601ca03d"],{"31b4":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.FromData?n("div",[n("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:t.FromData.title,width:"700"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[["/marketing/coupon/save.html"===t.FromData.action?n("div",{staticClass:"radio acea-row row-middle"},[n("div",{staticClass:"name ivu-form-item-content"},[t._v("优惠券类型")]),n("Radio-group",{on:{"on-change":t.couponsType},model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[n("Radio",{attrs:{label:0}},[t._v("通用券")]),n("Radio",{attrs:{label:1}},[t._v("品类券")]),n("Radio",{attrs:{label:2}},[t._v("商品券")])],1)],1):t._e()],n("form-create",{ref:"fc",staticClass:"formBox",attrs:{option:t.config,rule:Array.from(t.FromData.rules),handleIcon:"false"},on:{"on-submit":t.onSubmit}})],2)],1):t._e()},r=[],i=n("9860"),o=n.n(i),c=n("b6bd"),s={name:"edit",components:{formCreate:o.a.$form()},props:{FromData:{type:Object,default:null}},data:function(){return{modals:!1,type:0,config:{global:{upload:{props:{onSuccess:function(t,e){200===t.status?e.url=t.data.src:this.Message.error(t.msg)}}}}}}},methods:{couponsType:function(){this.$parent.addType(this.type)},onSubmit:function(t){var e=this,n={};n=t,Object(c["a"])({url:this.FromData.action,method:this.FromData.method,data:n}).then((function(t){e.$Message.success(t.msg),e.modals=!1,setTimeout((function(){e.$emit("submitFail")}),1e3)})).catch((function(t){e.$Message.error(t.msg)}))},cancel:function(){this.type=0}}},u=s,l=(n("bbe1"),n("2877")),d=Object(l["a"])(u,a,r,!1,null,"704305ea",null);e["a"]=d.exports},"359f":function(t,e,n){},"389d":function(t,e,n){},"90e7":function(t,e,n){"use strict";n.d(e,"n",(function(){return r})),n.d(e,"k",(function(){return i})),n.d(e,"d",(function(){return o})),n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return u})),n.d(e,"l",(function(){return l})),n.d(e,"p",(function(){return d})),n.d(e,"q",(function(){return f})),n.d(e,"m",(function(){return m})),n.d(e,"e",(function(){return p})),n.d(e,"o",(function(){return h})),n.d(e,"f",(function(){return g})),n.d(e,"j",(function(){return b})),n.d(e,"g",(function(){return v})),n.d(e,"h",(function(){return w})),n.d(e,"i",(function(){return y}));var a=n("b6bd");function r(){return Object(a["a"])({url:"/supplier",method:"get"})}function i(t){return Object(a["a"])({url:"/supplier",method:"put",data:t})}function o(t){return Object(a["a"])({url:"city",method:"get",params:t})}function c(t){return Object(a["a"])({url:"admin",method:"get",params:t})}function s(){return Object(a["a"])({url:"admin/create",method:"get"})}function u(t){return Object(a["a"])({url:"admin/".concat(t,"/edit"),method:"get"})}function l(t){return Object(a["a"])({url:"admin/set_status/".concat(t.id,"/").concat(t.status),method:"put"})}function d(t){return Object(a["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function f(t,e){return Object(a["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function m(t){return Object(a["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function p(t){return Object(a["a"])({url:"city",method:"get",params:t})}function h(t,e){return Object(a["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function g(t){return Object(a["a"])({url:"/print/list",method:"get",params:t})}function b(t){var e=t.id,n=t.status;return Object(a["a"])({url:"/print/set_status/".concat(e,"/").concat(n),method:"get"})}function v(t,e){return Object(a["a"])({url:"/print/save/".concat(t),method:"post",data:e})}function w(t,e){return Object(a["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function y(t,e){return Object(a["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},a4e2:function(t,e,n){"use strict";var a=n("359f"),r=n.n(a);r.a},aacb:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[n("Button",{attrs:{type:"primary"},on:{click:t.add}},[t._v("添加管理员")]),n("Table",{staticClass:"mt25",attrs:{columns:t.columns1,data:t.list,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",loading:t.loading,"highlight-row":""},scopedSlots:t._u([{key:"head_pic",fn:function(t){var e=t.row;return[n("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"pictrue"},[n("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.head_pic,expression:"row.head_pic"}]})])]}},{key:"status",fn:function(e){var a=e.row;return[n("i-switch",{attrs:{value:a.status,"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.onchangeIsShow(a)}},model:{value:a.status,callback:function(e){t.$set(a,"status",e)},expression:"row.status"}},[n("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),n("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"action",fn:function(e){var a=e.row,r=e.index;return[n("a",{on:{click:function(e){return t.edit(a)}}},[t._v("编辑")]),n("Divider",{attrs:{type:"vertical"}}),n("a",{on:{click:function(e){return t.del(a,"删除管理员",r)}}},[t._v("删除")])]}}])}),n("div",{staticClass:"acea-row row-right page"},[n("Page",{attrs:{total:t.total,current:t.formValidate.page,"show-elevator":"","show-total":"","page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1),n("admin-from",{ref:"adminfrom",attrs:{FromData:t.FromData},on:{submitFail:t.submitFail}})],1)},r=[],i=n("a34a"),o=n.n(i),c=n("2f62"),s=n("90e7"),u=n("31b4");function l(t,e,n,a,r,i,o){try{var c=t[i](o),s=c.value}catch(u){return void n(u)}c.done?e(s):Promise.resolve(s).then(a,r)}function d(t){return function(){var e=this,n=arguments;return new Promise((function(a,r){var i=t.apply(e,n);function o(t){l(i,a,r,o,c,"next",t)}function c(t){l(i,a,r,o,c,"throw",t)}o(void 0)}))}}function f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?f(n,!0).forEach((function(e){p(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):f(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function p(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var h={name:"systemAdmin",components:{adminFrom:u["a"]},data:function(){return{total:0,loading:!1,formValidate:{page:1,limit:20},list:[],columns1:[{title:"ID",key:"id",width:80},{title:"头像",slot:"head_pic",minWidth:150},{title:"名称",key:"real_name",minWidth:250},{title:"账号",key:"account",minWidth:180},{title:"状态",slot:"status",minWidth:90},{title:"操作",slot:"action",fixed:"right",minWidth:120}],FromData:null}},computed:m({},Object(c["e"])("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getList()},methods:{onchangeIsShow:function(t){var e=this,n={id:t.id,status:t.status};Object(s["l"])(n).then(function(){var t=d(o.a.mark((function t(n){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$Message.success(n.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},submitFail:function(){this.getList()},getList:function(){var t=this;this.loading=!0,Object(s["c"])(this.formValidate).then(function(){var e=d(o.a.mark((function e(n){return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.total=n.data.count,t.list=n.data.list,t.loading=!1;case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},pageChange:function(t){this.formValidate.page=t,this.getList()},add:function(){var t=this;Object(s["b"])().then(function(){var e=d(o.a.mark((function e(n){return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.FromData=n.data,t.$refs.adminfrom.modals=!0;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},edit:function(t){var e=this;Object(s["a"])(t.id).then(function(){var t=d(o.a.mark((function t(n){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!1!==n.data.status){t.next=2;break}return t.abrupt("return",e.$authLapse(n.data));case 2:e.FromData=n.data,e.$refs.adminfrom.modals=!0;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},del:function(t,e,n){var a=this,r={title:e,num:n,url:"admin/".concat(t.id),method:"DELETE",ids:""};this.$modalSure(r).then((function(t){a.$Message.success(t.msg),a.list.splice(n,1),a.list.length||(a.formValidate.page=1==a.formValidate.page?1:a.formValidate.page-1),a.getList()})).catch((function(t){a.$Message.error(t.msg)}))}}},g=h,b=(n("a4e2"),n("2877")),v=Object(b["a"])(g,a,r,!1,null,"10d45116",null);e["default"]=v.exports},bbe1:function(t,e,n){"use strict";var a=n("389d"),r=n.n(a);r.a}}]);