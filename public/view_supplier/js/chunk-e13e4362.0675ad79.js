(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e13e4362"],{"47b9":function(e,t,n){"use strict";n.r(t);var r,i=12e4,o=function(){function e(e){var t=this;this._defaults=e,this._worker=null,this._idleCheckInterval=setInterval((function(){return t._checkIfIdle()}),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((function(){return t._stopWorker()}))}return e.prototype._stopWorker=function(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null},e.prototype.dispose=function(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()},e.prototype._checkIfIdle=function(){if(this._worker){var e=Date.now()-this._lastUsedTime;e>i&&this._stopWorker()}},e.prototype._getClient=function(){return this._lastUsedTime=Date.now(),this._client||(this._worker=monaco.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client},e.prototype.getLanguageServiceWorker=function(){for(var e,t=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return this._getClient().then((function(t){e=t})).then((function(e){return t._worker.withSyncedResources(n)})).then((function(t){return e}))},e}();function a(e,t){void 0===t&&(t=!1);var n=e.length,r=0,i="",o=0,a=16,l=0,f=0,h=0,p=0,m=0;function d(t,n){var i=0,o=0;while(i<t||!n){var a=e.charCodeAt(r);if(a>=48&&a<=57)o=16*o+a-48;else if(a>=65&&a<=70)o=16*o+a-65+10;else{if(!(a>=97&&a<=102))break;o=16*o+a-97+10}r++,i++}return i<t&&(o=-1),o}function g(e){r=e,i="",o=0,a=16,m=0}function v(){var t=r;if(48===e.charCodeAt(r))r++;else{r++;while(r<e.length&&c(e.charCodeAt(r)))r++}if(r<e.length&&46===e.charCodeAt(r)){if(r++,!(r<e.length&&c(e.charCodeAt(r))))return m=3,e.substring(t,r);r++;while(r<e.length&&c(e.charCodeAt(r)))r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if(r++,(r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&c(e.charCodeAt(r))){r++;while(r<e.length&&c(e.charCodeAt(r)))r++;n=r}else m=3;return e.substring(t,n)}function y(){var t="",i=r;while(1){if(r>=n){t+=e.substring(i,r),m=2;break}var o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(u(o)){t+=e.substring(i,r),m=2;break}m=6}r++}else{if(t+=e.substring(i,r),r++,r>=n){m=2;break}var a=e.charCodeAt(r++);switch(a){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var s=d(4,!0);s>=0?t+=String.fromCharCode(s):m=4;break;default:m=5}i=r}}return t}function b(){if(i="",m=0,o=r,f=l,p=h,r>=n)return o=n,a=17;var t=e.charCodeAt(r);if(s(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(s(t));return a=15}if(u(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),l++,h=r,a=14;switch(t){case 123:return r++,a=1;case 125:return r++,a=2;case 91:return r++,a=3;case 93:return r++,a=4;case 58:return r++,a=6;case 44:return r++,a=5;case 34:return r++,i=y(),a=10;case 47:var d=r-1;if(47===e.charCodeAt(r+1)){r+=2;while(r<n){if(u(e.charCodeAt(r)))break;r++}return i=e.substring(d,r),a=12}if(42===e.charCodeAt(r+1)){r+=2;var g=n-1,b=!1;while(r<g){var S=e.charCodeAt(r);if(42===S&&47===e.charCodeAt(r+1)){r+=2,b=!0;break}r++,u(S)&&(13===S&&10===e.charCodeAt(r)&&r++,l++,h=r)}return b||(r++,m=1),i=e.substring(d,r),a=13}return i+=String.fromCharCode(t),r++,a=16;case 45:if(i+=String.fromCharCode(t),r++,r===n||!c(e.charCodeAt(r)))return a=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=v(),a=11;default:while(r<n&&x(t))r++,t=e.charCodeAt(r);if(o!==r){switch(i=e.substring(o,r),i){case"true":return a=8;case"false":return a=9;case"null":return a=7}return a=16}return i+=String.fromCharCode(t),r++,a=16}}function x(e){if(s(e)||u(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function S(){var e;do{e=b()}while(e>=12&&e<=15);return e}return{setPosition:g,getPosition:function(){return r},scan:t?S:b,getToken:function(){return a},getTokenValue:function(){return i},getTokenOffset:function(){return o},getTokenLength:function(){return r-o},getTokenStartLine:function(){return f},getTokenStartCharacter:function(){return o-p},getTokenError:function(){return m}}}function s(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function u(e){return 10===e||13===e||8232===e||8233===e}function c(e){return e>=48&&e<=57}function l(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=r.DEFAULT);var i=null,o=[],a=[];function s(e){Array.isArray(o)?o.push(e):null!==i&&(o[i]=e)}var u={onObjectBegin:function(){var e={};s(e),a.push(o),o=e,i=null},onObjectProperty:function(e){i=e},onObjectEnd:function(){o=a.pop()},onArrayBegin:function(){var e=[];s(e),a.push(o),o=e,i=null},onArrayEnd:function(){o=a.pop()},onLiteralValue:s,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}};return d(e,u,n),o[0]}function f(e){if(!e.parent||!e.parent.children)return[];var t=f(e.parent);if("property"===e.parent.type){var n=e.parent.children[0].value;t.push(n)}else if("array"===e.parent.type){var r=e.parent.children.indexOf(e);-1!==r&&t.push(r)}return t}function h(e){switch(e.type){case"array":return e.children.map(h);case"object":for(var t=Object.create(null),n=0,r=e.children;n<r.length;n++){var i=r[n],o=i.children[1];o&&(t[i.children[0].value]=h(o))}return t;case"null":case"string":case"number":case"boolean":return e.value;default:return}}function p(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}function m(e,t,n){if(void 0===n&&(n=!1),p(e,t,n)){var r=e.children;if(Array.isArray(r))for(var i=0;i<r.length&&r[i].offset<=t;i++){var o=m(r[i],t,n);if(o)return o}return e}}function d(e,t,n){void 0===n&&(n=r.DEFAULT);var i=a(e,!1);function o(e){return e?function(){return e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}var u=o(t.onObjectBegin),c=s(t.onObjectProperty),l=o(t.onObjectEnd),f=o(t.onArrayBegin),h=o(t.onArrayEnd),p=s(t.onLiteralValue),m=s(t.onSeparator),d=o(t.onComment),g=s(t.onError),v=n&&n.disallowComments,y=n&&n.allowTrailingComma;function b(){while(1){var e=i.scan();switch(i.getTokenError()){case 4:x(14);break;case 5:x(15);break;case 3:x(13);break;case 1:v||x(11);break;case 2:x(12);break;case 6:x(16);break}switch(e){case 12:case 13:v?x(10):d();break;case 16:x(1);break;case 15:case 14:break;default:return e}}}function x(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),g(e),t.length+n.length>0){var r=i.getToken();while(17!==r){if(-1!==t.indexOf(r)){b();break}if(-1!==n.indexOf(r))break;r=b()}}}function S(e){var t=i.getTokenValue();return e?p(t):c(t),b(),!0}function k(){switch(i.getToken()){case 11:var e=0;try{e=JSON.parse(i.getTokenValue()),"number"!==typeof e&&(x(2),e=0)}catch(t){x(2)}p(e);break;case 7:p(null);break;case 8:p(!0);break;case 9:p(!1);break;default:return!1}return b(),!0}function w(){return 10!==i.getToken()?(x(3,[],[2,5]),!1):(S(!1),6===i.getToken()?(m(":"),b(),I()||x(4,[],[2,5])):x(5,[],[2,5]),!0)}function C(){u(),b();var e=!1;while(2!==i.getToken()&&17!==i.getToken()){if(5===i.getToken()){if(e||x(4,[],[]),m(","),b(),2===i.getToken()&&y)break}else e&&x(6,[],[]);w()||x(4,[],[2,5]),e=!0}return l(),2!==i.getToken()?x(7,[2],[]):b(),!0}function A(){f(),b();var e=!1;while(4!==i.getToken()&&17!==i.getToken()){if(5===i.getToken()){if(e||x(4,[],[]),m(","),b(),4===i.getToken()&&y)break}else e&&x(6,[],[]);I()||x(4,[],[4,5]),e=!0}return h(),4!==i.getToken()?x(8,[4],[]):b(),!0}function I(){switch(i.getToken()){case 3:return A();case 1:return C();case 10:return S(!0);default:return k()}}return b(),17===i.getToken()?!!n.allowEmptyContent||(x(4,[],[]),!1):I()?(17!==i.getToken()&&x(9,[],[]),!0):(x(4,[],[]),!1)}(function(e){e.DEFAULT={allowTrailingComma:!1}})(r||(r={}));var g,v,y,b,x,S,k,w,C,A,I,T,E,O,j,P,M,_,V,F,N=a,R=l,$=m,L=f,D=h;function W(e,t){if(e===t)return!0;if(null===e||void 0===e||null===t||void 0===t)return!1;if(typeof e!==typeof t)return!1;if("object"!==typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!W(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var o=[];for(r in t)o.push(r);if(o.sort(),!W(i,o))return!1;for(n=0;n<i.length;n++)if(!W(e[i[n]],t[i[n]]))return!1}return!0}function U(e){return"number"===typeof e}function q(e){return"undefined"!==typeof e}function B(e){return"boolean"===typeof e}function K(e){return"string"===typeof e}(function(e){function t(e,t){return{line:e,character:t}}function n(e){var t=e;return Se.objectLiteral(t)&&Se.number(t.line)&&Se.number(t.character)}e.create=t,e.is=n})(g||(g={})),function(e){function t(e,t,n,r){if(Se.number(e)&&Se.number(t)&&Se.number(n)&&Se.number(r))return{start:g.create(e,t),end:g.create(n,r)};if(g.is(e)&&g.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+r+"]")}function n(e){var t=e;return Se.objectLiteral(t)&&g.is(t.start)&&g.is(t.end)}e.create=t,e.is=n}(v||(v={})),function(e){function t(e,t){return{uri:e,range:t}}function n(e){var t=e;return Se.defined(t)&&v.is(t.range)&&(Se.string(t.uri)||Se.undefined(t.uri))}e.create=t,e.is=n}(y||(y={})),function(e){function t(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}}function n(e){var t=e;return Se.defined(t)&&v.is(t.targetRange)&&Se.string(t.targetUri)&&(v.is(t.targetSelectionRange)||Se.undefined(t.targetSelectionRange))&&(v.is(t.originSelectionRange)||Se.undefined(t.originSelectionRange))}e.create=t,e.is=n}(b||(b={})),function(e){function t(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}function n(e){var t=e;return Se.number(t.red)&&Se.number(t.green)&&Se.number(t.blue)&&Se.number(t.alpha)}e.create=t,e.is=n}(x||(x={})),function(e){function t(e,t){return{range:e,color:t}}function n(e){var t=e;return v.is(t.range)&&x.is(t.color)}e.create=t,e.is=n}(S||(S={})),function(e){function t(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}}function n(e){var t=e;return Se.string(t.label)&&(Se.undefined(t.textEdit)||j.is(t))&&(Se.undefined(t.additionalTextEdits)||Se.typedArray(t.additionalTextEdits,j.is))}e.create=t,e.is=n}(k||(k={})),function(e){e["Comment"]="comment",e["Imports"]="imports",e["Region"]="region"}(w||(w={})),function(e){function t(e,t,n,r,i){var o={startLine:e,endLine:t};return Se.defined(n)&&(o.startCharacter=n),Se.defined(r)&&(o.endCharacter=r),Se.defined(i)&&(o.kind=i),o}function n(e){var t=e;return Se.number(t.startLine)&&Se.number(t.startLine)&&(Se.undefined(t.startCharacter)||Se.number(t.startCharacter))&&(Se.undefined(t.endCharacter)||Se.number(t.endCharacter))&&(Se.undefined(t.kind)||Se.string(t.kind))}e.create=t,e.is=n}(C||(C={})),function(e){function t(e,t){return{location:e,message:t}}function n(e){var t=e;return Se.defined(t)&&y.is(t.location)&&Se.string(t.message)}e.create=t,e.is=n}(A||(A={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(I||(I={})),function(e){e.Unnecessary=1,e.Deprecated=2}(T||(T={})),function(e){function t(e,t,n,r,i,o){var a={range:e,message:t};return Se.defined(n)&&(a.severity=n),Se.defined(r)&&(a.code=r),Se.defined(i)&&(a.source=i),Se.defined(o)&&(a.relatedInformation=o),a}function n(e){var t=e;return Se.defined(t)&&v.is(t.range)&&Se.string(t.message)&&(Se.number(t.severity)||Se.undefined(t.severity))&&(Se.number(t.code)||Se.string(t.code)||Se.undefined(t.code))&&(Se.string(t.source)||Se.undefined(t.source))&&(Se.undefined(t.relatedInformation)||Se.typedArray(t.relatedInformation,A.is))}e.create=t,e.is=n}(E||(E={})),function(e){function t(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return Se.defined(n)&&n.length>0&&(i.arguments=n),i}function n(e){var t=e;return Se.defined(t)&&Se.string(t.title)&&Se.string(t.command)}e.create=t,e.is=n}(O||(O={})),function(e){function t(e,t){return{range:e,newText:t}}function n(e,t){return{range:{start:e,end:e},newText:t}}function r(e){return{range:e,newText:""}}function i(e){var t=e;return Se.objectLiteral(t)&&Se.string(t.newText)&&v.is(t.range)}e.replace=t,e.insert=n,e.del=r,e.is=i}(j||(j={})),function(e){function t(e,t){return{textDocument:e,edits:t}}function n(e){var t=e;return Se.defined(t)&&z.is(t.textDocument)&&Array.isArray(t.edits)}e.create=t,e.is=n}(P||(P={})),function(e){function t(e,t){var n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),n}function n(e){var t=e;return t&&"create"===t.kind&&Se.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Se.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Se.boolean(t.options.ignoreIfExists)))}e.create=t,e.is=n}(M||(M={})),function(e){function t(e,t,n){var r={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(r.options=n),r}function n(e){var t=e;return t&&"rename"===t.kind&&Se.string(t.oldUri)&&Se.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Se.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Se.boolean(t.options.ignoreIfExists)))}e.create=t,e.is=n}(_||(_={})),function(e){function t(e,t){var n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),n}function n(e){var t=e;return t&&"delete"===t.kind&&Se.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Se.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Se.boolean(t.options.ignoreIfNotExists)))}e.create=t,e.is=n}(V||(V={})),function(e){function t(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return Se.string(e.kind)?M.is(e)||_.is(e)||V.is(e):P.is(e)})))}e.is=t}(F||(F={}));var J,z,H,G,Z,X,Q,Y,ee,te,ne,re,ie,oe,ae,se,ue,ce,le,fe,he,pe,me,de,ge,ve,ye,be=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(j.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(j.replace(e,t))},e.prototype.delete=function(e){this.edits.push(j.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}();(function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach((function(e){if(P.is(e)){var n=new be(e.edits);t._textEditChanges[e.textDocument.uri]=n}})):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new be(e.changes[n]);t._textEditChanges[n]=r})))}Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(z.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t=e,n=this._textEditChanges[t.uri];if(!n){var r=[],i={textDocument:t,edits:r};this._workspaceEdit.documentChanges.push(i),n=new be(r),this._textEditChanges[t.uri]=n}return n}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");n=this._textEditChanges[e];if(!n){r=[];this._workspaceEdit.changes[e]=r,n=new be(r),this._textEditChanges[e]=n}return n},e.prototype.createFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(M.create(e,t))},e.prototype.renameFile=function(e,t,n){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(_.create(e,t,n))},e.prototype.deleteFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(V.create(e,t))},e.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")}})();(function(e){function t(e){return{uri:e}}function n(e){var t=e;return Se.defined(t)&&Se.string(t.uri)}e.create=t,e.is=n})(J||(J={})),function(e){function t(e,t){return{uri:e,version:t}}function n(e){var t=e;return Se.defined(t)&&Se.string(t.uri)&&(null===t.version||Se.number(t.version))}e.create=t,e.is=n}(z||(z={})),function(e){function t(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}}function n(e){var t=e;return Se.defined(t)&&Se.string(t.uri)&&Se.string(t.languageId)&&Se.number(t.version)&&Se.string(t.text)}e.create=t,e.is=n}(H||(H={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(G||(G={})),function(e){function t(t){var n=t;return n===e.PlainText||n===e.Markdown}e.is=t}(G||(G={})),function(e){function t(e){var t=e;return Se.objectLiteral(e)&&G.is(t.kind)&&Se.string(t.value)}e.is=t}(Z||(Z={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(X||(X={})),function(e){e.PlainText=1,e.Snippet=2}(Q||(Q={})),function(e){e.Deprecated=1}(Y||(Y={})),function(e){function t(e){return{label:e}}e.create=t}(ee||(ee={})),function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t}(te||(te={})),function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}function n(e){var t=e;return Se.string(t)||Se.objectLiteral(t)&&Se.string(t.language)&&Se.string(t.value)}e.fromPlainText=t,e.is=n}(ne||(ne={})),function(e){function t(e){var t=e;return!!t&&Se.objectLiteral(t)&&(Z.is(t.contents)||ne.is(t.contents)||Se.typedArray(t.contents,ne.is))&&(void 0===e.range||v.is(e.range))}e.is=t}(re||(re={})),function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t}(ie||(ie={})),function(e){function t(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return Se.defined(t)&&(i.documentation=t),Se.defined(n)?i.parameters=n:i.parameters=[],i}e.create=t}(oe||(oe={})),function(e){e.Text=1,e.Read=2,e.Write=3}(ae||(ae={})),function(e){function t(e,t){var n={range:e};return Se.number(t)&&(n.kind=t),n}e.create=t}(se||(se={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(ue||(ue={})),function(e){e.Deprecated=1}(ce||(ce={})),function(e){function t(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}e.create=t}(le||(le={})),function(e){function t(e,t,n,r,i,o){var a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(a.children=o),a}function n(e){var t=e;return t&&Se.string(t.name)&&Se.number(t.kind)&&v.is(t.range)&&v.is(t.selectionRange)&&(void 0===t.detail||Se.string(t.detail))&&(void 0===t.deprecated||Se.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))}e.create=t,e.is=n}(fe||(fe={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(he||(he={})),function(e){function t(e,t){var n={diagnostics:e};return void 0!==t&&null!==t&&(n.only=t),n}function n(e){var t=e;return Se.defined(t)&&Se.typedArray(t.diagnostics,E.is)&&(void 0===t.only||Se.typedArray(t.only,Se.string))}e.create=t,e.is=n}(pe||(pe={})),function(e){function t(e,t,n){var r={title:e};return O.is(t)?r.command=t:r.edit=t,void 0!==n&&(r.kind=n),r}function n(e){var t=e;return t&&Se.string(t.title)&&(void 0===t.diagnostics||Se.typedArray(t.diagnostics,E.is))&&(void 0===t.kind||Se.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||O.is(t.command))&&(void 0===t.isPreferred||Se.boolean(t.isPreferred))&&(void 0===t.edit||F.is(t.edit))}e.create=t,e.is=n}(me||(me={})),function(e){function t(e,t){var n={range:e};return Se.defined(t)&&(n.data=t),n}function n(e){var t=e;return Se.defined(t)&&v.is(t.range)&&(Se.undefined(t.command)||O.is(t.command))}e.create=t,e.is=n}(de||(de={})),function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}function n(e){var t=e;return Se.defined(t)&&Se.number(t.tabSize)&&Se.boolean(t.insertSpaces)}e.create=t,e.is=n}(ge||(ge={})),function(e){function t(e,t,n){return{range:e,target:t,data:n}}function n(e){var t=e;return Se.defined(t)&&v.is(t.range)&&(Se.undefined(t.target)||Se.string(t.target))}e.create=t,e.is=n}(ve||(ve={})),function(e){function t(e,t){return{range:e,parent:t}}function n(t){var n=t;return void 0!==n&&v.is(n.range)&&(void 0===n.parent||e.is(n.parent))}e.create=t,e.is=n}(ye||(ye={}));var xe;(function(e){function t(e,t,n,r){return new ke(e,t,n,r)}function n(e){var t=e;return!!(Se.defined(t)&&Se.string(t.uri)&&(Se.undefined(t.languageId)||Se.string(t.languageId))&&Se.number(t.lineCount)&&Se.func(t.getText)&&Se.func(t.positionAt)&&Se.func(t.offsetAt))}function r(e,t){for(var n=e.getText(),r=i(t,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=n.length,a=r.length-1;a>=0;a--){var s=r[a],u=e.offsetAt(s.range.start),c=e.offsetAt(s.range.end);if(!(c<=o))throw new Error("Overlapping edit");n=n.substring(0,u)+s.newText+n.substring(c,n.length),o=u}return n}function i(e,t){if(e.length<=1)return e;var n=e.length/2|0,r=e.slice(0,n),o=e.slice(n);i(r,t),i(o,t);var a=0,s=0,u=0;while(a<r.length&&s<o.length){var c=t(r[a],o[s]);e[u++]=c<=0?r[a++]:o[s++]}while(a<r.length)e[u++]=r[a++];while(s<o.length)e[u++]=o[s++];return e}e.create=t,e.is=n,e.applyEdits=r})(xe||(xe={}));var Se,ke=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return g.create(0,e);while(n<r){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return g.create(o,e-t[o])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();(function(e){var t=Object.prototype.toString;function n(e){return"undefined"!==typeof e}function r(e){return"undefined"===typeof e}function i(e){return!0===e||!1===e}function o(e){return"[object String]"===t.call(e)}function a(e){return"[object Number]"===t.call(e)}function s(e){return"[object Function]"===t.call(e)}function u(e){return null!==e&&"object"===typeof e}function c(e,t){return Array.isArray(e)&&e.every(t)}e.defined=n,e.undefined=r,e.boolean=i,e.string=o,e.number=a,e.func=s,e.objectLiteral=u,e.typedArray=c})(Se||(Se={}));var we,Ce,Ae,Ie=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(t,n){for(var r=0,i=t;r<i.length;r++){var o=i[r];if(e.isIncremental(o)){var a=Oe(o.range),s=this.offsetAt(a.start),u=this.offsetAt(a.end);this._content=this._content.substring(0,s)+o.text+this._content.substring(u,this._content.length);var c=Math.max(a.start.line,0),l=Math.max(a.end.line,0),f=this._lineOffsets,h=Ee(o.text,!1,s);if(l-c===h.length)for(var p=0,m=h.length;p<m;p++)f[p+c+1]=h[p];else h.length<1e4?f.splice.apply(f,[c+1,l-c].concat(h)):this._lineOffsets=f=f.slice(0,c+1).concat(h,f.slice(l+1));var d=o.text.length-(u-s);if(0!==d)for(p=c+1+h.length,m=f.length;p<m;p++)f[p]=f[p]+d}else{if(!e.isFull(o))throw new Error("Unknown change event received");this._content=o.text,this._lineOffsets=void 0}}this._version=n},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=Ee(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};while(n<r){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e.isIncremental=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"===typeof t.rangeLength)},e.isFull=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function Te(e,t){if(e.length<=1)return e;var n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);Te(r,t),Te(i,t);var o=0,a=0,s=0;while(o<r.length&&a<i.length){var u=t(r[o],i[a]);e[s++]=u<=0?r[o++]:i[a++]}while(o<r.length)e[s++]=r[o++];while(a<i.length)e[s++]=i[a++];return e}function Ee(e,t,n){void 0===n&&(n=0);for(var r=t?[n]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function Oe(e){var t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function je(e){var t=Oe(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function Pe(e,t){var n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,n){var r=n[0];return"undefined"!==typeof t[r]?t[r]:e})),n}function Me(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return Pe(t,n)}function _e(e){return Me}(function(e){function t(e,t,n,r){return new Ie(e,t,n,r)}function n(e,t,n){if(e instanceof Ie)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")}function r(e,t){for(var n=e.getText(),r=Te(t.map(je),(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=n.length,o=r.length-1;o>=0;o--){var a=r[o],s=e.offsetAt(a.range.start),u=e.offsetAt(a.range.end);if(!(u<=i))throw new Error("Overlapping edit");n=n.substring(0,s)+a.newText+n.substring(u,n.length),i=s}return n}e.create=t,e.update=n,e.applyEdits=r})(we||(we={})),function(e){e[e["Undefined"]=0]="Undefined",e[e["EnumValueMismatch"]=1]="EnumValueMismatch",e[e["UnexpectedEndOfComment"]=257]="UnexpectedEndOfComment",e[e["UnexpectedEndOfString"]=258]="UnexpectedEndOfString",e[e["UnexpectedEndOfNumber"]=259]="UnexpectedEndOfNumber",e[e["InvalidUnicode"]=260]="InvalidUnicode",e[e["InvalidEscapeCharacter"]=261]="InvalidEscapeCharacter",e[e["InvalidCharacter"]=262]="InvalidCharacter",e[e["PropertyExpected"]=513]="PropertyExpected",e[e["CommaExpected"]=514]="CommaExpected",e[e["ColonExpected"]=515]="ColonExpected",e[e["ValueExpected"]=516]="ValueExpected",e[e["CommaOrCloseBacketExpected"]=517]="CommaOrCloseBacketExpected",e[e["CommaOrCloseBraceExpected"]=518]="CommaOrCloseBraceExpected",e[e["TrailingComma"]=519]="TrailingComma",e[e["DuplicateKey"]=520]="DuplicateKey",e[e["CommentNotPermitted"]=521]="CommentNotPermitted",e[e["SchemaResolveError"]=768]="SchemaResolveError"}(Ce||(Ce={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[G.Markdown,G.PlainText],commitCharactersSupport:!0}}}}}(Ae||(Ae={}));var Ve,Fe=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ne=_e(),Re={"color-hex":{errorMessage:Ne("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:Ne("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:Ne("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:Ne("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:Ne("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/}},$e=function(){function e(e,t,n){this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!0,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}();(function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}Fe(t,e)})($e),function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.type="boolean",i.value=n,i}Fe(t,e)}($e),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}Fe(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!0,configurable:!0})}($e),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}Fe(t,e)}($e),function(e){function t(t,n,r){var i=e.call(this,t,n,r)||this;return i.type="string",i.value="",i}Fe(t,e)}($e),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="property",r.colonOffset=-1,r}Fe(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!0,configurable:!0})}($e),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}Fe(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!0,configurable:!0})}($e);function Le(e){return B(e)?e?{}:{not:{}}:e}(function(e){e[e["Key"]=0]="Key",e[e["Enum"]=1]="Enum"})(Ve||(Ve={}));var De=function(){function e(e,t){void 0===e&&(e=-1),void 0===t&&(t=null),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){var t;(t=this.schemas).push.apply(t,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||Ke(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),We=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!0,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),Ue=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=null}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===Ce.EnumValueMismatch&&(r.message=Ne("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function qe(e){return D(e)}function Be(e){return L(e)}function Ke(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return $(this.root,e,t)},e.prototype.visit=function(e){if(this.root){var t=function(n){var r=e(n),i=n.children;if(Array.isArray(i))for(var o=0;o<i.length&&r;o++)r=t(i[o]);return r};t(this.root)}},e.prototype.validate=function(e,t){if(this.root&&t){var n=new Ue;return Je(this.root,t,n,We.instance),n.problems.map((function(t){var n=v.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return E.create(n,t.message,t.severity,t.code)}))}return null},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1),void 0===n&&(n=null);var r=new De(t,n);return this.root&&e&&Je(this.root,e,new Ue,r),r.schemas}})();function Je(e,t,n,r){if(e&&r.include(e)){switch(e.type){case"object":u(e,t,n,r);break;case"array":s(e,t,n,r);break;case"string":a(e,t,n,r);break;case"number":o(e,t,n,r);break;case"property":return Je(e.valueNode,t,n,r)}i(),r.add({node:e,schema:t})}function i(){function i(t){return e.type===t||"integer"===t&&"number"===e.type&&e.isInteger}if(Array.isArray(t.type)?t.type.some(i)||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.errorMessage||Ne("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(i(t.type)||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.errorMessage||Ne("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)})),Array.isArray(t.allOf))for(var o=0,a=t.allOf;o<a.length;o++){var s=a[o];Je(e,Le(s),n,r)}var u=Le(t.not);if(u){var c=new Ue,l=r.newSub();Je(e,u,c,l),c.hasProblems()||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("notSchemaWarning","Matches a schema that is not allowed.")});for(var f=0,h=l.schemas;f<h.length;f++){var p=h[f];p.inverted=!p.inverted,r.add(p)}}var m=function(t,i){for(var o=[],a=null,s=0,u=t;s<u.length;s++){var c=u[s],l=Le(c),f=new Ue,h=r.newSub();if(Je(e,l,f,h),f.hasProblems()||o.push(l),a)if(i||f.hasProblems()||a.validationResult.hasProblems()){var p=f.compare(a.validationResult);p>0?a={schema:l,validationResult:f,matchingSchemas:h}:0===p&&(a.matchingSchemas.merge(h),a.validationResult.mergeEnumValues(f))}else a.matchingSchemas.merge(h),a.validationResult.propertiesMatches+=f.propertiesMatches,a.validationResult.propertiesValueMatches+=f.propertiesValueMatches;else a={schema:l,validationResult:f,matchingSchemas:h}}return o.length>1&&i&&n.problems.push({location:{offset:e.offset,length:1},severity:I.Warning,message:Ne("oneOfWarning","Matches multiple schemas when only one must validate.")}),null!==a&&(n.merge(a.validationResult),n.propertiesMatches+=a.validationResult.propertiesMatches,n.propertiesValueMatches+=a.validationResult.propertiesValueMatches,r.merge(a.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&m(t.anyOf,!1),Array.isArray(t.oneOf)&&m(t.oneOf,!0);var d=function(t){var i=new Ue,o=r.newSub();Je(e,Le(t),i,o),n.merge(i),n.propertiesMatches+=i.propertiesMatches,n.propertiesValueMatches+=i.propertiesValueMatches,r.merge(o)},g=function(t,n,i){var o=Le(t),a=new Ue,s=r.newSub();Je(e,o,a,s),r.merge(s),a.hasProblems()?i&&d(i):n&&d(n)},v=Le(t.if);if(v&&g(v,Le(t.then),Le(t.else)),Array.isArray(t.enum)){for(var y=qe(e),b=!1,x=0,S=t.enum;x<S.length;x++){var k=S[x];if(W(y,k)){b=!0;break}}n.enumValues=t.enum,n.enumValueMatch=b,b||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,code:Ce.EnumValueMismatch,message:t.errorMessage||Ne("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(q(t.const)){y=qe(e);W(y,t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,code:Ce.EnumValueMismatch,message:t.errorMessage||Ne("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&e.parent&&n.problems.push({location:{offset:e.parent.offset,length:e.parent.length},severity:I.Warning,message:t.deprecationMessage})}function o(e,t,n,r){var i=e.value;function o(e,t){return U(t)?t:B(t)&&t?e:void 0}function a(e,t){if(!B(t)||!t)return e}U(t.multipleOf)&&i%t.multipleOf!==0&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)});var s=o(t.minimum,t.exclusiveMinimum);U(s)&&i<=s&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",s)});var u=o(t.maximum,t.exclusiveMaximum);U(u)&&i>=u&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",u)});var c=a(t.minimum,t.exclusiveMinimum);U(c)&&i<c&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("minimumWarning","Value is below the minimum of {0}.",c)});var l=a(t.maximum,t.exclusiveMaximum);U(l)&&i>l&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("maximumWarning","Value is above the maximum of {0}.",l)})}function a(e,t,n,r){if(U(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)}),U(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)}),K(t.pattern)){var i=new RegExp(t.pattern);i.test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.patternErrorMessage||t.errorMessage||Ne("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}if(t.format)switch(t.format){case"uri":case"uri-reference":var o=void 0;if(e.value){var a=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);a?a[2]||"uri"!==t.format||(o=Ne("uriSchemeMissing","URI with a scheme is expected.")):o=Ne("uriMissing","URI is expected.")}else o=Ne("uriEmpty","URI expected.");o&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.patternErrorMessage||t.errorMessage||Ne("uriFormatWarning","String is not a URI: {0}",o)});break;case"color-hex":case"date-time":case"date":case"time":case"email":var s=Re[t.format];e.value&&s.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.patternErrorMessage||t.errorMessage||s.errorMessage});default:}}function s(e,t,n,r){if(Array.isArray(t.items)){for(var i=t.items,o=0;o<i.length;o++){var a=i[o],s=Le(a),u=new Ue,c=e.items[o];c?(Je(c,s,u,r),n.mergePropertyMatch(u)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"===typeof t.additionalItems)for(var l=i.length;l<e.items.length;l++){u=new Ue;Je(e.items[l],t.additionalItems,u,r),n.mergePropertyMatch(u)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var f=Le(t.items);if(f)for(var h=0,p=e.items;h<p.length;h++){c=p[h],u=new Ue;Je(c,f,u,r),n.mergePropertyMatch(u)}}var m=Le(t.contains);if(m){var d=e.items.some((function(e){var t=new Ue;return Je(e,m,t,We.instance),!t.hasProblems()}));d||n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:t.errorMessage||Ne("requiredItemMissingWarning","Array does not contain required item.")})}if(U(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)}),U(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)}),!0===t.uniqueItems){var g=qe(e),v=g.some((function(e,t){return t!==g.lastIndexOf(e)}));v&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("uniqueItemsWarning","Array has duplicate items.")})}}function u(e,t,n,r){for(var i=Object.create(null),o=[],a=0,s=e.properties;a<s.length;a++){var u=s[a],c=u.keyNode.value;i[c]=u.valueNode,o.push(c)}if(Array.isArray(t.required))for(var l=0,f=t.required;l<f.length;l++){var h=f[l];if(!i[h]){var p=e.parent&&"property"===e.parent.type&&e.parent.keyNode,m=p?{offset:p.offset,length:p.length}:{offset:e.offset,length:1};n.problems.push({location:m,severity:I.Warning,message:Ne("MissingRequiredPropWarning",'Missing property "{0}".',h)})}}var d=function(e){var t=o.indexOf(e);while(t>=0)o.splice(t,1),t=o.indexOf(e)};if(t.properties)for(var g=0,v=Object.keys(t.properties);g<v.length;g++){h=v[g];d(h);var y=t.properties[h],b=i[h];if(b)if(B(y))if(y)n.propertiesMatches++,n.propertiesValueMatches++;else{u=b.parent;n.problems.push({location:{offset:u.keyNode.offset,length:u.keyNode.length},severity:I.Warning,message:t.errorMessage||Ne("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{var x=new Ue;Je(b,y,x,r),n.mergePropertyMatch(x)}}if(t.patternProperties)for(var S=0,k=Object.keys(t.patternProperties);S<k.length;S++)for(var w=k[S],C=new RegExp(w),A=0,T=o.slice(0);A<T.length;A++){h=T[A];if(C.test(h)){d(h);b=i[h];if(b){y=t.patternProperties[w];if(B(y))if(y)n.propertiesMatches++,n.propertiesValueMatches++;else{u=b.parent;n.problems.push({location:{offset:u.keyNode.offset,length:u.keyNode.length},severity:I.Warning,message:t.errorMessage||Ne("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{x=new Ue;Je(b,y,x,r),n.mergePropertyMatch(x)}}}}if("object"===typeof t.additionalProperties)for(var E=0,O=o;E<O.length;E++){h=O[E],b=i[h];if(b){x=new Ue;Je(b,t.additionalProperties,x,r),n.mergePropertyMatch(x)}}else if(!1===t.additionalProperties&&o.length>0)for(var j=0,P=o;j<P.length;j++){h=P[j],b=i[h];if(b){u=b.parent;n.problems.push({location:{offset:u.keyNode.offset,length:u.keyNode.length},severity:I.Warning,message:t.errorMessage||Ne("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}}if(U(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)}),U(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)}),t.dependencies)for(var M=0,_=Object.keys(t.dependencies);M<_.length;M++){c=_[M];var V=i[c];if(V){var F=t.dependencies[c];if(Array.isArray(F))for(var N=0,R=F;N<R.length;N++){var $=R[N];i[$]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:Ne("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",$,c)})}else{y=Le(F);if(y){x=new Ue;Je(e,y,x,r),n.mergePropertyMatch(x)}}}}var L=Le(t.propertyNames);if(L)for(var D=0,W=e.properties;D<W.length;D++){var q=W[D];c=q.keyNode;c&&Je(c,L,n,We.instance)}}}function ze(e,t,n){if(null!==e&&"object"===typeof e){var r=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";for(var i="[\n",o=0;o<e.length;o++)i+=r+ze(e[o],r,n),o<e.length-1&&(i+=","),i+="\n";return i+=t+"]",i}var a=Object.keys(e);if(0===a.length)return"{}";for(i="{\n",o=0;o<a.length;o++){var s=a[o];i+=r+JSON.stringify(s)+": "+ze(e[s],r,n),o<a.length-1&&(i+=","),i+="\n"}return i+=t+"}",i}return n(e)}function He(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function Ge(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function Ze(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}var Xe=_e(),Qe=[",","}","]"],Ye=[":"];(function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r,this.templateVarIdCounter=0}e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--)if(this.contributions[t].resolveCompletion){var n=this.contributions[t].resolveCompletion(e);if(n)return n}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,i={items:[],isIncomplete:!1},o=e.getText(),a=e.offsetAt(t),s=n.getNodeFromOffset(a,!0);if(this.isInComment(e,s?s.offset:0,a))return Promise.resolve(i);if(s&&a===s.offset+s.length&&a>0){var u=o[a-1];("object"===s.type&&"}"===u||"array"===s.type&&"]"===u)&&(s=s.parent)}var c=this.getCurrentWord(e,a),l=null;if(!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type){var f=a-c.length;f>0&&'"'===o[f-1]&&f--,l=v.create(e.positionAt(f),t)}else l=v.create(e.positionAt(s.offset),e.positionAt(s.offset+s.length));var h=!1,p={},m={add:function(e){var t=e.label,n=p[t];if(n)n.documentation||(n.documentation=e.documentation);else{if(t=t.replace(/[\n]/g,"↵"),t.length>60){var r=t.substr(0,57).trim()+"...";p[r]||(t=r)}l&&(e.textEdit=j.replace(l,e.insertText)),h&&(e.commitCharacters=e.kind===X.Property?Ye:Qe),e.label=t,p[t]=e,i.items.push(e)}},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){console.error(e)},log:function(e){console.log(e)},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then((function(t){var u=[],f=!0,h="",d=null;if(s&&"string"===s.type){var g=s.parent;g&&"property"===g.type&&g.keyNode===s&&(f=!g.valueNode,d=g,h=o.substr(s.offset+1,s.length-2),g&&(s=g.parent))}if(s&&"object"===s.type){if(s.offset===a)return i;var v=s.properties;v.forEach((function(e){d&&d===e||(p[e.keyNode.value]=ee.create("__"))}));var y="";f&&(y=r.evaluateSeparatorAfter(e,e.offsetAt(l.end))),t?r.getPropertyCompletions(t,n,s,f,y,m):r.getSchemaLessPropertyCompletions(n,s,h,m);var b=Be(s);r.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,b,c,f,""===y,m);n&&u.push(n)})),!t&&c.length>0&&'"'!==o.charAt(a-c.length-1)&&(m.add({kind:X.Property,label:r.getLabelForValue(c),insertText:r.getInsertTextForProperty(c,null,!1,y),insertTextFormat:Q.Snippet,documentation:""}),m.setAsIncomplete())}var x={};return t?r.getValueCompletions(t,n,s,a,e,m,x):r.getSchemaLessValueCompletions(n,s,a,e,m),r.contributions.length>0&&r.getContributedValueCompletions(n,s,a,e,m,u),r.promiseConstructor.all(u).then((function(){if(0===m.getNumberOfProposals()){var t=a;!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type||(t=s.offset+s.length);var n=r.evaluateSeparatorAfter(e,t);r.addFillerValueCompletions(x,n,m)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,i,o){var a=this,s=t.getMatchingSchemas(e.schema,n.offset);s.forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"===typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var s={kind:X.Property,label:e,insertText:a.getInsertTextForProperty(e,n,r,i),insertTextFormat:Q.Snippet,filterText:a.getFilterTextForValue(e),documentation:a.fromMarkup(n.markdownDescription)||n.description||""};Ge(s.insertText,"$1"+i)&&(s.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(s)}}))}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var i=this,o=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:X.Property,label:t,insertText:i.getInsertTextForValue(t,""),insertTextFormat:Q.Snippet,filterText:i.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var a=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===a&&e.valueNode&&"object"===e.valueNode.type&&o(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&o(e)}));else"object"===t.type&&r.add({kind:X.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",null,!0,""),insertTextFormat:Q.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,i){var o=this,a=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(a=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:Q.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:Q.Snippet,documentation:""});var s=this.evaluateSeparatorAfter(r,a),u=function(e){Ke(e.parent,n,!0)||i.add({kind:o.getSuggestionKind(e.type),label:o.getLabelTextForMatchingNode(e,r),insertText:o.getInsertTextForMatchingNode(e,r,s),insertTextFormat:Q.Snippet,documentation:""}),"boolean"===e.type&&o.addBooleanValueCompletion(!e.value,s,i)};if("property"===t.type&&n>t.colonOffset){var c=t.valueNode;if(c&&(n>c.offset+c.length||"object"===c.type||"array"===c.type))return;var l=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===l&&e.valueNode&&u(e.valueNode),!0})),"$schema"===l&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(s,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var f=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===f&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(u),!0}))}else t.items.forEach(u)},e.prototype.getValueCompletions=function(e,t,n,r,i,o,a){var s=this,u=r,c=null,l=null;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(u=n.offset+n.length,l=n,n=n.parent),n){if("property"===n.type&&r>n.colonOffset){var f=n.valueNode;if(f&&r>f.offset+f.length)return;c=n.keyNode.value,n=n.parent}if(n&&(null!==c||"array"===n.type)){var h=this.evaluateSeparatorAfter(i,u),p=t.getMatchingSchemas(e.schema,n.offset,l);p.forEach((function(e){if(e.node===n&&!e.inverted&&e.schema){if("array"===n.type&&e.schema.items)if(Array.isArray(e.schema.items)){var t=s.findItemAtOffset(n,i,r);t<e.schema.items.length&&s.addSchemaValueCompletions(e.schema.items[t],h,o,a)}else s.addSchemaValueCompletions(e.schema.items,h,o,a);if(e.schema.properties){var u=e.schema.properties[c];u&&s.addSchemaValueCompletions(u,h,o,a)}}})),"$schema"!==c||n.parent||this.addDollarSchemaCompletions(h,o),a["boolean"]&&(this.addBooleanValueCompletion(!0,h,o),this.addBooleanValueCompletion(!1,h,o)),a["null"]&&this.addNullValueCompletion(h,o)}}else this.addSchemaValueCompletions(e.schema,"",o,a)},e.prototype.getContributedValueCompletions=function(e,t,n,r,i,o){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),"property"===t.type&&n>t.colonOffset){var a=t.keyNode.value,s=t.valueNode;if(!s||n<=s.offset+s.length){var u=Be(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(r.uri,u,a,i);t&&o.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(r.uri,i);t&&o.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;"object"===typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var i=this;void 0===r&&(r=0);var o=!1;if(q(e.default)){for(var a=e.type,s=e.default,u=r;u>0;u--)s=[s],a="array";n.add({kind:this.getSuggestionKind(a),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:Q.Snippet,detail:Xe("json.suggest.default","Default value")}),o=!0}Array.isArray(e.examples)&&e.examples.forEach((function(a){for(var s=e.type,u=a,c=r;c>0;c--)u=[u],s="array";n.add({kind:i.getSuggestionKind(s),label:i.getLabelForValue(u),insertText:i.getInsertTextForValue(u,t),insertTextFormat:Q.Snippet}),o=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(a){var s,u,c=e.type,l=a.body,f=a.label;if(q(l)){e.type;for(var h=r;h>0;h--)l=[l],"array";s=i.getInsertTextForSnippetValue(l,t),u=i.getFilterTextForSnippetValue(l),f=f||i.getLabelForSnippetValue(l)}else if("string"===typeof a.bodyText){var p="",m="",d="";for(h=r;h>0;h--)p=p+d+"[\n",m=m+"\n"+d+"]",d+="\t",c="array";s=p+d+a.bodyText.split("\n").join("\n"+d)+m+t,f=f||s,u=s.replace(/[\n]/g,"")}n.add({kind:i.getSuggestionKind(c),label:f,documentation:i.fromMarkup(a.markdownDescription)||a.description,insertText:s,insertTextFormat:Q.Snippet,filterText:u}),o=!0})),o||"object"!==typeof e.items||Array.isArray(e.items)||this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(q(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:Q.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,i=e.enum.length;r<i;r++){var o=e.enum[r],a=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?a=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(a=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:Q.Snippet,documentation:a})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!q(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):t[n]=!0}},e.prototype.addFillerValueCompletions=function(e,t,n){e["object"]&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:Q.Snippet,detail:Xe("defaults.object","New object"),documentation:""}),e["array"]&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:Q.Snippet,detail:Xe("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:Q.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:Q.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this,r=this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e}));r.forEach((function(r){return t.add({kind:X.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:Q.Snippet,documentation:""})}))},e.prototype.getLabelForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){var t=JSON.stringify(e);return t.replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){var n=function(e){return"string"===typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)};return ze(e,"",n)+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),n=this.getInsertTextForPlainText(n),'"${1:'+n+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:null}if(!e)return X.Value;switch(e){case"string":return X.Value;case"object":return X.Module;case"property":return X.Property;default:return X.Value}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:var n=t.getText().substr(e.offset,e.length);return n}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var o,a=i+": ",s=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var u=t.defaultSnippets[0].body;q(u)&&(o=this.getInsertTextForSnippetValue(u,""))}s+=t.defaultSnippets.length}if(t.enum&&(o||1!==t.enum.length||(o=this.getInsertTextForGuessedValue(t.enum[0],"")),s+=t.enum.length),q(t.default)&&(o||(o=this.getInsertTextForGuessedValue(t.default,"")),s++),Array.isArray(t.examples)&&t.examples.length&&(o||(o=this.getInsertTextForGuessedValue(t.examples[0],"")),s+=t.examples.length),0===s){var c=Array.isArray(t.type)?t.type[0]:t.type;switch(c||(t.properties?c="object":t.items&&(c="array")),c){case"boolean":o="$1";break;case"string":o='"$1"';break;case"object":o="{$1}";break;case"array":o="[$1]";break;case"number":case"integer":o="${1:0}";break;case"null":o="${1:null}";break;default:return i}}}return(!o||s>1)&&(o="$1"),a+o+r},e.prototype.getCurrentWord=function(e,t){var n=t-1,r=e.getText();while(n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n)))n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=N(e.getText(),!0);n.setPosition(t);var r=n.scan();switch(r){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var r=N(t.getText(),!0),i=e.items,o=i.length-1;o>=0;o--){var a=i[o];if(n>a.offset+a.length){r.setPosition(a.offset+a.length);var s=r.scan();return 5===s&&n>=r.getTokenOffset()+r.getTokenLength()?o+1:o}if(n>=a.offset)return o}return 0},e.prototype.isInComment=function(e,t,n){var r=N(e.getText(),!1);r.setPosition(t);var i=r.scan();while(17!==i&&r.getTokenOffset()+r.getTokenLength()<n)i=r.scan();return(12===i||13===i)&&r.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:G.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!q(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(G.Markdown)}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!q(this.supportsCommitCharacters)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=e&&e.completionItem&&!!e.completionItem.commitCharactersSupport}return this.supportsCommitCharacters}})(),function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}e.prototype.doHover=function(e,t,n){var r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||("object"===i.type||"array"===i.type)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);var o=i;if("string"===i.type){var a=i.parent;if(a&&"property"===a.type&&a.keyNode===i&&(i=a.valueNode,!i))return this.promise.resolve(null)}for(var s=v.create(e.positionAt(o.offset),e.positionAt(o.offset+o.length)),u=function(e){var t={contents:e,range:s};return t},c=Be(i),l=this.contributions.length-1;l>=0;l--){var f=this.contributions[l],h=f.getInfoContribution(e.uri,c);if(h)return h.then((function(e){return u(e)}))}return this.schemaService.getSchemaForResource(e.uri,n).then((function(e){if(e){var t=n.getMatchingSchemas(e.schema,i.offset),r=null,o=null,a=null,s=null;t.every((function(e){if(e.node===i&&!e.inverted&&e.schema&&(r=r||e.schema.title,o=o||e.schema.markdownDescription||et(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(qe(i));e.schema.markdownEnumDescriptions?a=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(a=et(e.schema.enumDescriptions[t])),a&&(s=e.schema.enum[t],"string"!==typeof s&&(s=JSON.stringify(s)))}return!0}));var c="";return r&&(c=et(r)),o&&(c.length>0&&(c+="\n\n"),c+=o),a&&(c.length>0&&(c+="\n\n"),c+="`"+tt(s)+"`: "+a),u([c])}return null}))}}();function et(e){if(e){var t=e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3");return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}}function tt(e){return-1!==e.indexOf("`")?"`` "+e+" ``":e}var nt=n("c623"),rt=_e(),it=function(){function e(e){try{this.patternRegExp=new RegExp(Ze(e)+"$")}catch(t){this.patternRegExp=null}this.schemas=[]}return e.prototype.addSchema=function(e){this.schemas.push(e)},e.prototype.matchesPattern=function(e){return this.patternRegExp&&this.patternRegExp.test(e)},e.prototype.getSchemas=function(){return this.schemas},e}(),ot=function(){function e(e,t,n){this.service=e,this.url=t,this.dependencies={},n&&(this.unresolvedSchema=this.service.promise.resolve(new at(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e.url,e.dependencies)}))),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=null,this.unresolvedSchema=null,this.dependencies={}},e}(),at=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e}(),st=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){return Le(this.getSectionRecursive(e,this.schema))},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"===typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,i=Object.keys(t.patternProperties);r<i.length;r++){var o=i[r],a=new RegExp(o);if(a.test(n))return this.getSectionRecursive(e,t.patternProperties[o])}else{if("object"===typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var s=parseInt(n,10);if(!isNaN(s)&&t.items[s])return this.getSectionRecursive(e,t.items[s])}else if(t.items)return this.getSectionRecursive(e,t.items)}return null},e}();(function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations={},this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={}}e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=nt["a"].parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){while(this.callOnDispose.length>0)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){var t=this,n=!1;e=this.normalizeId(e);var r=[e],i=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));while(r.length)for(var o=r.pop(),a=0;a<i.length;a++){var s=i[a];s&&(s.url===o||s.dependencies[o])&&(s.url!==o&&r.push(s.url),s.clearSchema(),i[a]=void 0,n=!0)}return n},e.prototype.normalizeId=function(e){try{return nt["a"].parse(e).toString()}catch(t){return e}},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=this.normalizeId(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(e.schemaAssociations){var i=e.schemaAssociations;for(var o in i){var a=i[o];this.contributionAssociations[o]=a;for(var s=this.getOrAddFilePatternAssociation(o),u=0,c=a;u<c.length;u++){var l=c[u];n=this.normalizeId(l);s.addSchema(n)}}}},e.prototype.addSchemaHandle=function(e,t){var n=new ot(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.getOrAddFilePatternAssociation=function(e){var t=this.filePatternAssociationById[e];return t||(t=new it(e),this.filePatternAssociationById[e]=t,this.filePatternAssociations.push(t)),t},e.prototype.registerExternalSchema=function(e,t,n){void 0===t&&(t=null);var r=this.normalizeId(e);if(this.registeredSchemasIds[r]=!0,t)for(var i=0,o=t;i<o.length;i++){var a=o[i];this.getOrAddFilePatternAssociation(a).addSchema(r)}return n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={},this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t in this.contributionAssociations)for(var n=this.getOrAddFilePatternAssociation(t),r=0,i=this.contributionAssociations[t];r<i.length;r++){var o=i[r];e=this.normalizeId(o);n.addSchema(e)}},e.prototype.getResolvedSchema=function(e){var t=this.normalizeId(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(null)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=rt("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",ut(e));return this.promise.resolve(new at({},[t]))}return this.requestService(e).then((function(t){if(!t){var n=rt("json.schema.nocontent","Unable to load schema from '{0}': No content.",ut(e));return new at({},[n])}var r={},i=[];r=R(t,i);var o=i.length?[rt("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",ut(e),i[0].offset)]:[];return new at(r,o)}),(function(t){var n=t.toString(),r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),Ge(n,".")&&(n=n.substr(0,n.length-1)),new at({},[rt("json.schema.nocontent","Unable to load schema from '{0}': {1}.",ut(e),n)])}))},e.prototype.resolveSchemaContent=function(e,t,n){var r=this,i=e.errors.slice(0),o=e.schema;if(o.$schema){var a=this.normalizeId(o.$schema);if("http://json-schema.org/draft-03/schema"===a)return this.promise.resolve(new st({},[rt("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));"https://json-schema.org/draft/2019-09/schema"===a&&e.errors.push(rt("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported."))}var s=this.contextService,u=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some((function(e){return n=n[e],!n})),n},c=function(e,t,n,r){var o=u(t,r);if(o)for(var a in o)o.hasOwnProperty(a)&&!e.hasOwnProperty(a)&&(e[a]=o[a]);else i.push(rt("json.schema.invalidref","$ref '{0}' in '{1}' can not be resolved.",r,n))},l=function(e,t,n,o,a){s&&!/^\w+:\/\/.*/.test(t)&&(t=s.resolveRelativePath(t,o)),t=r.normalizeId(t);var u=r.getOrAddSchemaHandle(t);return u.getUnresolvedSchema().then((function(r){if(a[t]=!0,r.errors.length){var o=n?t+"#"+n:t;i.push(rt("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,r.errors[0]))}return c(e,r.schema,t,n),f(e,r.schema,t,u.dependencies)}))},f=function(e,t,n,i){if(!e||"object"!==typeof e)return Promise.resolve(null);var o=[e],a=[],s=[],u=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];"object"===typeof i&&o.push(i)}},f=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if("object"===typeof i)for(var a in i){var s=i[a];"object"===typeof s&&o.push(s)}}},h=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if(Array.isArray(i))for(var a=0,s=i;a<s.length;a++){var u=s[a];"object"===typeof u&&o.push(u)}}},p=function(e){var r=[];while(e.$ref){var o=e.$ref,a=o.split("#",2);if(delete e.$ref,a[0].length>0)return void s.push(l(e,a[0],a[1],n,i));-1===r.indexOf(o)&&(c(e,t,n,a[1]),r.push(o))}u(e.items,e.additionalProperties,e.not,e.contains,e.propertyNames,e.if,e.then,e.else),f(e.definitions,e.properties,e.patternProperties,e.dependencies),h(e.anyOf,e.allOf,e.oneOf,e.items)};while(o.length){var m=o.pop();a.indexOf(m)>=0||(a.push(m),p(m))}return r.promise.all(s)};return f(o,o,t,n).then((function(e){return new st(o,i)}))},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter((function(e){return"$schema"===e.keyNode.value&&e.valueNode&&"string"===e.valueNode.type}));if(n.length>0){var r=qe(n[0].valueNode);if(r&&He(r,".")&&this.contextService&&(r=this.contextService.resolveRelativePath(r,e)),r){var i=this.normalizeId(r);return this.getOrAddSchemaHandle(i).getResolvedSchema()}}}for(var o=Object.create(null),a=[],s=0,u=this.filePatternAssociations;s<u.length;s++){var c=u[s];if(c.matchesPattern(e))for(var l=0,f=c.getSchemas();l<f.length;l++){var h=f[l];o[h]||(a.push(h),o[h]=!0)}}return a.length>0?this.createCombinedSchema(e,a).getResolvedSchema():this.promise.resolve(null)},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)}})();function ut(e){try{var t=nt["a"].parse(e);if("file"===t.scheme)return t.fsPath}catch(n){}return e}var ct=_e(),lt=(function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}e.prototype.configure=function(e){e&&(this.validationEnabled=e.validate,this.commentSeverity=e.allowComments?void 0:I.Error)},e.prototype.doValidation=function(e,t,n,r){var i=this;if(!this.validationEnabled)return this.promise.resolve([]);var o=[],a={},s=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;a[t]||(a[t]=!0,o.push(e))},u=function(r){var a=n?pt(n.trailingCommas):I.Error,u=n?pt(n.comments):i.commentSeverity;if(r){if(r.errors.length&&t.root){var c=t.root,l="object"===c.type?c.properties[0]:null;if(l&&"$schema"===l.keyNode.value){var f=l.valueNode||l,h=v.create(e.positionAt(f.offset),e.positionAt(f.offset+f.length));s(E.create(h,r.errors[0],I.Warning,Ce.SchemaResolveError))}else{h=v.create(e.positionAt(c.offset),e.positionAt(c.offset+1));s(E.create(h,r.errors[0],I.Warning,Ce.SchemaResolveError))}}else{var p=t.validate(e,r.schema);p&&p.forEach(s)}ft(r.schema)&&(u=void 0),ht(r.schema)&&(a=void 0)}for(var m=0,d=t.syntaxErrors;m<d.length;m++){var g=d[m];if(g.code===Ce.TrailingComma){if("number"!==typeof a)continue;g.severity=a}s(g)}if("number"===typeof u){var y=ct("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){s(E.create(e,y,u,Ce.CommentNotPermitted))}))}return o};if(r){var c=r.id||"schemaservice://untitled/"+lt++;return this.jsonSchemaService.resolveSchemaContent(new at(r),c,{}).then((function(e){return u(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return u(e)}))}}(),0);function ft(e){if(e&&"object"===typeof e){if(B(e.allowComments))return e.allowComments;if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=n[t],i=ft(r);if(B(i))return i}}}function ht(e){if(e&&"object"===typeof e){if(B(e.allowTrailingCommas))return e.allowTrailingCommas;if(B(e["allowsTrailingCommas"]))return e["allowsTrailingCommas"];if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=n[t],i=ht(r);if(B(i))return i}}}function pt(e){switch(e){case"error":return I.Error;case"warning":return I.Warning;case"ignore":return}}var mt=48,dt=57,gt=65,vt=97,yt=102;function bt(e){return e<mt?0:e<=dt?e-mt:(e<vt&&(e+=vt-gt),e>=vt&&e<=yt?e-vt+10:0)}function xt(e){if("#"!==e[0])return null;switch(e.length){case 4:return{red:17*bt(e.charCodeAt(1))/255,green:17*bt(e.charCodeAt(2))/255,blue:17*bt(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*bt(e.charCodeAt(1))/255,green:17*bt(e.charCodeAt(2))/255,blue:17*bt(e.charCodeAt(3))/255,alpha:17*bt(e.charCodeAt(4))/255};case 7:return{red:(16*bt(e.charCodeAt(1))+bt(e.charCodeAt(2)))/255,green:(16*bt(e.charCodeAt(3))+bt(e.charCodeAt(4)))/255,blue:(16*bt(e.charCodeAt(5))+bt(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*bt(e.charCodeAt(1))+bt(e.charCodeAt(2)))/255,green:(16*bt(e.charCodeAt(3))+bt(e.charCodeAt(4)))/255,blue:(16*bt(e.charCodeAt(5))+bt(e.charCodeAt(6)))/255,alpha:(16*bt(e.charCodeAt(7))+bt(e.charCodeAt(8)))/255}}return null}(function(){function e(e){this.schemaService=e}e.prototype.findDocumentSymbols=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return null;var o=n.resultLimit,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Ge(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var l=c[u];if("object"===l.type)for(var f=0,h=l.properties;f<h.length;f++){var p=h[f];if("key"===p.keyNode.value&&p.valueNode){var m=y.create(e.uri,St(e,l));if(s.push({name:qe(p.valueNode),kind:ue.Function,location:m}),o--,o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}var d=[{node:i,containerName:""}],g=0,v=!1,b=[],x=function(t,n){"array"===t.type?t.items.forEach((function(e){e&&d.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=y.create(e.uri,St(e,t)),s=n?n+"."+t.keyNode.value:t.keyNode.value;b.push({name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),location:a,containerName:n}),d.push({node:i,containerName:s})}else v=!0}))};while(g<d.length){var S=d[g++];x(S.node,S.containerName)}return v&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),b},e.prototype.findDocumentSymbols2=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return null;var o=n.resultLimit,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Ge(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var l=c[u];if("object"===l.type)for(var f=0,h=l.properties;f<h.length;f++){var p=h[f];if("key"===p.keyNode.value&&p.valueNode){var m=St(e,l),d=St(e,p.keyNode);if(s.push({name:qe(p.valueNode),kind:ue.Function,range:m,selectionRange:d}),o--,o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}var g=[],v=[{node:i,result:g}],y=0,b=!1,x=function(t,n){"array"===t.type?t.items.forEach((function(t,i){if(t)if(o>0){o--;var a=St(e,t),s=a,u=String(i),c={name:u,kind:r.getSymbolKind(t.type),range:a,selectionRange:s,children:[]};n.push(c),v.push({result:c.children,node:t})}else b=!0})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=St(e,t),s=St(e,t.keyNode),u={name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),range:a,selectionRange:s,children:[]};n.push(u),v.push({result:u.children,node:i})}else b=!0}))};while(y<v.length){var S=v[y++];x(S.node,S.result)}return b&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),g},e.prototype.getSymbolKind=function(e){switch(e){case"object":return ue.Module;case"string":return ue.String;case"number":return ue.Number;case"array":return ue.Array;case"boolean":return ue.Boolean;default:return ue.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"↵")),t&&t.trim()?t:'"'+t+'"'},e.prototype.findDocumentColors=function(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var o=n&&"number"===typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE,a=t.getMatchingSchemas(r.schema),s={},u=0,c=a;u<c.length;u++){var l=c[u];if(!l.inverted&&l.schema&&("color"===l.schema.format||"color-hex"===l.schema.format)&&l.node&&"string"===l.node.type){var f=String(l.node.offset);if(!s[f]){var h=xt(qe(l.node));if(h){var p=St(e,l.node);i.push({color:h,range:p})}if(s[f]=!0,o--,o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var i,o=[],a=Math.round(255*n.red),s=Math.round(255*n.green),u=Math.round(255*n.blue);function c(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return i=1===n.alpha?"#"+c(a)+c(s)+c(u):"#"+c(a)+c(s)+c(u)+c(Math.round(255*n.alpha)),o.push({label:i,textEdit:j.replace(r,JSON.stringify(i))}),o}})();function St(e,t){return v.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var kt=_e(),wt={schemaAssociations:{},schemas:{"http://json-schema.org/draft-04/schema#":{title:kt("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{title:kt("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},Ct={id:kt("schema.json.id","A unique identifier for the schema."),$schema:kt("schema.json.$schema","The schema to verify this document against."),title:kt("schema.json.title","A descriptive title of the element."),description:kt("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:kt("schema.json.default","A default value. Used by suggestions."),multipleOf:kt("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:kt("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:kt("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:kt("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:kt("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:kt("schema.json.maxLength","The maximum length of a string."),minLength:kt("schema.json.minLength","The minimum length of a string."),pattern:kt("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:kt("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:kt("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:kt("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:kt("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:kt("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:kt("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:kt("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:kt("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:kt("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:kt("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:kt("schema.json.properties","A map of property names to schemas for each property."),patternProperties:kt("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:kt("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:kt("schema.json.enum","The set of literal values that are valid."),type:kt("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:kt("schema.json.format","Describes the format expected for the value."),allOf:kt("schema.json.allOf","An array of schemas, all of which must match."),anyOf:kt("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:kt("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:kt("schema.json.not","A schema which must not match."),$id:kt("schema.json.$id","A unique identifier for the schema."),$ref:kt("schema.json.$ref","Reference a definition hosted on any location."),$comment:kt("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:kt("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:kt("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:kt("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:kt("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:kt("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:kt("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:kt("schema.json.contentEncoding","Describes the content encoding of a string property."),if:kt("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:kt("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:kt("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(var At in wt.schemas){var It=wt.schemas[At];for(var Tt in It.properties){var Et=It.properties[Tt];!0===Et&&(Et=It.properties[Tt]={});var Ot=Ct[Tt];Ot?Et["description"]=Ot:console.log(Tt+": localize('schema.json."+Tt+'\', "")')}}monaco.Uri;var jt=monaco.Range,Pt=function(){function e(e,t,n){var r=this;this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);var i=function(e){var t,n=e.getModeId();n===r._languageId&&(r._listener[e.uri.toString()]=e.onDidChangeContent((function(){clearTimeout(t),t=setTimeout((function(){return r._doValidate(e.uri,n)}),500)})),r._doValidate(e.uri,n))},o=function(e){monaco.editor.setModelMarkers(e,r._languageId,[]);var t=e.uri.toString(),n=r._listener[t];n&&(n.dispose(),delete r._listener[t])};this._disposables.push(monaco.editor.onDidCreateModel(i)),this._disposables.push(monaco.editor.onWillDisposeModel((function(e){o(e),r._resetSchema(e.uri)}))),this._disposables.push(monaco.editor.onDidChangeModelLanguage((function(e){o(e.model),i(e.model),r._resetSchema(e.model.uri)}))),this._disposables.push(n.onDidChange((function(e){monaco.editor.getModels().forEach((function(e){e.getModeId()===r._languageId&&(o(e),i(e))}))}))),this._disposables.push({dispose:function(){for(var e in monaco.editor.getModels().forEach(o),r._listener)r._listener[e].dispose()}}),monaco.editor.getModels().forEach(i)}return e.prototype.dispose=function(){this._disposables.forEach((function(e){return e&&e.dispose()})),this._disposables=[]},e.prototype._resetSchema=function(e){this._worker().then((function(t){t.resetSchema(e.toString())}))},e.prototype._doValidate=function(e,t){this._worker(e).then((function(n){return n.doValidation(e.toString()).then((function(n){var r=n.map((function(t){return _t(e,t)})),i=monaco.editor.getModel(e);i&&i.getModeId()===t&&monaco.editor.setModelMarkers(i,t,r)}))})).then(void 0,(function(e){console.error(e)}))},e}();function Mt(e){switch(e){case I.Error:return monaco.MarkerSeverity.Error;case I.Warning:return monaco.MarkerSeverity.Warning;case I.Information:return monaco.MarkerSeverity.Info;case I.Hint:return monaco.MarkerSeverity.Hint;default:return monaco.MarkerSeverity.Info}}function _t(e,t){var n="number"===typeof t.code?String(t.code):t.code;return{severity:Mt(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}function Vt(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function Ft(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function Nt(e){if(e)return new jt(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Rt(e){var t=monaco.languages.CompletionItemKind;switch(e){case X.Text:return t.Text;case X.Method:return t.Method;case X.Function:return t.Function;case X.Constructor:return t.Constructor;case X.Field:return t.Field;case X.Variable:return t.Variable;case X.Class:return t.Class;case X.Interface:return t.Interface;case X.Module:return t.Module;case X.Property:return t.Property;case X.Unit:return t.Unit;case X.Value:return t.Value;case X.Enum:return t.Enum;case X.Keyword:return t.Keyword;case X.Snippet:return t.Snippet;case X.Color:return t.Color;case X.File:return t.File;case X.Reference:return t.Reference}return t.Property}function $t(e){if(e)return{range:Nt(e.range),text:e.newText}}var Lt=function(){function e(e){this._worker=e}return Object.defineProperty(e.prototype,"triggerCharacters",{get:function(){return[" ",":"]},enumerable:!0,configurable:!0}),e.prototype.provideCompletionItems=function(e,t,n,r){var i=e.uri;return this._worker(i).then((function(e){return e.doComplete(i.toString(),Vt(t))})).then((function(n){if(n){var r=e.getWordUntilPosition(t),i=new jt(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),o=n.items.map((function(e){var t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,range:i,kind:Rt(e.kind)};return e.textEdit&&(t.range=Nt(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map($t)),e.insertTextFormat===Q.Snippet&&(t.insertTextRules=monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:n.isIncomplete,suggestions:o}}}))},e}();function Dt(e){return e&&"object"===typeof e&&"string"===typeof e.kind}function Wt(e){return"string"===typeof e?{value:e}:Dt(e)?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"}}function Ut(e){if(e)return Array.isArray(e)?e.map(Wt):[Wt(e)]}var qt=function(){function e(e){this._worker=e}return e.prototype.provideHover=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.doHover(r.toString(),Vt(t))})).then((function(e){if(e)return{range:Nt(e.range),contents:Ut(e.contents)}}))},e}();function Bt(e){var t=monaco.languages.SymbolKind;switch(e){case ue.File:return t.Array;case ue.Module:return t.Module;case ue.Namespace:return t.Namespace;case ue.Package:return t.Package;case ue.Class:return t.Class;case ue.Method:return t.Method;case ue.Property:return t.Property;case ue.Field:return t.Field;case ue.Constructor:return t.Constructor;case ue.Enum:return t.Enum;case ue.Interface:return t.Interface;case ue.Function:return t.Function;case ue.Variable:return t.Variable;case ue.Constant:return t.Constant;case ue.String:return t.String;case ue.Number:return t.Number;case ue.Boolean:return t.Boolean;case ue.Array:return t.Array}return t.Function}var Kt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentSymbols=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentSymbols(n.toString())})).then((function(e){if(e)return e.map((function(e){return{name:e.name,detail:"",containerName:e.containerName,kind:Bt(e.kind),range:Nt(e.location.range),selectionRange:Nt(e.location.range),tags:[]}}))}))},e}();function Jt(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var zt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentFormattingEdits=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.format(r.toString(),null,Jt(t)).then((function(e){if(e&&0!==e.length)return e.map($t)}))}))},e}(),Ht=function(){function e(e){this._worker=e}return e.prototype.provideDocumentRangeFormattingEdits=function(e,t,n,r){var i=e.uri;return this._worker(i).then((function(e){return e.format(i.toString(),Ft(t),Jt(n)).then((function(e){if(e&&0!==e.length)return e.map($t)}))}))},e}(),Gt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentColors=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentColors(n.toString())})).then((function(e){if(e)return e.map((function(e){return{color:e.color,range:Nt(e.range)}}))}))},e.prototype.provideColorPresentations=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getColorPresentations(r.toString(),t.color,Ft(t.range))})).then((function(e){if(e)return e.map((function(e){var t={label:e.label};return e.textEdit&&(t.textEdit=$t(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map($t)),t}))}))},e}(),Zt=function(){function e(e){this._worker=e}return e.prototype.provideFoldingRanges=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getFoldingRanges(r.toString(),t)})).then((function(e){if(e)return e.map((function(e){var t={start:e.startLine+1,end:e.endLine+1};return"undefined"!==typeof e.kind&&(t.kind=Xt(e.kind)),t}))}))},e}();function Xt(e){switch(e){case w.Comment:return monaco.languages.FoldingRangeKind.Comment;case w.Imports:return monaco.languages.FoldingRangeKind.Imports;case w.Region:return monaco.languages.FoldingRangeKind.Region}}var Qt=function(){function e(e){this._worker=e}return e.prototype.provideSelectionRanges=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getSelectionRanges(r.toString(),t.map(Vt))})).then((function(e){if(e)return e.map((function(e){var t=[];while(e)t.push({range:Nt(e.range)}),e=e.parent;return t}))}))},e}();function Yt(e){return{getInitialState:function(){return new hn(null,null,!1)},tokenize:function(t,n,r,i){return pn(e,t,n,r,i)}}}var en="delimiter.bracket.json",tn="delimiter.array.json",nn="delimiter.colon.json",rn="delimiter.comma.json",on="keyword.json",an="keyword.json",sn="string.value.json",un="number.json",cn="string.key.json",ln="comment.block.json",fn="comment.line.json",hn=function(){function e(e,t,n){this._state=e,this.scanError=t,this.lastWasColon=n}return e.prototype.clone=function(){return new e(this._state,this.scanError,this.lastWasColon)},e.prototype.equals=function(t){return t===this||!!(t&&t instanceof e)&&(this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon)},e.prototype.getStateData=function(){return this._state},e.prototype.setStateData=function(e){this._state=e},e}();function pn(e,t,n,r,i){void 0===r&&(r=0);var o=0,a=!1;switch(n.scanError){case 2:t='"'+t,o=1;break;case 1:t="/*"+t,o=2;break}var s,u,c=N(t),l=n.lastWasColon;u={tokens:[],endState:n.clone()};while(1){var f=r+c.getPosition(),h="";if(s=c.scan(),17===s)break;if(f===r+c.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+t.substr(c.getPosition(),3));switch(a&&(f-=o),a=o>0,s){case 1:h=en,l=!1;break;case 2:h=en,l=!1;break;case 3:h=tn,l=!1;break;case 4:h=tn,l=!1;break;case 6:h=nn,l=!0;break;case 5:h=rn,l=!1;break;case 8:case 9:h=on,l=!1;break;case 7:h=an,l=!1;break;case 10:h=l?sn:cn,l=!1;break;case 11:h=un,l=!1;break}if(e)switch(s){case 12:h=fn;break;case 13:h=ln;break}u.endState=new hn(n.getStateData(),c.getTokenError(),l),u.tokens.push({startIndex:f,scopes:h})}return u}function mn(e){var t=[],n=[],r=new o(e);t.push(r);var i=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r.getLanguageServiceWorker.apply(r,e)};function a(){var t=e.languageId,r=e.modeConfiguration;gn(n),r.documentFormattingEdits&&n.push(monaco.languages.registerDocumentFormattingEditProvider(t,new zt(i))),r.documentRangeFormattingEdits&&n.push(monaco.languages.registerDocumentRangeFormattingEditProvider(t,new Ht(i))),r.completionItems&&n.push(monaco.languages.registerCompletionItemProvider(t,new Lt(i))),r.hovers&&n.push(monaco.languages.registerHoverProvider(t,new qt(i))),r.documentSymbols&&n.push(monaco.languages.registerDocumentSymbolProvider(t,new Kt(i))),r.tokens&&n.push(monaco.languages.setTokensProvider(t,Yt(!0))),r.colors&&n.push(monaco.languages.registerColorProvider(t,new Gt(i))),r.foldingRanges&&n.push(monaco.languages.registerFoldingRangeProvider(t,new Zt(i))),r.diagnostics&&n.push(new Pt(t,i,e)),r.selectionRanges&&n.push(monaco.languages.registerSelectionRangeProvider(t,new Qt(i)))}a(),t.push(monaco.languages.setLanguageConfiguration(e.languageId,vn));var s=e.modeConfiguration;return e.onDidChange((function(e){e.modeConfiguration!==s&&(s=e.modeConfiguration,a())})),t.push(dn(n)),dn(t)}function dn(e){return{dispose:function(){return gn(e)}}}function gn(e){while(e.length)e.pop().dispose()}n.d(t,"setupMode",(function(){return mn}));var vn={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]}},c623:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r,i,o=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();if("object"===typeof e)i="win32"===e.platform;else if("object"===typeof navigator){var a=navigator.userAgent;i=a.indexOf("Windows")>=0}var s=/^\w[\w\d+.-]*$/,u=/^\//,c=/^\/\//;function l(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!s.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!u.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(c.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}function f(e,t){return e||t?e:"file"}function h(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==m&&(t=m+t):t=m;break}return t}var p="",m="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,g=function(){function e(e,t,n,r,i,o){void 0===o&&(o=!1),"object"===typeof e?(this.scheme=e.scheme||p,this.authority=e.authority||p,this.path=e.path||p,this.query=e.query||p,this.fragment=e.fragment||p):(this.scheme=f(e,o),this.authority=t||p,this.path=h(this.scheme,n||p),this.query=r||p,this.fragment=i||p,l(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&("string"===typeof t.authority&&"string"===typeof t.fragment&&"string"===typeof t.path&&"string"===typeof t.query&&"string"===typeof t.scheme&&"function"===typeof t.fsPath&&"function"===typeof t.with&&"function"===typeof t.toString)},Object.defineProperty(e.prototype,"fsPath",{get:function(){return k(this)},enumerable:!0,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=p),void 0===n?n=this.authority:null===n&&(n=p),void 0===r?r=this.path:null===r&&(r=p),void 0===i?i=this.query:null===i&&(i=p),void 0===o?o=this.fragment:null===o&&(o=p),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new y(t,n,r,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var n=d.exec(e);return n?new y(n[2]||p,decodeURIComponent(n[4]||p),decodeURIComponent(n[5]||p),decodeURIComponent(n[7]||p),decodeURIComponent(n[9]||p),t):new y(p,p,p,p,p)},e.file=function(e){var t=p;if(i&&(e=e.replace(/\\/g,m)),e[0]===m&&e[1]===m){var n=e.indexOf(m,2);-1===n?(t=e.substring(2),e=m):(t=e.substring(2,n),e=e.substring(n)||m)}return new y("file",t,e,p,p)},e.from=function(e){return new y(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),w(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new y(t);return n._formatted=t.external,n._fsPath=t._sep===v?t.fsPath:null,n}return t},e}(),v=i?1:void 0,y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return o(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=k(this)),this._fsPath},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?w(this,!0):(this._formatted||(this._formatted=w(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=v),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(g),b=(r={},r[58]="%3A",r[47]="%2F",r[63]="%3F",r[35]="%23",r[91]="%5B",r[93]="%5D",r[64]="%40",r[33]="%21",r[36]="%24",r[38]="%26",r[39]="%27",r[40]="%28",r[41]="%29",r[42]="%2A",r[43]="%2B",r[44]="%2C",r[59]="%3B",r[61]="%3D",r[32]="%20",r);function x(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var a=b[o];void 0!==a?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=a):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function S(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=b[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function k(e){var t;return t=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?e.path[1].toLowerCase()+e.path.substr(2):e.path,i&&(t=t.replace(/\//g,"\\")),t}function w(e,t){var n=t?S:x,r="",i=e.scheme,o=e.authority,a=e.path,s=e.query,u=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=m,r+=m),o){var c=o.indexOf("@");if(-1!==c){var l=o.substr(0,c);o=o.substr(c+1),c=l.indexOf(":"),-1===c?r+=n(l,!1):(r+=n(l.substr(0,c),!1),r+=":",r+=n(l.substr(c+1),!1)),r+="@"}o=o.toLowerCase(),c=o.indexOf(":"),-1===c?r+=n(o,!1):(r+=n(o.substr(0,c),!1),r+=o.substr(c))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2)){var f=a.charCodeAt(1);f>=65&&f<=90&&(a="/"+String.fromCharCode(f+32)+":"+a.substr(3))}else if(a.length>=2&&58===a.charCodeAt(1)){f=a.charCodeAt(0);f>=65&&f<=90&&(a=String.fromCharCode(f+32)+":"+a.substr(2))}r+=n(a,!0)}return s&&(r+="?",r+=n(s,!1)),u&&(r+="#",r+=t?u:x(u,!1)),r}}).call(this,n("f28c"))}}]);