(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-441b1f68"],{"0351":function(t,e,r){"use strict";var n=r("ec31"),s=r.n(n);s.a},"90e7":function(t,e,r){"use strict";r.d(e,"n",(function(){return s})),r.d(e,"k",(function(){return a})),r.d(e,"d",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"b",(function(){return l})),r.d(e,"a",(function(){return c})),r.d(e,"l",(function(){return u})),r.d(e,"p",(function(){return p})),r.d(e,"q",(function(){return m})),r.d(e,"m",(function(){return f})),r.d(e,"e",(function(){return d})),r.d(e,"o",(function(){return _})),r.d(e,"f",(function(){return h})),r.d(e,"j",(function(){return y})),r.d(e,"g",(function(){return v})),r.d(e,"h",(function(){return b})),r.d(e,"i",(function(){return g}));var n=r("b6bd");function s(){return Object(n["a"])({url:"/supplier",method:"get"})}function a(t){return Object(n["a"])({url:"/supplier",method:"put",data:t})}function i(t){return Object(n["a"])({url:"city",method:"get",params:t})}function o(t){return Object(n["a"])({url:"admin",method:"get",params:t})}function l(){return Object(n["a"])({url:"admin/create",method:"get"})}function c(t){return Object(n["a"])({url:"admin/".concat(t,"/edit"),method:"get"})}function u(t){return Object(n["a"])({url:"admin/set_status/".concat(t.id,"/").concat(t.status),method:"put"})}function p(t){return Object(n["a"])({url:"setting/shipping_templates/list",method:"get",params:t})}function m(t,e){return Object(n["a"])({url:"setting/shipping_templates/save/".concat(t),method:"post",data:e})}function f(t){return Object(n["a"])({url:"setting/shipping_templates/".concat(t,"/edit"),method:"get"})}function d(t){return Object(n["a"])({url:"city",method:"get",params:t})}function _(t,e){return Object(n["a"])({url:"/system/form/info/".concat(t),method:"get",params:e})}function h(t){return Object(n["a"])({url:"/print/list",method:"get",params:t})}function y(t){var e=t.id,r=t.status;return Object(n["a"])({url:"/print/set_status/".concat(e,"/").concat(r),method:"get"})}function v(t,e){return Object(n["a"])({url:"/print/save/".concat(t),method:"post",data:e})}function b(t,e){return Object(n["a"])({url:"/print/content/".concat(t),method:"get",params:e})}function g(t,e){return Object(n["a"])({url:"/print/save_content/".concat(t),method:"post",data:e})}},d89c:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Card",{attrs:{bordered:!1,"dis-hover":""}},[r("Button",{staticClass:"mb15",attrs:{type:"primary"},on:{click:t.addTicket}},[t._v("添加打印机")]),r("Table",{attrs:{columns:t.columns,data:t.dataList,loading:t.loading},scopedSlots:t._u([{key:"plat_type",fn:function(e){var n=e.row;return[r("div",[t._v(t._s(1==n.plat_type?"易联云":"飞鹅云"))])]}},{key:"status",fn:function(e){var n=e.row;return[r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},on:{"on-change":function(e){return t.statusChange(n)}},model:{value:n.status,callback:function(e){t.$set(n,"status",e)},expression:"row.status"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])]}},{key:"print_event",fn:function(e){var n=e.row;return[r("div",[t._v("\n          "+t._s(-1!=n.print_event.indexOf("2")?"支付后打印":"")+"\n          "+t._s(-1!=n.print_event.indexOf("1")?"下单后打印":"")+"\n        ")])]}},{key:"action",fn:function(e){var n=e.row;return[r("a",{on:{click:function(e){return t.onSetting(n.id)}}},[t._v("设计")]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.onEdit(n)}}},[t._v("编辑")]),r("Divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(e){return t.onDelete(n.id)}}},[t._v("删除")])]}}])}),r("div",{staticClass:"acea-row row-right page"},[r("Page",{attrs:{total:t.total,"page-size":t.limit,"show-total":""},on:{"on-change":t.onChange}})],1)],1),r("addTicket",{ref:"ticket",attrs:{formData:t.currentRow},on:{printerList:t.printerList}})],1)},s=[],a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{attrs:{closable:"",title:t.id?"编辑打印机":"添加打印机","mask-closable":!1,"z-index":11,width:"650"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[r("div",[r("Form",{ref:"form",attrs:{size:"small",model:t.form,"label-width":120}},[r("FormItem",{attrs:{label:"平台选择："}},[r("RadioGroup",{attrs:{"true-value":1,"false-value":2},model:{value:t.form.plat_type,callback:function(e){t.$set(t.form,"plat_type",e)},expression:"form.plat_type"}},[r("Radio",{attrs:{label:1}},[t._v("易联云")]),r("Radio",{attrs:{label:2}},[t._v("飞鹅云")])],1),r("p",{staticClass:"desc on"},[t._v("打印平台选择")])],1),r("FormItem",{attrs:{label:"打印机名称：",prop:"name"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写打印机名称"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),1==t.form.plat_type?r("div",[r("FormItem",{attrs:{label:"开发者ID：",prop:"yly_user_id"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写用户id"},model:{value:t.form.yly_user_id,callback:function(e){t.$set(t.form,"yly_user_id",e)},expression:"form.yly_user_id"}}),r("p",{staticClass:"desc"},[t._v("易联云开发者ID")])],1),r("FormItem",{attrs:{label:"应用密钥：",prop:"yly_app_secret"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写应用密钥"},model:{value:t.form.yly_app_secret,callback:function(e){t.$set(t.form,"yly_app_secret",e)},expression:"form.yly_app_secret"}}),r("p",{staticClass:"desc"},[t._v("易联云应用密钥")])],1),r("FormItem",{attrs:{label:"应用ID：",prop:"yly_app_id"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写应用ID"},model:{value:t.form.yly_app_id,callback:function(e){t.$set(t.form,"yly_app_id",e)},expression:"form.yly_app_id"}}),r("p",{staticClass:"desc"},[t._v("易联云应用ID")])],1),r("FormItem",{attrs:{label:"终端号：",prop:"yly_sn"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写终端号"},model:{value:t.form.yly_sn,callback:function(e){t.$set(t.form,"yly_sn",e)},expression:"form.yly_sn"}}),r("p",{staticClass:"desc"},[t._v("\n            易联云打印机终端号，打印机型号：易联云打印机K4无线版\n            "),r("Poptip",{attrs:{placement:"bottom",trigger:"hover",width:"256",transfer:"",padding:"8px"}},[r("a",[t._v("查看示例")]),r("div",{staticClass:"exampleImg",attrs:{slot:"content"},slot:"content"},[r("img",{attrs:{src:t.baseURL+"/statics/system/kuadi100Dump.png",alt:""}})])])],1)],1)],1):r("div",[r("FormItem",{attrs:{label:"飞鹅云USER：",prop:"fey_user"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写飞鹅云USER"},model:{value:t.form.fey_user,callback:function(e){t.$set(t.form,"fey_user",e)},expression:"form.fey_user"}}),r("p",{staticClass:"desc"},[t._v("飞鹅云后台注册账号")])],1),r("FormItem",{attrs:{label:"飞鹅云UKEY：",prop:"fey_ukey"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写飞鹅云UKEY"},model:{value:t.form.fey_ukey,callback:function(e){t.$set(t.form,"fey_ukey",e)},expression:"form.fey_ukey"}}),r("p",{staticClass:"desc"},[t._v("\n            飞鹅云后台注册账号后生成的UKEY(注：这不是填打印机的KEY)\n          ")])],1),r("FormItem",{attrs:{label:"飞鹅云SN：",prop:"fey_sn"}},[r("Input",{staticClass:"w-450",attrs:{placeholder:"请填写飞鹅云SN"},model:{value:t.form.fey_sn,callback:function(e){t.$set(t.form,"fey_sn",e)},expression:"form.fey_sn"}}),r("p",{staticClass:"desc"},[t._v("\n            打印机标签上的编号，必须要在管理后台里添加打印机或调用API接口添加之后才能调用API\n          ")])],1)],1),r("FormItem",{attrs:{label:"打印联数：",prop:"print_num"}},[r("div",{staticClass:"acea-row row-middle"},[r("InputNumber",{staticClass:"w-450 num",attrs:{min:1,placeholder:"请填写打印联数"},model:{value:t.form.print_num,callback:function(e){t.$set(t.form,"print_num",e)},expression:"form.print_num"}}),t._v("联\n        ")],1),r("p",{staticClass:"desc"},[t._v("打印机单次打印张数")])]),r("FormItem",{attrs:{label:"打印时机："}},[r("CheckboxGroup",{attrs:{size:"small"},model:{value:t.form.print_event,callback:function(e){t.$set(t.form,"print_event",e)},expression:"form.print_event"}},[r("Checkbox",{attrs:{label:"2"}},[t._v("支付后打印")]),r("Checkbox",{attrs:{label:"1"}},[t._v("下单后打印")])],1)],1),r("FormItem",{attrs:{label:"小票打印开关："}},[r("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])])],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancel}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.addPrinterConfirm}},[t._v("确定")])],1)])},i=[],o=r("d708"),l=r("90e7"),c={name:"addTicket",props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{baseURL:o["a"].apiBaseURL.replace(/supplierapi/,""),id:0,modals:!1,form:{plat_type:1,name:"",yly_user_id:"",yly_app_secret:"",yly_app_id:"",yly_sn:"",print_num:1,print_event:[],status:1,fey_user:"",fey_ukey:"",fey_sn:""}}},watch:{formData:function(t){var e=this;Object.keys(this.form).forEach((function(r){e.form[r]=t[r]}))}},methods:{react:function(){this.form={plat_type:1,name:"",yly_user_id:"",yly_app_secret:"",yly_app_id:"",yly_sn:"",print_num:1,print_event:[],status:1,fey_user:"",fey_ukey:"",fey_sn:""}},cancel:function(){this.modals=!1},addPrinterConfirm:function(){var t=this;if(""==this.form.name.trim())return this.$Message.error("请填写打印机名称");if(1==this.form.plat_type){if(""==this.form.yly_user_id.trim())return this.$Message.error("请填写用户id");if(""==this.form.yly_app_secret.trim())return this.$Message.error("请填写应用密钥");if(""==this.form.yly_app_id.trim())return this.$Message.error("请填写应用ID");if(""==this.form.yly_sn.trim())return this.$Message.error("请填写终端号")}else{if(""==this.form.fey_user.trim())return this.$Message.error("请填写飞鹅云USER");if(""==this.form.fey_ukey.trim())return this.$Message.error("请填写飞鹅云UKEY");if(""==this.form.fey_sn.trim())return this.$Message.error("请填写飞鹅云SN")}if(!this.form.print_num)return this.$Message.error("请填写打印联数");Object(l["g"])(this.id,this.form).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$emit("printerList")})).catch((function(e){t.$Message.error(e.msg)}))}}},u=c,p=(r("0351"),r("2877")),m=Object(p["a"])(u,a,i,!1,null,"2ae4b862",null),f=m.exports,d={components:{addTicket:f},data:function(){return{columns:[{title:"ID",key:"id",width:80},{title:"打印机名称",key:"name",minWidth:200},{title:"平台",slot:"plat_type",minWidth:150},{title:"打印联数",key:"print_num",minWidth:150},{title:"打印时机",slot:"print_event",minWidth:150},{title:"创建时间",key:"add_time",minWidth:150},{title:"打印开关",slot:"status",minWidth:120},{title:"操作",slot:"action",align:"center",width:150}],dataList:[],total:100,limit:10,page:1,loading:!1,currentRow:{}}},created:function(){this.printerList()},methods:{printerList:function(){var t=this;this.loading=!0,Object(l["f"])({page:this.page,limit:this.limit}).then((function(e){var r=e.data,n=r.list,s=r.count;t.dataList=n,t.total=s,t.loading=!1})).catch((function(e){t.$Message.error(e.msg)}))},onChange:function(t){this.page=t,this.printerList()},statusChange:function(t){var e=this;Object(l["j"])(t).then((function(t){e.$Message.success(t.msg),e.printerList()})).catch((function(t){e.$Message.error(t.msg)}))},onDelete:function(t){var e=this;this.$modalSure({title:"删除打印机",url:"/print/del/".concat(t),method:"delete",ids:""}).then((function(t){e.$Message.success(t.msg),e.printerList()}))},addTicket:function(){this.$refs.ticket.react(),this.$refs.ticket.modals=!0,this.$refs.ticket.id=0},onEdit:function(t){this.currentRow=t,this.$refs.ticket.modals=!0,this.$refs.ticket.id=t.id},onSetting:function(t){this.$router.push({path:"".concat(o["a"].roterPre,"/setting/content/").concat(t)})}}},_=d,h=Object(p["a"])(_,n,s,!1,null,null,null);e["default"]=h.exports},ec31:function(t,e,r){}}]);