(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1189e90a"],{"05d3":function(t,e,s){},"0a1e":function(t,e,s){},"2f57":function(t,e,s){"use strict";var a=s("05d3"),i=s.n(a);i.a},"30a5":function(t,e,s){t.exports=s.p+"view_supplier/img/svip-user.b89bb400.png"},"40d6":function(t,e,s){"use strict";var a=s("7690"),i=s.n(a);i.a},4397:function(t,e,s){"use strict";var a=s("a532"),i=s.n(a);i.a},"5c3ac":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[s("Form",{ref:"orderData",staticClass:"tabform",attrs:{model:t.orderData,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[s("div",{staticClass:"acea-row"},[s("FormItem",{attrs:{label:"时间选择："}},[s("DatePicker",{staticClass:"mr30",staticStyle:{width:"250px"},attrs:{editable:!1,clearable:!0,value:t.timeVal,format:"yyyy/MM/dd HH:mm:ss",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1),s("FormItem",{attrs:{label:"订单类型："}},[s("Select",{staticClass:"mr30",staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"全部订单"},on:{"on-change":t.userSearchs},model:{value:t.orderData.type,callback:function(e){t.$set(t.orderData,"type",e)},expression:"orderData.type"}},[s("Option",{attrs:{value:""}},[t._v("全部订单")]),s("Option",{attrs:{value:"0"}},[t._v("普通订单")]),s("Option",{attrs:{value:"1"}},[t._v("秒杀订单")]),s("Option",{attrs:{value:"2"}},[t._v("砍价订单")]),s("Option",{attrs:{value:"3"}},[t._v("拼团订单")]),s("Option",{attrs:{value:"4"}},[t._v("积分订单")]),s("Option",{attrs:{value:"5"}},[t._v("套餐订单")]),s("Option",{attrs:{value:"6"}},[t._v("预售订单")]),s("Option",{attrs:{value:"7"}},[t._v("新人订单")]),s("Option",{attrs:{value:"8"}},[t._v("抽奖订单")]),s("Option",{attrs:{value:"9"}},[t._v("拼单订单")]),s("Option",{attrs:{value:"10"}},[t._v("桌码订单")])],1)],1),s("FormItem",{attrs:{label:"订单状态："}},[s("Select",{staticClass:"mr30",staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.userSearchs},model:{value:t.orderData.status,callback:function(e){t.$set(t.orderData,"status",e)},expression:"orderData.status"}},[s("Option",{attrs:{value:""}},[t._v("全部")]),s("Option",{attrs:{value:"0"}},[t._v("未支付")]),s("Option",{attrs:{value:"1"}},[t._v("未发货")]),s("Option",{attrs:{value:"2"}},[t._v("待收货")]),s("Option",{attrs:{value:"3"}},[t._v("待评价")]),s("Option",{attrs:{value:"4"}},[t._v("交易完成")]),s("Option",{attrs:{value:"-2"}},[t._v("已退款")]),s("Option",{attrs:{value:"-4"}},[t._v("已删除")])],1)],1),s("FormItem",{attrs:{label:"支付方式："}},[s("Select",{staticClass:"mr30",staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"全部"},on:{"on-change":t.userSearchs},model:{value:t.orderData.pay_type,callback:function(e){t.$set(t.orderData,"pay_type",e)},expression:"orderData.pay_type"}},t._l(t.payList,(function(e){return s("Option",{key:e.id,attrs:{value:e.val}},[t._v(t._s(e.label))])})),1)],1),s("FormItem",{attrs:{label:"订单搜索："}},[s("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入","element-id":"name",clearable:"",maxlength:"20"},model:{value:t.orderData.real_name,callback:function(e){t.$set(t.orderData,"real_name",e)},expression:"orderData.real_name"}},[s("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend","default-label":"全部"},slot:"prepend",model:{value:t.orderData.field_key,callback:function(e){t.$set(t.orderData,"field_key",e)},expression:"orderData.field_key"}},[s("Option",{attrs:{value:"all"}},[t._v("全部")]),s("Option",{attrs:{value:"order_id"}},[t._v("订单号")]),s("Option",{attrs:{value:"uid"}},[t._v("用户UID")]),s("Option",{attrs:{value:"real_name"}},[t._v("用户姓名")]),s("Option",{attrs:{value:"user_phone"}},[t._v("用户电话")]),s("Option",{attrs:{value:"title"}},[t._v("商品名称")]),s("Option",{attrs:{value:"total_num"}},[t._v("商品件数")])],1)],1)],1),s("Button",{staticClass:"ml20",attrs:{type:"primary"},on:{click:t.userSearchs}},[t._v("查询")]),s("Button",{staticClass:"ml20",on:{click:t.reset}},[t._v("重置")])],1)])],1),s("Card",{staticClass:"mt15",attrs:{bordered:!1,"dis-hover":""}},[s("div",{staticClass:"acea-row row-between"},[s("div",[s("Tooltip",{attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[s("Button",{staticClass:"mr10",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.delAll}},[t._v("批量删除订单")])],1),s("Button",{staticClass:"mr10",attrs:{type:"primary"},on:{click:function(e){t.manualModal=!0}}},[t._v("手动批量发货")]),s("Tooltip",{attrs:{content:"本页至少选中一项",disabled:!!t.checkUidList.length&&0==t.isAll}},[s("Button",{staticClass:"mr10",attrs:{type:"primary",disabled:!t.checkUidList.length&&0==t.isAll},on:{click:t.printOreder}},[t._v("打印配货单")])],1),s("Dropdown",{staticClass:"mr10",on:{"on-click":t.exports}},[s("Button",{staticStyle:{width:"110px"}},[t._v("\n              "+t._s(t.exportList[t.exportListOn].label)+"\n              "),s("Icon",{attrs:{type:"ios-arrow-down"}})],1),s("DropdownMenu",{attrs:{slot:"list"},slot:"list"},t._l(t.exportList,(function(e,a){return s("DropdownItem",{key:a,staticStyle:{"font-size":"12px !important"},attrs:{name:e.name}},[t._v(t._s(e.label))])})),1)],1)],1),s("div",[s("Button",{staticClass:"mr10",on:{click:t.queuemModal}},[t._v("批量发货记录")]),s("Button",{staticClass:"mr10",on:{click:t.getExpressList}},[t._v("下载物流公司对照表")])],1)]),s("vxe-table",{ref:"xTable",staticClass:"mt25",attrs:{border:"inner",loading:t.loading,"row-id":"id","expand-config":{accordion:!0},"checkbox-config":{reserve:!0},data:t.orderList},on:{"checkbox-all":t.checkboxAll,"checkbox-change":t.checkboxItem}},[s("vxe-column",{attrs:{type:"",width:"0"}}),s("vxe-column",{attrs:{type:"expand",width:"35"},scopedSlots:t._u([{key:"content",fn:function(e){var a=e.row;return[s("div",{staticClass:"tdinfo"},[s("Row",{staticClass:"expand-row"},[s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("商品总价：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.total_price)}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("下单时间：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.add_time)}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("推广人：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.spread_nickname?a.spread_nickname:"无")}})])],1),s("Row",{staticClass:"expand-row"},[s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("用户备注：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.mark?a.mark:"无")}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("商家备注：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(a.remark?a.remark:"无")}})])],1)],1)]}}])}),s("vxe-column",{attrs:{type:"checkbox",width:"100"},scopedSlots:t._u([{key:"header",fn:function(){return[s("div",[s("Dropdown",{attrs:{transfer:""},on:{"on-click":t.allPages},scopedSlots:t._u([{key:"list",fn:function(){return[s("DropdownMenu",[s("DropdownItem",{attrs:{name:"0"}},[t._v("当前页")]),s("DropdownItem",{attrs:{name:"1"}},[t._v("所有页")])],1)]},proxy:!0}])},[s("a",{staticClass:"acea-row row-middle",attrs:{href:"javascript:void(0)"}},[s("span",[t._v("全选("+t._s(1==t.isAll?t.total-t.checkUidList.length:t.checkUidList.length)+")")]),s("Icon",{attrs:{type:"ios-arrow-down"}})],1)])],1)]},proxy:!0}])}),s("vxe-column",{attrs:{field:"order_id",title:"订单号","min-width":"190"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[1===a.is_del&&null==a.delete_time?s("Tooltip",{attrs:{theme:"dark","max-width":"300",delay:600,content:"用户已删除"}},[s("span",{staticStyle:{color:"#ed4014",display:"block"}},[t._v(t._s(a.order_id))])]):s("span",{staticStyle:{color:"#2D8cF0",display:"block",cursor:"pointer"},on:{click:function(e){return t.changeMenu(a,"2")}}},[t._v(t._s(a.order_id))])]}}])}),s("vxe-column",{attrs:{field:"info",title:"商品信息","min-width":"330"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[s("Tooltip",{attrs:{transfer:!0,theme:"dark","max-width":"300",delay:600}},[t._l(a._info,(function(e,a){return s("div",{key:a,staticClass:"tabBox"},[s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.image:e.cart_info.productInfo.image,expression:"\n\t\t\t\t\t        val.cart_info.productInfo.attrInfo\n\t\t\t\t\t          ? val.cart_info.productInfo.attrInfo.image\n\t\t\t\t\t          : val.cart_info.productInfo.image\n\t\t\t\t\t      "}]})]),s("span",{staticClass:"tabBox_tit line1"},[e.cart_info.is_gift?s("span",{staticClass:"font-color-red"},[t._v("赠品")]):t._e(),t._v("\n\t\t\t\t\t    "+t._s(e.cart_info.productInfo.store_name+" | ")+"\n\t\t\t\t\t    "+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:"")+"\n\t\t\t\t\t  ")])])})),s("div",{attrs:{slot:"content"},slot:"content"},t._l(a._info,(function(e,a){return s("div",{key:a},[e.cart_info.is_gift?s("p",{staticClass:"font-color-red"},[t._v("赠品")]):t._e(),s("p",[t._v(t._s(e.cart_info.productInfo.store_name))]),s("p",[t._v(" "+t._s(e.cart_info.productInfo.attrInfo?e.cart_info.productInfo.attrInfo.suk:""))]),s("p",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.cart_info.sum_price+" x "+e.cart_info.cart_num))])])})),0)],2)]}}])}),s("vxe-column",{attrs:{field:"nickname",title:"用户信息","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[s("a",{on:{click:function(e){return t.showUserInfo(a)}}},[t._v(t._s(a.nickname))]),null!=a.delete_time?s("span",{staticStyle:{color:"#ed4014"}},[t._v(" (已注销)")]):t._e()]}}])}),s("vxe-column",{attrs:{field:"pink_name",title:"订单类型","min-width":"110"}}),s("vxe-column",{attrs:{field:"pay_price",title:"实际支付","min-width":"90"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[s("div",[t._v(t._s(a.paid>0?a.pay_price:0))])]}}])}),s("vxe-column",{attrs:{field:"pay_type_name",title:"支付方式","min-width":"110"}}),s("vxe-column",{attrs:{field:"_pay_time",title:"支付时间","min-width":"130"}}),s("vxe-column",{attrs:{field:"statusName",title:"订单状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[s("Tag",{directives:[{name:"show",rawName:"v-show",value:3==a.status,expression:"row.status == 3"}],attrs:{color:"default",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),s("Tag",{directives:[{name:"show",rawName:"v-show",value:1==a.status||2==a.status,expression:"row.status == 1 || row.status == 2"}],attrs:{color:"orange",size:"medium"},domProps:{innerHTML:t._s(a.status_name.status_name)}}),s("Tag",{directives:[{name:"show",rawName:"v-show",value:0==a.status,expression:"row.status == 0"}],attrs:{color:"red",size:"medium"},domProps:{innerHTML:t._s(a.status_name.status_name)}}),s("Tag",{directives:[{name:"show",rawName:"v-show",value:4==a.status,expression:"row.status == 4"}],attrs:{color:"red",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),s("Tag",{directives:[{name:"show",rawName:"v-show",value:5==a.status,expression:"row.status == 5"}],attrs:{color:"red",size:"medium"}},[t._v(t._s(a.status_name.status_name))]),!a.is_all_refund&&a.refund.length?s("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("部分退款中")]):t._e(),a.is_all_refund&&a.refund.length&&6!=a.refund_type?s("Tag",{attrs:{color:"orange",size:"medium"}},[t._v("退款中")]):t._e(),a.status_name.pics?s("div",{staticClass:"pictrue-box",attrs:{size:"medium"}},t._l(a.status_name.pics||[],(function(t,e){return s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],key:e},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"item"}],staticClass:"pictrue mr10",attrs:{src:t}})])})),0):t._e()]}}])}),s("vxe-column",{attrs:{field:"pay_type",title:"支付状态","min-width":"90"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[s("div",[t._v(t._s(1==a.paid?"已支付":"未支付"))])]}}])}),s("vxe-column",{attrs:{field:"action",title:"操作",align:"center",width:"130",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[2!==a._status&&8!==a._status&&4!==a.status||1!==a.shipping_type||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time?t._e():s("a",{on:{click:function(e){return t.sendOrder(a)}}},[t._v("发送货")]),2!==a._status&&8!==a._status&&4!==a.status||1!==a.shipping_type||null!==a.pinkStatus&&2!==a.pinkStatus||null!=a.delete_time?t._e():s("Divider",{attrs:{type:"vertical"}}),s("a",{on:{click:function(e){return t.changeMenu(a,"2")}}},[t._v("详情")])]}}])})],1),s("div",{staticClass:"acea-row row-right mt15"},[s("Page",{attrs:{total:t.total,current:t.orderData.page,"show-elevator":"","show-total":"","page-size":t.orderData.limit},on:{"on-change":t.pageChange}})],1)],1),s("edit-from",{ref:"edits",attrs:{FromData:t.FromData},on:{submitFail:t.submitFail}}),s("details-from",{ref:"detailss",attrs:{orderDatalist:t.orderDatalist,orderId:t.orderId,"row-active":t.rowActive,formType:1}}),s("user-details",{ref:"userDetails",attrs:{fromType:"order"}}),s("order-remark",{ref:"remarks",attrs:{orderId:t.orderId},on:{submitFail:t.submitFail}}),s("order-record",{ref:"record"}),s("order-send",{ref:"send",attrs:{orderId:t.orderId},on:{submitFail:t.submitFail}}),s("Modal",{attrs:{title:"手动批量发货","class-name":"vertical-center-modal"},on:{"on-ok":t.manualModalOk,"on-cancel":t.manualModalCancel},model:{value:t.manualModal,callback:function(e){t.manualModal=e},expression:"manualModal"}},[s("Row",{attrs:{type:"flex"}},[s("Col",{attrs:{span:"4"}},[s("div",{staticStyle:{"line-height":"32px","text-align":"right"}},[t._v("文件：")])]),s("Col",{attrs:{span:"20"}},[s("Upload",{ref:"upload",attrs:{action:t.uploadAction,headers:t.uploadHeaders,accept:".xlsx,.xls",format:["xlsx","xls"],disabled:!!t.fileList.length,"on-success":t.uploadSuccess,"on-remove":t.removeFile}},[s("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("上传文件")])],1)],1)],1)],1),s("Modal",{staticClass:"paymentFooter",attrs:{title:"订单核销",scrollable:"",width:"400","class-name":"vertical-center-modal"},model:{value:t.modals2,callback:function(e){t.modals2=e},expression:"modals2"}},[s("Form",{ref:"writeOffFrom",staticClass:"tabform",attrs:{model:t.writeOffFrom,rules:t.writeOffRules,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[s("FormItem",{attrs:{prop:"code","label-for":"code"}},[s("Input",{staticStyle:{width:"100%"},attrs:{search:"","enter-button":"验证",type:"text",placeholder:"请输入12位核销码",number:""},on:{"on-search":function(e){return t.search("writeOffFrom")}},model:{value:t.writeOffFrom.code,callback:function(e){t.$set(t.writeOffFrom,"code",t._n(e))},expression:"writeOffFrom.code"}})],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("Button",{attrs:{type:"primary"},on:{click:t.ok}},[t._v("立即核销")]),s("Button",{on:{click:function(e){return t.del("writeOffFrom")}}},[t._v("取消")])],1)],1),s("auto-send",{ref:"sends",attrs:{selectArr:t.checkUidList,isAll:t.isAll}}),s("queue-list",{ref:"queue"})],1)},i=[],r=s("a34a"),n=s.n(r),o=s("2f62"),l=s("31b4"),c=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,width:"1000"},on:{"on-visible-change":t.changeModal},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?s("Form",{ref:"formItem",attrs:{model:t.formItem,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[s("FormItem",{attrs:{label:"选择类型："}},[s("RadioGroup",{on:{"on-change":t.changeRadio},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[t.productType?t._e():s("Radio",{attrs:{label:"1"}},[t._v("发货")]),s("Radio",{attrs:{label:"3"}},[t._v("无需配送")])],1)],1),s("div",[1==t.formItem.type?s("FormItem",{attrs:{label:"快递公司：",required:""}},[s("Select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":t.expressChange},model:{value:t.formItem.delivery_name,callback:function(e){t.$set(t.formItem,"delivery_name",e)},expression:"formItem.delivery_name"}},t._l(t.express,(function(e,a){return s("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1):t._e(),"1"===t.formItem.express_record_type&&1==t.formItem.type?s("FormItem",{attrs:{label:"快递单号：",required:""}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:t.formItem.delivery_id,callback:function(e){t.$set(t.formItem,"delivery_id",e)},expression:"formItem.delivery_id"}}),"顺丰速运"==t.formItem.delivery_name?s("div",{staticClass:"trips"},[s("p",[t._v("顺丰请输入单号 :收件人或寄件人手机号后四位，")]),s("p",[t._v("例如：SF000000000000:3941")])]):t._e()],1):t._e(),"2"===t.formItem.express_record_type&&"1"===t.formItem.type?[s("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单："}},[s("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择电子面单"},on:{"on-change":t.expressTempChange},model:{value:t.formItem.express_temp_id,callback:function(e){t.$set(t.formItem,"express_temp_id",e)},expression:"formItem.express_temp_id"}},t._l(t.expressTemp,(function(e,a){return s("Option",{key:a,attrs:{value:e.temp_id}},[t._v(t._s(e.title))])})),1),t.formItem.express_temp_id?s("Button",{attrs:{type:"text"},on:{click:t.preview}},[t._v("预览")]):t._e()],1),s("FormItem",{attrs:{label:"寄件人姓名："}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.to_name,callback:function(e){t.$set(t.formItem,"to_name",e)},expression:"formItem.to_name"}})],1),s("FormItem",{attrs:{label:"寄件人电话："}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.to_tel,callback:function(e){t.$set(t.formItem,"to_tel",e)},expression:"formItem.to_tel"}})],1),s("FormItem",{attrs:{label:"寄件人地址："}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.to_addr,callback:function(e){t.$set(t.formItem,"to_addr",e)},expression:"formItem.to_addr"}})],1)]:t._e()],2),s("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.formItem.type,expression:"formItem.type === '2'"}]},[s("FormItem",{attrs:{label:"送货人：",required:""}},[s("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择送货人"},on:{"on-change":t.shDeliveryChange},model:{value:t.formItem.sh_delivery,callback:function(e){t.$set(t.formItem,"sh_delivery",e)},expression:"formItem.sh_delivery"}},t._l(t.deliveryList,(function(e,a){return s("Option",{key:a,attrs:{value:e.id}},[t._v(t._s(e.wx_name)+"（"+t._s(e.phone)+"）")])})),1)],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.formItem.type,expression:"formItem.type === '3'"}]},[s("FormItem",{attrs:{label:"备注："}},[s("Input",{staticStyle:{width:"80%"},attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.fictitious_content,callback:function(e){t.$set(t.formItem,"fictitious_content",e)},expression:"formItem.fictitious_content"}})],1)],1),t.splitOrder>1&&"3"!==t.formItem.type?s("div",[s("FormItem",{attrs:{label:"分单发货："}},[s("i-switch",{attrs:{size:"large",disabled:8===t.orderStatus},on:{"on-change":t.changeSplitStatus},model:{value:t.splitSwitch,callback:function(e){t.splitSwitch=e},expression:"splitSwitch"}},[s("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),s("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),s("div",{staticClass:"trips"},[s("p",[t._v("\n\t\t\t\t\t\t\t可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n\t\t\t\t\t\t")])]),t.splitSwitch&&t.manyFormValidate.length?s("Table",{attrs:{data:t.manyFormValidate,columns:t.header,border:""},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var a=e.row;e.index;return[s("div",{staticClass:"product-data"},[s("img",{staticClass:"image",attrs:{src:a.cart_info.productInfo.image}}),s("div",{staticClass:"line2"},[t._v("\n\t\t\t\t\t\t\t\t\t"+t._s(a.cart_info.productInfo.store_name)+"\n\t\t\t\t\t\t\t\t")])])]}},{key:"value",fn:function(e){var a=e.row;e.index;return[s("div",[t._v(t._s(a.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var a=e.row;e.index;return[s("div",[t._v("\n\t\t\t\t\t\t\t\t"+t._s(a.cart_info.productInfo.attrInfo?a.cart_info.productInfo.attrInfo.price:a.cart_info.productInfo.price)+"\n\t\t\t\t\t\t\t")])]}},{key:"price",fn:function(e){var a=e.row;e.index;return[s("div",[s("div",[t._v(t._s(a.cart_info.truePrice))])])]}}],null,!1,2826354387)}):t._e()],1)],1):t._e()],1):t._e(),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("Button",{on:{click:t.cancel}},[t._v("取消")]),t.stopSubmit?s("Button",{attrs:{type:"warning"}},[t._v("存在售后待处理订单")]):s("Button",{attrs:{type:"primary"},on:{click:t.putSend}},[t._v("提交")])],1),s("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:t.temp,expression:"temp"}],ref:"viewer"},[s("img",{staticStyle:{display:"none"},attrs:{src:t.temp.pic}})])],1)},d=[],u=s("f8b7");function m(t,e,s,a,i,r,n){try{var o=t[r](n),l=o.value}catch(c){return void s(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function p(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var r=t.apply(e,s);function n(t){m(r,a,i,n,o,"next",t)}function o(t){m(r,a,i,n,o,"throw",t)}n(void 0)}))}}function f(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function h(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?f(s,!0).forEach((function(e){v(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):f(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function v(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var _={name:"orderSend",props:{orderId:Number},data:function(){var t=this;return{productType:0,orderStatus:0,splitSwitch:!1,formItem:{type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0,manyFormValidate:[],stopSubmit:!1,header:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"image",width:200,align:"center"},{title:"规格",slot:"value",align:"center",minWidth:120},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:100},{title:"商品优惠价",slot:"price",align:"center",minWidth:100},{title:"总数",key:"cart_num",align:"center",minWidth:80},{title:"待发数量",key:"surplus_num",align:"center",width:180,render:function(e,s){return e("div",[e("InputNumber",{props:{min:1,max:s.row.numShow,value:s.row.surplus_num||1},on:{"on-change":function(e){s.row.surplus_num=e||1,t.manyFormValidate[s.index]=s.row,t.selectData.forEach((function(e,a){e.cart_id===s.row.cart_id&&t.selectData.splice(a,1,s.row)}))}}})])}}],selectData:[]}},computed:h({},Object(o["e"])("store/order",["splitOrder"])),methods:{selectOne:function(t){this.selectData=t},changeModal:function(t){t||this.cancel()},changeSplitStatus:function(t){var e=this;t&&Object(u["F"])(this.orderId).then((function(t){var s=t.data;s.forEach((function(t){t.numShow=t.surplus_num})),e.manyFormValidate=s}))},changeRadio:function(t){switch(this.$refs.formItem.resetFields(),t){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="1",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="";break;case"3":this.formItem.fictitious_content="";break;default:break}},changeExpress:function(t){switch(t){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[],this.getList(2);break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.getList(1);break;default:break}},reset:function(){this.formItem={type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""}},getList:function(t){var e=this,s=2===t?1:"";Object(u["h"])(s).then(function(){var t=p(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.express=s.data,e.getSheetInfo();case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},putSend:function(t){var e=this,s={id:this.orderId,datas:this.formItem};if("1"===this.formItem.type&&"2"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("1"===this.formItem.type&&"1"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.delivery_id)return this.$Message.error("快递单号不能为空")}if("2"===this.formItem.type&&""===this.formItem.sh_delivery)return this.$Message.error("送货人不能为空");this.splitSwitch?(s.datas.cart_ids=[],this.selectData.forEach((function(t){s.datas.cart_ids.push({cart_id:t.cart_id,cart_num:t.surplus_num})})),Object(u["G"])(s).then((function(t){e.modals=!1,e.$Message.success(t.msg),e.$emit("submitFail"),e.reset(),e.splitSwitch=!1})).catch((function(t){e.$Message.error(t.msg)}))):Object(u["v"])(s).then(function(){var t=p(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.modals=!1,e.$Message.success(s.msg),e.splitSwitch=!1,e.$emit("submitFail"),e.reset();case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},cancel:function(t){this.modals=!1,this.orderStatus=0,this.splitSwitch=!1,this.selectData=[],this.reset()},expressChange:function(t){var e=this,s=this.express.find((function(e){return e.value===t}));void 0!==s&&(this.formItem.delivery_code=s.code,"2"===this.formItem.express_record_type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(u["q"])({com:this.formItem.delivery_code}).then((function(t){e.expressTemp=t.data,t.data.length||e.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(t){e.$Message.error(t.msg)}))))},getCartInfo:function(){var t=this;Object(u["F"])(this.orderId).then((function(e){var s=e.data;s.forEach((function(t){t.numShow=t.surplus_num})),t.manyFormValidate=s,t.productType=s[0].product_type,3==t.productType&&(t.formItem.type="3",t.formItem.fictitious_content="")}))},getDeliveryList:function(){var t=this;Object(u["p"])().then((function(e){t.deliveryList=e.data.list})).catch((function(e){t.$Message.error(e.msg)}))},getSheetInfo:function(){var t=this;Object(u["t"])().then((function(e){var s=e.data;for(var a in s)s.hasOwnProperty(a)&&(t.formItem[a]=s[a]);t.export_open=void 0===s.export_open||s.export_open,t.export_open||(t.formItem.express_record_type="1"),t.formItem.to_addr=s.to_add})).catch((function(e){t.$Message.error(e.msg)}))},shDeliveryChange:function(t){if(t){var e=this.deliveryList.find((function(e){return e.id===t}));this.formItem.sh_delivery_name=e.wx_name,this.formItem.sh_delivery_id=e.phone,this.formItem.sh_delivery_uid=e.uid}},expressTempChange:function(t){this.temp=this.expressTemp.find((function(e){return t===e.temp_id})),void 0===this.temp&&(this.temp={})},preview:function(){this.$refs.viewer.$viewer.show()}}},g=_,y=(s("40d6"),s("2877")),w=Object(y["a"])(g,c,d,!1,null,"300cac78",null),b=w.exports,k=s("91b4"),x=s("5465"),I=s("7dc5"),C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Drawer",{attrs:{closable:!1,width:"1000","class-name":"order_box",styles:{padding:0}},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"acea-row user-row"},[a("div",{staticClass:"avatar mr15"},[a("img",{attrs:{src:t.psInfo.avatar}})]),a("div",{staticClass:"user-row-text"},[a("div",[a("span",{staticClass:"nickname"},[t._v(t._s(t.psInfo.nickname||"-")+t._s(null!=t.psInfo.delete_time?" (已注销)":""))]),a("i",{staticClass:"iconfont",class:{iconxiaochengxu:"routine"===t.psInfo.user_type,icongongzhonghao:"wechat"===t.psInfo.user_type,iconPC:"pc"===t.psInfo.user_type,iconh5:"h5"===t.psInfo.user_type,iconapp:"app"===t.psInfo.user_type}})]),a("div",{staticClass:"level"},[t.psInfo.is_money_level?a("img",{attrs:{src:s("30a5")}}):t._e(),t.psInfo.level?a("span",{staticClass:"vip"},[t._v("V"+t._s(t.psInfo.level))]):t._e()])]),"order"!==t.fromType&&null==t.psInfo.delete_time?a("div",{staticClass:"user-row-action"},[a("Button",{directives:[{name:"show",rawName:"v-show",value:t.isEdit,expression:"isEdit"}],on:{click:function(e){t.isEdit=!1}}},[t._v("取消")]),a("Button",{directives:[{name:"show",rawName:"v-show",value:t.isEdit,expression:"isEdit"}],attrs:{type:"primary"},on:{click:t.finish}},[t._v("完成")]),a("Button",{directives:[{name:"show",rawName:"v-show",value:!t.isEdit,expression:"!isEdit"}],attrs:{disabled:"info"!==t.activeName,type:"primary"},on:{click:function(e){t.isEdit=!0}}},[t._v("编辑")]),a("Button",{attrs:{type:"success"},on:{click:function(e){return t.changeMenu("2")}}},[t._v("积分余额")]),a("Button",{on:{click:function(e){return t.changeMenu("3")}}},[t._v("赠送会员")])],1):t._e()]),a("div",{staticClass:"acea-row info-row"},t._l(t.detailsData,(function(e,s){return a("div",{key:s,staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[t._v(t._s(e.title))]),a("div",[t._v(t._s(e.value)+t._s(e.key))])])})),0),a("Tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.list,(function(e,s){return a("TabPane",{key:s,attrs:{label:e.label,name:e.val}},["info"===e.val?[a("user-info",{directives:[{name:"show",rawName:"v-show",value:!t.isEdit,expression:"!isEdit"}],attrs:{"ps-info":t.psInfo,workMemberInfo:t.workMemberInfo,workClientInfo:t.workClientInfo}})]:[a("Table",{ref:"table",refInFor:!0,attrs:{columns:t.columns,data:t.userLists,loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},scopedSlots:t._u([{key:"coupon_price",fn:function(e){var s=e.row;return[1==s.coupon_type?a("span",[t._v(t._s(s.coupon_price)+"元")]):t._e(),2==s.coupon_type?a("span",[t._v(t._s(parseFloat(s.coupon_price)/10)+"折（"+t._s(s.coupon_price.toString().split(".")[0])+"%）")]):t._e()]}},{key:"product",fn:function(e){var s=e.row;return[a("div",{staticClass:"product"},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:s.image,expression:"row.image"}]})]),a("div",{staticClass:"title"},[t._v(t._s(s.store_name))])])]}}],null,!0)}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"update:current":function(e){return t.$set(t.userFrom,"page",e)},"on-change":t.pageChange}})],1)]],2)})),1)],1)},D=[],O=s("c24f"),$=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"user-info"},[s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("基本信息")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("用户编号：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.uid))])]),s("div",{staticClass:"item"},[s("div",[t._v("真实姓名：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.real_name||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("手机号码：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.phone||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("生日：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.birthday||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("性别：")]),t.psInfo.sex?s("div",{staticClass:"value"},[t._v(t._s(1==t.psInfo.sex?"男":"女"))]):s("div",{staticClass:"value"},[t._v("保密")])]),s("div",{staticClass:"item"},[s("div",[t._v("身份证号：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.card_id||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("用户地址：")]),s("div",{staticClass:"value"},[t._v(t._s(""+t.psInfo.provincials+t.psInfo.addres||"-"))])])])]),t._m(0),s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("用户概况")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("推广资格：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.spread_open?"启用":"禁用"))])]),s("div",{staticClass:"item"},[s("div",[t._v("用户状态：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.status?"开启":"锁定"))])]),s("div",{staticClass:"item"},[s("div",[t._v("用户等级：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.level))])]),s("div",{staticClass:"item"},[s("div",[t._v("用户标签：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.label_list))])]),s("div",{staticClass:"item"},[s("div",[t._v("用户分组：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.group_name||"无"))])]),s("div",{staticClass:"item"},[s("div",[t._v("推广人：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.spread_uid_nickname||"无"))])]),s("div",{staticClass:"item"},[s("div",[t._v("注册时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t._f("timeFormat")(t.psInfo.add_time)))])]),s("div",{staticClass:"item"},[s("div",[t._v("登录时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t._f("timeFormat")(t.psInfo.last_time)))])])])]),s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("用户备注")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.psInfo.mark||"-"))])])])]),t.workMemberInfo?s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("企业成员信息")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",{staticClass:"avatar"},[s("img",{attrs:{src:t.workMemberInfo.qr_code,alt:""}})])])]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("姓名：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.name||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("职务信息：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.position||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("手机号码：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.mobile||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("性别：")]),s("div",{staticClass:"value"},[t._v(t._s(t._f("gender")(t.workMemberInfo.gender)))])]),s("div",{staticClass:"item"},[s("div",{staticStyle:{width:"40px"}},[t._v("邮箱：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.biz_mail||"-"))])]),s("div",{staticClass:"item",staticStyle:{"margin-right":"30px"}},[s("div",[t._v("地址：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.address||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workMemberInfo.remark||"-"))])])])]):t._e(),t.workClientInfo?s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("企业客户信息")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("姓名：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.name||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("职务信息：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.position||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.remark||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("性别：")]),s("div",{staticClass:"value"},[t._v(t._s(t._f("gender")(t.workClientInfo.gender)))])]),s("div",{staticClass:"item"},[s("div",[t._v("企业主体名称：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.corp_full_name||"-"))])]),s("div",{staticClass:"item"},[s("div",[t._v("企业主体简称：")]),s("div",{staticClass:"value"},[t._v(t._s(t.workClientInfo.corp_name||"-"))])])])]):t._e()])},M=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"section"},[s("div",{staticClass:"section-hd"},[t._v("密码")]),s("div",{staticClass:"section-bd"},[s("div",{staticClass:"item"},[s("div",[t._v("登录密码：")]),s("div",{staticClass:"value"},[t._v("********")])])])])}],S=s("5a0c"),L=s.n(S),F={name:"userInfo",props:{psInfo:Object,workMemberInfo:Object,workClientInfo:Object},filters:{timeFormat:function(t){return t?L()(1e3*t).format("YYYY-MM-DD HH:mm:ss"):"-"},gender:function(t){return 1==t?"男":2==t?"女":"未知"}}},j=F,T=(s("4397"),Object(y["a"])(j,$,M,!1,null,"b17a281c",null)),P=T.exports;function N(t,e,s,a,i,r,n){try{var o=t[r](n),l=o.value}catch(c){return void s(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function A(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var r=t.apply(e,s);function n(t){N(r,a,i,n,o,"next",t)}function o(t){N(r,a,i,n,o,"throw",t)}n(void 0)}))}}var R={name:"userDetails",components:{userInfo:P},props:["levelList","labelList","groupList","fromType"],data:function(){return{theme2:"light",list:[{val:"info",label:"用户信息"},{val:"order",label:"消费记录"},{val:"integral",label:"积分明细"},{val:"sign",label:"签到记录"},{val:"coupon",label:"持有优惠券"},{val:"balance_change",label:"余额变动"},{val:"spread",label:"好友关系"},{val:"visit",label:"浏览足迹"},{val:"recom",label:"推荐人变更记录"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"info",page:1,limit:12},total:0,columns:[],userLists:[],psInfo:{},workMemberInfo:{},workClientInfo:{},activeName:"info",isEdit:!1,groupOptions:[],labelOptions:[]}},watch:{activeName:function(t){this.userFrom.page=1,"info"!=t&&(this.isEdit=!1,"visit"==t?this.getVisitList():"recom"==t?this.getSpreadList():this.changeType(t))},modals:function(t){t&&(this.isEdit=!1)}},created:function(){},methods:{changeMenu:function(t){if("99"===t)return this.getDetails(this.userId),this.$parent.getList(),void(this.isEdit=!1);this.$parent.changeMenu(this.psInfo,t)},finish:function(){this.$refs.userForm[0].detailsPut()},getSpreadList:function(){var t=this;this.loading=!0,Object(O["c"])({id:this.userId,datas:{page:this.userFrom.page,limit:this.userFrom.limit}}).then(function(){var e=A(n.a.mark((function e(s){var a;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:200===s.status?(a=s.data,t.userLists=a.list,t.total=a.count,t.columns=[{title:"推荐人ID",key:"spread_uid",minWidth:120},{title:"推荐人",key:"nickname",minWidth:120,render:function(t,e){return t("div",[t("img",{style:{borderRadius:"50%",marginRight:"10px",verticalAlign:"middle"},attrs:{with:38,height:38},directives:[{name:"lazy",value:e.row.avatar},{name:"viewer"}]}),t("span",{style:{verticalAlign:"middle"}},e.row.nickname)])}},{title:"变更方式",key:"type",minWidth:120},{title:"变更时间",key:"spread_time",minWidth:120}],t.loading=!1):(t.loading=!1,t.$Message.error(s.msg));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},getVisitList:function(){var t=this;this.loading=!0,Object(O["f"])({id:this.userId,datas:{page:this.userFrom.page,limit:this.userFrom.limit}}).then(function(){var e=A(n.a.mark((function e(s){var a;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:200===s.status?(a=s.data,t.userLists=a.list,t.total=a.count,t.columns=[{title:"商品信息",slot:"product",minWidth:400},{title:"价格",key:"product_price",minWidth:120,render:function(t,e){return t("div","¥".concat(e.row.product_price))}},{title:"浏览时间",key:"add_time",minWidth:120}],t.loading=!1):(t.loading=!1,t.$Message.error(s.msg));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},getDetails:function(t){var e=this;this.userId=t,this.spinShow=!0,Object(O["a"])(t).then(function(){var t=A(n.a.mark((function t(s){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===s.status?(a=s.data,e.detailsData=a.headerList,"order"!==e.fromType&&(i=e.groupList.find((function(t){return t.id==a.ps_info.group_id})),i&&(a.ps_info.group_name=i.group_name)),e.psInfo=a.ps_info,e.workMemberInfo=a.workMemberInfo,e.workClientInfo=a.workClientInfo,e.spinShow=!1):(e.spinShow=!1,e.$Message.error(s.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},pageChange:function(t){switch(this.userFrom.page=t,this.activeName){case"visit":this.getVisitList();break;case"recom":this.getSpreadList();break;default:this.changeType(this.userFrom.type);break}},changeType:function(t){var e=this;this.loading=!0,this.userFrom.type=t,this.activeName=t;var s={id:this.userId,datas:this.userFrom};Object(O["b"])(s).then(function(){var t=A(n.a.mark((function t(s){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(200!==s.status){t.next=21;break}a=s.data,e.userLists=a.list,e.total=a.count,t.t0=e.userFrom.type,t.next="order"===t.t0?7:"integral"===t.t0?9:"sign"===t.t0?11:"coupon"===t.t0?13:"balance_change"===t.t0?15:17;break;case 7:return e.columns=[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"商品总价",key:"total_price",minWidth:110},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],t.abrupt("break",18);case 9:return e.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"number",minWidth:120},{title:"变化前积分",key:"balance",minWidth:120},{title:"日期",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",18);case 11:return e.columns=[{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",18);case 13:return e.columns=[{title:"优惠券名称",key:"coupon_title",minWidth:120},{title:"面值",slot:"coupon_price",minWidth:120},{title:"有效期(天)",key:"coupon_time",minWidth:120},{title:"兑换时间",key:"_add_time",minWidth:120}],t.abrupt("break",18);case 15:return e.columns=[{title:"动作",key:"title",minWidth:120},{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",18);case 17:e.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"type",minWidth:120},{title:"加入时间",key:"add_time",minWidth:120}];case 18:e.loading=!1,t.next=23;break;case 21:e.loading=!1,e.$Message.error(s.msg);case 23:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))}}},E=R,W=(s("2f57"),s("9e00"),Object(y["a"])(E,C,D,!1,null,"2a09842f",null)),z=W.exports,V=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,"class-name":"vertical-center-modal"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[s("Form",{ref:"formItem",attrs:{model:t.formItem,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[s("FormItem",{attrs:{label:"选择类型："}},[s("RadioGroup",{on:{"on-change":t.changeRadio},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[s("Radio",{attrs:{label:"1"}},[t._v("打印电子面单")]),s("Radio",{attrs:{label:"2"}},[t._v("送货")]),s("Radio",{attrs:{label:"3"}},[t._v("虚拟")])],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.formItem.type,expression:"formItem.type === '1'"}]},[s("FormItem",{attrs:{label:"快递公司：",required:""}},[s("Select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":t.expressChange},model:{value:t.formItem.delivery_name,callback:function(e){t.$set(t.formItem,"delivery_name",e)},expression:"formItem.delivery_name"}},t._l(t.express,(function(e,a){return s("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1),"1"===t.formItem.type?[s("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单：",required:""}},[s("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择电子面单"},on:{"on-change":t.expressTempChange},model:{value:t.formItem.express_temp_id,callback:function(e){t.$set(t.formItem,"express_temp_id",e)},expression:"formItem.express_temp_id"}},t._l(t.expressTemp,(function(e,a){return s("Option",{key:a,attrs:{value:e.temp_id}},[t._v(t._s(e.title))])})),1),t.formItem.express_temp_id?s("Button",{attrs:{type:"text"},on:{click:t.preview}},[t._v("预览")]):t._e()],1),s("FormItem",{attrs:{label:"寄件人姓名：",required:""}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.to_name,callback:function(e){t.$set(t.formItem,"to_name",e)},expression:"formItem.to_name"}})],1),s("FormItem",{attrs:{label:"寄件人电话：",required:""}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.to_tel,callback:function(e){t.$set(t.formItem,"to_tel",e)},expression:"formItem.to_tel"}})],1),s("FormItem",{attrs:{label:"寄件人地址：",required:""}},[s("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.to_addr,callback:function(e){t.$set(t.formItem,"to_addr",e)},expression:"formItem.to_addr"}})],1)]:t._e()],2),s("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.formItem.type,expression:"formItem.type === '2'"}]},[s("FormItem",{attrs:{label:"送货人：",required:""}},[s("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择送货人"},on:{"on-change":t.shDeliveryChange},model:{value:t.formItem.sh_delivery,callback:function(e){t.$set(t.formItem,"sh_delivery",e)},expression:"formItem.sh_delivery"}},t._l(t.deliveryList,(function(e,a){return s("Option",{key:a,attrs:{value:e.id}},[t._v(t._s(e.wx_name)+"（"+t._s(e.phone)+"）")])})),1)],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.formItem.type,expression:"formItem.type === '3'"}]},[s("FormItem",{attrs:{label:"备注："}},[s("Input",{staticStyle:{width:"80%"},attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.fictitious_content,callback:function(e){t.$set(t.formItem,"fictitious_content",e)},expression:"formItem.fictitious_content"}})],1)],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("Button",{on:{click:t.cancel}},[t._v("取消")]),s("Button",{attrs:{type:"primary"},on:{click:t.putSend}},[t._v("提交")])],1),s("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:t.temp,expression:"temp"}],ref:"viewer"},[s("img",{staticStyle:{display:"none"},attrs:{src:t.temp.pic}})])],1)},B=[];function U(t,e,s,a,i,r,n){try{var o=t[r](n),l=o.value}catch(c){return void s(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function q(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var r=t.apply(e,s);function n(t){U(r,a,i,n,o,"next",t)}function o(t){U(r,a,i,n,o,"throw",t)}n(void 0)}))}}var Y={name:"orderSend",props:{isAll:{type:Number|String,default:0},ids:{type:Array,default:function(){return[]}},where:{type:Object,default:function(){return{}}},selectArr:{type:Array,default:function(){return[]}}},data:function(){return{formItem:{type:"1",express_record_type:"2",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0}},watch:{"formItem.express_temp_id":function(t){}},methods:{changeRadio:function(t){switch(this.$refs.formItem.resetFields(),t){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="2",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="",this.formItem.express_record_type="1";break;case"3":this.formItem.fictitious_content="",this.formItem.express_record_type="1";break}},changeExpress:function(t){switch(t){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[];break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="";break;default:break}},reset:function(){this.formItem={type:"1",express_record_type:"2",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""}},getList:function(){var t=this;Object(u["h"])(1).then(function(){var e=q(n.a.mark((function e(s){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.express=s.data,t.getSheetInfo();case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},putSend:function(t){var e=this,s=Object.assign(this.formItem);if(1==this.isAll?(s.all=1,s.ids=this.selectArr,s.where=this.where):(s.all=0,s.ids=this.selectArr),"1"===this.formItem.type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("2"===this.formItem.type&&(this.formItem.express_temp_id&&(this.formItem.express_temp_id=""),""===this.formItem.sh_delivery))return this.$Message.error("送货人不能为空");Object(u["u"])(s).then(function(){var t=q(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.modals=!1,e.$Message.success(s.msg),e.reset();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg),e.modals=!1}))},cancel:function(t){this.modals=!1,this.reset()},expressChange:function(t){var e=this,s=this.express.find((function(e){return e.value===t}));s&&(this.formItem.delivery_code=s.code,"1"===this.formItem.type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(u["q"])({com:this.formItem.delivery_code}).then((function(t){e.expressTemp=t.data,t.data.length||e.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(t){e.$Message.error(t.msg)}))))},getDeliveryList:function(){var t=this;Object(u["p"])().then((function(e){t.deliveryList=e.data.list})).catch((function(e){t.$Message.error(e.msg)}))},getSheetInfo:function(){var t=this;Object(u["t"])().then((function(e){var s=e.data;for(var a in s)s.hasOwnProperty(a)&&"express_temp_id"!==a&&(t.formItem[a]=s[a]);t.export_open=void 0===s.export_open||s.export_open,t.export_open||(t.formItem.express_record_type="1"),t.formItem.to_addr=s.to_add})).catch((function(e){t.$Message.error(e.msg)}))},shDeliveryChange:function(t){var e=this.deliveryList.find((function(e){return e.id===t}));this.formItem.sh_delivery_name=e.wx_name,this.formItem.sh_delivery_id=e.phone,this.formItem.sh_delivery_uid=e.uid},expressTempChange:function(t){this.temp=this.expressTemp.find((function(e){return t===e.temp_id}))},preview:function(){this.$refs.viewer.$viewer.show()}}},Q=Y,H=(s("b309"),Object(y["a"])(Q,V,B,!1,null,"7eb0952e",null)),J=H.exports,G=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Modal",{attrs:{title:"任务列表",width:"1000","footer-hide":"","class-name":"vertical-center-modal"},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[t.modal?s("Card",{attrs:{bordered:!1,"dis-hover":""}},[s("Form",{ref:"formValidate",staticClass:"tabform",attrs:{inline:"",model:t.formValidate,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[s("FormItem",{attrs:{label:"操作时间："}},[s("DatePicker",{staticStyle:{width:"200px"},attrs:{editable:!1,value:t.timeVal,format:"yyyy/MM/dd",type:"datetimerange",placement:"bottom-start",placeholder:"自定义时间",options:t.options},on:{"on-change":t.onchangeTime}})],1),s("FormItem",{attrs:{label:"类型："}},[s("Select",{staticStyle:{width:"200px"},attrs:{clearable:""},on:{"on-change":t.typeSearchs},model:{value:t.formValidate.type,callback:function(e){t.$set(t.formValidate,"type",e)},expression:"formValidate.type"}},t._l(t.typeList,(function(e){return s("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1),s("FormItem",{attrs:{label:"状态："}},[s("Select",{staticStyle:{width:"200px"},attrs:{clearable:""},on:{"on-change":t.statusSearchs},model:{value:t.formValidate.status,callback:function(e){t.$set(t.formValidate,"status",e)},expression:"formValidate.status"}},t._l(t.statusList,(function(e){return s("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),s("Table",{attrs:{height:"500",columns:t.columns1,data:t.data1,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(e){var a=e.row;e.index;return[a.is_show_log?[s("a",{on:{click:function(e){return t.deliveryLook(a)}}},[t._v("查看")]),s("Divider",{attrs:{type:"vertical"}})]:t._e(),[s("Dropdown",{on:{"on-click":function(e){return t.changeMenu(a,e)}}},[s("a",[t._v("更多"),s("Icon",{attrs:{type:"ios-arrow-down"}})],1),s("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[[7,8,9,10].includes(a.type)?s("DropdownItem",{attrs:{name:"1"}},[t._v("下载")]):t._e(),1==a.status?s("DropdownItem",{attrs:{name:"2"}},[t._v("重新执行")]):t._e(),a.is_stop_button?s("DropdownItem",{attrs:{name:"3"}},[t._v("停止任务")]):t._e(),s("DropdownItem",{attrs:{name:"4"}},[t._v("清除任务")])],1)],1)]]}}],null,!1,2501981875)}),s("div",{staticClass:"acea-row row-right page"},[s("Page",{attrs:{total:t.page1.total,current:t.page1.pageNum,"show-elevator":"","show-total":"","page-size":t.page1.pageSize,"show-sizer":""},on:{"on-change":t.pageChange,"on-page-size-change":t.limitChange}})],1)],1):t._e(),s("Modal",{attrs:{width:"900","footer-hide":""},model:{value:t.modal1,callback:function(e){t.modal1=e},expression:"modal1"}},[s("Table",{staticClass:"mt25",attrs:{height:"500",columns:t.columns4,data:t.data2,loading:t.loading2}}),s("div",{staticClass:"acea-row row-right page"},[s("Page",{attrs:{total:t.page2.total,current:t.page2.pageNum,"show-elevator":"","show-total":"","page-size":t.page2.pageSize,"show-sizer":""},on:{"on-change":t.pageChange2,"on-page-size-change":t.limitChange2}})],1)],1)],1)},K=[],X=s("2e83");function Z(t,e,s,a,i,r,n){try{var o=t[r](n),l=o.value}catch(c){return void s(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function tt(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var r=t.apply(e,s);function n(t){Z(r,a,i,n,o,"next",t)}function o(t){Z(r,a,i,n,o,"throw",t)}n(void 0)}))}}function et(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function st(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?et(s,!0).forEach((function(e){at(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):et(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function at(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var it={data:function(){return{modal:!1,columns1:[{title:"ID",key:"id"},{title:"操作时间",key:"add_time"},{title:"发货单数",key:"total_num"},{title:"成功发货单数",key:"success_num"},{title:"发货类型",key:"title"},{title:"状态",key:"status_cn"},{title:"操作",slot:"action",width:150,align:"center"}],data1:[],page1:{total:0,pageNum:1,pageSize:10},formValidate:{type:"",status:"",data:""},options:{shortcuts:[{text:"今天",value:function(){var t=new Date,e=new Date;return e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())),[e,t]}},{text:"昨天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-1))),[e,t]}},{text:"最近7天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-6))),[e,t]}},{text:"最近30天",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),[e,t]}},{text:"上月",value:function(){var t=new Date,e=new Date,s=new Date(e.getFullYear(),e.getMonth(),0).getDate();return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,1))),t.setTime(t.setTime(new Date((new Date).getFullYear(),(new Date).getMonth()-1,s))),[e,t]}},{text:"本月",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),1))),[e,t]}},{text:"本年",value:function(){var t=new Date,e=new Date;return e.setTime(e.setTime(new Date((new Date).getFullYear(),0,1))),[e,t]}}]},timeVal:[],typeList:[{label:"批量删除订单",value:"6"},{label:"批量手动发货",value:"7"},{label:"批量打印电子面单",value:"8"},{label:"批量配送",value:"9"},{label:"批量虚拟发货",value:"10"}],statusList:[{label:"未处理",value:"0"},{label:"处理中",value:"1"},{label:"已完成",value:"2"},{label:"处理失败",value:"3"}],columns2:[{title:"订单ID",key:"order_id"},{title:"物流公司",key:"delivery_name"},{title:"物流单号",key:"delivery_id"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns3:[{title:"订单ID",key:"order_id"},{title:"备注",key:"fictitious_content"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns5:[{title:"订单ID",key:"order_id"},{title:"配送员",key:"delivery_name"},{title:"配送员电话",key:"delivery_id"},{title:"处理状态",key:"status_cn"},{title:"异常原因",key:"error"}],columns4:[],data2:[],page2:{total:0,pageNum:1,pageSize:12},modal1:!1,deliveryLog:null,deliveryLogId:0,deliveryLogType:"",loading:!1,loading2:!1}},computed:st({},Object(o["e"])("admin/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:96},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getQueue()},methods:{getQueue:function(){var t=this,e={page:this.page1.pageNum,limit:this.page1.pageSize};this.formValidate.status&&(e.status=this.formValidate.status),this.formValidate.type&&(e.type=this.formValidate.type),this.formValidate.data&&(e.data=this.formValidate.data),this.loading=!0,Object(u["C"])(e).then((function(e){t.loading=!1,t.data1=e.data.list,t.page1.total=e.data.count})).catch((function(e){t.loading=!1}))},pageChange:function(t){this.page1.pageNum=t,this.getQueue()},pageChange2:function(t){this.page2.pageNum=t,this.getDeliveryLog()},limitChange:function(t){this.page1.pageSize=t,this.getQueue()},limitChange2:function(t){this.page2.pageSize=t,this.getDeliveryLog()},onchangeTime:function(t){this.timeVal=t,this.formValidate.data=this.timeVal[0]?this.timeVal.join("-"):"",this.page1.pageNum=1,this.getQueue()},typeSearchs:function(){this.page1.pageNum=1,this.getQueue()},statusSearchs:function(){this.page1.pageNum=1,this.getQueue()},getDeliveryLog:function(){var t=this;this.loading2=!0,Object(u["b"])(this.deliveryLogId,this.deliveryLogType,{page:this.page2.pageNum,limit:this.page2.pageSize}).then((function(e){t.loading2=!1,t.data2=e.data.list,t.page2.total=e.data.count})).catch((function(e){t.loading2=!1}))},deliveryLook:function(t){switch(this.modal1=!0,this.deliveryLogId=t.id,this.deliveryLogType=t.cache_type,this.deliveryLog=t,t.type){case 7:case 8:this.columns4=this.columns2;break;case 9:this.columns4=this.columns5;break;case 10:this.columns4=this.columns3;break}this.getDeliveryLog()},changeMenu:function(t,e){var s=this;switch(e){case"1":this.exports(t.id,t.type,t.cache_type);break;case"2":this.queueAgain(t.id,t.type);break;case"3":this.$Modal.confirm({title:"谨慎操作",content:"<p>确认停止该任务？</p>",onOk:function(){s.stopQueue(t.id)}});break;case"4":this.queueDel(t.id,t.type);break}},exports:function(){var t=tt(n.a.mark((function t(e,s,a){var i,r,o,l,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=[],r=[],o=[],l="",t.next=3,this.getExcelData(e,s,a);case 3:c=t.sent,l||(l=c.filename),r.length||(r=c.filekey),i.length||(i=c.header),o=c.export,Object(X["a"])(i,r,l,o);case 9:case"end":return t.stop()}}),t,this)})));function e(e,s,a){return t.apply(this,arguments)}return e}(),getExcelData:function(t,e,s){return new Promise((function(a,i){Object(u["a"])(t,e,s).then((function(t){return a(t.data)}))}))},queueAgain:function(t,e){var s=this;Object(u["A"])(t,e).then((function(t){s.$Message.success(t.msg),s.getQueue()})).catch((function(t){s.$Message.error(t.msg)}))},queueDel:function(t,e){var s=this;Object(u["B"])(t,e).then((function(t){s.$Message.success(t.msg),s.getQueue()})).catch((function(t){s.$Message.error(t.msg)}))},stopQueue:function(t){var e=this;Object(u["I"])(t).then((function(t){e.$Message.success(t.msg),e.getQueue()})).catch((function(t){e.$Message.error(t.msg)}))}}},rt=it,nt=Object(y["a"])(rt,G,K,!1,null,null,null),ot=nt.exports,lt=s("0b65"),ct=s("c276"),dt=s("d708"),ut=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"tdinfo"},[s("Row",{staticClass:"expand-row"},[s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("商品总价：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.total_price)}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("下单时间：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.add_time)}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("推广人：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.spread_nickname?t.row.spread_nickname:"无")}})])],1),s("Row",[s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("用户备注：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.mark?t.row.mark:"无")}})]),s("Col",{attrs:{span:"8"}},[s("span",{staticClass:"expand-key"},[t._v("商家备注：")]),s("span",{staticClass:"expand-value",domProps:{textContent:t._s(t.row.remark?t.row.remark:"无")}})])],1)],1)},mt=[],pt={name:"table-expand",props:{row:Object}},ft=pt,ht=(s("9cc7"),Object(y["a"])(ft,ut,mt,!1,null,"b662070e",null));ht.exports;function vt(t,e,s,a,i,r,n){try{var o=t[r](n),l=o.value}catch(c){return void s(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function _t(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var r=t.apply(e,s);function n(t){vt(r,a,i,n,o,"next",t)}function o(t){vt(r,a,i,n,o,"throw",t)}n(void 0)}))}}function gt(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function yt(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?gt(s,!0).forEach((function(e){wt(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):gt(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function wt(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var bt={name:"index",components:{editFrom:l["a"],detailsFrom:k["a"],orderRecord:x["a"],orderRemark:I["a"],orderSend:b,userDetails:z,autoSend:J,queueList:ot},data:function(){var t=function(t,e,s){if(!e)return s(new Error("请填写核销码"));if(Number.isInteger(e)){var a=/\b\d{12}\b/;a.test(e)?s():s(new Error("请填写12位数字"))}else s(new Error("请填写12位数字"))};return{isAll:0,isCheckBox:!1,checkUidList:[],manualModal:!1,timeVal:[],options:lt["a"],payList:[{label:"全部",val:""},{label:"微信支付",val:"1"},{label:"支付宝支付",val:"4"},{label:"余额支付",val:"2"},{label:"线下支付",val:"3"}],orderData:{page:1,limit:10,type:"",status:"",data:"",real_name:"",pay_type:"",field_key:"all"},orderList:[],total:0,loading:!1,orderConNum:0,orderConId:0,orderId:0,delfromData:{},rowActive:{},orderDatalist:null,FromData:null,file:"",uploadAction:"".concat(dt["a"].apiBaseURL,"/file/upload/1"),uploadHeaders:{},fileList:[],modals2:!1,writeOffRules:{code:[{validator:t,trigger:"blur",required:!0}]},writeOffFrom:{code:"",confirm:0},exportListOn:0,exportList:[{name:"1",label:"导出发货单"},{name:"0",label:"导出订单"}]}},watch:{$route:function(){this.$route.fullPath==="".concat(dt["a"].roterPre,"/order/list?type=7&status=1")&&this.getPath()}},computed:yt({},Object(o["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:80},labelPosition:function(){return this.isMobile?"top":"right"}}),created:function(){this.getToken(),this.$route.fullPath==="".concat(dt["a"].roterPre,"/order/list?type=7&status=1")?this.getPath():this.getList()},mounted:function(){},methods:{getPath:function(){this.orderData.page=1,this.orderData.status=this.$route.query.status,this.getList()},allReset:function(){this.isAll=0,this.isCheckBox=!1,this.$refs.xTable.setAllCheckboxRow(!1),this.checkUidList=[]},allPages:function(t){this.isAll=t,0==t?this.$refs.xTable.toggleAllCheckboxRow():(this.isCheckBox?(this.$refs.xTable.setAllCheckboxRow(!1),this.isCheckBox=!1,this.isAll=0):(this.$refs.xTable.setAllCheckboxRow(!0),this.isCheckBox=!0,this.isAll=1),this.checkUidList=[])},checkboxItem:function(t){var e=parseInt(t.rowid),s=this.checkUidList.indexOf(e);-1!==s?this.checkUidList=this.checkUidList.filter((function(t){return t!==e})):this.checkUidList.push(e)},checkboxAll:function(){var t=this.$refs.xTable.getCheckboxRecords(!0),e=this.$refs.xTable.getCheckboxReserveRecords(!0);0==this.isAll&&this.checkUidList.length<=e.length&&!this.isCheckBox&&(e=[]),e=e.concat(t);var s=[];e.forEach((function(t){s.push(parseInt(t.id))})),this.checkUidList=s,t.length||(this.isCheckBox=!1)},printOreder:function(){if(this.checkUidList.length>10||1==this.isAll&&this.total>10)return this.$Message.error("最多批量打印10个订单");var t=[];1==this.isAll&&this.total<=10&&this.orderList.forEach((function(e){t.push(parseInt(e.id))}));var e=this.$router.resolve({path:"".concat(dt["a"].roterPre,"/order/distribution"),query:{id:1==this.isAll?t.join(","):this.checkUidList.join(",")}});window.open(e.href,"_blank")},reset:function(){this.timeVal=[],this.orderData={page:1,limit:10,type:"",status:"",data:"",real_name:"",pay_type:""},this.getList()},queuemModal:function(){this.$refs.queue.modal=!0},delAll:function(){var t=this;if(0===this.checkUidList.length&&0==this.isAll)return this.$Message.error("请先选择删除的订单！");var e={all:this.isAll,ids:this.checkUidList},s={title:"删除订单",url:"/order/dels",method:"post",ids:e};this.$modalSure(s).then((function(e){t.$Message.success(e.msg),t.allReset(),t.getList()})).catch((function(e){t.$Message.error(e.msg)}))},onAuto:function(){this.$refs.sends.modals=!0,this.$refs.sends.getList(),this.$refs.sends.getDeliveryList()},exports:function(){var t=_t(n.a.mark((function t(e){var s,a,i,r,o,l,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.exportListOn=this.exportList.findIndex((function(t){return t.name===e})),s=[],a=[],i=[],r="",o=JSON.parse(JSON.stringify(this.orderData)),o.page=1,o.type=e,o.ids=this.checkUidList.join(),l=0;case 7:if(!(l<o.page+1)){t.next=24;break}return t.next=10,this.downOrderData(o);case 10:if(c=t.sent,r||(r=c.filename),a.length||(a=c.filekey),s.length||(s=c.header),!c.export.length){t.next=19;break}i=i.concat(c.export),o.page++,t.next=21;break;case 19:return Object(X["a"])(s,a,r,i),t.abrupt("return");case 21:l++,t.next=7;break;case 24:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),downOrderData:function(t){return new Promise((function(e,s){Object(u["J"])(t).then((function(t){return e(t.data)}))}))},writeOff:function(){this.modals2=!0},search:function(t){var e=this;this.$refs[t].validate((function(t){t?(e.writeOffFrom.confirm=0,Object(u["z"])(e.writeOffFrom).then(function(){var t=_t(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===s.status?e.$Message.success(s.msg):e.$Message.error(s.msg);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))):e.$Message.error("请填写正确的核销码")}))},ok:function(){var t=this;this.writeOffFrom.code?(this.writeOffFrom.confirm=1,Object(u["z"])(this.writeOffFrom).then(function(){var e=_t(n.a.mark((function e(s){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:200===s.status?(t.$Message.success(s.msg),t.modals2=!1,t.$refs[name].resetFields(),t.getList()):t.$Message.error(s.msg);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))):this.$Message.warning("请先验证订单！")},del:function(t){this.modals2=!1,this.writeOffFrom.confirm=0,this.$refs[t].resetFields()},getToken:function(){this.uploadHeaders["Authori-zation"]="Bearer "+ct["a"].cookies.get("token")},uploadSuccess:function(t,e,s){200===t.status?(this.$Message.success(t.msg),this.file=t.data.src,this.fileList=s):this.$Message.error(t.msg)},removeFile:function(t,e){this.file="",this.fileList=e},getExpressList:function(){var t=_t(n.a.mark((function t(){var e,s,a,i,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=[],s=[],a=[],i="",t.next=3,this.getExcelData();case 3:r=t.sent,i||(i=r.filename),s.length||(s=r.filekey),e.length||(e=r.header),a=r.export,Object(X["a"])(e,s,i,a);case 9:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getExcelData:function(){return new Promise((function(t,e){Object(u["c"])().then((function(e){return t(e.data)}))}))},manualModalOk:function(){var t=this;this.$refs.upload.clearFiles(),Object(u["o"])({file:this.file}).then((function(e){t.$Message.success(e.msg),t.fileList=[]})).catch((function(e){t.$Message.error(e.msg),t.fileList=[]}))},manualModalCancel:function(){this.fileList=[],this.$refs.upload.clearFiles()},bindWrite:function(t){var e=this;this.$Modal.confirm({title:"提示",content:"确定要核销该订单吗？",cancelText:"取消",closable:!0,maskClosable:!0,onOk:function(){Object(u["K"])(t.order_id).then((function(t){e.$Message.success(t.msg),e.getList()}))},onCancel:function(){}})},delivery:function(t,e){var s=this;Object(u["e"])(t.id).then(function(){var a=_t(n.a.mark((function a(i){return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:s.orderConNum=e,s.orderConId=t.pid,s.FromData=i.data,s.$refs.edits.modals=!0,1!=e&&s.getData(s.orderId,1);case 5:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()).catch((function(t){s.$Message.error(t.msg)}))},edit:function(t){this.getOrderData(t.id)},getOrderData:function(t){var e=this;Object(u["i"])(t).then(function(){var t=_t(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!1!==s.data.status){t.next=2;break}return t.abrupt("return",e.$authLapse(s.data));case 2:e.$authLapse(s.data),e.FromData=s.data,e.$refs.edits.modals=!0;case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},sendOrder:function(t,e){var s=this;this.orderConId=t.pid,this.orderConNum=e,this.$store.commit("store/order/setSplitOrder",t.total_num),this.$refs.send.modals=!0,this.orderId=t.id,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(e){s.$refs.send.getCartInfo(t._status,t.id)}))},submitFail:function(){this.getList(),1!=this.orderConNum?this.getData(this.orderId,1):this.$refs.detailss.getSplitOrder(this.orderConId)},changeMenu:function(t,e,s){var a=this;switch(this.orderId=t.id,this.orderConId=t.pid>0?t.pid:t.id,this.orderConNum=s,e){case"1":this.delfromData={title:"修改立即支付",url:"/order/pay_offline/".concat(t.id),method:"post",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.getData(t.id,1),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"2":this.rowActive=t,this.getData(t.id);break;case"3":this.$refs.record.modals=!0,this.$refs.record.getList(t.id);break;case"4":this.$refs.remarks.formValidate.remark=t.remark,this.$refs.remarks.modals=!0;break;case"5":this.getOnlyRefundData(t.id,t.refund_type);break;case"55":this.getRefundData(t.id,t.refund_type);break;case"6":this.getRefundIntegral(t.id);break;case"7":this.getNoRefundData(t.id);break;case"8":this.delfromData={title:"修改确认收货",url:"/order/take/".concat(t.id),method:"put",ids:""},this.$modalSure(this.delfromData).then((function(e){a.$Message.success(e.msg),a.getList(),s?a.$refs.detailss.getSplitOrder(t.pid):a.getData(t.id,1)})).catch((function(t){a.$Message.error(t.msg)}));break;case"10":this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"11":this.delfromData={title:"立即打印电子面单",info:"您确认打印此电子面单吗?",url:"/order/order_dump/".concat(t.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){a.$Message.success(t.msg),a.getList()})).catch((function(t){a.$Message.error(t.msg)}));break;case"12":var i=this.$router.resolve({path:"".concat(dt["a"].roterPre,"/order/distribution"),query:{id:t.id}});window.open(i.href,"_blank");break;default:this.delfromData={title:"删除订单",url:"/order/del/".concat(t.id),method:"DELETE",ids:""},this.delOrder(t,this.delfromData)}},getData:function(t,e){var s=this;Object(u["d"])(t).then(function(){var t=_t(n.a.mark((function t(a){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e||(s.$refs.detailss.modals=!0),s.$refs.detailss.activeName="detail",s.orderDatalist=a.data,s.orderDatalist.orderInfo.refund_reason_wap_img)try{s.orderDatalist.orderInfo.refund_reason_wap_img=JSON.parse(s.orderDatalist.orderInfo.refund_reason_wap_img)}catch(i){s.orderDatalist.orderInfo.refund_reason_wap_img=[]}case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){s.$Message.error(t.msg)}))},getOnlyRefundData:function(t,e){var s=this;this.$modalForm(Object(u["m"])(t)).then((function(){s.getList(),s.$refs.detailss.modals=!1}))},getRefundData:function(t,e){var s=this;this.delfromData={title:"是否立即退货退款",url:"/refund/agree/".concat(t),method:"get"},this.$modalSure(this.delfromData).then((function(t){s.$Message.success(t.msg),s.getList()})).catch((function(t){s.$Message.error(t.msg)}))},getRefundIntegral:function(t){var e=this;Object(u["D"])(t).then(function(){var t=_t(n.a.mark((function t(s){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.FromData=s.data,e.$refs.edits.modals=!0;case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},getNoRefundData:function(t){var e=this;this.$modalForm(Object(u["n"])(t)).then((function(){e.getList()}))},delOrder:function(t,e){var s=this;if(1===t.is_del)this.$modalSure(e).then((function(t){s.$Message.success(t.msg),s.getList(),s.$refs.detailss.modals=!1})).catch((function(t){s.$Message.error(t.msg)}));else{var a="错误！",i="<p>您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！</p>";this.$Modal.error({title:a,content:i})}},getList:function(){var t=this;this.loading=!0,Object(u["r"])(this.orderData).then((function(e){var s=e.data;s.data.forEach((function(e){e.id==t.orderId&&(t.rowActive=e)})),t.$set(t,"orderList",s.data),t.total=e.data.count,t.loading=!1,t.$nextTick((function(){if(1==this.isAll)this.isCheckBox?this.$refs.xTable.setAllCheckboxRow(!0):this.$refs.xTable.setAllCheckboxRow(!1);else{var t=this.$refs.xTable.getCheckboxReserveRecords(!0);(!this.checkUidList.length||this.checkUidList.length<=t.length)&&this.$refs.xTable.setAllCheckboxRow(!1)}}))})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},onchangeTime:function(t){"00:00:00"===t[1].slice(-8)?(t[1]=t[1].slice(0,-8)+"23:59:59",this.timeVal=t):this.timeVal=t,this.orderData.data=this.timeVal[0]?this.timeVal.join("-"):"",this.orderData.page=1,this.allReset(),this.getList()},showUserInfo:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.activeName="info",this.$refs.userDetails.getDetails(t.uid)},pageChange:function(t){this.orderData.page=t,this.getList()},userSearchs:function(){this.allReset(),this.orderData.page=1,this.getList()}}},kt=bt,xt=(s("e32d"),Object(y["a"])(kt,a,i,!1,null,"30e12018",null));e["default"]=xt.exports},"5f36":function(t,e,s){},7690:function(t,e,s){},"7d6f":function(t,e,s){},"9cc7":function(t,e,s){"use strict";var a=s("0a1e"),i=s.n(a);i.a},"9e00":function(t,e,s){"use strict";var a=s("7d6f"),i=s.n(a);i.a},a532:function(t,e,s){},a8fa:function(t,e,s){},b309:function(t,e,s){"use strict";var a=s("a8fa"),i=s.n(a);i.a},c24f:function(t,e,s){"use strict";s.d(e,"a",(function(){return i})),s.d(e,"b",(function(){return r})),s.d(e,"f",(function(){return n})),s.d(e,"c",(function(){return o})),s.d(e,"d",(function(){return l})),s.d(e,"e",(function(){return c}));var a=s("b6bd");function i(t){return Object(a["a"])({url:"user/user/".concat(t),method:"get"})}function r(t){return Object(a["a"])({url:"user/one_info/".concat(t.id),method:"get",params:t.datas})}function n(t){return Object(a["a"])({url:"user/visit_list/".concat(t.id),method:"get",params:t.datas})}function o(t){return Object(a["a"])({url:"user/spread_list/".concat(t.id),method:"get",params:t.datas})}function l(t){return Object(a["a"])({url:"user/user",method:"get",params:t})}function c(t){return Object(a["a"])({url:"user/search",method:"get",params:t})}},e32d:function(t,e,s){"use strict";var a=s("5f36"),i=s.n(a);i.a}}]);