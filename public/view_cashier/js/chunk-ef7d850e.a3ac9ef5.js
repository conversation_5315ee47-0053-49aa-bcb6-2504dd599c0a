(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ef7d850e"],{"16a0":function(t,e,n){"use strict";var r=n("7da6"),l=n.n(r);l.a},4073:function(t,e,n){t.exports=n.p+"view_cashier/img/logos.67b9963c.png"},"7da6":function(t,e,n){},8108:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"login"},[n("div",[t._m(0),n("Form",{ref:"formInline",attrs:{model:t.formInline,rules:t.ruleInline}},[n("FormItem",{attrs:{prop:"appUrl"}},[n("Input",{attrs:{placeholder:"请输入域名"},scopedSlots:t._u([{key:"prepend",fn:function(){return[n("Select",{staticStyle:{width:"90px"},model:{value:t.select1,callback:function(e){t.select1=e},expression:"select1"}},[n("Option",{attrs:{value:"https:"}},[t._v("https://")]),n("Option",{attrs:{value:"http:"}},[t._v("http://")])],1)]},proxy:!0}]),model:{value:t.formInline.appUrl,callback:function(e){t.$set(t.formInline,"appUrl",e)},expression:"formInline.appUrl"}})],1),n("FormItem",[n("Button",{staticClass:"btn",attrs:{type:"primary",long:"",size:"default"},on:{click:function(e){return t.handleSubmit("formInline")}}},[t._v("确定")])],1)],1)],1)])},l=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:n("4073")}})])}],a={data:function(){return{select1:"https:",formInline:{appUrl:""},ruleInline:{appUrl:[{required:!0,message:"请输入域名",trigger:"blur"}]}}},created:function(){},mounted:function(){},methods:{handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){t&&(localStorage.setItem("protocol",e.select1),localStorage.setItem("api-url",e.formInline.appUrl),window.location.reload())}))}}},o=a,i=(n("16a0"),n("2877")),s=Object(i["a"])(o,r,l,!1,null,"78c2dae3",null);e["default"]=s.exports}}]);