(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e3f056ce"],{"0493":function(t,e,a){t.exports=a.p+"view_cashier/img/no-record.2e1e1105.png"},"0eb7":function(t,e,a){"use strict";var r=a("72ab"),s=a.n(r);s.a},"22f89":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单备注","class-name":"remark-modal","footer-hide":""},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"input-wrapper"},[a("Input",{staticStyle:{width:"100%"},attrs:{maxlength:"200","show-word-limit":"",rows:5,type:"textarea",placeholder:"订单备注"},model:{value:t.formValidate.remark,callback:function(e){t.$set(t.formValidate,"remark",e)},expression:"formValidate.remark"}})],1),a("div",{staticClass:"btn-wrapper"},[a("Button",{attrs:{type:"primary",size:"large",long:""},on:{click:function(e){return t.putRemark("formValidate")}}},[t._v("提交")])],1)])},s=[],i=a("a34a"),n=a.n(i),o=a("f8b7");function c(t,e,a,r,s,i,n){try{var o=t[i](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(r,s)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){c(i,r,s,n,o,"next",t)}function o(t){c(i,r,s,n,o,"throw",t)}n(void 0)}))}}var d={name:"orderMark",data:function(){return{formValidate:{remark:""},modals:!1,ruleValidate:{remark:[{required:!0,message:"请输入备注信息",trigger:"blur"}]}}},props:{orderId:Number,currentTab:{type:String,default:""},remarkType:{default:"",type:String}},created:function(){},methods:{cancel:function(t){this.modals=!1},refundmark:function(t){this.formValidate.remark=t},getRemark:function(t){var e=this;Object(o["db"])(t).then((function(t){e.formValidate.remark=t.data.remarks})).catch((function(t){e.$Message.error(t.msg)}))},getVipRemark:function(t){var e=this;Object(o["ib"])(t).then((function(t){e.formValidate.remark=t.data.remarks})).catch((function(t){e.$Message.error(t.msg)}))},putRemark:function(t){var e=this,a={id:this.orderId,remark:this.formValidate};if(this.formValidate.remark.trim()){var r=null;r=this.remarkType?Object(o["ab"])(a):3==this.currentTab?Object(o["V"])(a.id,{remarks:this.formValidate.remark}):4==this.currentTab?Object(o["X"])(a.id,{remarks:this.formValidate.remark}):Object(o["bb"])(a),r.then(function(){var t=l(n.a.mark((function t(a){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$Message.success(a.msg),e.modals=!1,e.$emit("submitFail");case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))}else this.$Message.warning("请填写备注信息")}}},u=d,f=(a("ea2e"),a("2877")),m=Object(f["a"])(u,r,s,!1,null,"3ef3af22",null);e["a"]=m.exports},"72ab":function(t,e,a){},a464:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content"},[a("div",{staticClass:"filter-box"},t._l(t.filterList,(function(e,r){return a("div",{directives:[{name:"show",rawName:"v-show",value:"verify"===t.orderType&&"type"!==e.type||"order"===t.orderType||"table"===t.orderType,expression:"orderType === 'verify' && item.type !== 'type' || orderType === 'order' || orderType === 'table'"}],key:r,staticClass:"box"},[a("div",{staticClass:"title"},[t._v(t._s(e.title))]),a("div",{staticClass:"tabs-box"},[t._l(e.tabs,(function(r,s){return a("div",{directives:[{name:"show",rawName:"v-show",value:e.tabs.length&&"staff_id"!==e.type,expression:"item.tabs.length && item.type !== 'staff_id'"}],key:s,staticClass:"tabs",class:t.searchData[e.type]===r.type?"on":"",on:{click:function(a){return t.selectFilter(e,s)}}},[a("span",[t._v(t._s(r.name))])])})),"time"===e.type?a("div",{staticClass:"tabs-time"},[a("DatePicker",{staticStyle:{width:"200px"},attrs:{format:"yyyy/MM/dd",type:"date",placeholder:"开始时间",clearable:!0},on:{"on-change":t.changeStartTime,"on-open-change":t.timeStatus},model:{value:t.startTime,callback:function(e){t.startTime=e},expression:"startTime"}}),a("span",{staticClass:"bl"},[t._v("~")]),a("DatePicker",{staticStyle:{width:"200px"},attrs:{format:"yyyy/MM/dd",type:"date",placeholder:"结束时间",clearable:!0},on:{"on-change":t.changeEndTime,"on-open-change":t.timeStatus},model:{value:t.endTime,callback:function(e){t.endTime=e},expression:"endTime"}})],1):t._e(),"staff_id"===e.type?a("div",{staticClass:"tabs-box"},t._l(t.dataList,(function(r){return a("div",{key:r.id,staticClass:"tabs",class:t.searchData[e.type]===r.id?"on":"",on:{click:function(a){return t.selectFilter(e,r.id)}}},[t._v("\n            "+t._s(r.staff_name)+"\n          ")])})),0):t._e()],2)])})),0),a("div",{staticClass:"search-btn"},[a("div",{staticClass:"btn reset",on:{click:t.init}},[t._v("重置")]),a("div",{staticClass:"btn suc",on:{click:t.search}},[t._v("确定")])])])},s=[],i=a("b4ea");function n(t){return l(t)||c(t)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function c(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function l(t){if(Array.isArray(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}var d={name:"index",props:{orderType:{type:String,default:"order"}},data:function(){return{startTime:"",endTime:"",filterList:[{title:"订单类型",type:"type",tabs:[{name:"全部",type:""},{name:"核销订单",type:5},{name:"收银台订单",type:6},{name:"配送订单",type:7}]},{title:"订单状态",type:"status",tabs:[{name:"全部",type:""},{name:"未支付",type:0},{name:"待评价",type:3},{name:"已完成",type:4},{name:"已删除",type:-4}]},{title:"创建时间",type:"time",tabs:[{name:"全部",type:""},{name:"今天",type:"today"},{name:"昨天",type:"yesterday"},{name:"近七天",type:"lately7"},{name:"近三十天",type:"lately30"},{name:"本月",type:"month"},{name:"本年",type:"year"}]},{title:"店员",type:"staff_id",tabs:[]}],dataList:[],searchData:{type:"",status:"",time:"",staff_id:0}}},created:function(){if(this.cashierList(),"table"==this.orderType){for(var t=[],e=0;e<this.filterList.length;e++)if("type"!=this.filterList[e].type){if("status"==this.filterList[e].type){for(var a=[],r=0;r<this.filterList[e].tabs.length;r++)-4!=this.filterList[e].tabs[r].type&&a.push(this.filterList[e].tabs[r]);this.filterList[e].tabs=a}t.push(this.filterList[e])}this.filterList=t}},methods:{init:function(){var t=this;this.searchData={type:"",status:"",time:"",staff_id:0},this.$nextTick((function(e){t.startTime="",t.endTime=""}))},changeStartTime:function(t){this.searchData.time[0]=t,this.startTime=t},changeEndTime:function(t){this.searchData.time[1]=t,this.endTime=t},timeStatus:function(t){"string"===typeof this.searchData.time&&(this.searchData.time=["",""])},cashierList:function(){var t=this;Object(i["a"])().then((function(e){var a=[{staff_name:"全部",id:0}];t.dataList=[].concat(a,n(e.data.staffList))}))},selectFilter:function(t,e){"staff_id"==t.type?this.searchData[t.type]=e:this.searchData[t.type]=t.tabs[e].type},search:function(){this.$emit("search",this.searchData)}}},u=d,f=(a("abba"),a("2877")),m=Object(f["a"])(u,r,s,!1,null,"c852e872",null);e["a"]=m.exports},abba:function(t,e,a){"use strict";var r=a("cff1"),s=a.n(r);s.a},b4ea:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return i}));var r=a("b6bd");function s(){return Object(r["a"])({url:"user/cashier_list",method:"get"})}function i(t){return Object(r["a"])({url:"staff/staff",method:"get",params:t})}},cfcb:function(t,e,a){},cff1:function(t,e,a){},d487:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"order-list",on:{scroll:t.addPage}},t._l(t.orderData,(function(e,r){return a("div",{key:r,staticClass:"item",class:{sel:t.sel===r},on:{click:function(a){return t.selectOrder(e,r)}}},[a("div",{staticClass:"top"},[a("div",{staticClass:"order-sty"},[e.pink_name&&"verify"!==t.orderType?a("span",{staticClass:"type-sty",style:"color: "+e.color+";border-color:"+e.color},[t._v(t._s(e.pink_name))]):t._e(),"table"==t.orderType?a("span",{staticClass:"order"},[t._v(t._s(e.serial_number||e.order_id))]):a("span",{staticClass:"order"},[t._v("单号:"+t._s(e.order_id))])]),a("div",{staticClass:"time"},[t._v("\n        "+t._s(e.add_time)+"\n      ")])]),a("div",{staticClass:"mid acea-row row-between"},[10==e.type||e._infoData.length>1?a("div",{staticClass:"acea-row"},t._l(e._infoData,(function(t,e){return a("div",{directives:[{name:"show",rawName:"v-show",value:e<5,expression:"i < 5"}],key:e,staticClass:"pic"},[a("img",{attrs:{src:t.cart_info.productInfo.image}})])})),0):a("div",{staticClass:"acea-row row-middle"},[t._l(e._infoData,(function(t,e){return a("div",{key:e,staticClass:"pic"},[a("img",{attrs:{src:t.cart_info.productInfo.image}})])})),e._infoData.length?a("div",{staticClass:"store_name line2"},[t._v("\n        "+t._s(e._infoData[0].cart_info.productInfo.store_name)+"\n        ")]):t._e()],2),a("div",[a("p",[a("span",{staticClass:"num"},[t._v("¥"+t._s(e.orderId&&e.orderId.pay_price||e.pay_price))])]),a("p",{staticClass:"total_num"},[t._v("共"+t._s(e.total_num)+"件商品")])])]),a("div",{staticClass:"footer"},["table"==t.orderType?a("div",{staticClass:"text-333"},[1==e.status?a("span",[t._v("未支付")]):2==e.status?a("span",[t._v(t._s(e.orderId&&e.orderId.is_del?"已删除":e.orderId&&e.orderId.refund_status?"已退款":e.orderId&&e.orderId.paid?"已结算":"待付款"))]):3==e.status?a("span",[t._v("已完成")]):a("span",[t._v("加购中")])]):a("div",{staticClass:"text-333"},[t._v("\n        "+t._s(e.status_name.status_name)+"\n        "),!e.is_all_refund&&e.refund.length?a("span",{staticClass:"trip"},[t._v("\n          (部分退款中)\n        ")]):t._e(),e.is_all_refund&&e.refund.length&&6!=e.refund_type?a("span",{staticClass:"trip"},[t._v("\n          (退款中)\n        ")]):t._e()]),e.clerk_name?a("div",{staticClass:"shouyin"},[t._v("\n        收银员："+t._s(e.clerk_name)+"\n      ")]):t._e(),e.orderId?a("div",{staticClass:"shouyin"},[t._v("订单编号："+t._s(e.orderId.order_id))]):t._e()])])})),0)},s=[],i={name:"orderList",props:{orderData:{type:Array},total:{type:Number},orderType:{type:String,default:"order"}},data:function(){return{sel:0}},methods:{selectOrder:function(t,e){this.sel=e,this.$emit("selectOrder",t)},addPage:function(t){(!t||t.target.scrollHeight-t.target.scrollTop-t.target.clientHeight<=0&&this.orderData.length<this.total)&&(console.log(t?t.target.scrollHeight-t.target.scrollTop-t.target.clientHeight:""),this.$emit("addPage"))}}},n=i,o=(a("0eb7"),a("2877")),c=Object(o["a"])(n,r,s,!1,null,"040bc193",null);e["a"]=c.exports},ea2e:function(t,e,a){"use strict";var r=a("cfcb"),s=a.n(r);s.a}}]);