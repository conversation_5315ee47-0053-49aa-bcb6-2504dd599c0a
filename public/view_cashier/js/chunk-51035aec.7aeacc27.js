(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-51035aec"],{"265f":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{},[r("PageHeader",{staticClass:"product_tabs",attrs:{title:t.$route.meta.title,"hidden-breadcrumb":""}},[r("div",{attrs:{slot:"title"},slot:"title"},[r("div",{staticStyle:{float:"left"}},[r("router-link",{attrs:{to:{path:t.roterPre+"/cashier/index"}}},[r("Button",{staticClass:"mr20",attrs:{icon:"ios-arrow-back",size:"small"}},[t._v("返回\n            ")])],1),r("span",{staticClass:"mr20",domProps:{textContent:t._s(t.$route.meta.title)}})],1)])])],1),r("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[r("div",{staticClass:"box"},[r("Form",{ref:"formValidate",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":120,"label-position":"right"}},[r("FormItem",{attrs:{label:"头像：",prop:"avatar"}},[r("div",{staticClass:"picBox"},[r("Upload",{attrs:{action:t.uploadUrl,headers:t.header,"show-upload-list":!1,"on-success":t.handleSuccess}},[t.formValidate.avatar?r("div",{staticClass:"pictrue"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.formValidate.avatar,expression:"formValidate.avatar"}]})]):r("div",{staticClass:"upLoad acea-row row-center-wrapper"},[r("Icon",{attrs:{type:"ios-camera-outline",size:"26"}})],1)])],1)]),r("FormItem",{attrs:{label:"账号",prop:""}},[r("Input",{staticClass:"input",attrs:{type:"text",disabled:!0},model:{value:t.account,callback:function(e){t.account=e},expression:"account"}})],1),r("FormItem",{attrs:{label:"姓名",prop:"real_name"}},[r("Input",{staticClass:"input",attrs:{type:"text"},model:{value:t.formValidate.real_name,callback:function(e){t.$set(t.formValidate,"real_name",e)},expression:"formValidate.real_name"}})],1),r("FormItem",{attrs:{label:"原始密码",prop:"pwd"}},[r("Input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.pwd,callback:function(e){t.$set(t.formValidate,"pwd",e)},expression:"formValidate.pwd"}})],1),r("FormItem",{attrs:{label:"新密码",prop:"new_pwd"}},[r("Input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.new_pwd,callback:function(e){t.$set(t.formValidate,"new_pwd",e)},expression:"formValidate.new_pwd"}})],1),r("FormItem",{attrs:{label:"确认新密码",prop:"conf_pwd"}},[r("Input",{staticClass:"input",attrs:{type:"password"},model:{value:t.formValidate.conf_pwd,callback:function(e){t.$set(t.formValidate,"conf_pwd",e)},expression:"formValidate.conf_pwd"}})],1),r("FormItem",[r("Button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1)]),r("Modal",{attrs:{width:"950px",scrollable:"","footer-hide":"",closable:"",title:"上传用户头像","mask-closable":!1,"z-index":1},model:{value:t.modalPic,callback:function(e){t.modalPic=e},expression:"modalPic"}},[t.modalPic?r("uploadPictures",{attrs:{isChoice:t.isChoice,gridBtn:t.gridBtn,gridPic:t.gridPic},on:{getPic:t.getPic}}):t._e()],1)],1)},n=[],o=r("c24f"),u=r("2f62"),c=r("b0e7"),i=r("d708"),s=r("c276");function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(r,!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var m={name:"setting_user",components:{uploadPictures:c["a"]},computed:d({},Object(u["e"])("store/layout",["isMobile"]),{},Object(u["e"])("store/userLevel",["categoryId"]),{labelWidth:function(){return this.isMobile?void 0:75},labelPosition:function(){return this.isMobile?"top":"left"}}),data:function(){return{roterPre:i["a"].roterPre,uploadUrl:i["a"].apiBaseURL+"/upload/image",modalPic:!1,isChoice:"单选",account:"",formValidate:{real_name:"",pwd:"",new_pwd:"",conf_pwd:"",avatar:""},ruleValidate:{real_name:[{required:!0,message:"您的姓名不能为空",trigger:"blur"}],pwd:[{required:!0,message:"请输入您的原始密码",trigger:"blur"}],new_pwd:[{required:!0,message:"请输入您的新密码",trigger:"blur"}],conf_pwd:[{required:!0,message:"请确认您的新密码",trigger:"blur"}]},gridPic:{xl:6,lg:8,md:12,sm:12,xs:12},gridBtn:{xl:4,lg:8,md:8,sm:8,xs:8},header:{}}},mounted:function(){this.getInfo(),this.getToken()},methods:{getToken:function(){this.header["Authori-zation"]="Bearer "+s["a"].cookies.get("token")},modalPicTap:function(){this.modalPic=!0},getPic:function(t){this.formValidate.avatar=t.att_dir,this.modalPic=!1},handleSuccess:function(t,e,r){var a=this;if(200===t.status){var n={avatar:t.data.url};Object(o["q"])(n).then((function(t){a.$Message.success(t.msg),a.formValidate.avatar=n.avatar,a.$store.dispatch("cashier/user/set",{avatar:n.avatar});var e=window.localStorage,r=e.getItem("cashier_user_info"),o=JSON.parse(r);o.avatar=n.avatar,e.setItem("cashier_user_info",JSON.stringify(o))})).catch((function(t){a.$Message.error(t.msg)}))}else this.$Message.error(t.msg)},getInfo:function(){var t=this;Object(o["p"])().then((function(e){var r=e.data;t.account=r.account,t.formValidate.avatar=r.avatar,t.formValidate.real_name=r.staff_name})).catch((function(e){t.$Message.error(e.msg)}))},handleSubmit:function(t){var e=this;Object(o["q"])(this.formValidate).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))}}},p=m,b=(r("653df"),r("2877")),h=Object(b["a"])(p,a,n,!1,null,"51deb73c",null);e["default"]=h.exports},"653df":function(t,e,r){"use strict";var a=r("a8fc"),n=r.n(a);n.a},a8fc:function(t,e,r){},c24f:function(t,e,r){"use strict";r.d(e,"z",(function(){return n})),r.d(e,"s",(function(){return o})),r.d(e,"t",(function(){return u})),r.d(e,"a",(function(){return c})),r.d(e,"y",(function(){return i})),r.d(e,"r",(function(){return s})),r.d(e,"u",(function(){return l})),r.d(e,"b",(function(){return d})),r.d(e,"C",(function(){return f})),r.d(e,"f",(function(){return m})),r.d(e,"m",(function(){return p})),r.d(e,"d",(function(){return b})),r.d(e,"h",(function(){return h})),r.d(e,"x",(function(){return g})),r.d(e,"v",(function(){return v})),r.d(e,"B",(function(){return _})),r.d(e,"w",(function(){return O})),r.d(e,"A",(function(){return w})),r.d(e,"o",(function(){return j})),r.d(e,"q",(function(){return P})),r.d(e,"c",(function(){return y})),r.d(e,"p",(function(){return V})),r.d(e,"n",(function(){return x})),r.d(e,"g",(function(){return k})),r.d(e,"e",(function(){return C})),r.d(e,"i",(function(){return I})),r.d(e,"k",(function(){return $})),r.d(e,"j",(function(){return M})),r.d(e,"l",(function(){return S}));var a=r("b6bd");function n(){return Object(a["a"])({url:"user/user_label_cate",method:"get"})}function o(){return Object(a["a"])({url:"user/user_label_cate/create",method:"get"})}function u(t){return Object(a["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function c(t){return Object(a["a"])({url:"user/user_label",method:"get",params:t})}function i(){return Object(a["a"])({url:"user/user_label/create",method:"get"})}function s(t){return Object(a["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function l(t){return Object(a["a"])({url:"user/get_list",method:"get",params:t})}function d(t){return Object(a["a"])({url:"user/cashier_list",method:"get",params:t})}function f(t){return Object(a["a"])({url:"user/search",method:"get",params:t})}function m(t){return Object(a["a"])({url:"user/label/".concat(t),method:"get"})}function p(t,e){return Object(a["a"])({url:"user/label/".concat(t),method:"post",data:e})}function b(t){return Object(a["a"])({url:"user/info/".concat(t),method:"get"})}function h(t){return Object(a["a"])({url:"user/record/".concat(t.id),method:"get",params:t.datas})}function g(t){return Object(a["a"])({url:"user/set_label",method:"post",data:t})}function v(){return Object(a["a"])({url:"store/recharge_info",method:"get"})}function _(){return Object(a["a"])({url:"user/member/ship",method:"get"})}function O(t){return Object(a["a"])({url:"store/recharge",method:"post",data:t})}function w(t){return Object(a["a"])({url:"/user/member",method:"post",data:t})}function j(t){return Object(a["a"])({url:"staff/binding/user",method:"post",data:t})}function P(t){return Object(a["a"])({url:"updatePwd",method:"PUT",data:t})}function y(t,e){return Object(a["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function V(){return Object(a["a"])({url:"user/cashier_info ",method:"get"})}function x(t){return Object(a["a"])({url:"user/read/".concat(t),method:"get"})}function k(t,e){return Object(a["a"])({url:"user/one_info/".concat(t),method:"get",params:e})}function C(t){return Object(a["a"])({url:"user/member_card",method:"get",params:t})}function I(t){return Object(a["a"])({url:"user/mer_recharge",method:"post",data:t})}function $(t){return Object(a["a"])({url:"user/search_user_info",method:"post",data:t})}function M(t){return Object(a["a"])({url:"user/register_user",method:"post",data:t})}function S(t,e){return Object(a["a"])({url:"user/update/".concat(t),method:"post",data:e})}}}]);