(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7142288e"],{"1dd0":function(e,t,r){"use strict";var s=r("7437"),a=r.n(s);a.a},"2e61":function(e,t,r){"use strict";r.r(t);var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"order"},[s("div",{staticClass:"left"},[s("div",{staticClass:"left-top"},[s("div",{staticClass:"title"},[e._v("订单列表")]),s("div",{staticClass:"sx",on:{click:function(t){e.filterModal=!e.filterModal}}},[e._v("\n        "+e._s(e.filterModal?"关闭":"筛选")+"\n        "),e.filterModal?e._e():s("Icon",{staticClass:"ios-funnel-outline",attrs:{color:"#666",type:"ios-funnel-outline"}})],1)]),s("div",{staticClass:"order-box"},[e.filterModal?e._e():s("div",{staticClass:"search"},[s("Input",{attrs:{search:"","enter-button":"搜索",size:"large",placeholder:"搜索订单编号"},on:{"on-search":e.search},model:{value:e.orderData.keyword,callback:function(t){e.$set(e.orderData,"keyword",t)},expression:"orderData.keyword"}})],1),e.orderListData.length?s("orderList",{directives:[{name:"show",rawName:"v-show",value:!e.filterModal,expression:"!filterModal"}],staticClass:"order-list",attrs:{total:e.count,orderData:e.orderListData},on:{addPage:e.addPage,selectOrder:e.selectOrder}}):e.orderListData.length||e.filterModal?e._e():s("div",{staticClass:"no-order"},[s("img",{attrs:{src:r("66c9"),alt:""}}),s("span",{staticClass:"trip"},[e._v("噢～目前暂无订单")])]),s("filter-modal",{directives:[{name:"show",rawName:"v-show",value:e.filterModal,expression:"filterModal"}],on:{search:e.searchList}})],1)]),s("div",{staticClass:"order-data"},[s("div",{staticClass:"header"},[e._l(e.tabs,(function(t,r){return s("div",{key:r,staticClass:"item",class:e.sle===r?"sel":r===e.sle-1?"neighbor-left":r===e.sle+1?"neighbor-right":"def",on:{click:function(t){return e.tabClick(r)}}},[s("div",{staticClass:"item-wrap"},[e._v("\n          "+e._s(t)+"\n        ")])])})),s("div",{staticClass:"box",class:2===e.sle?"neighbor-right":""})],2),s("div",{staticClass:"content",class:{"border-radius":e.sle}},[0===e.sle&&e.selectOrderData.id?s("userOrder",{staticClass:"orders",attrs:{canSend:e.canSend,selectData:e.selectOrderData},on:{remarks:e.remarks}}):e._e(),1===e.sle&&e.selectOrderData.id?s("orderDetails",{staticClass:"orders",attrs:{orderDatalist:e.orderInfoData}}):e._e(),2===e.sle&&e.selectOrderData.id?s("orderRecord",{attrs:{id:e.selectOrderData.id}}):e.selectOrderData.id?e._e():s("div",{staticClass:"no-order"},[s("img",{attrs:{src:r("0493"),alt:""}}),s("span",{staticClass:"trip"},[e._v("噢～目前暂无挂单记录")])])],1),e.selectOrderData.order_id?s("div",{staticClass:"footer"},[s("div",{staticClass:"footer-left"},[e.selectOrderData.clerk_name?s("span",{staticClass:"clerk"},[e._v("收银员："+e._s(e.selectOrderData.clerk_name))]):e._e(),s("span",{staticClass:"pay"},[e._v("实付：")]),s("span",{staticClass:"num"},[e._v("¥"+e._s(e.selectOrderData.pay_price||0))])]),s("div",{staticClass:"footer-right"},[e.open_erp?e._e():s("div",{staticClass:"btn",on:{click:e.remarks}},[e._v("订单备注")]),e.open_erp?e._e():s("div",{staticClass:"btn",on:{click:e.point}},[e._v("小票打印")]),2!==e.selectOrderData._status&&8!==e.selectOrderData._status&&4!==e.selectOrderData.status||3!==e.selectOrderData.shipping_type||null!==e.selectOrderData.pinkStatus&&2!==e.selectOrderData.pinkStatus?e._e():s("div",{staticClass:"btn pay",on:{click:e.orderSend}},[e._v("\n          发送货\n        ")]),[0,1,2,5].includes(e.selectOrderData.refund_type)&&(parseFloat(e.selectOrderData.pay_price)>parseFloat(e.selectOrderData.refund_price)||0==e.selectOrderData.pay_price)&&!e.selectOrderData.refund.length&&1==e.selectOrderData.paid?s("div",{staticClass:"btn pay",on:{click:e.getRefundData}},[e._v("\n          "+e._s(2==e.selectOrderData.refund_type?"同意退货":"立即退款")+"\n        ")]):e._e(),e.selectOrderData.is_del||e.selectOrderData.paid||"offline"!=e.selectOrderData.pay_type?e._e():s("div",{staticClass:"btn pay",on:{click:e.openConfirm}},[e._v("\n          确认支付\n        ")])])]):e._e()]),s("order-remark",{ref:"remarks",attrs:{orderId:e.selectOrderData.id},on:{submitFail:e.submitFail}}),s("orderSend",{ref:"send",attrs:{orderId:e.orderId,status:e.status,pay_type:e.pay_type},on:{submitFail:e.send}}),s("Modal",{attrs:{title:"确认支付",width:"450","class-name":"confirm-modal"},on:{"on-ok":e.payOffline},model:{value:e.confirmVisible,callback:function(t){e.confirmVisible=t},expression:"confirmVisible"}},[s("Icon",{attrs:{type:"ios-alert"}}),e._v("确认该笔订单要使用线下支付吗？\n  ")],1),s("Modal",{attrs:{title:"手动退款",width:"960","class-name":"refund-modal"},on:{"on-visible-change":e.visibleChange},model:{value:e.refundModal,callback:function(t){e.refundModal=t},expression:"refundModal"}},[s("Form",{attrs:{"label-width":100}},[s("FormItem",{attrs:{label:"退款金额：",required:""}},[s("InputNumber",{staticStyle:{width:"408px"},model:{value:e.refundMoney,callback:function(t){e.refundMoney=t},expression:"refundMoney"}})],1),e.refundProductNum>1?s("FormItem",{attrs:{label:"分单退款："}},[s("i-switch",{attrs:{"true-value":1,"false-value":0,size:"large"},model:{value:e.isSplitOrder,callback:function(t){e.isSplitOrder=t},expression:"isSplitOrder"}},[s("span",{attrs:{slot:"open"},slot:"open"},[e._v("开启")]),s("span",{attrs:{slot:"close"},slot:"close"},[e._v("关闭")])]),s("div",{staticClass:"tips"},[e._v("可选择表格中的商品单独退款，退款后且不能撤回，请谨慎操作！")]),s("Table",{directives:[{name:"show",rawName:"v-show",value:e.isSplitOrder,expression:"isSplitOrder"}],ref:"refundTable",attrs:{"max-height":"500",columns:e.refundColumns,data:e.refundProduct},on:{"on-selection-change":e.selectionChange},scopedSlots:e._u([{key:"product",fn:function(t){var r=t.row;return[s("div",{directives:[{name:"viewer",rawName:"v-viewer",value:{navbar:!1,toolbar:!1},expression:"{navbar:false,toolbar:false}"}],staticClass:"image-wrap"},[s("img",{staticClass:"image",attrs:{src:r.productInfo.attrInfo.image}})]),s("div",{staticClass:"title"},[e._v(e._s(r.productInfo.store_name))])]}},{key:"action",fn:function(t){var r=t.row,a=t.index;return[s("InputNumber",{attrs:{max:r.cart_num-r.refund_num,min:1,precision:0,"controls-outside":""},on:{"on-change":e.numChange},model:{value:e.refundProduct[a].refundNum,callback:function(t){e.$set(e.refundProduct[a],"refundNum",t)},expression:"refundProduct[index].refundNum"}})]}}],null,!1,1422384662)})],1):e._e()],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("Button",{on:{click:e.cancelRefundModal}},[e._v("取消")]),s("Button",{attrs:{type:"primary"},on:{click:e.submitRefund}},[e._v("提交")])],1)],1)],1)},a=[],i=r("d487"),n=r("9568"),o=r("ea47"),c=r("bd57"),l=r("6dc2"),d=r("22f89"),u=r("a464"),m=r("a123"),f=r("f8b7");function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,s)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(r,!0).forEach((function(t){_(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var v={components:{orderList:i["a"],goodsList:n["a"],userOrder:o["a"],orderDetails:c["a"],orderRemark:d["a"],orderRecord:l["a"],filterModal:u["a"],orderSend:m["a"]},data:function(){return{orderId:0,orderListData:[],tabs:["商品信息","订单详情","订单记录"],sle:0,filterModal:!1,userFrom:{keyword:"",page:1,limit:9},orderData:{type:"",status:"",time:"",staff_id:"",real_name:"",page:1,limit:7},selectOrderData:{},orderInfoData:{},count:0,status:0,pay_type:"",canSend:!0,tengxun_map_key:"",open_erp:null,confirmVisible:!1,refundModal:!1,refundMoney:0,isSplitOrder:0,refundColumns:[{type:"selection",width:60,align:"center"},{title:"商品信息",width:210,slot:"product"},{title:"规格",render:function(e,t){return e("div",t.row.productInfo.attrInfo.suk)}},{title:"售价",render:function(e,t){return e("div",t.row.productInfo.attrInfo.price)}},{title:"优惠价",key:"refundPrice"},{title:"总数",key:"cart_num"},{title:"退款数量",slot:"action"}],refundProduct:[],refundSelection:[]}},computed:{refundProductNum:function(){var e=this;return this.refundProduct.reduce((function(t,r){var s=r.refundNum;return e.$computes.Add(t,s)}),0)},productSelection:function(){var e=new Map(this.refundProduct.map((function(e){return[e.id,e]})));return this.refundSelection.filter((function(t){return e.has(t.id)})).map((function(t){return e.get(t.id)}))}},watch:{refundSelection:{handler:function(e){var t=this;this.refundMoney=e.reduce((function(e,r){var s=r.refundPrice,a=r.refundNum;return t.$computes.Add(e,t.$computes.Mul(s,a))}),0)},deep:!0},isSplitOrder:function(e){var t=this;this.$nextTick((function(){t.$refs.refundTable.selectAll(!!e)}))}},created:function(){this.getOrderList(),this.getErpConfig()},methods:{numberChange:function(e){var t=this;this.$nextTick((function(){var r=Number(e).toString(),s=r.indexOf(".");-1===s||2>=r.length-s-1?t.refundProduct.refund_price=Number(r):t.refundProduct.refund_price=Number(Number(r).toFixed(2))}))},openConfirm:function(){this.confirmVisible=!0},payOffline:function(){var e=this;Object(f["S"])(this.selectOrderData.id).then((function(){e.$Message.success("支付成功"),e.reloading=!0,e.limitTemp=e.orderData.limit,e.pageTemp=e.orderData.page,e.orderData.limit*=e.orderData.page,e.orderData.page=1,e.getOrderList()})).catch((function(t){e.$Message.error(t.msg)}))},orderSend:function(){var e=this;this.$store.commit("store/order/setSplitOrder",this.selectOrderData.total_num),this.$refs.send.modals=!0,this.orderId=this.selectOrderData.id,this.status=this.selectOrderData._status,this.pay_type=this.selectOrderData.pay_type,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(t){e.$refs.send.getCartInfo(e.selectOrderData._status,e.selectOrderData.id)}))},addPage:function(){this.orderListData.length<this.count&&this.orderData.page++,this.getOrderList()},searchList:function(e){this.filterModal=!1,this.orderData.page=1,this.orderData=h({},this.orderData,{},e),this.orderListData=[],this.sle=0,this.search()},point:function(){var e=this;this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(this.selectOrderData.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))},search:function(){this.orderData.page=1,this.orderListData=[],this.selectOrderData={},this.getOrderList()},remarks:function(){this.$refs.remarks.modals=!0,this.$refs.remarks.formValidate.remark=this.selectOrderData.remark},send:function(){this.canSend=!1,this.search()},submitFail:function(){},getRefundData:function(){var e=this;if(2===this.selectOrderData.refund_type)this.delfromData={title:"是否立即退货",url:"/refund/agree/".concat(this.selectOrderData.id),method:"get"},this.$modalSure(this.delfromData).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}));else{var t=Object.values(this.selectOrderData._info).map((function(t){var r=t.cart_info,s=r.cart_num-r.refund_num;return h({},r,{refundPrice:e.$computes.Div(r.refund_price,r.cart_num),refundNum:s,_disabled:!s})}));this.refundProduct=t,1===this.refundProductNum&&(this.refundSelection=t),this.refundModal=!0}},getOrderInfo:function(e){var t=this;e&&Object(f["B"])(e).then((function(e){t.orderInfoData=e.data})).catch((function(e){t.$Message.error(e.msg)}))},selectOrder:function(e){var t=this;this.sle=0,this.selectOrderData={},this.$nextTick((function(){t.selectOrderData=e}))},tabClick:function(e){switch(e){case 1:this.getOrderInfo(this.selectOrderData.id)}this.sle=e},getOrderList:function(){var e=this;Object(f["C"])(this.orderData).then((function(t){t.data.list=t.data.list.map((function(t){var r=[];for(var s in t._info){var a=t._info[s];r.push(a)}return e.$set(t,"_infoData",r),t})),e.count=t.data.count,e.orderListData=e.orderListData.concat(t.data.list),1==e.orderData.page&&(e.selectOrderData=e.orderListData[0]||{})})).catch((function(t){e.$Message.error(t.msg)}))},getErpConfig:function(){var e=this;Object(f["v"])().then((function(t){e.open_erp=t.data.open_erp,e.tengxun_map_key=t.data.tengxun_map_key})).catch((function(t){e.$Message.error(t.msg)}))},visibleChange:function(e){this.isSplitOrder=0,e||(this.refundSelection=[])},cancelRefundModal:function(){this.refundModal=!1},submitRefund:function(){var e=this,t={refund_price:this.refundMoney,is_split_order:this.isSplitOrder};if(this.isSplitOrder){if(!this.refundSelection.length)return this.$Message.warning("请选择需要退款的商品");t.cart_ids=this.productSelection.map((function(e){var t=e.id,r=e.refundNum;return{cart_id:t,cart_num:r}}))}Object(f["M"])(this.selectOrderData.id,t).then((function(t){e.$Message.success(t.msg),e.refundModal=!1,e.reloading=!0,e.limitTemp=e.orderData.limit,e.pageTemp=e.orderData.page,e.orderData.limit*=e.orderData.page,e.orderData.page=1,e.getOrderList()})).catch((function(t){e.$Message.error(t.msg)}))},selectionChange:function(e){this.refundSelection=e,this.refundMoneyCompute()},numChange:function(){this.refundMoneyCompute()},refundMoneyCompute:function(){var e=0,t=!0,r=!1,s=void 0;try{for(var a,i=this.productSelection[Symbol.iterator]();!(t=(a=i.next()).done);t=!0){var n=a.value;try{e=this.$computes.Add(e,this.$computes.Mul(n.refundNum,n.refundPrice))}catch(o){console.error("计算退款金额发生错误",o);break}}}catch(c){r=!0,s=c}finally{try{t||null==i.return||i.return()}finally{if(r)throw s}}this.refundMoney=e}}},y=v,g=(r("5a68"),r("2877")),b=Object(g["a"])(y,s,a,!1,null,"2f033556",null);t["default"]=b.exports},"5a68":function(e,t,r){"use strict";var s=r("942e"),a=r.n(s);a.a},7437:function(e,t,r){},"942e":function(e,t,r){},a123:function(e,t,r){"use strict";var s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,width:"1000"},on:{"on-visible-change":e.changeModal},model:{value:e.modals,callback:function(t){e.modals=t},expression:"modals"}},[e.modals?r("Form",{ref:"formItem",attrs:{model:e.formItem,"label-width":100},nativeOn:{submit:function(e){e.preventDefault()}}},[r("FormItem",{attrs:{label:"选择类型："}},[r("RadioGroup",{on:{"on-change":e.changeRadio},model:{value:e.formItem.type,callback:function(t){e.$set(e.formItem,"type",t)},expression:"formItem.type"}},[e.productType?e._e():r("Radio",{attrs:{label:"1"}},[e._v("发货")]),e.productType?e._e():r("Radio",{attrs:{label:"2"}},[e._v("送货")]),r("Radio",{attrs:{label:"3"}},[e._v("无需配送")])],1)],1),1==e.formItem.type?r("FormItem",{directives:[{name:"show",rawName:"v-show",value:e.export_open,expression:"export_open"}],attrs:{label:"发货类型："}},[r("RadioGroup",{on:{"on-change":e.changeExpress},model:{value:e.formItem.express_record_type,callback:function(t){e.$set(e.formItem,"express_record_type",t)},expression:"formItem.express_record_type"}},[r("Radio",{attrs:{label:"1"}},[e._v("手动填写")]),r("Radio",{attrs:{label:"2"}},[e._v("电子面单打印")])],1)],1):e._e(),r("div",[1==e.formItem.type?r("FormItem",{attrs:{label:"快递公司："}},[r("Select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":e.expressChange},model:{value:e.formItem.delivery_name,callback:function(t){e.$set(e.formItem,"delivery_name",t)},expression:"formItem.delivery_name"}},e._l(e.express,(function(t,s){return r("Option",{key:t.value,attrs:{value:t.value}},[e._v(e._s(t.value)+"\n          ")])})),1)],1):e._e(),"1"===e.formItem.express_record_type&&1==e.formItem.type?r("FormItem",{attrs:{label:"快递单号："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:e.formItem.delivery_id,callback:function(t){e.$set(e.formItem,"delivery_id",t)},expression:"formItem.delivery_id"}}),"顺丰速运"==e.formItem.delivery_name?r("div",{staticClass:"trips"},[r("p",[e._v("顺丰请输入单号 :收件人或寄件人手机号后四位，")]),r("p",[e._v("例如：SF000000000000:3941")])]):e._e()],1):e._e(),"2"===e.formItem.express_record_type?[r("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单："}},[r("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择电子面单"},on:{"on-change":e.expressTempChange},model:{value:e.formItem.express_temp_id,callback:function(t){e.$set(e.formItem,"express_temp_id",t)},expression:"formItem.express_temp_id"}},e._l(e.expressTemp,(function(t,s){return r("Option",{key:s,attrs:{value:t.temp_id}},[e._v(e._s(t.title)+"\n            ")])})),1),e.formItem.express_temp_id?r("Button",{attrs:{type:"text"},on:{click:e.preview}},[e._v("预览")]):e._e()],1),r("FormItem",{attrs:{label:"寄件人姓名："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:e.formItem.to_name,callback:function(t){e.$set(e.formItem,"to_name",t)},expression:"formItem.to_name"}})],1),r("FormItem",{attrs:{label:"寄件人电话："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:e.formItem.to_tel,callback:function(t){e.$set(e.formItem,"to_tel",t)},expression:"formItem.to_tel"}})],1),r("FormItem",{attrs:{label:"寄件人地址："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:e.formItem.to_addr,callback:function(t){e.$set(e.formItem,"to_addr",t)},expression:"formItem.to_addr"}})],1)]:e._e()],2),r("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.formItem.type,expression:"formItem.type === '2'"}]},[r("FormItem",{attrs:{label:"送货人："}},[r("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择送货人"},on:{"on-change":e.shDeliveryChange},model:{value:e.formItem.sh_delivery,callback:function(t){e.$set(e.formItem,"sh_delivery",t)},expression:"formItem.sh_delivery"}},e._l(e.deliveryList,(function(t,s){return r("Option",{key:s,attrs:{value:t.id}},[e._v("\n            "+e._s(t.wx_name)+"（"+e._s(t.phone)+"）\n          ")])})),1)],1)],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.formItem.type,expression:"formItem.type === '3'"}]},[r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticStyle:{width:"80%"},attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:e.formItem.fictitious_content,callback:function(t){e.$set(e.formItem,"fictitious_content",t)},expression:"formItem.fictitious_content"}})],1)],1),e.splitOrder>1&&"3"!==e.formItem.type?r("div",[r("FormItem",{attrs:{label:"分单发货："}},[r("i-switch",{attrs:{size:"large",disabled:8===e.orderStatus},on:{"on-change":e.changeSplitStatus},model:{value:e.splitSwitch,callback:function(t){e.splitSwitch=t},expression:"splitSwitch"}},[r("span",{attrs:{slot:"open"},slot:"open"},[e._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[e._v("关闭")])]),r("div",{staticClass:"trips"},[r("p",[e._v("\n            可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n          ")])]),e.splitSwitch&&e.manyFormValidate.length?r("Table",{attrs:{data:e.manyFormValidate,columns:e.header},on:{"on-selection-change":e.selectOne},scopedSlots:e._u([{key:"image",fn:function(t){var s=t.row;t.index;return[r("div",{staticClass:"product-data"},[r("img",{staticClass:"image",attrs:{src:s.cart_info.productInfo.image}}),r("div",{staticClass:"name line2"},[e._v("\n                "+e._s(s.cart_info.productInfo.store_name)+"\n              ")])])]}},{key:"value",fn:function(t){var s=t.row;t.index;return[r("div",[e._v(e._s(s.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(t){var s=t.row;t.index;return[r("div",[e._v(e._s(s.cart_info.productInfo.attrInfo?s.cart_info.productInfo.attrInfo.price:s.cart_info.productInfo.price)+"\n            ")])]}},{key:"price",fn:function(t){var s=t.row;t.index;return[r("div",[e._v(e._s(s.cart_info.truePrice))])]}}],null,!1,409803649)}):e._e()],1)],1):e._e()],1):e._e(),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:e.cancel}},[e._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:e.putSend}},[e._v("提交")])],1),r("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:e.temp,expression:"temp"}],ref:"viewer"},[r("img",{staticStyle:{display:"none"},attrs:{src:e.temp.pic}})])],1)},a=[],i=r("a34a"),n=r.n(i),o=r("2f62"),c=r("f8b7");function l(e,t,r,s,a,i,n){try{var o=e[i](n),c=o.value}catch(l){return void r(l)}o.done?t(c):Promise.resolve(c).then(s,a)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(s,a){var i=e.apply(t,r);function n(e){l(i,s,a,n,o,"next",e)}function o(e){l(i,s,a,n,o,"throw",e)}n(void 0)}))}}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,s)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(r,!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p={name:"orderSend",props:{orderId:Number,status:Number,pay_type:String},data:function(){var e=this;return{productType:0,orderStatus:0,splitSwitch:!0,formItem:{type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0,manyFormValidate:[],header:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"image",align:"center",width:200},{title:"规格",slot:"value",align:"center",minWidth:120},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:100},{title:"商品优惠价",slot:"price",align:"center",minWidth:100},{title:"总数",key:"cart_num",align:"center",minWidth:80},{title:"待发数量",key:"surplus_num",align:"center",width:180,render:function(t,r){return t("div",[t("InputNumber",{props:{min:1,max:r.row.numShow,value:r.row.surplus_num||1},on:{"on-change":function(t){r.row.surplus_num=t||1,e.manyFormValidate[r.index]=r.row,e.selectData.forEach((function(t,s){t.cart_id===r.row.cart_id&&e.selectData.splice(s,1,r.row)}))}}})])}}],selectData:[]}},computed:m({},Object(o["e"])("store/order",["splitOrder"])),methods:{selectOne:function(e){this.selectData=e},changeModal:function(e){e||this.cancel()},changeSplitStatus:function(e){var t=this;e&&Object(c["fb"])(this.orderId).then((function(e){var r=e.data;r.forEach((function(e){e.numShow=e.surplus_num})),t.manyFormValidate=r}))},changeRadio:function(e){switch(this.$refs.formItem.resetFields(),e){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="1",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="";break;case"3":this.formItem.fictitious_content="";break;default:break}},changeExpress:function(e){switch(e){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[],this.getList(2);break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.getList(1);break;default:break}},reset:function(){this.formItem={type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""}},getList:function(e){var t=this,r=2===e?1:"";Object(c["y"])(r).then(function(){var e=d(n.a.mark((function e(r){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.express=r.data,t.getSheetInfo();case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},putSend:function(e){var t=this,r={id:this.orderId,datas:this.formItem};if("1"===this.formItem.type&&"2"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("1"===this.formItem.type&&"1"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.delivery_id)return this.$Message.error("快递单号不能为空")}if("2"===this.formItem.type&&""===this.formItem.sh_delivery)return this.$Message.error("送货人不能为空");this.splitSwitch&&(r.datas.cart_ids=[],this.selectData.forEach((function(e){r.datas.cart_ids.push({cart_id:e.cart_id,cart_num:e.surplus_num})}))),Object(c["Z"])(r).then((function(e){t.modals=!1,t.$Message.success(e.msg),t.$emit("submitFail"),t.reset(),t.splitSwitch=!1})).catch((function(e){t.$Message.error(e.msg)}))},cancel:function(e){this.modals=!1,this.orderStatus=0,this.splitSwitch=!1,this.selectData=[],this.reset()},expressChange:function(e){var t=this,r=this.express.find((function(t){return t.value===e}));void 0!==r&&(this.formItem.delivery_code=r.code,"2"===this.formItem.express_record_type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(c["O"])({com:this.formItem.delivery_code}).then((function(e){t.expressTemp=e.data,e.data.length||t.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(e){t.$Message.error(e.msg)}))))},getCartInfo:function(e,t){var r=this;this.$set(this,"orderStatus",e),this.$set(this,"splitSwitch",8===e),Object(c["fb"])(this.orderId).then((function(e){var t=e.data;t.forEach((function(e){e.numShow=e.surplus_num})),r.manyFormValidate=t,r.productType=t[0].product_type,3==r.productType&&(r.formItem.type="3",r.formItem.fictitious_content="")}))},getDeliveryList:function(){var e=this;Object(c["N"])().then((function(t){e.deliveryList=t.data.list})).catch((function(t){e.$Message.error(t.msg)}))},getSheetInfo:function(){var e=this;Object(c["Q"])().then((function(t){var r=t.data;for(var s in r)r.hasOwnProperty(s)&&(e.formItem[s]=r[s]);e.export_open=void 0===r.export_open||r.export_open,e.export_open||(e.formItem.express_record_type="1"),e.formItem.to_addr=r.to_add})).catch((function(t){e.$Message.error(t.msg)}))},shDeliveryChange:function(e){if(e){var t=this.deliveryList.find((function(t){return t.id===e}));this.formItem.sh_delivery_name=t.wx_name,this.formItem.sh_delivery_id=t.phone,this.formItem.sh_delivery_uid=t.uid}},expressTempChange:function(e){this.temp=this.expressTemp.find((function(t){return e===t.temp_id})),void 0===this.temp&&(this.temp={})},preview:function(){this.$refs.viewer.$viewer.show()}}},h=p,_=(r("1dd0"),r("2877")),v=Object(_["a"])(h,s,a,!1,null,"cd104c5a",null);t["a"]=v.exports}}]);