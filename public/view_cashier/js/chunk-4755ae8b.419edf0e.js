(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4755ae8b"],{"16f5":function(t,a){t.exports="data:image/png;base64,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"},"29ba":function(t,a){t.exports="data:image/png;base64,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"},"58cd":function(t,a,s){"use strict";var i=s("c1bd"),e=s.n(i);e.a},"5f8d":function(t,a,s){},"653d":function(t,a,s){t.exports=s.p+"view_cashier/img/tourist.908b01d3.png"},bd57:function(t,a,s){"use strict";var i=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"detail"},[t.orderDatalist.orderInfo&&10!=t.orderDatalist.orderInfo.type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("收货信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("收货人：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),s("li",{staticClass:"item"},[s("div",[t._v("收货电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])]),s("li",{staticClass:"item"},[s("div",[t._v("收货地址：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_address))])])])]):t._e(),s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("用户信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("用户昵称：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.uid?t.orderDatalist.userInfo.nickname:"游客")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("绑定电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.userInfo.phone))])])])]),6==t.orderDatalist.orderInfo.product_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("预约信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("服务类型：")]),s("div",{staticClass:"value"},[t._v(t._s(3==t.orderDatalist.orderInfo.reservation_type?"上门服务":"到店服务"))])]),s("li",{staticClass:"item"},[s("div",[t._v("预约模式：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time_id>0?"购买时预约":"先买后约"))])]),t.orderDatalist.orderInfo.reservation_time?s("li",{staticClass:"item"},[s("div",[t._v("预约日期：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time))])]):t._e(),t.orderDatalist.orderInfo.reservation_show_time?s("li",{staticClass:"item"},[s("div",[t._v("预约时段：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_show_time))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("预约人：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),s("li",{staticClass:"item"},[s("div",[t._v("预约电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])])]),t.orderDatalist.orderInfo.user_address.trim()?s("div",{staticClass:"item"},[s("div",[t._v("预约地址：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_address))])]):t._e()]):t._e(),s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("订单信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("创建时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._add_time))])]),s("li",{staticClass:"item"},[s("div",[t._v("商品总数：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.total_num))])]),s("li",{staticClass:"item"},[s("div",[t._v("商品总价：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.total_price))])]),t.orderDatalist.orderInfo.first_order_price>0?s("li",{staticClass:"item"},[s("div",[t._v("首单优惠：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.first_order_price))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("优惠券金额：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.coupon_price))])]),s("li",{staticClass:"item"},[s("div",[t._v("积分抵扣：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.deduction_price||0)+"\n          ")])]),parseFloat(t.orderDatalist.orderInfo.use_integral)?s("li",{staticClass:"item"},[s("div",[t._v("使用积分：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(parseFloat(t.orderDatalist.orderInfo.use_integral))+"\n          ")])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("支付邮费：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.pay_postage))])]),s("li",{staticClass:"item"},[s("div",[t._v("会员商品优惠：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.vip_true_price||0)+"\n          ")])]),0!=t.orderDatalist.orderInfo.first_order_price?s("li",{staticClass:"item"},[s("div",[t._v("新人首单优惠：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.first_order_price)+"\n          ")])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?s("li",{staticClass:"item"},[s("div",[t._v("门店名称：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._store_name))])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?s("li",{staticClass:"item"},[s("div",[t._v("核销码：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.verify_code))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("推广人：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.spread_name)+"/ID:"+t._s(t.orderDatalist.userInfo.spread_uid)+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("支付时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._pay_time))])]),s("li",{staticClass:"item"},[s("div",[t._v("支付方式：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo._status._payType)+"\n          ")])]),t.orderDatalist.orderInfo.store_order_sn?s("li",{staticClass:"item"},[s("div",[t._v("原订单号：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.store_order_sn))])]):t._e()])]),t.orderDatalist.orderInfo.promotions_detail.length?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("活动信息")]),s("ul",{staticClass:"list"},t._l(t.orderDatalist.orderInfo.promotions_detail,(function(a,i){return s("li",{key:i,staticClass:"item"},[s("div",[t._v(t._s(a.title)+"：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(a.promotions_price))])])})),0)]):t._e(),t.isShow||6==t.orderDatalist.orderInfo.product_type&&t.orderDatalist.orderInfo.reservation_time_id>0||6!=t.orderDatalist.orderInfo.product_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("自定义留言")]),t._l(t.orderDatalist.orderInfo.custom_form,(function(a,i){return s("div",{key:i},[6==t.orderDatalist.orderInfo.product_type&&a.length?s("div",{staticClass:"item fs-14 mb10"},[t._v(t._s(t.orderDatalist.orderInfo.custom_form_title)+t._s(i+1))]):t._e(),s("ul",{staticClass:"list"},t._l(a,(function(a,i){return s("li",{directives:[{name:"show",rawName:"v-show",value:a.value&&-1==["uploadPicture","dateranges"].indexOf(a.name)||a.value.length&&-1!=["uploadPicture","dateranges"].indexOf(a.name),expression:"(item.value && ['uploadPicture','dateranges'].indexOf(item.name) == -1) || (item.value.length && ['uploadPicture','dateranges'].indexOf(item.name) != -1)"}],key:i,staticClass:"item"},[s("div",{staticClass:"txtVal"},[t._v(t._s(a.titleConfig.value)+"：")]),"dateranges"===a.name?s("div",{staticClass:"value"},[t._v(t._s(a.value[0]+"/"+a.value[1]))]):"uploadPicture"===a.name?s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"value"},t._l(a.value,(function(t,a){return s("div",{key:a,staticClass:"image"},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0):s("div",{staticClass:"value"},[t._v(t._s(a.value||"-"))])])})),0)])}))],2):t._e(),t.orderDatalist.orderInfo.mark?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("买家留言")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item mark"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.mark||"-"))])])])]):t._e(),t.orderDatalist.orderInfo.remark?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("订单备注")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item mark"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.remark||"-"))])])])]):t._e(),"express"===t.orderDatalist.orderInfo.delivery_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("物流信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("快递公司：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("快递单号：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_id)+"\n          ")])])])]):t._e(),"send"===t.orderDatalist.orderInfo.delivery_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("配送信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("送货人姓名：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("送货人电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.delivery_id))])])])]):t._e()])},e=[],r={name:"orderDetails",props:{orderDatalist:{type:Object}},data:function(){return{isShow:0}},watch:{orderDatalist:function(t){var a=this;t.orderInfo&&t.orderInfo.custom_form&&t.orderInfo.custom_form.length&&t.orderInfo.custom_form.forEach((function(t){t.length&&t.forEach((function(t){if(t.value)return a.isShow=1}))}))}},methods:{}},o=r,l=(s("58cd"),s("2877")),n=Object(l["a"])(o,i,e,!1,null,"869a80b2",null);a["a"]=n.exports},c1bd:function(t,a,s){},c95b:function(t,a,s){"use strict";var i=s("5f8d"),e=s.n(i);e.a},ea47:function(t,a,s){"use strict";var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"order-user"},[i("div",{staticClass:"sel-user"},[i("div",{staticClass:"avatar"},[i("img",{attrs:{src:t.selectData.avatar||s("653d"),alt:"头像"}})]),i("div",{staticClass:"item-right"},[i("div",{staticClass:"user"},[i("div",[t._v(t._s(t.selectData.uid?t.selectData.nickname:"游客"))])]),t.selectData.uid?i("div",{staticClass:"money"},[i("div",[t.selectData.phone?i("span",{staticClass:"pr20"},[t._v(t._s(t.selectData.phone))]):t._e(),t._v("余额 "),i("span",{staticClass:"num"},[t._v(t._s(t.selectData.now_money||0))])]),i("div",[t._v("\n          积分 "),i("span",{staticClass:"num"},[t._v(t._s(t.selectData.integral||0))])])]):t._e()])]),i("div",{staticClass:"cart-num"},[i("div",{staticClass:"cart-num-left"},[i("span",[t._v("共")]),i("span",{staticClass:"num"},[t._v(t._s(t.total_num))]),i("span",[t._v("件商品")])])]),i("div",{staticClass:"goods-list"},[i("Table",{attrs:{columns:t.columns,data:t.tableList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"info",fn:function(a){var s=a.row;return[i("div",{staticClass:"tabBox"},[i("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:s.productInfo.attrInfo?s.productInfo.attrInfo.image:s.productInfo.image,expression:"\n                row.productInfo.attrInfo\n                  ? row.productInfo.attrInfo.image\n                  : row.productInfo.image\n              "}]})]),i("span",{staticClass:"tabBox_tit line2"},[s.is_gift?i("span",{staticClass:"is-gift"},[t._v("赠品")]):t._e(),t._v("\n              "+t._s(s.productInfo.store_name)+"\n            "),s.productInfo.attrInfo?i("span",[t._v(" | \n              "+t._s(s.productInfo.attrInfo?s.productInfo.attrInfo.suk:"")+"\n            ")]):t._e()])])]}},{key:"sum_true_price",fn:function(a){var s=a.row;return[s.is_gift?i("div",{staticClass:"tabBox"},[t._v("-")]):i("div",{staticClass:"tabBox"},[t._v(t._s(11==t.selectData.type?s.price:s.sum_price*s.cart_num||0))])]}}])})],1)])},e=[],r=s("a34a"),o=s.n(r),l=s("9568"),n=s("f8b7");function d(t,a,s,i,e,r,o){try{var l=t[r](o),n=l.value}catch(d){return void s(d)}l.done?a(n):Promise.resolve(n).then(i,e)}function v(t){return function(){var a=this,s=arguments;return new Promise((function(i,e){var r=t.apply(a,s);function o(t){d(r,i,e,o,l,"next",t)}function l(t){d(r,i,e,o,l,"throw",t)}o(void 0)}))}}var c={name:"userOrder",components:{goodsList:l["a"]},props:["selectData"],data:function(){var t=this;return{columns:[{title:"商品信息",slot:"info",minWidth:200},{title:"单价",render:function(a,s){var i="-";return s.row.is_gift||(i=11==t.selectData.type?s.row.price:s.row.sum_price),a("div",i)}},{title:"数量",render:function(a,s){var i="-";return s.row.is_gift||(i=11==t.selectData.type?1:s.row.cart_num),a("div",i)}},{title:"总金额",slot:"sum_true_price",width:150}],tableList:[],loading:!1,give_integral_img:s("29ba"),give_coupon_img:s("16f5"),total_num:0}},created:function(){var t=v(o.a.mark((function t(){var a,s,i,e,r,l=this;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(11!=this.selectData.type){t.next=8;break}return t.next=3,Object(n["c"])(this.selectData.id);case 3:i=t.sent,e=i.data,a=e,t.next=9;break;case 8:a=Object.values(this.selectData._info);case 9:s=a.map((function(t){var a=t.cart_info;return a})),this.total_num=s.length,r=function(t,a){return{is_gift:1,productInfo:{image:t,store_name:a}}},this.selectData.give_integral&&s.push(r(this.give_integral_img,"赠送".concat(this.selectData.give_integral,"积分"))),this.selectData.give_coupon.forEach((function(t){s.push(r(l.give_coupon_img,t.coupon_title))})),this.tableList=s;case 15:case"end":return t.stop()}}),t,this)})));function a(){return t.apply(this,arguments)}return a}(),methods:{getRefundData:function(){this.$emit("getRefundData")},remarks:function(){this.$emit("remarks")},point:function(){var t=this;this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(this.selectData.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(a){t.$Message.success(a.msg)})).catch((function(a){t.$Message.error(a.msg)}))},orderSend:function(){this.$emit("orderSend")}}},u=c,A=(s("c95b"),s("2877")),m=Object(A["a"])(u,i,e,!1,null,"0e1bd477",null);a["a"]=m.exports}}]);