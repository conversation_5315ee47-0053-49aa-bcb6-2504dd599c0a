(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b5acbf50"],{"1de2":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("Card",{staticClass:"ivu-mt listbox",attrs:{bordered:!1,"dis-hover":""}},[a("Form",{ref:"userFrom",attrs:{model:t.userFrom,"label-width":t.labelWidth,"label-position":t.labelPosition},nativeOn:{submit:function(t){t.preventDefault()}}},[a("Row",{staticClass:"mart",attrs:{gutter:16}},[a("Col",{attrs:{span:"18"}},[a("FormItem",{attrs:{label:"用户搜索：",labelWidth:100,"label-for":"nickname"}},[a("Input",{staticStyle:{width:"50%"},attrs:{placeholder:"请输入ID或者手机号","element-id":"nickname",clearable:""},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}},[a("Select",{staticStyle:{width:"80px"},attrs:{slot:"prepend"},slot:"prepend",model:{value:t.userFrom.field_key,callback:function(e){t.$set(t.userFrom,"field_key",e)},expression:"userFrom.field_key"}},[a("Option",{attrs:{value:"all"}},[t._v("全部")]),a("Option",{attrs:{value:"uid"}},[t._v("ID")]),a("Option",{attrs:{value:"phone"}},[t._v("手机号")])],1)],1)],1)],1),a("Col",{staticClass:"ivu-text-right userFrom",attrs:{span:"6"}},[a("FormItem",[a("Button",{staticClass:"mr15",attrs:{type:"primary",icon:"ios-search",label:"default"},on:{click:t.userSearchs}},[t._v("搜索")]),a("Button",{staticClass:"ResetSearch",on:{click:function(e){return t.reset("userFrom")}}},[t._v("重置")])],1)],1)],1)],1)],1),a("Card",{staticClass:"ivu-mt",attrs:{bordered:!1,"dis-hover":""}},[a("Row",{attrs:{type:"flex",justify:"space-between"}},[a("Col",{attrs:{span:"24"}},[a("Button",{staticClass:"button",attrs:{disabled:t.datanew.length<=0},on:{click:t.setLabel}},[t._v("批量设置标签")])],1),a("Col",{attrs:{span:"24"}},[a("Table",{ref:"selection",staticClass:"ivu-mt",attrs:{columns:t.columns,data:t.dataList,loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"},on:{"on-select-all":t.selectall,"on-select-all-cancel":t.selectall,"on-sort-change":t.sortChanged,"on-selection-change":t.select},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"nickname",fn:function(e){var i=e.row;e.index;return[a("div",{staticClass:"acea-row"},[a("Icon",{directives:[{name:"show",rawName:"v-show",value:"男"===i.sex,expression:"row.sex === '男'"}],staticClass:"mr5",attrs:{type:"md-male",color:"#2db7f5",size:"15"}}),a("Icon",{directives:[{name:"show",rawName:"v-show",value:"女"===i.sex,expression:"row.sex === '女'"}],staticClass:"mr5",attrs:{type:"md-female",color:"#ed4014",size:"15"}}),a("div",{domProps:{textContent:t._s(i.nickname)}})],1)]}},{key:"isMember",fn:function(e){var i=e.row;e.index;return[a("div",[t._v(t._s(i.isMember?"是":"否"))])]}},{key:"action",fn:function(e){var i=e.row;e.index;return[a("a",{on:{click:function(e){return t.detail(i)}}},[t._v("详情")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.recharge(i)}}},[t._v("充值")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.paying(i)}}},[t._v("付费会员")]),a("Divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(e){return t.openLabel(i)}}},[t._v("设置标签")])]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{staticClass:"box",attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1),a("user-details",{ref:"userDetails"}),a("Recharges",{ref:"recharges"}),a("Paying",{ref:"payings"}),a("Setusers",{ref:"setusers"}),a("Modal",{attrs:{scrollable:"",title:"请选择用户标签",closable:!1,width:"320","footer-hide":!0},model:{value:t.labelShow,callback:function(e){t.labelShow=e},expression:"labelShow"}},[a("userLabel",{attrs:{uid:t.labelActive.uid},on:{close:t.labelClose}})],1)],1)},r=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"修改店员","mask-closable":!1,width:"900"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("Table",{ref:"selection",attrs:{loading:t.loading,"highlight-row":"","no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果",columns:t.columns,data:t.tableList},scopedSlots:t._u([{key:"avatars",fn:function(t){var e=t.row;t.index;return[a("viewer",[a("div",{staticClass:"tabBox_img"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.avatar,expression:"row.avatar"}]})])])]}},{key:"status",fn:function(e){var i=e.row;e.index;return[1==i.status?a("span",[t._v("开启")]):t._e(),0==i.status?a("span",[t._v("关闭")]):t._e()]}}])}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.formValidate.page,"page-size":t.formValidate.limit},on:{"on-change":t.pageChange}})],1)],1)],1)},s=[],o=a("b4ea"),l=a("c24f"),c={name:"setusers",data:function(){var t=this;return{modals:!1,total:0,formValidate:{page:1,limit:15},loading:!1,tableList:[],currentChoose:"",id:"",columns:[{title:"选择",key:"id",width:70,align:"center",render:function(e,a){var i=a.row.id,r=!1;t.currentChoose===i?(t.current(a.row),r=!0):r=!1;var n=t;return e("div",[e("Radio",{props:{value:r},on:{"on-change":function(){n.currentChoose=i,t.$parent.getList()}}})])}},{title:"头像",slot:"avatars",Width:60},{title:"微信昵称",key:"staff_name",minWidth:80},{title:"手机号",key:"phone",minWidth:100},{title:"账号状态",slot:"status",minWidth:80}]}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Object(o["b"])(this.formValidate).then((function(e){t.tableList=e.data.list,t.total=e.data.count}))},getId:function(t){this.id=t},cancel:function(){this.$Message.info("已取消"),this.currentChoose=""},pageChange:function(t){this.formValidate.page=t,this.getList()},current:function(t){var e=this,a={uid:this.id,staff_id:t.id};Object(l["o"])(a).then((function(t){e.modals=!1,e.$Message.success(t.msg),e.currentChoose="",e.$parent.getList()})).catch((function(t){e.modals=!1,e.$Message.error(t.msg),e.currentChoose=""}))}}},u=c,d=(a("70d7"),a("2877")),h=Object(d["a"])(u,n,s,!1,null,"7db8335b",null),f=h.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户充值","mask-closable":!1,width:"583"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"header_tab left",class:1==t.tabs?"on":"",on:{click:function(e){return t.tab(1)}}},[t._v("优惠充值")]),a("div",{staticClass:"header_tab",class:2==t.tabs?"on":"",on:{click:function(e){return t.tab(2)}}},[t._v("自定义充值")])]),1==t.tabs?a("div",{staticClass:"content"},t._l(t.data,(function(e,i){return a("div",{staticClass:"content_box",class:t.contTabs==i?"content_tab":"",on:{click:function(a){return t.contTab(e,i)}}},[a("div",{staticClass:"top"},[t._v("¥"+t._s(e.price))]),a("div",{staticClass:"bottom"},[t._v("额外赠送：¥ "+t._s(e.give_money))])])})),0):a("div",{staticClass:"contents"},[a("div",[a("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"0.00",type:"number"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("div",{staticClass:"title"},[t._v("自定义充值的金额无赠送优惠")])],1)]),a("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticClass:"footer",attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"付款码详情","mask-closable":!1,width:"400"},on:{"on-cancel":t.cancel},model:{value:t.qr,callback:function(e){t.qr=e},expression:"qr"}},[a("div",{staticClass:"qrbox"},[a("div",{ref:"qrCodeUrl",staticClass:"qrcode"}),t.qrtip?t._e():a("div",[t._v("微信扫码充值，将于 "+t._s(t.$moment(1e3*t.codeKey.invalid).format("YYYY-MM-DD H:mm:ss"))+" 过期")]),t.qrtip?a("div",{staticClass:"tips"},[t._v("若已充值，联系客服")]):t._e(),t.qrtip?a("div",{staticClass:"qrtips"},[t._v("已失效，请重新刷新")]):t._e()])])],1)},g=[],p=a("d044"),v=a.n(p),b={name:"userDetails",data:function(){return{codeKey:"",value:"",modals:!1,tabs:1,contTabs:0,data:"",tabdata:"",id:"",qr:!1,timeNum:0,qrtip:!1}},mounted:function(){},methods:{creatQrCode:function(){var t=this.codeKey.code_url;new v.a(this.$refs.qrCodeUrl,{text:t,width:160,height:160,colorDark:"#000000",colorLight:"#ffffff",correctLevel:v.a.CorrectLevel.H})},tab:function(t){this.tabs=t},contTab:function(t,e){this.contTabs=e,this.tabdata=t},getList:function(t){var e=this;Object(l["v"])().then((function(a){e.data=a.data.recharge_quota,e.id=t,e.contTab(e.data[0],0)}))},save:function(){var t=this,e={uid:t.id,rechar_id:t.tabdata.id,price:t.tabdata.price};2==t.tabs&&(e.price=t.value,e.rechar_id=0),Object(l["w"])(e).then((function(e){t.modals=!1,t.codeKey=e.data.data.jsConfig,t.qr=!0,t.creatQrCode(),t.scanTime=setInterval((function(){if(t.timeNum++,t.timeNum>=60){t.timeNum=0,window.clearInterval(t.scanTime);var a=Date.parse(new Date)/1e3;a>=t.codeKey.invalid&&(t.qrtip=!0)}else t.getScanStatus(e.data.data.order_id)}),1e3),t.tabs=1,t.value=""})).catch((function(e){t.timeNum=0,window.clearInterval(t.scanTime),t.$Message.success(e.msg)}))},getScanStatus:function(t){var e=this,a=this;Object(l["c"])(1,{order_id:t,end_time:a.codeKey.invalid}).then((function(t){t.data.status&&(a.$parent.getList(),a.timeNum=0,a.qr=!1,a.qrtip=!1,window.clearInterval(a.scanTime),a.$Message.success("充值成功"),e.$refs.qrCodeUrl.innerHTML="")}))},cancel:function(){this.tabs=1,this.qr=!1,this.qrtip=!1,this.$refs.qrCodeUrl.innerHTML="",this.timeNum=0,window.clearInterval(this.scanTime)}},beforeDestroy:function(){this.timeNum=0,this.$refs.qrCodeUrl.innerHTML="",window.clearInterval(this.scanTime)}},_=b,w=(a("389a"),Object(d["a"])(_,m,g,!1,null,"34984312",null)),y=w.exports,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户充值","mask-closable":!1,width:"583"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[a("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"header_tab left",class:1==t.tabs?"on":"",on:{click:function(e){return t.tab(1)}}},[t._v("充值会员")])]),1==t.tabs?a("div",{staticClass:"content"},t._l(t.data,(function(e,i){return a("div",{staticClass:"content_box",class:t.contTabs==i?"content_tab":"",on:{click:function(a){return t.contTab(e,i)}}},[a("div",{staticClass:"top"},[t._v("¥"+t._s(e.pre_price))]),a("div",{staticClass:"bottom"},[t._v(t._s(e.title)+"原价：¥ "+t._s(e.price))])])})),0):t._e(),a("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticClass:"footer",attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"付款码详情","mask-closable":!1,width:"400"},on:{"on-cancel":t.cancel},model:{value:t.qr,callback:function(e){t.qr=e},expression:"qr"}},[a("div",{staticClass:"qrbox"},[a("div",{ref:"qrCodeUrl",staticClass:"qrcode"}),t.qrtip?t._e():a("div",[t._v("微信扫码充值，将于 "+t._s(t.$moment(1e3*t.codeKey.invalid).format("YYYY-MM-DD H:mm:ss"))+" 过期")]),t.qrtip?a("div",{staticClass:"tips"},[t._v("若已充值，联系客服")]):t._e(),t.qrtip?a("div",{staticClass:"qrtips"},[t._v("已失效，请重新刷新")]):t._e()])])],1)},k=[],L={name:"userDetails",data:function(){return{codeKey:"",value:"",modals:!1,tabs:1,contTabs:0,data:"",tabdata:"",id:"",qr:!1,timeNum:0,qrtip:!1}},mounted:function(){},methods:{creatQrCode:function(){var t=this.codeKey.code_url;new v.a(this.$refs.qrCodeUrl,{text:t,width:160,height:160,colorDark:"#000000",colorLight:"#ffffff",correctLevel:v.a.CorrectLevel.H})},tab:function(t){this.tabs=t},contTab:function(t,e){this.contTabs=e,this.tabdata=t},getList:function(t){var e=this;Object(l["B"])().then((function(a){e.data=a.data,e.id=t,e.contTab(e.data[0],0)}))},save:function(){var t=this,e=this,a={uid:this.id,member_id:this.tabdata.id};Object(l["A"])(a).then((function(a){"领取成功"==a.msg?(t.$Message.success(a.msg),t.$parent.getList(),t.modals=!1):(t.modals=!1,t.codeKey=a.data.result.jsConfig,t.qr=!0,t.creatQrCode(),t.scanTime=setInterval((function(){if(t.timeNum++,t.timeNum>=60){t.timeNum=0,window.clearInterval(t.scanTime);var i=Date.parse(new Date)/1e3;i>=t.codeKey.invalid&&(e.qrtip=!0)}else t.getScanStatus(a.data.result.order_id)}),1e3),t.tabs=1,t.value="")})).catch((function(e){t.timeNum=0,window.clearInterval(t.scanTime),t.$Message.error(e.msg)}))},getScanStatus:function(t){var e=this;Object(l["c"])(2,{order_id:t,end_time:this.codeKey.invalid}).then((function(t){t.data.status&&(e.$parent.getList(),e.timeNum=0,window.clearInterval(e.scanTime),e.qr=!1,e.qrtip=!1,e.$Message.success("充值成功"),e.$refs.qrCodeUrl.innerHTML="")}))},cancel:function(){this.tabs=1,this.qr=!1,this.qrtip=!1,this.$refs.qrCodeUrl.innerHTML="",this.timeNum=0,window.clearInterval(this.scanTime)}},beforeDestroy:function(){this.timeNum=0,this.$refs.qrCodeUrl.innerHTML="",window.clearInterval(this.scanTime)}},D=L,T=(a("681df"),Object(d["a"])(D,C,k,!1,null,"424103fe",null)),O=T.exports,A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户详情","mask-closable":!1,width:"900"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.spinShow?a("Spin",{attrs:{size:"large",fix:""}}):t._e(),a("div",{staticClass:"acea-row"},[a("div",{staticClass:"avatar mr15"},[a("img",{attrs:{src:t.psInfo.avatar}})]),a("div",{staticClass:"dashboard-workplace-header-tip"},[a("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:t._s(t.psInfo.nickname||"-")}}),a("div",{staticClass:"dashboard-workplace-header-tip-desc"},t._l(t.detailsData,(function(e,i){return a("span",{key:i,staticClass:"dashboard-workplace-header-tip-desc-sp"},[t._v(t._s(e.title+"："+e.value))])})),0)])]),a("Row",{staticClass:"mt25",attrs:{type:"flex",justify:"space-between"}},[a("Col",{staticClass:"user_menu",attrs:{span:"4"}},[a("Menu",{attrs:{theme:t.theme2,"active-name":t.activeName},on:{"on-select":t.changeType}},t._l(t.list,(function(e,i){return a("MenuItem",{key:i,attrs:{name:e.val}},[t._v("\n                        "+t._s(e.label)+"\n                    ")])})),1)],1),a("Col",{attrs:{span:"20"}},[a("Table",{ref:"table",attrs:{columns:t.columns,data:t.userLists,"max-height":"400",loading:t.loading,"no-userFrom-text":"暂无数据","no-filtered-userFrom-text":"暂无筛选结果"}}),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:t.total,current:t.userFrom.page,"show-elevator":"","show-total":"","page-size":t.userFrom.limit},on:{"on-change":t.pageChange}})],1)],1)],1)],1)],1)},E=[],M=a("a34a"),I=a.n(M);function P(t,e,a,i,r,n,s){try{var o=t[n](s),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(i,r)}function x(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var n=t.apply(e,a);function s(t){P(n,i,r,s,o,"next",t)}function o(t){P(n,i,r,s,o,"throw",t)}s(void 0)}))}}var B={name:"userDetails",data:function(){return{theme2:"light",list:[{val:"order",label:"消费记录"},{val:"integral",label:"积分明细"},{val:"sign",label:"签到记录"},{val:"coupon",label:"持有优惠券"},{val:"balance_change",label:"余额变动"},{val:"spread",label:"好友关系"}],modals:!1,spinShow:!1,detailsData:[],userId:0,loading:!1,userFrom:{type:"order",page:1,limit:20},total:0,columns:[],userLists:[],psInfo:{},activeName:"order"}},created:function(){},methods:{getDetails:function(t){var e=this;this.activeName="order",this.userId=t,this.spinShow=!0,Object(l["d"])(t).then(function(){var t=x(I.a.mark((function t(a){var i;return I.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:200===a.status?(i=a.data,e.detailsData=i.headerList,e.psInfo=i.ps_info,e.changeType(e.activeName,1),e.spinShow=!1):(e.spinShow=!1,e.$Message.error(a.msg));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.spinShow=!1,e.$Message.error(t.msg)}))},pageChange:function(t){this.userFrom.page=t,this.changeType(this.userFrom.type,t)},changeType:function(t,e){var a=this;this.loading=!0,this.userFrom.type=t,this.activeName=t,""===this.userFrom.type&&(this.userFrom.type="order");var i={id:this.userId,datas:this.userFrom};e||(e=1),this.userFrom.page=e,Object(l["h"])(i).then(function(){var t=x(I.a.mark((function t(e){var i;return I.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(200!==e.status){t.next=22;break}i=e.data,a.userLists=i.list,a.total=i.count,a.loading=!1,t.t0=a.userFrom.type,t.next="order"===t.t0?8:"integral"===t.t0?10:"sign"===t.t0?12:"coupon"===t.t0?14:"balance_change"===t.t0?16:18;break;case 8:return a.columns=[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"商品总价",key:"total_price",minWidth:110},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],t.abrupt("break",19);case 10:return a.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"number",minWidth:120},{title:"变化前积分",key:"balance",minWidth:120},{title:"日期",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",19);case 12:return a.columns=[{title:"动作",key:"title",minWidth:120},{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",19);case 14:return a.columns=[{title:"优惠券名称",key:"coupon_title",minWidth:120},{title:"面值",key:"coupon_price",minWidth:120},{title:"有效期(天)",key:"coupon_time",minWidth:120},{title:"领取时间",key:"_add_time",minWidth:120}],t.abrupt("break",19);case 16:return a.columns=[{title:"动作",key:"title",minWidth:120},{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],t.abrupt("break",19);case 18:a.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"type",minWidth:120},{title:"加入时间",key:"add_time",minWidth:120}];case 19:a.loading=!1,t.next=24;break;case 22:a.loading=!1,a.$Message.error(e.msg);case 24:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){a.loading=!1,a.$Message.error(t.msg)}))}}},N=B,S=(a("f164"),a("9e4a"),Object(d["a"])(N,A,E,!1,null,"77a7a972",null)),j=S.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"label-wrapper"},[t.labelList.length?a("div",{staticClass:"list-box"},t._l(t.labelList,(function(e,i){return a("div",{key:i,staticClass:"label-box"},[a("div",{staticClass:"title"},[t._v(t._s(e.name))]),a("div",{staticClass:"list"},t._l(e.label,(function(e,i){return a("div",{key:i,staticClass:"label-item",class:{on:e.disabled},on:{click:function(a){return t.selectLabel(e)}}},[t._v(t._s(e.label_name))])})),0)])})),0):a("div",[t._v("暂无标签")]),a("div",{staticClass:"footer"},[a("Button",{staticClass:"btns",attrs:{type:"primary"},on:{click:t.subBtn}},[t._v("确定")]),a("Button",{staticClass:"btns",attrs:{type:"primary",ghost:""},on:{click:t.cancel}},[t._v("取消")])],1)])},F=[],$={name:"userLabel",props:{uid:{type:String|Number,default:""}},data:function(){return{labelList:[],activeIds:[],isUser:!1}},watch:{uid:{handler:function(t,e){t!=e&&this.getList()},deep:!0}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Object(l["f"])(this.uid).then((function(e){e.data.map((function(e){e.label.map((function(e){e.disabled&&t.activeIds.push(e.id)}))})),t.labelList=e.data}))},selectLabel:function(t){if(t.disabled){var e=this.activeIds.indexOf(t.id);this.activeIds.splice(e,1),t.disabled=!1}else this.activeIds.push(t.id),t.disabled=!0},subBtn:function(){var t=this,e=[];this.labelList.map((function(t){t.label.map((function(t){0==t.disabled&&e.push(t.id)}))})),this.$emit("close"),Object(l["m"])(this.uid,{label_ids:this.activeIds,un_label_ids:e}).then((function(e){t.activeIds=[],t.labelList=[],t.$Message.success(e.msg),t.$emit("close")})).catch((function(e){t.$Message.error(e.msg)}))},cancel:function(){this.activeIds=[],this.labelList=[],this.$emit("close")}}},W=$,q=(a("3531"),Object(d["a"])(W,R,F,!1,null,"245ca612",null)),H=q.exports,U=a("2f62");function K(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function G(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?K(a,!0).forEach((function(e){Y(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):K(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function Y(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var Q={name:"user",components:{userDetails:j,userLabel:H,Recharges:y,Setusers:f,Paying:O},data:function(){return{total:0,loading:!1,columns:[{type:"selection",width:60,align:"center"},{title:"ID",key:"uid",width:60},{title:"头像",slot:"avatars",minWidth:60},{title:"昵称",slot:"nickname",minWidth:150},{title:"付费会员",slot:"isMember",minWidth:90},{title:"用户等级",key:"level",minWidth:90},{title:"标签",key:"labels",minWidth:90},{title:"手机号",key:"phone",minWidth:100},{title:"用户类型",key:"user_type",minWidth:100},{title:"余额",key:"now_money",sortable:"custom",minWidth:100},{title:"关联店员",key:"staff_name",minWidth:100},{title:"操作",slot:"action",fixed:"right",minWidth:290,align:"center"}],dataList:[],datanew:[],dataid:[],userFrom:{keyword:"",page:1,limit:15,field_key:"all"},labelShow:!1,labelActive:{uid:0}}},computed:G({},Object(U["e"])("store/layout",["isMobile"]),{labelWidth:function(){return this.isMobile?void 0:120},labelPosition:function(){return this.isMobile?"top":"right"}}),mounted:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(l["u"])(this.userFrom).then((function(e){t.loading=!1,t.total=e.data.count,t.dataList=e.data.list})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},userSearchs:function(){var t=this;this.userFrom.page=1,""==this.userFrom.keyword?this.getList():(this.loading=!0,Object(l["C"])(this.userFrom).then((function(e){t.dataList=e.data.list,t.total=e.data.count,t.loading=!1})))},reset:function(t){this.userFrom={keyword:"",field_key:"all",page:1,limit:15},this.getList()},select:function(t){this.datanew=t;var e=[];this.datanew.map((function(t){e.push(t.uid)})),this.dataid=e},selectall:function(t){if(0==t.length)this.dataid=[];else{this.datanew=t;var e=[];this.datanew.map((function(t){e.push(t.uid)})),this.dataid=e}},setLabel:function(){var t=this;if(0==this.datanew.length)this.$Message.warning("请选择要设置标签的用户");else{this.dataid.join(",");var e={all:0};e.uids=this.dataid,this.$modalForm(Object(l["x"])(e)).then((function(){return t.getList()}))}},detail:function(t){this.$refs.userDetails.modals=!0,this.$refs.userDetails.getDetails(t.uid)},recharge:function(t){this.$refs.recharges.modals=!0,this.$refs.recharges.getList(t.uid)},paying:function(t){this.$refs.payings.modals=!0,this.$refs.payings.getList(t.uid)},setUser:function(t){this.$refs.setusers.modals=!0,this.$refs.setusers.getId(t.uid)},openLabel:function(t){this.labelShow=!0,this.labelActive.uid=t.uid},labelClose:function(){this.labelShow=!1,this.labelActive.uid=0,this.getList()},pageChange:function(t){this.userFrom.page=t,this.getList()},sortChanged:function(t){this.userFrom[t.key]=t.order,this.getList()}}},z=Q,X=(a("ee13"),Object(d["a"])(z,i,r,!1,null,"30b91781",null));e["default"]=X.exports},2878:function(t,e,a){},3531:function(t,e,a){"use strict";var i=a("91b4"),r=a.n(i);r.a},"389a":function(t,e,a){"use strict";var i=a("a85c"),r=a.n(i);r.a},"681df":function(t,e,a){"use strict";var i=a("70fa"),r=a.n(i);r.a},"70d7":function(t,e,a){"use strict";var i=a("8a83"),r=a.n(i);r.a},"70fa":function(t,e,a){},"8a83":function(t,e,a){},"91b4":function(t,e,a){},"9e4a":function(t,e,a){"use strict";var i=a("2878"),r=a.n(i);r.a},a85c:function(t,e,a){},b4ea:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var i=a("b6bd");function r(){return Object(i["a"])({url:"user/cashier_list",method:"get"})}function n(t){return Object(i["a"])({url:"staff/staff",method:"get",params:t})}},c24f:function(t,e,a){"use strict";a.d(e,"z",(function(){return r})),a.d(e,"s",(function(){return n})),a.d(e,"t",(function(){return s})),a.d(e,"a",(function(){return o})),a.d(e,"y",(function(){return l})),a.d(e,"r",(function(){return c})),a.d(e,"u",(function(){return u})),a.d(e,"b",(function(){return d})),a.d(e,"C",(function(){return h})),a.d(e,"f",(function(){return f})),a.d(e,"m",(function(){return m})),a.d(e,"d",(function(){return g})),a.d(e,"h",(function(){return p})),a.d(e,"x",(function(){return v})),a.d(e,"v",(function(){return b})),a.d(e,"B",(function(){return _})),a.d(e,"w",(function(){return w})),a.d(e,"A",(function(){return y})),a.d(e,"o",(function(){return C})),a.d(e,"q",(function(){return k})),a.d(e,"c",(function(){return L})),a.d(e,"p",(function(){return D})),a.d(e,"n",(function(){return T})),a.d(e,"g",(function(){return O})),a.d(e,"e",(function(){return A})),a.d(e,"i",(function(){return E})),a.d(e,"k",(function(){return M})),a.d(e,"j",(function(){return I})),a.d(e,"l",(function(){return P}));var i=a("b6bd");function r(){return Object(i["a"])({url:"user/user_label_cate",method:"get"})}function n(){return Object(i["a"])({url:"user/user_label_cate/create",method:"get"})}function s(t){return Object(i["a"])({url:"user/user_label_cate/".concat(t,"/edit"),method:"get"})}function o(t){return Object(i["a"])({url:"user/user_label",method:"get",params:t})}function l(){return Object(i["a"])({url:"user/user_label/create",method:"get"})}function c(t){return Object(i["a"])({url:"user/user_label/".concat(t,"/edit"),method:"get"})}function u(t){return Object(i["a"])({url:"user/get_list",method:"get",params:t})}function d(t){return Object(i["a"])({url:"user/cashier_list",method:"get",params:t})}function h(t){return Object(i["a"])({url:"user/search",method:"get",params:t})}function f(t){return Object(i["a"])({url:"user/label/".concat(t),method:"get"})}function m(t,e){return Object(i["a"])({url:"user/label/".concat(t),method:"post",data:e})}function g(t){return Object(i["a"])({url:"user/info/".concat(t),method:"get"})}function p(t){return Object(i["a"])({url:"user/record/".concat(t.id),method:"get",params:t.datas})}function v(t){return Object(i["a"])({url:"user/set_label",method:"post",data:t})}function b(){return Object(i["a"])({url:"store/recharge_info",method:"get"})}function _(){return Object(i["a"])({url:"user/member/ship",method:"get"})}function w(t){return Object(i["a"])({url:"store/recharge",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/user/member",method:"post",data:t})}function C(t){return Object(i["a"])({url:"staff/binding/user",method:"post",data:t})}function k(t){return Object(i["a"])({url:"updatePwd",method:"PUT",data:t})}function L(t,e){return Object(i["a"])({url:"check_order_status/".concat(t),method:"post",data:e})}function D(){return Object(i["a"])({url:"user/cashier_info ",method:"get"})}function T(t){return Object(i["a"])({url:"user/read/".concat(t),method:"get"})}function O(t,e){return Object(i["a"])({url:"user/one_info/".concat(t),method:"get",params:e})}function A(t){return Object(i["a"])({url:"user/member_card",method:"get",params:t})}function E(t){return Object(i["a"])({url:"user/mer_recharge",method:"post",data:t})}function M(t){return Object(i["a"])({url:"user/search_user_info",method:"post",data:t})}function I(t){return Object(i["a"])({url:"user/register_user",method:"post",data:t})}function P(t,e){return Object(i["a"])({url:"user/update/".concat(t),method:"post",data:e})}},c729:function(t,e,a){},d044:function(t,e,a){var i;(function(e,a){t.exports=a()})(0,(function(){function t(t){this.mode=a.MODE_8BIT_BYTE,this.data=t,this.parsedData=[];for(var e=0,i=this.data.length;e<i;e++){var r=[],n=this.data.charCodeAt(e);n>65536?(r[0]=240|(1835008&n)>>>18,r[1]=128|(258048&n)>>>12,r[2]=128|(4032&n)>>>6,r[3]=128|63&n):n>2048?(r[0]=224|(61440&n)>>>12,r[1]=128|(4032&n)>>>6,r[2]=128|63&n):n>128?(r[0]=192|(1984&n)>>>6,r[1]=128|63&n):r[0]=n,this.parsedData.push(r)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function e(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}t.prototype={getLength:function(t){return this.parsedData.length},write:function(t){for(var e=0,a=this.parsedData.length;e<a;e++)t.put(this.parsedData[e],8)}},e.prototype={addData:function(e){var a=new t(e);this.dataList.push(a),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,a){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[i][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,a),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,a)},setupPositionProbePattern:function(t,e){for(var a=-1;a<=7;a++)if(!(t+a<=-1||this.moduleCount<=t+a))for(var i=-1;i<=7;i++)e+i<=-1||this.moduleCount<=e+i||(this.modules[t+a][e+i]=0<=a&&a<=6&&(0==i||6==i)||0<=i&&i<=6&&(0==a||6==a)||2<=a&&a<=4&&2<=i&&i<=4)},getBestMaskPattern:function(){for(var t=0,e=0,a=0;a<8;a++){this.makeImpl(!0,a);var i=s.getLostPoint(this);(0==a||t>i)&&(t=i,e=a)}return e},createMovieClip:function(t,e,a){var i=t.createEmptyMovieClip(e,a),r=1;this.make();for(var n=0;n<this.modules.length;n++)for(var s=n*r,o=0;o<this.modules[n].length;o++){var l=o*r,c=this.modules[n][o];c&&(i.beginFill(0,100),i.moveTo(l,s),i.lineTo(l+r,s),i.lineTo(l+r,s+r),i.lineTo(l,s+r),i.endFill())}return i},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=s.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var a=0;a<t.length;a++){var i=t[e],r=t[a];if(null==this.modules[i][r])for(var n=-2;n<=2;n++)for(var o=-2;o<=2;o++)this.modules[i+n][r+o]=-2==n||2==n||-2==o||2==o||0==n&&0==o}},setupTypeNumber:function(t){for(var e=s.getBCHTypeNumber(this.typeNumber),a=0;a<18;a++){var i=!t&&1==(e>>a&1);this.modules[Math.floor(a/3)][a%3+this.moduleCount-8-3]=i}for(a=0;a<18;a++){i=!t&&1==(e>>a&1);this.modules[a%3+this.moduleCount-8-3][Math.floor(a/3)]=i}},setupTypeInfo:function(t,e){for(var a=this.errorCorrectLevel<<3|e,i=s.getBCHTypeInfo(a),r=0;r<15;r++){var n=!t&&1==(i>>r&1);r<6?this.modules[r][8]=n:r<8?this.modules[r+1][8]=n:this.modules[this.moduleCount-15+r][8]=n}for(r=0;r<15;r++){n=!t&&1==(i>>r&1);r<8?this.modules[8][this.moduleCount-r-1]=n:r<9?this.modules[8][15-r-1+1]=n:this.modules[8][15-r-1]=n}this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var a=-1,i=this.moduleCount-1,r=7,n=0,o=this.moduleCount-1;o>0;o-=2){6==o&&o--;while(1){for(var l=0;l<2;l++)if(null==this.modules[i][o-l]){var c=!1;n<t.length&&(c=1==(t[n]>>>r&1));var u=s.getMask(e,i,o-l);u&&(c=!c),this.modules[i][o-l]=c,r--,-1==r&&(n++,r=7)}if(i+=a,i<0||this.moduleCount<=i){i-=a,a=-a;break}}}}},e.PAD0=236,e.PAD1=17,e.createData=function(t,a,i){for(var r=u.getRSBlocks(t,a),n=new d,o=0;o<i.length;o++){var l=i[o];n.put(l.mode,4),n.put(l.getLength(),s.getLengthInBits(l.mode,t)),l.write(n)}var c=0;for(o=0;o<r.length;o++)c+=r[o].dataCount;if(n.getLengthInBits()>8*c)throw new Error("code length overflow. ("+n.getLengthInBits()+">"+8*c+")");n.getLengthInBits()+4<=8*c&&n.put(0,4);while(n.getLengthInBits()%8!=0)n.putBit(!1);while(1){if(n.getLengthInBits()>=8*c)break;if(n.put(e.PAD0,8),n.getLengthInBits()>=8*c)break;n.put(e.PAD1,8)}return e.createBytes(n,r)},e.createBytes=function(t,e){for(var a=0,i=0,r=0,n=new Array(e.length),o=new Array(e.length),l=0;l<e.length;l++){var u=e[l].dataCount,d=e[l].totalCount-u;i=Math.max(i,u),r=Math.max(r,d),n[l]=new Array(u);for(var h=0;h<n[l].length;h++)n[l][h]=255&t.buffer[h+a];a+=u;var f=s.getErrorCorrectPolynomial(d),m=new c(n[l],f.getLength()-1),g=m.mod(f);o[l]=new Array(f.getLength()-1);for(h=0;h<o[l].length;h++){var p=h+g.getLength()-o[l].length;o[l][h]=p>=0?g.get(p):0}}var v=0;for(h=0;h<e.length;h++)v+=e[h].totalCount;var b=new Array(v),_=0;for(h=0;h<i;h++)for(l=0;l<e.length;l++)h<n[l].length&&(b[_++]=n[l][h]);for(h=0;h<r;h++)for(l=0;l<e.length;l++)h<o[l].length&&(b[_++]=o[l][h]);return b};for(var a={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},r={L:1,M:0,Q:3,H:2},n={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},s={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(s.getBCHDigit(e)-s.getBCHDigit(s.G15)>=0)e^=s.G15<<s.getBCHDigit(e)-s.getBCHDigit(s.G15);return(t<<10|e)^s.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(s.getBCHDigit(e)-s.getBCHDigit(s.G18)>=0)e^=s.G18<<s.getBCHDigit(e)-s.getBCHDigit(s.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return s.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,a){switch(t){case n.PATTERN000:return(e+a)%2==0;case n.PATTERN001:return e%2==0;case n.PATTERN010:return a%3==0;case n.PATTERN011:return(e+a)%3==0;case n.PATTERN100:return(Math.floor(e/2)+Math.floor(a/3))%2==0;case n.PATTERN101:return e*a%2+e*a%3==0;case n.PATTERN110:return(e*a%2+e*a%3)%2==0;case n.PATTERN111:return(e*a%3+(e+a)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new c([1],0),a=0;a<t;a++)e=e.multiply(new c([1,o.gexp(a)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case a.MODE_NUMBER:return 10;case a.MODE_ALPHA_NUM:return 9;case a.MODE_8BIT_BYTE:return 8;case a.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case a.MODE_NUMBER:return 12;case a.MODE_ALPHA_NUM:return 11;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case a.MODE_NUMBER:return 14;case a.MODE_ALPHA_NUM:return 13;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),a=0,i=0;i<e;i++)for(var r=0;r<e;r++){for(var n=0,s=t.isDark(i,r),o=-1;o<=1;o++)if(!(i+o<0||e<=i+o))for(var l=-1;l<=1;l++)r+l<0||e<=r+l||0==o&&0==l||s==t.isDark(i+o,r+l)&&n++;n>5&&(a+=3+n-5)}for(i=0;i<e-1;i++)for(r=0;r<e-1;r++){var c=0;t.isDark(i,r)&&c++,t.isDark(i+1,r)&&c++,t.isDark(i,r+1)&&c++,t.isDark(i+1,r+1)&&c++,0!=c&&4!=c||(a+=3)}for(i=0;i<e;i++)for(r=0;r<e-6;r++)t.isDark(i,r)&&!t.isDark(i,r+1)&&t.isDark(i,r+2)&&t.isDark(i,r+3)&&t.isDark(i,r+4)&&!t.isDark(i,r+5)&&t.isDark(i,r+6)&&(a+=40);for(r=0;r<e;r++)for(i=0;i<e-6;i++)t.isDark(i,r)&&!t.isDark(i+1,r)&&t.isDark(i+2,r)&&t.isDark(i+3,r)&&t.isDark(i+4,r)&&!t.isDark(i+5,r)&&t.isDark(i+6,r)&&(a+=40);var u=0;for(r=0;r<e;r++)for(i=0;i<e;i++)t.isDark(i,r)&&u++;var d=Math.abs(100*u/e/e-50)/5;return a+=10*d,a}},o={glog:function(t){if(t<1)throw new Error("glog("+t+")");return o.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return o.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},l=0;l<8;l++)o.EXP_TABLE[l]=1<<l;for(l=8;l<256;l++)o.EXP_TABLE[l]=o.EXP_TABLE[l-4]^o.EXP_TABLE[l-5]^o.EXP_TABLE[l-6]^o.EXP_TABLE[l-8];for(l=0;l<255;l++)o.LOG_TABLE[o.EXP_TABLE[l]]=l;function c(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var a=0;while(a<t.length&&0==t[a])a++;this.num=new Array(t.length-a+e);for(var i=0;i<t.length-a;i++)this.num[i]=t[i+a]}function u(t,e){this.totalCount=t,this.dataCount=e}function d(){this.buffer=[],this.length=0}c.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),a=0;a<this.getLength();a++)for(var i=0;i<t.getLength();i++)e[a+i]^=o.gexp(o.glog(this.get(a))+o.glog(t.get(i)));return new c(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=o.glog(this.get(0))-o.glog(t.get(0)),a=new Array(this.getLength()),i=0;i<this.getLength();i++)a[i]=this.get(i);for(i=0;i<t.getLength();i++)a[i]^=o.gexp(o.glog(t.get(i))+e);return new c(a,0).mod(t)}},u.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],u.getRSBlocks=function(t,e){var a=u.getRsBlockTable(t,e);if(void 0==a)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var i=a.length/3,r=[],n=0;n<i;n++)for(var s=a[3*n+0],o=a[3*n+1],l=a[3*n+2],c=0;c<s;c++)r.push(new u(o,l));return r},u.getRsBlockTable=function(t,e){switch(e){case r.L:return u.RS_BLOCK_TABLE[4*(t-1)+0];case r.M:return u.RS_BLOCK_TABLE[4*(t-1)+1];case r.Q:return u.RS_BLOCK_TABLE[4*(t-1)+2];case r.H:return u.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},d.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var a=0;a<e;a++)this.putBit(1==(t>>>e-a-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var h=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function f(){return"undefined"!=typeof CanvasRenderingContext2D}function m(){var t=!1,e=navigator.userAgent;if(/android/i.test(e)){t=!0;var a=e.toString().match(/android ([0-9]\.[0-9])/i);a&&a[1]&&(t=parseFloat(a[1]))}return t}var g=function(){var t=function(t,e){this._el=t,this._htOption=e};return t.prototype.draw=function(t){var e=this._htOption,a=this._el,i=t.getModuleCount();Math.floor(e.width/i),Math.floor(e.height/i);function r(t,e){var a=document.createElementNS("http://www.w3.org/2000/svg",t);for(var i in e)e.hasOwnProperty(i)&&a.setAttribute(i,e[i]);return a}this.clear();var n=r("svg",{viewBox:"0 0 "+String(i)+" "+String(i),width:"100%",height:"100%",fill:e.colorLight});n.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),a.appendChild(n),n.appendChild(r("rect",{fill:e.colorLight,width:"100%",height:"100%"})),n.appendChild(r("rect",{fill:e.colorDark,width:"1",height:"1",id:"template"}));for(var s=0;s<i;s++)for(var o=0;o<i;o++)if(t.isDark(s,o)){var l=r("use",{x:String(o),y:String(s)});l.setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),n.appendChild(l)}},t.prototype.clear=function(){while(this._el.hasChildNodes())this._el.removeChild(this._el.lastChild)},t}(),p="svg"===document.documentElement.tagName.toLowerCase(),v=p?g:f()?function(){function t(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}if(this._android&&this._android<=2.1){var e=1/window.devicePixelRatio,a=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(t,i,r,n,s,o,l,c,u){if("nodeName"in t&&/img/i.test(t.nodeName))for(var d=arguments.length-1;d>=1;d--)arguments[d]=arguments[d]*e;else"undefined"==typeof c&&(arguments[1]*=e,arguments[2]*=e,arguments[3]*=e,arguments[4]*=e);a.apply(this,arguments)}}function i(t,e){var a=this;if(a._fFail=e,a._fSuccess=t,null===a._bSupportDataURI){var i=document.createElement("img"),r=function(){a._bSupportDataURI=!1,a._fFail&&a._fFail.call(a)},n=function(){a._bSupportDataURI=!0,a._fSuccess&&a._fSuccess.call(a)};return i.onabort=r,i.onerror=r,i.onload=n,void(i.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==")}!0===a._bSupportDataURI&&a._fSuccess?a._fSuccess.call(a):!1===a._bSupportDataURI&&a._fFail&&a._fFail.call(a)}var r=function(t,e){this._bIsPainted=!1,this._android=m(),this._htOption=e,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=e.width,this._elCanvas.height=e.height,t.appendChild(this._elCanvas),this._el=t,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.alt="Scan me!",this._elImage.style.display="none",this._el.appendChild(this._elImage),this._bSupportDataURI=null};return r.prototype.draw=function(t){var e=this._elImage,a=this._oContext,i=this._htOption,r=t.getModuleCount(),n=i.width/r,s=i.height/r,o=Math.round(n),l=Math.round(s);e.style.display="none",this.clear();for(var c=0;c<r;c++)for(var u=0;u<r;u++){var d=t.isDark(c,u),h=u*n,f=c*s;a.strokeStyle=d?i.colorDark:i.colorLight,a.lineWidth=1,a.fillStyle=d?i.colorDark:i.colorLight,a.fillRect(h,f,n,s),a.strokeRect(Math.floor(h)+.5,Math.floor(f)+.5,o,l),a.strokeRect(Math.ceil(h)-.5,Math.ceil(f)-.5,o,l)}this._bIsPainted=!0},r.prototype.makeImage=function(){this._bIsPainted&&i.call(this,t)},r.prototype.isPainted=function(){return this._bIsPainted},r.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},r.prototype.round=function(t){return t?Math.floor(1e3*t)/1e3:t},r}():function(){var t=function(t,e){this._el=t,this._htOption=e};return t.prototype.draw=function(t){for(var e=this._htOption,a=this._el,i=t.getModuleCount(),r=Math.floor(e.width/i),n=Math.floor(e.height/i),s=['<table style="border:0;border-collapse:collapse;">'],o=0;o<i;o++){s.push("<tr>");for(var l=0;l<i;l++)s.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+r+"px;height:"+n+"px;background-color:"+(t.isDark(o,l)?e.colorDark:e.colorLight)+';"></td>');s.push("</tr>")}s.push("</table>"),a.innerHTML=s.join("");var c=a.childNodes[0],u=(e.width-c.offsetWidth)/2,d=(e.height-c.offsetHeight)/2;u>0&&d>0&&(c.style.margin=d+"px "+u+"px")},t.prototype.clear=function(){this._el.innerHTML=""},t}();function b(t,e){for(var a=1,i=_(t),n=0,s=h.length;n<=s;n++){var o=0;switch(e){case r.L:o=h[n][0];break;case r.M:o=h[n][1];break;case r.Q:o=h[n][2];break;case r.H:o=h[n][3];break}if(i<=o)break;a++}if(a>h.length)throw new Error("Too long data");return a}function _(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=t?3:0)}return i=function(t,e){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:r.H},"string"===typeof e&&(e={text:e}),e)for(var a in e)this._htOption[a]=e[a];"string"==typeof t&&(t=document.getElementById(t)),this._htOption.useSVG&&(v=g),this._android=m(),this._el=t,this._oQRCode=null,this._oDrawing=new v(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)},i.prototype.makeCode=function(t){this._oQRCode=new e(b(t,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(t),this._oQRCode.make(),this._el.title=t,this._oDrawing.draw(this._oQRCode),this.makeImage()},i.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||this._android>=3)&&this._oDrawing.makeImage()},i.prototype.clear=function(){this._oDrawing.clear()},i.CorrectLevel=r,i}))},ee13:function(t,e,a){"use strict";var i=a("c729"),r=a.n(i);r.a},f164:function(t,e,a){"use strict";var i=a("fc85"),r=a.n(i);r.a},fc85:function(t,e,a){}}]);