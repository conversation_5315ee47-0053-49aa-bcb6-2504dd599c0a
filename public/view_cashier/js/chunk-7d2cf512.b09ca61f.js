(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d2cf512"],{"0493":function(t,e,n){t.exports=n.p+"view_cashier/img/no-record.2e1e1105.png"},"16b2":function(t,e,n){"use strict";var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("Drawer",{ref:"drawer",attrs:{value:t.visible,width:"800","class-name":"settle-drawer"},on:{"on-visible-change":t.visibleChange}},[n("div",{attrs:{slot:"header"},slot:"header"},[n("div",{staticClass:"title"},[t._v("选择支付方式")]),n("div",{staticClass:"btn-group"},[n("ButtonGroup",{attrs:{shape:"circle"}},[t._l(t.list,(function(e){return[e.status?n("Button",{key:e.label,attrs:{type:e.value===t.type?"primary":"default"},on:{click:function(n){return t.typeChange(e.value)}}},[t._v(t._s(e.label))]):t._e()]}))],2)],1)]),n("div",{staticClass:"drawer-body"},[n("div",{staticClass:"receivable"},[n("div",{staticClass:"title"},[n("span",{staticClass:"inner"},[t._v("应收金额(元)")])]),n("div",{staticClass:"money"},[t._v("\n        ￥"),n("span",{staticClass:"number"},[t._v(t._s(t.money))])])]),"cash"===t.type?n("div",{staticClass:"counter"},[n("div",{staticClass:"received"},[t._v(t._s(t.collection))]),n("div",{staticClass:"balance"},[t._v("\n        需找零(元)："),n("span",{staticClass:"money"},[t._v("￥"+t._s(t.money>t.collection?0:this.$computes.Sub(t.collection,t.money||0)))])]),n("div",{staticClass:"keypad"},[n("div",{staticClass:"left"},t._l(t.numList,(function(e){return n("Button",{key:e,on:{click:function(n){return t.numTap(e)}}},[t._v(t._s(e))])})),1),n("div",{staticClass:"right"},[n("Button",{on:{click:t.delNum}},[n("Icon",{attrs:{type:"ios-backspace-outline"}})],1),n("Button",{on:{click:function(e){return t.delNum(-1)}}},[t._v("C")]),n("Button",{staticClass:"enter",on:{click:t.cashBnt}},[t._v("确认")])],1)])]):!t.type||t.verify?n("div",{staticClass:"payment-code"},[n("Input",{key:t.type,ref:"input",attrs:{placeholder:"请点击输入框聚焦扫码或输入编码号"},on:{"on-enter":t.cashBnt},model:{value:t.payNum,callback:function(e){t.payNum="string"===typeof e?e.trim():e},expression:"payNum"}}),n("div",{staticClass:"tips-wrap",class:{balance:"yue"===t.type}})],1):t._e()]),n("div",{staticClass:"drawer-footer"},[n("Button",{on:{click:t.handleCancel}},[t._v("取消收款")]),"yue"!==t.type||t.verify?t._e():n("Button",{attrs:{type:"primary"},on:{click:t.cashBnt}},[t._v("扣除余额")])],1)])},a=[],o={model:{prop:"visible",event:"change"},props:{visible:{type:Boolean,default:!1},money:{type:[Number,String],default:0},collection:{type:[Number,String],default:0},zIndex:{type:[Number,String],default:9999},type:{type:String,default:""},verify:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}}},data:function(){return{numList:["1","2","3","4","5","6","7","8","9","0","00","."],payNum:""}},watch:{type:function(t){var e=this;this.$nextTick((function(){(!t||"yue"===t&&e.verify)&&e.$refs.input.focus()}))},zIndex:function(t){var e=this.$refs.drawer.$el;e.querySelector(".ivu-drawer-mask").style.zIndex=t,e.querySelector(".ivu-drawer-wrap").style.zIndex=t}},methods:{visibleChange:function(t){var e=this;t?this.$nextTick((function(){e.$refs.input.focus()})):this.payNum="",this.$emit("change",t)},typeChange:function(t){this.payNum="",this.$emit("payPrice",t)},numTap:function(t){this.$emit("numTap",t)},delNum:function(t){this.$emit("delNum",t)},cashBnt:function(){!this.payNum&&(""===this.type||"yue"===this.type&&this.verify)||("yue"===this.type&&this.isURL(this.payNum)&&(this.payNum=this.getCodeFromLink(this.payNum)),this.$emit("cashBnt",this.payNum),this.payNum="")},isURL:function(t){var e=/^(http|https):\/\/[^ "]+$/;return e.test(t)},getCodeFromLink:function(t){var e=new URL(t),n=new URLSearchParams(e.search),r=n.get("code");return r},handleCancel:function(){this.$emit("change",!1)}}},c=o,i=(n("3c09"),n("2877")),u=Object(i["a"])(c,r,a,!1,null,"1e92d696",null);e["a"]=u.exports},"1a3d":function(t,e,n){t.exports=n.p+"view_cashier/img/process2.f9f8c6c6.png"},"3c09":function(t,e,n){"use strict";var r=n("f19d"),a=n.n(r);a.a},"5e36":function(t,e,n){},"66c9":function(t,e,n){t.exports=n.p+"view_cashier/img/no-order.1faafc6c.png"},"6b58":function(t,e,n){"use strict";var r=n("e9aa"),a=n.n(r);a.a},bde5:function(t,e,n){t.exports=n.p+"view_cashier/img/no-cup.596a2a07.png"},bff0:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("Row",[r("Col",[r("Input",{staticStyle:{width:"500px"},attrs:{"element-id":"nickname","enter-button":"搜索",placeholder:"请输入ID或者手机号",search:""},on:{"on-search":t.orderSearch},model:{value:t.userFrom.keyword,callback:function(e){t.$set(t.userFrom,"keyword",e)},expression:"userFrom.keyword"}})],1)],1),t.dataList.length?r("div",{staticClass:"store-people",on:{scroll:t.getList}},[r("div",{staticClass:"store-list"},t._l(t.dataList,(function(e,n){return r("div",{key:n,staticClass:"item",class:t.uid===e.uid?"on":"",on:{click:function(n){return t.selectStore(e)}}},[e.delete_time?r("span",{staticClass:"del"},[t._v("已注销")]):t._e(),r("div",{staticClass:"avatar"},[r("img",{attrs:{src:e.avatar,alt:""}})]),r("div",{staticClass:"msg"},[r("div",{staticClass:"msg-top"},[r("span",{staticClass:"name"},[t._v(t._s(e.nickname))]),r("span",{staticClass:"id"},[t._v("(id:"+t._s(e.uid)+")")])]),r("div",{staticClass:"msg-mid"},[t._v("\n            手机号:"+t._s(e.phone||"无")+"\n          ")]),r("div",{staticClass:"msg-btn"},[r("span",[t._v("类型:"+t._s(e.user_type||"无"))]),r("span",{staticClass:"now-money"},[t._v("\n              余额:"+t._s(e.now_money)+"\n            ")])])])])})),0)]):r("div",{staticClass:"no-cump"},[r("img",{attrs:{alt:"",src:n("f8c4")}}),r("span",{staticClass:"trip"},[t._v("噢噢～目前暂无用户")])])],1)},a=[],o=n("c24f"),c={name:"userList",props:["uid"],data:function(){return{total:0,userFrom:{keyword:"",page:1,limit:12,field_key:"all"},loading:!1,dataList:[],currentid:0,columns:[{title:"ID",key:"uid",width:80},{title:"头像",slot:"avatars",minWidth:50},{title:"昵称",key:"nickname",minWidth:70},{title:"手机号",key:"phone",minWidth:70},{title:"用户类型",key:"user_type",minWidth:70},{title:"余额",key:"now_money",sortable:"custom",minWidth:70}]}},created:function(){this.getList()},methods:{selectStore:function(t){t.delete_time?this.$Message.error("用户已注销"):this.$emit("getUserId",t)},sortChanged:function(t){this.userFrom[t.key]=t.order,this.getList()},getList:function(t){var e=this;(!t||t.target.scrollHeight-t.target.scrollTop-t.target.clientHeight<=0&&this.dataList.length<this.total)&&(this.loading=!0,Object(o["u"])(this.userFrom).then((function(t){e.loading=!1,e.total=t.data.count,e.dataList=e.dataList.concat(t.data.list),e.dataList.length<t.data.count&&e.userFrom.page++})).catch((function(t){e.loading=!1,e.$Message.error(t.msg)})))},usersearchList:function(){var t=this;this.loading=!0,Object(o["C"])(this.userFrom).then((function(e){t.dataList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},orderSearch:function(){this.userFrom.page=1,this.dataList=[],this.getList()},pageChange:function(t){this.userFrom.page=t,this.getList()}}},i=c,u=(n("e02d"),n("2877")),s=Object(u["a"])(i,r,a,!1,null,"3db69558",null);e["default"]=s.exports},e02d:function(t,e,n){"use strict";var r=n("5e36"),a=n.n(r);a.a},e449:function(t,e,n){"use strict";var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticStyle:{width:"100%"}},[r("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"优惠券列表","mask-closable":!1,width:"1070"},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.dataList.length?r("div",{staticClass:"wrapper"},t._l(t.dataList,(function(e,n){return r("div",{key:n,staticClass:"item",class:e.used&&t.couponId==e.id?"used":""},[r("div",{staticClass:"item-left"},[r("div",{staticClass:"itemCon acea-row row-between-wrapper"},[r("div",{staticClass:"span"},[1===e.coupon_type?r("div",{staticClass:"money"},[r("span",{staticClass:"fu"},[t._v("¥")]),t._v(t._s(e.coupon_price)+"\n              ")]):t._e(),2===e.coupon_type?r("div",{staticClass:"money"},[t._v("\n                "+t._s((e.coupon_price/10).toFixed(2))),r("span",{staticClass:"fu"},[t._v("折")])]):t._e(),r("div",{staticClass:"info"},[t._v("满"+t._s(e.use_min_price)+"元可用")])]),!0===e.is_use?r("div",{staticClass:"bnt"},[r("span",[t._v("已 领 取")])]):!1===e.is_use?r("div",{staticClass:"bnt",on:{click:function(n){return t.receiveCoupon(e)}}},[r("span",[t._v("立 即 领 取")])]):2===e.is_use?r("div",{staticClass:"bnt"},[r("span",[t._v("已 过 期")])]):t._e()]),r("div",{staticClass:"roll up-roll"}),r("div",{staticClass:"roll down-roll"}),r("div",{staticClass:"cou-msg"},[r("div",{staticClass:"title line1"},[t._v(t._s(e.coupon_title))]),r("div",{staticClass:"type"},[t._v("\n              "+t._s(0===e.type?"通用券":1===e.type?"品类券":"商品券")+"\n            ")]),e.end_time?r("div",{staticClass:"time"},[r("span",[t._v(t._s(t._f("formatDate")(e.add_time)))]),t._v("\n              ~\n              "),r("span",[t._v(t._s(t._f("formatDate")(e.end_time)))])]):r("div",{staticClass:"time"},[r("span",[t._v("不限时")])])])]),r("div",{staticClass:"use",class:e.used&&t.couponId==e.id&&e.showCoupon?"use-on":"",on:{click:function(n){return t.select(e)}}},[t._v("\n          "+t._s(t.couponId==e.id&&e.showCoupon?"已选择":e.used?"立即使用":"领取并使用")+"\n        ")])])})),0):r("div",{staticClass:"no-cump"},[r("img",{attrs:{src:n("bde5"),alt:""}}),r("span",{staticClass:"trip"},[t._v("暂无优惠券，可以看看其他活动哟～")])])])],1)},a=[],o=n("f8b7"),c=n("b6bd");function i(t,e){return Object(c["a"])({url:"coupon/receive/".concat(t),method:"post",data:e})}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(n,!0).forEach((function(e){d(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(n).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function d(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t,e){/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds()};for(var r in n)if(new RegExp("(".concat(r,")")).test(e)){var a=n[r]+"";e=e.replace(RegExp.$1,1===RegExp.$1.length?a:f(a))}return e}function f(t){return("00"+t).substr(t.length)}var p={min:"%s最小长度为:min",max:"%s最大长度为:max",length:"%s长度必须为:length",range:"%s长度为:range",pattern:"$s格式错误"};var m=function(t,e){t.message=function(t){return e.replace("%s",t||"")}};function h(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s({required:!0,message:t,type:"string"},e)}function g(t){return v.pattern(/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,t)}m(h,"请输入%s"),m(g,"%s格式不正确");var v=Object.keys(p).reduce((function(t,e){return t[e]=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a="range"===e?{min:t[0],max:t[1]}:d({},e,t);return s({message:n.replace(":".concat(e),"range"===e?"".concat(t[0],"-").concat(t[1]):t),type:"string"},a,{},r)},m(t[e],p[e]),t}),{}),b={name:"userList",filters:{formatDate:function(t){if(0!==t){var e=new Date(1e3*t);return l(e,"yyyy-MM-dd")}}},props:{uid:{type:Number,value:0},couponId:{type:Number,value:0},cartList:{type:Array,value:0}},data:function(){return{modals:!1,loading:!1,dataList:[]}},created:function(){},methods:{select:function(t){var e=this;if(t.showCoupon=!t.showCoupon,t.used)t.id=t.showCoupon?t.id:0,this.$emit("getCouponId",t);else{var n={couponId:t.id};i(this.uid,n).then((function(t){e.$emit("getCouponId",t.data)})).catch((function(t){e.$Message.error(t.msg)}))}},getList:function(){var t=this;this.loading=!0;var e=[];this.cartList.forEach((function(t){t.cart.forEach((function(t){e.push(t.id)}))})),Object(o["k"])(this.uid,{cart_id:e}).then((function(e){t.loading=!1,e.data.forEach((function(e){parseInt(t.couponId)==e.id?e.showCoupon=!0:e.showCoupon=!1})),t.dataList=e.data})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},cancel:function(){this.currentid=""}}},_=b,y=(n("6b58"),n("2877")),O=Object(y["a"])(_,r,a,!1,null,"398e2e63",null);e["a"]=O.exports},e9aa:function(t,e,n){},f19d:function(t,e,n){},f8b7:function(t,e,n){"use strict";n.d(e,"q",(function(){return a})),n.d(e,"h",(function(){return o})),n.d(e,"r",(function(){return c})),n.d(e,"i",(function(){return i})),n.d(e,"d",(function(){return u})),n.d(e,"m",(function(){return s})),n.d(e,"n",(function(){return d})),n.d(e,"f",(function(){return l})),n.d(e,"g",(function(){return f})),n.d(e,"s",(function(){return p})),n.d(e,"e",(function(){return m})),n.d(e,"Y",(function(){return h})),n.d(e,"j",(function(){return g})),n.d(e,"l",(function(){return v})),n.d(e,"p",(function(){return b})),n.d(e,"y",(function(){return _})),n.d(e,"Z",(function(){return y})),n.d(e,"O",(function(){return O})),n.d(e,"N",(function(){return j})),n.d(e,"Q",(function(){return C})),n.d(e,"fb",(function(){return w})),n.d(e,"bb",(function(){return k})),n.d(e,"ab",(function(){return L})),n.d(e,"G",(function(){return $})),n.d(e,"db",(function(){return x})),n.d(e,"ib",(function(){return I})),n.d(e,"X",(function(){return N})),n.d(e,"V",(function(){return E})),n.d(e,"k",(function(){return S})),n.d(e,"jb",(function(){return B})),n.d(e,"cb",(function(){return D})),n.d(e,"U",(function(){return F})),n.d(e,"T",(function(){return M})),n.d(e,"A",(function(){return P})),n.d(e,"z",(function(){return R})),n.d(e,"o",(function(){return T})),n.d(e,"C",(function(){return U})),n.d(e,"B",(function(){return W})),n.d(e,"D",(function(){return z})),n.d(e,"F",(function(){return q})),n.d(e,"L",(function(){return H})),n.d(e,"E",(function(){return A})),n.d(e,"eb",(function(){return J})),n.d(e,"t",(function(){return G})),n.d(e,"v",(function(){return Y})),n.d(e,"a",(function(){return K})),n.d(e,"hb",(function(){return Q})),n.d(e,"x",(function(){return V})),n.d(e,"R",(function(){return X})),n.d(e,"I",(function(){return Z})),n.d(e,"K",(function(){return tt})),n.d(e,"w",(function(){return et})),n.d(e,"b",(function(){return nt})),n.d(e,"u",(function(){return rt})),n.d(e,"gb",(function(){return at})),n.d(e,"J",(function(){return ot})),n.d(e,"S",(function(){return ct})),n.d(e,"P",(function(){return it})),n.d(e,"M",(function(){return ut})),n.d(e,"H",(function(){return st})),n.d(e,"W",(function(){return dt})),n.d(e,"c",(function(){return lt}));var r=n("b6bd");function a(t){return Object(r["a"])({url:"product/get_list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"product/get_one_category",method:"get",params:t})}function c(t){return Object(r["a"])({url:"user/user_Info",method:"post",data:t})}function i(t){return Object(r["a"])({url:"order/cashier/code",method:"post",data:t})}function u(t,e){return Object(r["a"])({url:"cart/set_cart/".concat(t),method:"post",data:e})}function s(t,e){return Object(r["a"])({url:"product/get_info/".concat(t,"/").concat(e),method:"get"})}function d(t,e){return Object(r["a"])({url:"product/get_attr/".concat(t,"/").concat(e),method:"get"})}function l(t,e,n){return Object(r["a"])({url:"cart/get_cart/".concat(t,"/").concat(e),method:"get",params:n})}function f(t,e){return Object(r["a"])({url:"cart/set_cart_num/".concat(t),method:"put",data:e})}function p(t){return Object(r["a"])({url:"cart/change_cart",method:"put",data:t})}function m(t,e){return Object(r["a"])({url:"cart/del_cart/".concat(t),method:"DELETE",data:e})}function h(t){return Object(r["a"])({url:"promotions/count/".concat(t),method:"get"})}function g(t,e){return Object(r["a"])({url:"order/compute/".concat(t),method:"post",data:e})}function v(t,e){return Object(r["a"])({url:"/order/create/".concat(t),method:"post",data:e})}function b(t,e){return Object(r["a"])({url:"order/pay/".concat(t),method:"post",data:e})}function _(t){return Object(r["a"])({url:"order/express_list?status="+t,method:"get"})}function y(t){return Object(r["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function O(t){return Object(r["a"])({url:"/order/express/temp",method:"get",params:t})}function j(){return Object(r["a"])({url:"/order/delivery_list",method:"get"})}function C(){return Object(r["a"])({url:"/order/sheet_info",method:"get"})}function w(t){return Object(r["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function k(t){return Object(r["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function L(t){return Object(r["a"])({url:"order/refund/remark/".concat(t.id),method:"put",data:t.remark})}function $(t){return Object(r["a"])({url:"/refund/refund/".concat(t),method:"get"})}function x(t){return Object(r["a"])({url:"order/recharge/remark/".concat(t),method:"get"})}function I(t){return Object(r["a"])({url:"order/vip/remark/".concat(t),method:"get"})}function N(t,e){return Object(r["a"])({url:"order/vip/remark/".concat(t),method:"put",data:e})}function E(t,e){return Object(r["a"])({url:"order/recharge/remark/".concat(t),method:"put",data:e})}function S(t,e){return Object(r["a"])({url:"order/coupon_list/".concat(t),method:"post",data:e})}function B(t){return Object(r["a"])({url:"order/verify_cart_info",method:"get",params:t})}function D(t,e){return Object(r["a"])({url:"order/write_off/".concat(t),method:"put",data:e})}function F(t,e){return Object(r["a"])({url:"user/switch/".concat(e),method:"post",data:t})}function M(t){return Object(r["a"])({url:"order/cashier/hang",method:"post",data:t})}function P(t,e){return Object(r["a"])({url:"order/get_hang_list/".concat(t),method:"get",params:e})}function R(t){return Object(r["a"])({url:"order/get_user_list/".concat(t),method:"get"})}function T(t){return Object(r["a"])({url:"order/cashier/hang",method:"DELETE",params:{id:t}})}function U(t){return Object(r["a"])({url:"order/get_order_list",method:"post",data:t})}function W(t){return Object(r["a"])({url:"order/get_order_Info/".concat(t),method:"get"})}function z(t){return Object(r["a"])({url:"order/get_refund_Info/".concat(t),method:"get"})}function q(t){return Object(r["a"])({url:"order/get_refund_list",method:"get",params:t})}function H(t){return Object(r["a"])({url:"order/get_verify_list",method:"post",data:t})}function A(t){return Object(r["a"])({url:"order/get_order_status/".concat(t),method:"get"})}function J(t,e){return Object(r["a"])({url:"order/order_refund/".concat(t),method:"put",data:e})}function G(t){return Object(r["a"])({url:"order/del_hang",method:"delete",params:t})}function Y(){return Object(r["a"])({url:"erp/config",method:"get"})}function K(){return Object(r["a"])({url:"user/aux_screen",method:"get"})}function Q(t){return Object(r["a"])({url:"user/swith_user",method:"post",data:t})}function V(){return Object(r["a"])({url:"/code/list",method:"get"})}function X(t,e){return Object(r["a"])({url:"/order/write/form/".concat(t),method:"get",params:e})}function Z(t){return Object(r["a"])({url:"/get/table/list",method:"get",params:t})}function tt(t){return Object(r["a"])({url:"/table/uid/all",method:"get",params:t})}function et(t){return Object(r["a"])({url:"/get/cart/list",method:"get",params:t})}function nt(t){return Object(r["a"])({url:"/cancel/table",method:"get",params:t})}function rt(t){return Object(r["a"])({url:"/edit/table/cart",method:"post",data:t})}function at(t){return Object(r["a"])({url:"/staff/place",method:"get",params:t})}function ot(t){return Object(r["a"])({url:"/get/order/info/".concat(t),method:"get"})}function ct(t){return Object(r["a"])({url:"/pay_offline/".concat(t),method:"post"})}function it(t,e){return Object(r["a"])({url:"order/refund/".concat(t),method:"put",data:e})}function ut(t,e){return Object(r["a"])({url:"open/refund/".concat(t),method:"post",data:e})}function st(t){return Object(r["a"])({url:"table/update/info",method:"get",params:t})}function dt(t){return Object(r["a"])({url:"table/update",method:"post",data:t})}function lt(t){return Object(r["a"])({url:"card/benefits/".concat(t),method:"get"})}},f8c4:function(t,e,n){t.exports=n.p+"view_cashier/img/no-user.028d141b.png"}}]);