(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c4d9fcf2"],{"16f5":function(t,e){t.exports="data:image/png;base64,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"},"29ba":function(t,e){t.exports="data:image/png;base64,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"},"2cc0":function(t,e,s){},"3b2d":function(t,e,s){},"42e9":function(t,e,s){},4742:function(t,e,s){"use strict";var a=s("42e9"),r=s.n(a);r.a},"4d75":function(t,e,s){"use strict";var a=s("3b2d"),r=s.n(a);r.a},"52df":function(t,e,s){"use strict";s.d(e,"e",(function(){return r})),s.d(e,"b",(function(){return i})),s.d(e,"c",(function(){return o})),s.d(e,"d",(function(){return n})),s.d(e,"f",(function(){return l})),s.d(e,"j",(function(){return c})),s.d(e,"i",(function(){return d})),s.d(e,"k",(function(){return u})),s.d(e,"g",(function(){return v})),s.d(e,"h",(function(){return f})),s.d(e,"a",(function(){return m}));var a=s("b6bd");function r(t){return Object(a["a"])({url:"reservation/order/list",method:"get",params:t})}function i(){return Object(a["a"])({url:"user/all_staff_list",method:"get"})}function o(t){return Object(a["a"])({url:"export/reservation/order",method:"get",params:t})}function n(t){return Object(a["a"])({url:"reservation/order/detail/".concat(t),method:"get"})}function l(t){return Object(a["a"])({url:"reservation/order/product_time/".concat(t),method:"get"})}function c(t,e){return Object(a["a"])({url:"reservation/order/update/".concat(t),method:"post",data:e})}function d(t,e){return Object(a["a"])({url:"reservation/order/service/set/".concat(t),method:"post",data:e})}function u(t){return Object(a["a"])({url:"reservation/notice/board",method:"get",params:t})}function v(t){return Object(a["a"])({url:"config/".concat(t),method:"get"})}function f(t,e){return Object(a["a"])({url:"config/".concat(t),method:"post",data:e})}function m(t){return Object(a["a"])({url:"city",method:"get",params:t})}},6107:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:1===t.currentPage,expression:"currentPage === 1"}],staticClass:"page1"},[a("div",{staticClass:"title"},[t._v("订单核销")]),a("div",[a("Input",{attrs:{search:"",autofocus:"","enter-button":"查询",placeholder:"输入/扫描核销码进行核销"},on:{"on-search":t.search},model:{value:t.orderData.keyword,callback:function(e){t.$set(t.orderData,"keyword",e)},expression:"orderData.keyword"}})],1),a("div",{staticClass:"btn"},[a("Button",{attrs:{type:"text"},on:{click:t.goAll}},[t._v("查看核销订单")])],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:2===t.currentPage,expression:"currentPage === 2"}],staticClass:"order"},[a("div",{staticClass:"left"},[a("div",{staticClass:"left-top"},[a("div",{staticClass:"title"},[a("Button",{attrs:{type:"text",icon:"ios-arrow-back"},on:{click:t.goBack}},[t._v("返回")]),a("span",{staticClass:"line"},[t._v("丨")]),t._v("\n          核销订单\n        ")],1),a("div",{staticClass:"sx",on:{click:function(e){t.filterModal=!t.filterModal}}},[t._v("\n          "+t._s(t.filterModal?"关闭":"筛选")+"\n          "),t.filterModal?t._e():a("Icon",{staticClass:"ios-funnel-outline",attrs:{color:"#666",type:"ios-funnel-outline"}})],1)]),a("div",{staticClass:"order-box"},[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.filterModal,expression:"!filterModal"}],staticClass:"search"},[a("Input",{attrs:{"enter-button":"搜索",placeholder:"搜索订单编号",search:"",size:"large"},on:{"on-search":t.search},model:{value:t.orderData.keyword,callback:function(e){t.$set(t.orderData,"keyword",e)},expression:"orderData.keyword"}})],1),t.orderListData.length?a("orderList",{directives:[{name:"show",rawName:"v-show",value:!t.filterModal,expression:"!filterModal"}],staticClass:"order-list",attrs:{orderData:t.orderListData,total:t.count,orderType:"verify"},on:{addPage:t.addPage,selectOrder:t.selectOrder}}):t.orderListData.length||t.filterModal?t._e():a("div",{staticClass:"no-order"},[a("img",{attrs:{alt:"",src:s("66c9")}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无订单")])]),a("filter-modal",{directives:[{name:"show",rawName:"v-show",value:t.filterModal,expression:"filterModal"}],attrs:{"order-type":"verify"},on:{search:t.searchList}})],1)]),a("div",{staticClass:"order-data"},[a("div",{staticClass:"header"},[t._l(t.tabs,(function(e,s){return a("div",{key:s,staticClass:"item",class:t.sle===s?"sel":s===t.sle-1?"neighbor-left":s===t.sle+1?"neighbor-right":"def",on:{click:function(e){return t.tabClick(s)}}},[a("div",{staticClass:"item-wrap"},[t._v("\n            "+t._s(e)+"\n          ")])])})),a("div",{staticClass:"box",class:2===t.sle?"neighbor-right":""})],2),a("div",{staticClass:"content",class:{"border-radius":t.sle}},[0===t.sle&&t.selectOrderData.order_id?a("userOrder",{staticClass:"orders",attrs:{orderNumId:t.selectOrderData.order_id,selectData:t.selectOrderData},on:{remarks:t.remarks,selectData:t.selectData}}):t.selectOrderData.order_id?t._e():a("div",{staticClass:"no-order"},[a("img",{attrs:{alt:"",src:s("0493")}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无订单")])]),1===t.sle&&t.selectOrderData.order_id?a("orderDetails",{staticClass:"orders",attrs:{id:t.selectOrderData.id}}):t._e(),2===t.sle&&t.selectOrderData.order_id?a("orderRecord",{attrs:{id:t.selectOrderData.id}}):t._e()],1),t.selectOrderData.order_id?a("div",{staticClass:"footer"},[a("div",{staticClass:"footer-left"},[t.selectOrderData.clerk_name?a("span",{staticClass:"clerk"},[t._v("收银员："+t._s(t.selectOrderData.clerk_name))]):t._e(),a("span",{staticClass:"pay"},[t._v("实付：")]),a("span",{staticClass:"num"},[t._v("¥"+t._s(t.selectOrderData.pay_price||0))])]),a("div",{staticClass:"footer-right"},[a("div",{staticClass:"btn",on:{click:t.remarks}},[t._v("订单备注")]),a("div",{staticClass:"btn",on:{click:t.point}},[t._v("小票打印")]),t.selectOrderData.status&&5!==t.selectOrderData.status||6===t.selectOrderData.refund_type||1!==t.selectOrderData.paid?t._e():a("div",{staticClass:"btn pay",on:{click:t.getVerifyData}},[t._v("\n            立即核销\n          ")])])]):t._e()]),a("order-remark",{ref:"remarks",attrs:{orderId:t.selectOrderData.id},on:{submitFail:t.submitFail}}),a("orderWriteOff",{ref:"writeOff",attrs:{orderNumId:t.selectOrderData.order_id}})],1)])},r=[],i=s("d487"),o=s("9568"),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"order-user"},[a("div",{staticClass:"sel-user"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.selectData.avatar||s("653d"),alt:"头像"}})]),a("div",{staticClass:"item-right"},[a("div",{staticClass:"user"},[a("div",[t._v(t._s(t.selectData.uid?t.selectData.nickname:"游客"))])]),t.selectData.uid?a("div",{staticClass:"money"},[a("div",[t.selectData.phone?a("span",{staticClass:"pr20"},[t._v(t._s(t.selectData.phone))]):t._e(),t._v("余额 "),a("span",{staticClass:"num"},[t._v(t._s(t.selectData.now_money||0))])]),a("div",[t._v("\n          积分 "),a("span",{staticClass:"num"},[t._v(t._s(t.selectData.integral||0))])])]):t._e()])]),a("div",{staticClass:"cart-num"},[a("div",{staticClass:"cart-num-left"},[a("span",[t._v("共")]),a("span",{staticClass:"num"},[t._v(t._s(t.total_num))]),a("span",[t._v("件商品")])])]),a("div",{staticClass:"goods-list"},[a("Table",{ref:"selection",attrs:{columns:t.columns,border:!1,data:t.writeOffData,loading:t.loading,"no-data-text":"暂无数据","no-filtered-data-text":"暂无筛选结果"},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var s=e.row;return[a("div",{staticClass:"product-data"},[a("img",{staticClass:"image",attrs:{src:s.cart_info.productInfo.image}}),a("div",{staticClass:"name line2"},[s.is_gift?a("span",{staticClass:"is-gift"},[t._v("赠品")]):t._e(),t._v("\n            "+t._s(s.cart_info.productInfo.store_name)+"\n          ")])])]}},{key:"value",fn:function(e){var s=e.row;return[a("div",[t._v(t._s(s.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var s=e.row;return[a("div",[t._v("\n          "+t._s(s.cart_info.productInfo.attrInfo?s.cart_info.productInfo.attrInfo.price:s.cart_info.productInfo.price)+"\n        ")])]}},{key:"price",fn:function(e){var s=e.row;return[a("div",[t._v(t._s(s.cart_info.truePrice))])]}},{key:"writeOff",fn:function(e){var s=e.row;return[s.is_gift?a("div",[t._v("-")]):null!==s.is_writeoff?a("div",[s.is_writeoff?a("div",[t._v("已核销")]):s.writeoffed_num?a("div",{staticStyle:{color:"#1890ff"}},[t._v("\n            已核销 "+t._s(s.writeoffed_num)+" 件\n          ")]):a("div",{staticStyle:{color:"#F5222D"}},[t._v("待核销")])]):t._e()]}},{key:"cartNum",fn:function(e){var s=e.row;return[s.is_gift?a("div",[t._v("-")]):a("div",[t._v(t._s(s.write_times))])]}},{key:"surplus_num",fn:function(e){var s=e.row,r=e.index;return[a("div",{staticClass:"acea-row row-middle"},[a("div",{staticClass:"surplus-num"},[a("div",{staticClass:"operation reduce",class:{off:s.value<=1},on:{click:function(e){return t.reduce(s,r)}}},[a("Icon",{attrs:{type:"md-remove-circle"}})],1),a("InputNumber",{attrs:{max:s.write_surplus_times,min:1,readonly:s.write_surplus_times<=1,precision:0},model:{value:t.writeOffData[r].value,callback:function(e){t.$set(t.writeOffData[r],"value",e)},expression:"writeOffData[index].value"}}),a("div",{staticClass:"operation add",class:{off:s.value>=s.write_surplus_times},on:{click:function(e){return t.add(s,r)}}},[a("Icon",{attrs:{type:"md-add-circle"}})],1)],1),6==s.product_type?a("div",{staticClass:"relative fs-14 ml-20 pointer text-wlll-1890FF",on:{click:function(e){return t.serviceTap(s)}}},[a("div",{staticClass:"badge"},[t._v(t._s(s.unservice_num))]),t._v("\n            待服务\n          ")]):t._e()])]}}])})],1),a("reservation",{ref:"reservation",on:{submitSuccess:t.submitSuccess}})],1)},l=[],c=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Drawer",{attrs:{title:"预约单"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[s("div",{staticClass:"acea-row"},[s("div",{staticClass:"left bg-w111-F5F5F5 px-25"},[s("div",{staticClass:"acea-row row-middle pointer h-52 border-b-1-EAEAEA fs-14 text-wlll-606266 pl-26"},[s("div",{staticClass:"w-100"},[t._v("预约人")]),s("div",{staticClass:"w-134"},[t._v("时段")]),s("div",{staticClass:"w-78"},[t._v("模式")]),s("div",{staticClass:"w-70"},[t._v("状态")])]),t._l(t.list,(function(e,a){return s("div",{key:a,staticClass:"acea-row row-middle pointer h-62 border-b-1-EAEAEA fs-14 text-wlll-303133 pl-26",class:t.id==e.id?"on":"",on:{click:function(s){return t.reservationTap(e.id)}}},[s("div",{staticClass:"w-100"},[t._v(t._s(e.reservation_name))]),s("div",{staticClass:"w-134"},[t._v(t._s(e.reservation_start)+"-"+t._s(e.reservation_end))]),s("div",{staticClass:"w-78"},[t._v(t._s(3==e.reservation_type?"上门":"到店"))]),-1==e.status?s("div",{staticClass:"w-70"},[t._v("已取消")]):0==e.status?s("div",{staticClass:"w-70"},[t._v("待服务")]):1==e.status?s("div",{staticClass:"w-70"},[t._v("进行中")]):2==e.status?s("div",{staticClass:"w-70"},[t._v("已完成")]):t._e()])}))],2),s("div",{staticClass:"flex-1"},[s("div",{staticClass:"conter ml-25 mr-25"},[t.info.cart_info?s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("预约服务：")]),s("div",{staticClass:"flex-1 h-36 lh-36 ml-6 border-1-DDDDDD bg-w111-F9F9F9 rd-4 px-6 text-wlll-303133 line1"},[t._v("\n\t\t\t\t\t\t"+t._s(t.info.cart_info.productInfo.store_name))])]):t._e(),t.info.cart_info?s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("服务规格：")]),s("div",{staticClass:"flex-1 h-36 lh-36 ml-6 border-1-DDDDDD bg-w111-F9F9F9 rd-4 px-6 text-wlll-303133 line1"},[t._v("\n\t\t\t\t\t\t"+t._s(t.info.cart_info.productInfo.attrInfo.suk))])]):t._e(),s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("联系人：")]),s("Input",{staticClass:"flex-1 ml-6",attrs:{disabled:t.disabled,placeholder:"请输入联系人"},model:{value:t.info.reservation_name,callback:function(e){t.$set(t.info,"reservation_name",e)},expression:"info.reservation_name"}})],1),s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("联系电话：")]),s("Input",{staticClass:"flex-1 ml-6",attrs:{disabled:t.disabled,type:"number",placeholder:"请输入联系电话"},model:{value:t.info.reservation_phone,callback:function(e){t.$set(t.info,"reservation_phone",e)},expression:"info.reservation_phone"}})],1),s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("预约日期：")]),s("DatePicker",{staticClass:"flex-1 ml-6",attrs:{transfer:!0,disabled:t.disabled,type:"date",placeholder:"选择预约日期"},model:{value:t.info.reservation_time,callback:function(e){t.$set(t.info,"reservation_time",e)},expression:"info.reservation_time"}})],1),s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("预约时间：")]),s("Select",{staticClass:"flex-1 ml-6",attrs:{transfer:!0,disabled:t.disabled,placeholder:"请选择预约时间",clearable:""},model:{value:t.info.reservation_time_id,callback:function(e){t.$set(t.info,"reservation_time_id",e)},expression:"info.reservation_time_id"}},t._l(t.reservationTime,(function(e,a){return s("Option",{attrs:{value:e.id}},[t._v(t._s(e.show_time))])})),1)],1),3==t.info.reservation_type?s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("上门地址：")]),s("Cascader",{staticClass:"flex-1 ml-6",attrs:{disabled:t.disabled,data:t.addresData,"load-data":t.loadData},on:{"on-change":t.addchack},model:{value:t.info.reservation_address_city_id,callback:function(e){t.$set(t.info,"reservation_address_city_id",e)},expression:"info.reservation_address_city_id"}})],1):t._e(),3==t.info.reservation_type?s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("详细地址：")]),s("Input",{staticClass:"flex-1 ml-6",attrs:{disabled:t.disabled,placeholder:"请输入详细地址"},model:{value:t.reservationAddress,callback:function(e){t.reservationAddress=e},expression:"reservationAddress"}})],1):t._e(),s("div",{staticClass:"acea-row row-middle mt-28 fs-13"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right"},[t._v("服务人员：")]),s("Select",{staticClass:"flex-1 h-36 ml-6",attrs:{transfer:!0,placeholder:"请选择服务人员",clearable:"",disabled:1==t.info.status},model:{value:t.service_staff_id,callback:function(e){t.service_staff_id=e},expression:"service_staff_id"}},t._l(t.staffList,(function(e,a){return s("Option",{attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1),t.isShow&&t.info.reservation_info.length?s("div",{staticClass:"acea-row mt-28 fs-14"},[s("div",{staticClass:"text-wlll-606266 w-84 text-right pt-5"},[t._v(t._s(t.info.custom_form_title)+"信息：")]),s("div",{staticClass:"acea-row flex-1 ml-6 border-1-DDDDDD bg-w111-F9F9F9 rd-4 px-6 text-wlll-303133 pb-24"},t._l(t.info.reservation_info,(function(e,a){return s("div",{key:a},["dateranges"===e.name?s("div",{staticClass:"mr-48 mt-24"},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(e.titleConfig.value)+"："+t._s(e.value[0]+"/"+e.value[1])+"\n\t\t\t\t\t\t\t")]):"uploadPicture"===e.name?s("div",{staticClass:"acea-row mt-24"},[s("div",[t._v("图片：")]),s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"acea-row flex-1"},t._l(e.value,(function(t,e){return s("div",{key:e,staticClass:"w-58 h-58 mr-8 mb5"},[s("img",{staticClass:"w-full h-full rd-4",attrs:{src:t}})])})),0)]):s("div",{staticClass:"mr-48 mt-24"},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(e.titleConfig.value)+"："+t._s(e.value||"-")+"\n\t\t\t\t\t\t\t")])])})),0)]):t._e()]),s("div",{staticClass:"footer acea-row row-center-wrapper"},[t.disabled?s("div",[0==t.info.status?s("div",{staticClass:"acea-row row-center-wrapper"},[s("div",{staticClass:"acea-row row-center-wrapper w-176 h-46 rd-30px fs-16 bg-w111-F5F5F5 text-wlll-606266 pointer",on:{click:t.cancelTap}},[t._v("取消预约")]),s("div",{staticClass:"acea-row row-center-wrapper w-176 h-46 rd-30px fs-16 bg-w111-F5F5F5 text-wlll-606266 pointer ml-20",on:{click:function(e){t.disabled=!1}}},[t._v("修改预约")]),s("div",{staticClass:"acea-row row-center-wrapper w-176 h-46 rd-30px fs-16 bg-w111-1890FF text-wlll-FFFFFF pointer ml-20",on:{click:t.serviceStart}},[t._v("开始服务")])]):t._e(),1==t.info.status?s("div",{staticClass:"acea-row row-center-wrapper w-176 h-46 rd-30px fs-16 bg-w111-1890FF text-wlll-FFFFFF pointer ml-20",on:{click:t.writeTap}},[t._v("立即核销")]):t._e()]):s("div",{staticClass:"acea-row row-center-wrapper"},[s("div",{staticClass:"w-176 h-46 rd-30px bg-w111-F5F5F5 acea-row row-center-wrapper text-wlll-606266 fs-16 pointer",on:{click:function(e){t.disabled=!0}}},[t._v("取消")]),s("div",{staticClass:"w-176 h-46 rd-30px bg-w111-1890FF acea-row row-center-wrapper text-wlll-FFFFFF fs-16 pointer ml-20",on:{click:t.editTap}},[t._v("确定")])])])])])])},d=[],u=s("52df"),v={name:"reservation",data:function(){return{modals:!1,list:[],id:0,info:{},staffList:[],service_staff_id:0,disabled:!0,reservationTime:[],addresData:[],reservationAddress:"",regionAddress:"",isShow:0}},mounted:function(){},methods:{addchack:function(t,e){this.info.reservation_address_city_id=t,this.regionAddress=e.map((function(t){return t.label})).join("/")},cityInfo:function(t){var e=this;Object(u["a"])(t).then((function(t){e.addresData=t.data}))},loadData:function(t,e){t.loading=!0,Object(u["a"])({pid:t.value}).then((function(s){t.children=s.data,t.loading=!1,e()}))},reservationTimeTap:function(t){var e=this;Object(u["f"])(t).then((function(t){e.reservationTime=t.data})).catch((function(t){e.$Message.error(t.msg)}))},editTap:function(){var t=this;if(!this.info.reservation_phone)return this.$Message.error("请输入联系电话");if(!/^1(3|4|5|7|8|9|6)\d{9}$/.test(this.info.reservation_phone))return this.$Message.error("请输入正确的联系电话");if(!this.info.reservation_time)return this.$Message.error("请选择预约日期");if(!this.info.reservation_time_id)return this.$Message.error("请选择预约时间");if(3==this.info.reservation_type){if(!this.info.reservation_address_city_id.length)return this.$Message.error("请选择省市区");if(!this.reservationAddress)return this.$Message.error("请输入上门地址")}var e=this.regionAddress+"/"+this.reservationAddress,s={reservation_name:this.info.reservation_name,reservation_phone:this.info.reservation_phone,reservation_time:this.info.reservation_time,reservation_time_id:this.info.reservation_time_id,reservation_address:e};Object(u["j"])(this.info.id,s).then((function(e){t.$Message.success(e.msg),t.disabled=!0,t.orderDetail(t.id)}))},reservationOrder:function(t){var e=this;this.cityInfo({pid:0}),Object(u["e"])({oid:t,status:0}).then((function(t){e.list=t.data.list,e.id=e.list[0].id,e.orderDetail(e.id),e.allStaffList()})).catch((function(t){e.$Message.error(t.msg)}))},writeTap:function(){var t=this,e={title:"核销",url:"reservation/order/service/set/".concat(this.info.id),method:"post",ids:{status:2,service_staff_id:0}};this.$modalSure(e).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$emit("submitSuccess")})).catch((function(e){t.$Message.error(e.msg)}))},allStaffList:function(){var t=this;Object(u["b"])().then((function(e){t.staffList=e.data})).catch((function(e){t.$Message.error(e.msg)}))},serviceStart:function(){var t=this;if(!this.service_staff_id)return this.$Message.error("请选择服务人员");var e={status:1,service_staff_id:this.service_staff_id};Object(u["i"])(this.id,e).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$emit("submitSuccess")})).catch((function(e){t.$Message.error(e.msg)}))},cancelTap:function(){var t=this,e={title:"取消预约",url:"reservation/order/cancel/".concat(this.id),method:"post"};this.$modalSure(e).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$emit("submitSuccess")})).catch((function(e){t.$Message.error(e.msg)}))},reservationTap:function(t){this.id=t,this.orderDetail(this.id)},orderDetail:function(t){var e=this;this.reservationTimeTap(t),Object(u["d"])(t).then((function(t){e.info=t.data;var s=t.data.reservation_address.split(" ");e.reservationAddress=s[s.length-1],s.pop(),e.regionAddress=s.join("/");var a=e.info.reservation_info;a.length&&a.forEach((function(t){if(t.value)return e.isShow=1})),e.service_staff_id=t.data.service_staff_id})).catch((function(t){e.$Message.error(t.msg)}))}}},f=v,m=(s("f6d2"),s("2877")),_=Object(m["a"])(f,c,d,!1,null,"2e255db7",null),h=_.exports,p=s("f8b7");function g(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function w(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?g(s,!0).forEach((function(e){C(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):g(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function C(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var D={name:"userOrder",props:["selectData","orderNumId"],components:{goodsList:o["a"],reservation:h},data:function(){return{writeOffData:[],writeOffItem:[],columns:[{type:"selection",width:50,align:"center"},{title:"商品",slot:"image",align:"left",minWidth:150},{title:"规格",slot:"value",align:"left",minWidth:90},{title:"商品售价",slot:"sellPrice",align:"left",minWidth:60},{title:"商品优惠价",slot:"price",align:"left",minWidth:60},{title:"状态",slot:"writeOff",align:"left",minWidth:70},{title:"总数",slot:"cartNum",align:"left",minWidth:30},{title:"待核销数量",key:"write_surplus_times",slot:"surplus_num",width:250}],selectDataList:[],loading:!1,give_integral_img:s("29ba"),give_coupon_img:s("16f5"),total_num:0}},watch:{selectData:{handler:function(t,e){if(t){var s={oid:t.id};t.id&&this.getWriteOff(s)}},deep:!0}},mounted:function(){var t={oid:this.selectData.id};this.selectData.id&&this.getWriteOff(t)},methods:{submitSuccess:function(){var t={oid:this.selectData.id};this.getWriteOff(t)},serviceTap:function(t){0!=t.unservice_num&&(this.$refs.reservation.modals=!0,this.$refs.reservation.reservationOrder(this.selectData.id))},reduce:function(t,e){t.value<=1||this.writeOffData[e].value--},add:function(t,e){t.value>=t.write_surplus_times||this.writeOffData[e].value++},getVerifyData:function(){if(!this.selectDataList.length)return this.$Message.error("请选择要核销的商品");var t=[];this.selectDataList.forEach((function(e){t.push({cart_id:e.cart_id,cart_num:e.surplus_num})}))},remarks:function(){this.$emit("remarks")},cancel:function(){this.$refs.selection.selectAll(!1)},selectOne:function(t){var e=t.map((function(t){return t.id}));this.writeOffData.forEach((function(t){t._checked=e.includes(t.id)})),this.selectDataList=this.writeOffData.filter((function(t){return t._checked})),this.$emit("selectData",this.selectDataList)},getWriteOff:function(t){var e=this;Object(p["jb"])(t).then((function(t){var s=t.data.cart_info||[],a=function(t,e){return{is_gift:1,write_surplus_times:0,cart_info:{productInfo:{image:t,store_name:e,attrInfo:{suk:"-",price:"-"}},truePrice:"-"}}};e.total_num=s.length,e.selectData.give_integral&&s.push(a(e.give_integral_img,"赠送".concat(e.selectData.give_integral,"积分"))),e.selectData.give_coupon.forEach((function(t){s.push(a(e.give_coupon_img,t.coupon_title))})),e.writeOffData=s.map((function(t){return w({},t,{value:t.write_surplus_times?1:0,_disabled:1==t.is_writeoff||1==t.is_gift,_checked:!1})}))})).catch((function(t){e.$Message.error(t.msg)}))}}},b=D,A=(s("9180"),Object(m["a"])(b,n,l,!1,null,"5f83dac2",null)),I=A.exports,O=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"detail"},[s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("收货信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("收货人：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),s("li",{staticClass:"item"},[s("div",[t._v("收货电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])])])]),s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("用户信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("用户昵称：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.uid?t.orderDatalist.userInfo.nickname:"游客")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("绑定电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.userInfo.phone))])])])]),6==t.orderDatalist.orderInfo.product_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("预约信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("服务类型：")]),s("div",{staticClass:"value"},[t._v(t._s(3==t.orderDatalist.orderInfo.reservation_type?"上门服务":"到店服务"))])]),s("li",{staticClass:"item"},[s("div",[t._v("预约模式：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time_id>0?"购买时预约":"先买后约"))])]),t.orderDatalist.orderInfo.reservation_time?s("li",{staticClass:"item"},[s("div",[t._v("预约日期：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time))])]):t._e(),t.orderDatalist.orderInfo.reservation_show_time?s("li",{staticClass:"item"},[s("div",[t._v("预约时段：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_show_time))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("预约人：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),s("li",{staticClass:"item"},[s("div",[t._v("预约电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])])]),t.orderDatalist.orderInfo.user_address.trim()?s("div",{staticClass:"address acea-row row-middle"},[s("div",[t._v("预约地址：")]),s("div",[t._v(t._s(t.orderDatalist.orderInfo.user_address))])]):t._e()]):t._e(),s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("订单信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("创建时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._add_time))])]),s("li",{staticClass:"item"},[s("div",[t._v("商品总数：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.total_num))])]),s("li",{staticClass:"item"},[s("div",[t._v("商品总价：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.total_price))])]),t.orderDatalist.orderInfo.first_order_price>0?s("li",{staticClass:"item"},[s("div",[t._v("首单优惠：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.first_order_price))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("优惠券金额：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.coupon_price))])]),s("li",{staticClass:"item"},[s("div",[t._v("积分抵扣：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.deduction_price||0)+"\n          ")])]),parseFloat(t.orderDatalist.orderInfo.use_integral)?s("li",{staticClass:"item"},[s("div",[t._v("使用积分：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(parseFloat(t.orderDatalist.orderInfo.use_integral))+"\n          ")])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("支付邮费：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.pay_postage))])]),s("li",{staticClass:"item"},[s("div",[t._v("会员商品优惠：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.vip_true_price||0)+"\n          ")])]),0!=t.orderDatalist.orderInfo.first_order_price?s("li",{staticClass:"item"},[s("div",[t._v("新人首单优惠：")]),s("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.first_order_price)+"\n          ")])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?s("li",{staticClass:"item"},[s("div",[t._v("门店名称：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._store_name))])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?s("li",{staticClass:"item"},[s("div",[t._v("核销码：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.verify_code))])]):t._e(),s("li",{staticClass:"item"},[s("div",[t._v("推广人：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.spread_name)+"/ID:"+t._s(t.orderDatalist.userInfo.spread_uid)+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("支付时间：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._pay_time))])]),s("li",{staticClass:"item"},[s("div",[t._v("支付方式：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo._status._payType)+"\n          ")])]),t.orderDatalist.orderInfo.store_order_sn?s("li",{staticClass:"item"},[s("div",[t._v("原订单号：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.store_order_sn))])]):t._e()])]),t.orderDatalist.orderInfo.promotions_detail.length?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("活动信息")]),s("ul",{staticClass:"list"},t._l(t.orderDatalist.orderInfo.promotions_detail,(function(e,a){return s("li",{key:a,staticClass:"item"},[s("div",[t._v(t._s(e.title)+"：")]),s("div",{staticClass:"value"},[t._v("￥"+t._s(e.promotions_price))])])})),0)]):t._e(),t.isShow||6==t.orderDatalist.orderInfo.product_type&&t.orderDatalist.orderInfo.reservation_time_id>0||6!=t.orderDatalist.orderInfo.product_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("自定义留言")]),t._l(t.orderDatalist.orderInfo.custom_form,(function(e,a){return s("div",{key:a},[6==t.orderDatalist.orderInfo.product_type&&e.length?s("div",{staticClass:"item fs-14 mb10"},[t._v(t._s(t.orderDatalist.orderInfo.custom_form_title)+t._s(a+1))]):t._e(),s("ul",{staticClass:"list"},t._l(e,(function(e,a){return s("li",{directives:[{name:"show",rawName:"v-show",value:e.value&&-1==["uploadPicture","dateranges"].indexOf(e.name)||e.value.length&&-1!=["uploadPicture","dateranges"].indexOf(e.name),expression:"(item.value && ['uploadPicture','dateranges'].indexOf(item.name) == -1) || (item.value.length && ['uploadPicture','dateranges'].indexOf(item.name) != -1)"}],key:a,staticClass:"item"},[s("div",{staticClass:"txtVal"},[t._v(t._s(e.titleConfig.value)+"：")]),"dateranges"===e.name?s("div",{staticClass:"value"},[t._v(t._s(e.value[0]+"/"+e.value[1]))]):"uploadPicture"===e.name?s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"value"},t._l(e.value,(function(t,e){return s("div",{key:e,staticClass:"image"},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0):s("div",{staticClass:"value"},[t._v(t._s(e.value||"-"))])])})),0)])}))],2):t._e(),t.orderDatalist.orderInfo.mark?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("买家留言")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item mark"},[s("div",[t._v("备注：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.mark||"-"))])])])]):t._e(),t.orderDatalist.orderInfo.remark?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("订单备注")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item mark"},[s("div",{staticClass:"item1"},[t._v("备注：")]),s("Tooltip",{attrs:{"max-width":"300",placement:"top-start"}},[s("span",{},[t._v(t._s(t.orderDatalist.orderInfo.remark))]),s("p",{attrs:{slot:"content"},slot:"content"},[t._v(t._s(t.orderDatalist.orderInfo.remark||"-"))])])],1)])]):t._e(),"express"===t.orderDatalist.orderInfo.delivery_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("物流信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("快递公司：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("快递单号：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_id)),s("span",{staticClass:"logisticsLook",on:{click:t.openLogistics}},[t._v("查询")])])])])]):t._e(),"send"===t.orderDatalist.orderInfo.delivery_type?s("div",{staticClass:"section"},[s("div",{staticClass:"title"},[t._v("配送信息")]),s("ul",{staticClass:"list"},[s("li",{staticClass:"item"},[s("div",[t._v("送货人姓名：")]),s("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),s("li",{staticClass:"item"},[s("div",[t._v("送货人电话：")]),s("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.delivery_id))])])])]):t._e()])},x=[],y={name:"orderDetails",props:{id:{type:Number}},data:function(){return{orderDatalist:{orderInfo:{},userInfo:{}},isShow:0}},watch:{id:function(t){this.getOrderInfo()},orderDatalist:function(t){var e=this;t.orderInfo&&t.orderInfo.custom_form&&t.orderInfo.custom_form.length&&t.orderInfo.custom_form.forEach((function(t){t.length&&t.forEach((function(t){if(t.value)return e.isShow=1}))}))}},mounted:function(){this.getOrderInfo()},methods:{getOrderInfo:function(){var t=this;Object(p["B"])(this.id).then((function(e){t.orderDatalist=e.data}))}}},k=y,F=(s("c13f"),Object(m["a"])(k,O,x,!1,null,"aca3731a",null)),M=F.exports,P=s("6dc2"),N=s("22f89"),T=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单核销",width:"1000","footer-hide":""},on:{"on-cancel":t.cancel},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[s("Card",{attrs:{bordered:!1,"dis-hover":""}},[s("Table",{ref:"selection",attrs:{columns:t.columns,border:"",data:t.writeOffData,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var a=e.row;e.index;return[s("div",{staticClass:"product-data"},[s("img",{staticClass:"image",attrs:{src:a.cart_info.productInfo.image}}),s("div",{staticClass:"name line2"},[t._v("\n            "+t._s(a.cart_info.productInfo.store_name)+"\n          ")])])]}},{key:"value",fn:function(e){var a=e.row;e.index;return[s("div",[t._v(t._s(a.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var a=e.row;e.index;return[s("div",[t._v(t._s(a.cart_info.productInfo.attrInfo?a.cart_info.productInfo.attrInfo.price:a.cart_info.productInfo.price)+"\n        ")])]}},{key:"price",fn:function(e){var a=e.row;e.index;return[s("div",[t._v(t._s(a.cart_info.truePrice))])]}},{key:"writeOff",fn:function(e){var a=e.row;e.index;return[a.is_writeoff?s("div",[t._v("已核销")]):s("div",{staticStyle:{color:"#F5222D"}},[t._v("待核销")])]}},{key:"cartNum",fn:function(e){var a=e.row;e.index;return[s("div",[t._v(t._s(a.cart_info.cart_num))])]}}])}),s("Button",{attrs:{type:"primary"},on:{click:t.submitWriteOff}},[t._v("确认核销")])],1)],1)},G=[],L={name:"orderRecord",props:{orderNumId:String},data:function(){var t=this;return{modals:!1,loading:!1,writeOffData:[],writeOffItem:[],columns:[{type:"selection",width:50,align:"center"},{title:"商品信息",slot:"image",align:"center",minWidth:150},{title:"规格",slot:"value",align:"center",minWidth:90},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:60},{title:"商品优惠价",slot:"price",align:"center",minWidth:60},{title:"状态",slot:"writeOff",align:"center",minWidth:30},{title:"总数",slot:"cartNum",align:"center",minWidth:30},{title:"待核销数量",slot:"surplus_num",align:"center",minWidth:130,render:function(e,s){return e("div",[e("InputNumber",{props:{min:1,max:s.row.numShow,value:s.row.surplus_num||1,disabled:!!s.row.is_writeoff},on:{"on-change":function(e){s.row.surplus_num=e||1,t.writeOffItem.length?t.writeOffItem.forEach((function(e){e.id==s.row.id?e.surplus_num=s.row.surplus_num:t.writeOffItem.push(s.row)})):t.writeOffItem.push(s.row),t.selectData.forEach((function(e,a){e.cart_id===s.row.cart_id&&t.selectData.splice(a,1,s.row)}))}}})])}}],selectData:[]}},mounted:function(){},methods:{cancel:function(){this.$refs.selection.selectAll(!1)},selectOne:function(t){var e=this;t.forEach((function(t){e.writeOffItem.forEach((function(e){t.id==e.id&&(t.surplus_num=e.surplus_num)}))})),this.selectData=t},getWriteOff:function(t){var e=this;Object(p["jb"])(t).then((function(t){var s=t.data.cart_info;s.forEach((function(t){t.numShow=t.surplus_num,t.is_writeoff&&(t._disabled=!0)})),e.writeOffData=s})).catch((function(t){e.$Message.error(t.msg)}))},submitWriteOff:function(){var t=this;if(!this.selectData.length)return this.$Message.error("请选择要核销的商品");var e=[];this.selectData.forEach((function(t){e.push({cart_id:t.cart_id,cart_num:t.surplus_num})})),Object(p["cb"])(this.orderNumId,{cart_ids:e}).then((function(e){t.$Message.success(e.msg),t.modals=!1,t.$refs.selection.selectAll(!1),t.$emit("submitSuccess")})).catch((function(e){t.$Message.error(e.msg)}))}}},j=L,S=(s("4742"),Object(m["a"])(j,T,G,!1,null,"03381d6f",null)),E=S.exports,W=s("a464");function B(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function Q(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?B(s,!0).forEach((function(e){z(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):B(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function z(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var K={components:{orderList:i["a"],goodsList:o["a"],userOrder:I,orderDetails:M,orderRemark:N["a"],orderRecord:P["a"],orderWriteOff:E,filterModal:W["a"]},data:function(){return{orderId:0,orderListData:[],tabs:["商品信息","订单详情","订单记录"],sle:0,filterModal:!1,userFrom:{keyword:"",page:1,limit:9},dataList:[],orderData:{keyword:"",type:"",status:"5",time:"",staff_id:"",real_name:"",page:1,limit:10},selectOrderData:{},orderInfoData:{},count:0,orderStatusList:[],selectOrderDatas:[],currentPage:1}},created:function(){this.getVerifyList()},methods:{addPage:function(){this.orderListData.length<this.count&&this.orderData.page++,this.getVerifyList()},search:function(){1==this.currentPage&&(this.currentPage=2),this.orderListData=[],this.selectOrderData={},this.orderData.page=1,this.sle=0,this.getVerifyList()},searchList:function(t){this.filterModal=!1,this.orderData=Q({},this.orderData,{},t),this.search()},remarks:function(){this.$refs.remarks.modals=!0,this.$refs.remarks.formValidate.remark=this.selectOrderData.remark},submitFail:function(){},point:function(){var t=this;this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(this.selectOrderData.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){t.$Message.success(e.msg)})).catch((function(e){t.$Message.error(e.msg)}))},selectData:function(t){this.selectOrderDatas=t},getVerifyData:function(){var t=this,e=this.selectOrderDatas.map((function(t){return{cart_id:t.cart_id,cart_num:t.value}}));if(!this.selectOrderDatas.length)return this.$Message.error("请选择要核销的商品");4==this.selectOrderData.product_type?this.$modalForm(Object(p["R"])(this.selectOrderData.id,{cart_num:this.selectOrderDatas[0].value})).then((function(e){t.$Message.success(e.msg),t.orderListData=[],t.getVerifyList(),t.selectOrderData.status=2})):Object(p["cb"])(this.selectOrderData.id,{cart_ids:e}).then((function(e){t.$Message.success(e.msg),t.orderListData=[],t.getVerifyList(),t.selectOrderData.status=2})).catch((function(e){t.$Message.error(e.msg)}))},selectOrder:function(t){this.selectOrderData=t},tabClick:function(t){switch(this.sle=t,t){case 1:break}},getVerifyList:function(){var t=this;Object(p["L"])(this.orderData).then((function(e){e.data.data=e.data.data.map((function(e){var s=[];for(var a in e._info){var r=e._info[a];s.push(r)}return t.$set(e,"_infoData",s),e})),t.orderListData=t.orderListData.concat(e.data.data),t.count=e.data.count,1==t.orderData.page&&(t.selectOrderData=t.orderListData[0]||{})})).catch((function(e){t.$Message.error(e.msg)}))},onSearch:function(){this.orderData.keyword&&(this.currentPage=2,this.search())},goBack:function(){this.currentPage=1,this.orderData.keyword="",this.orderData.type="",this.orderData.status="",this.orderData.time="",this.orderData.staff_id="",this.orderData.real_name="",this.orderData.page=1,this.filterModal=!1},goAll:function(){this.orderData.keyword="",this.search()}}},Y=K,V=(s("4d75"),Object(m["a"])(Y,a,r,!1,null,"5966ee44",null));e["default"]=V.exports},"652b":function(t,e,s){},"653d":function(t,e,s){t.exports=s.p+"view_cashier/img/tourist.908b01d3.png"},7695:function(t,e,s){},9180:function(t,e,s){"use strict";var a=s("652b"),r=s.n(a);r.a},c13f:function(t,e,s){"use strict";var a=s("2cc0"),r=s.n(a);r.a},f6d2:function(t,e,s){"use strict";var a=s("7695"),r=s.n(a);r.a}}]);