(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0b64a2dc"],{"13f2":function(t,e,i){"use strict";var a=i("3cc4"),s=i.n(a);s.a},"3cc4":function(t,e,i){},b0e7:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"Modal"},[i("Row",{staticClass:"colLeft"},[i("Col",{staticClass:"colLeft left",attrs:{xl:6,lg:6,md:6,sm:6,xs:24}},[i("div",{staticClass:"Nav"},[i("div",{staticClass:"input"},[i("Input",{staticStyle:{width:"90%"},attrs:{search:"","enter-button":"",placeholder:"请输入分类名称"},on:{"on-search":t.changePage},model:{value:t.uploadName.name,callback:function(e){t.$set(t.uploadName,"name",e)},expression:"uploadName.name"}})],1),i("div",{staticClass:"trees-coadd"},[i("div",{staticClass:"scollhide"},[i("div",{staticClass:"trees"},[i("Tree",{ref:"tree",staticClass:"treeBox",attrs:{data:t.treeData,render:t.renderContent,"load-data":t.loadData}}),t.searchClass&&t.treeData.length<=1?i("div",{staticClass:"searchNo"},[t._v("此分类暂无数据")]):t._e()],1)])])])]),i("Col",{staticClass:"colLeft right",attrs:{xl:18,lg:18,md:18,sm:18,xs:24}},[i("div",{staticClass:"conter"},[i("div",{staticClass:"bnt acea-row row-middle"},[i("Col",{attrs:{span:"24"}},[0!==t.isShow?i("Button",{staticClass:"mr10",staticStyle:{width:"100px"},attrs:{type:"primary",disabled:0===t.checkPicList.length},on:{click:t.checkPics}},[t._v("使用选中图片")]):t._e(),i("Upload",{staticClass:"mr10 mb10",staticStyle:{"margin-top":"1px",display:"inline-block"},attrs:{"show-upload-list":!1,action:t.fileUrl,"before-upload":t.beforeUpload,data:t.uploadData,headers:t.header,multiple:!0,"on-success":t.handleSuccess}},[i("Button",{attrs:{type:"primary"}},[t._v("上传图片")])],1),i("Button",{staticClass:"mr10",attrs:{type:"error",disabled:0===t.checkPicList.length},on:{click:function(e){return e.stopPropagation(),t.editPicList("图片")}}},[t._v("删除图片")]),i("i-select",{staticClass:"treeSel",staticStyle:{width:"250px"},attrs:{value:t.pids,placeholder:"图片移动至"}},[t._l(t.list,(function(e,a){return i("i-option",{key:a,staticStyle:{display:"none"},attrs:{value:e.value}},[t._v("\n            "+t._s(e.title)+"\n          ")])})),i("Tree",{ref:"reference",staticClass:"treeBox",attrs:{data:t.treeData2,render:t.renderContentSel,"load-data":t.loadData}})],2)],1)],1),i("div",{staticClass:"pictrueList acea-row"},[i("Row",{staticClass:"conter",attrs:{gutter:24}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowPic,expression:"isShowPic"}],staticClass:"imagesNo"},[i("Icon",{attrs:{type:"ios-images",size:"60",color:"#dbdbdb"}}),i("span",{staticClass:"imagesNo_sp"},[t._v("图片库为空")])],1),i("div",{staticClass:"acea-row mb10"},t._l(t.pictrueList,(function(e,a){return i("div",{key:a,staticClass:"pictrueList_pic mr10 mb10",on:{mouseenter:function(i){return t.enterMouse(e)},mouseleave:function(i){return t.enterMouse(e)}}},[e.num>0?i("p",{staticClass:"number"},[i("Badge",{attrs:{count:e.num,type:"error",offset:[11,12]}},[i("a",{staticClass:"demo-badge",attrs:{href:"#"}})])],1):t._e(),i("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.satt_dir,expression:"item.satt_dir"}],class:e.isSelect?"on":"",on:{click:function(i){return i.stopPropagation(),t.changImage(e,a,t.pictrueList)}}}),i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"},on:{mouseenter:function(i){return t.enterLeave(e)},mouseleave:function(i){return t.enterLeave(e)}}},[e.isEdit?i("Input",{staticStyle:{width:"80%"},attrs:{size:"small",type:"text"},on:{"on-blur":function(i){return t.bindTxt(e)}},model:{value:e.real_name,callback:function(i){t.$set(e,"real_name",i)},expression:"item.real_name"}}):i("p",[t._v("\n                  "+t._s(e.editName)+"\n                ")]),e.isShowEdit?i("span",{staticClass:"iconfont iconbianji1",on:{click:function(t){e.isEdit=!e.isEdit}}}):t._e()],1),i("div",{directives:[{name:"show",rawName:"v-show",value:e.realName&&e.real_name,expression:"item.realName && item.real_name"}],staticClass:"nameStyle"},[t._v("\n                "+t._s(e.real_name)+"\n              ")])])})),0)])],1),i("div",{staticClass:"footer acea-row row-right"},[i("Page",{attrs:{total:t.total,"show-elevator":"","show-total":"",current:t.fileData.page,"page-size":t.fileData.limit},on:{"on-change":t.pageChange}})],1)])])],1)],1)},s=[],n=i("a34a"),r=i.n(n),c=i("b6bd");function o(t){return Object(c["a"])({url:"file/category",method:"get",params:t})}function l(t){return Object(c["a"])({url:"file/category/create",method:"get",params:t})}function u(t){return Object(c["a"])({url:"file/category/".concat(t,"/edit"),method:"get"})}function d(t){return Object(c["a"])({url:"file/file",method:"get",params:t})}function h(t){return Object(c["a"])({url:"file/file/do_move",method:"put",data:t})}function f(t,e){return Object(c["a"])({url:"file/file/update/"+t,method:"put",data:e})}var p=i("d708"),m=i("c276");function g(t){return y(t)||w(t)||v()}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function w(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function y(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}function k(t,e,i,a,s,n,r){try{var c=t[n](r),o=c.value}catch(l){return void i(l)}c.done?e(o):Promise.resolve(o).then(a,s)}function b(t){return function(){var e=this,i=arguments;return new Promise((function(a,s){var n=t.apply(e,i);function r(t){k(n,a,s,r,c,"next",t)}function c(t){k(n,a,s,r,c,"throw",t)}r(void 0)}))}}var C={name:"uploadPictures",props:{isChoice:{type:String,default:""},gridBtn:{type:Object,default:null},gridPic:{type:Object,default:null},isShow:{type:Number,default:1}},data:function(){return{searchClass:!1,spinShow:!1,fileUrl:p["a"].apiBaseURL+"/file/upload",modalPic:!1,treeData:[],treeData2:[],pictrueList:[],uploadData:{},checkPicList:[],uploadName:{name:""},FromData:null,treeId:0,isJudge:!1,buttonProps:{type:"default",size:"small"},fileData:{pid:0,page:1,limit:24},total:0,pids:0,list:[],modalTitleSs:"",isShowPic:!1,header:{},ids:[]}},mounted:function(){this.getToken(),this.getList(),this.getFileList()},methods:{enterMouse:function(t){t.realName=!t.realName},enterLeave:function(t){t.isShowEdit=!t.isShowEdit},getToken:function(){this.header["Authori-zation"]="Bearer "+m["a"].cookies.get("token")},renderContent:function(t,e){var i=this,a=e.root,s=e.node,n=e.data,r=[];return 0==n.pid&&r.push(t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.append(a,s,n)}}},"添加分类")),""!==n.id&&r.push(t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.editPic(a,s,n)}}},"编辑分类"),t("div",{class:["ivu-dropdown-item"],on:{click:function(){i.remove(a,s,n,"分类")}}},"删除分类")),t("span",{class:["ivu-span"],style:{display:"inline-block",width:"88%",height:"32px",lineHeight:"32px",position:"relative",color:"rgba(0,0,0,0.6)",cursor:"pointer"},on:{mouseenter:function(){i.onMouseOver(a,s,n)},mouseleave:function(){i.onMouseOver(a,s,n)}}},[t("span",{on:{click:function(t){i.checkPicList=[],i.appendBtn(a,s,n,t)}}},n.title),t("div",{style:{display:"inline-block",float:"right"}},[t("Icon",{props:{type:"ios-more"},style:{marginRight:"8px",fontSize:"20px",display:n.flag?"inline":"none"},on:{click:function(){i.onClick(a,s,n)}}}),t("div",{class:["right-menu ivu-poptip-inner"],style:{width:"80px",position:"absolute",zIndex:"9",top:"0",right:"0",display:n.flag2?"block":"none"}},r)])])},renderContentSel:function(t,e){var i=this,a=e.root,s=e.node,n=e.data;return t("div",{style:{display:"inline-block",width:"90%"}},[t("span",[t("span",{style:{cursor:"pointer"},class:["ivu-tree-title"],on:{click:function(t){i.handleCheckChange(a,s,n,t)}}},n.title)])])},handleCheckChange:function(t,e,i,a){this.list=[];var s=i.id,n=i.title;this.list.push({value:s,title:n}),this.ids.length?(this.pids=s,this.getMove()):this.$Message.warning("请先选择图片");for(var r=this.$refs.reference.$el.querySelectorAll(".ivu-tree-title-selected"),c=0;c<r.length;c++)r[c].className="ivu-tree-title";a.path[0].className="ivu-tree-title  ivu-tree-title-selected"},getMove:function(){var t=this,e={pid:this.pids,images:this.ids.toString()};h(e).then(function(){var e=b(r.a.mark((function e(i){return r.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$Message.success(i.msg),t.getFileList(),t.pids=0,t.checkPicList=[],t.ids=[];case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},editPicList:function(t){var e=this;this.tits=t;var i={ids:this.ids.toString()},a={title:"删除选中图片",url:"file/file/delete",method:"POST",ids:i};this.$modalSure(a).then((function(t){e.$Message.success(t.msg),e.getFileList(),e.checkPicList=[]})).catch((function(t){e.$Message.error(t.msg)}))},onMouseOver:function(t,e,i){event.preventDefault(),i.flag=!i.flag,i.flag2&&(i.flag2=!1)},onClick:function(t,e,i){i.flag2=!i.flag2},appendBtn:function(t,e,i,a){this.treeId=i.id,this.fileData.page=1,this.getFileList();for(var s=this.$refs.tree.$el.querySelectorAll(".ivu-tree-title-selected"),n=0;n<s.length;n++)s[n].className="ivu-tree-title";a.path[0].className="ivu-tree-title  ivu-tree-title-selected"},append:function(t,e,i){this.treeId=i.id,this.getFrom()},remove:function(t,e,i,a){var s=this;this.tits=a;var n={title:"删除 [ "+i.title+" ] 分类",url:"file/category/".concat(i.id),method:"DELETE",ids:""};this.$modalSure(n).then((function(t){s.$Message.success(t.msg),s.getList(),s.checkPicList=[]})).catch((function(t){s.$Message.error(t.msg)}))},editPic:function(t,e,i){var a=this;this.$modalForm(u(i.id)).then((function(){return a.getList()}))},changePage:function(){this.getList("search")},getList:function(t){var e=this,i={title:"全部图片",id:"",pid:0};o(this.uploadName).then(function(){var a=b(r.a.mark((function a(s){return r.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.treeData=s.data.list,e.treeData.unshift(i),"search"!==t?e.treeData2=g(e.treeData):e.searchClass=!0,e.addFlag(e.treeData);case 4:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()).catch((function(t){e.$Message.error(t.msg)}))},loadData:function(t,e){var i=this;o({pid:t.id}).then(function(){var t=b(r.a.mark((function t(i){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=i.data.list,e(a);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){i.$Message.error(t.msg)}))},addFlag:function(t){var e=this;t.map((function(t){e.$set(t,"flag",!1),e.$set(t,"flag2",!1),t.children&&e.addFlag(t.children)}))},add:function(){this.treeId=0,this.getFrom()},getFileList:function(){var t=this;this.fileData.pid=this.treeId,d(this.fileData).then(function(){var e=b(r.a.mark((function e(i){return r.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.data.list.forEach((function(e){e.isSelect=!1,e.isEdit=!1,e.isShowEdit=!1,e.realName=!1,e.num=0,t.editName(e)})),t.pictrueList=i.data.list,t.pictrueList.length?t.isShowPic=!1:t.isShowPic=!0,t.total=i.data.count;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$Message.error(e.msg)}))},pageChange:function(t){this.fileData.page=t,this.getFileList(),this.checkPicList=[]},getFrom:function(){var t=this;this.$modalForm(l({id:this.treeId})).then((function(e){t.getList()}))},beforeUpload:function(){var t=this;this.uploadData={pid:this.treeId};var e=new Promise((function(e){t.$nextTick((function(){e(!0)}))}));return e},handleSuccess:function(t,e,i){200===t.status?(this.fileData.page=1,this.$Message.success(t.msg),this.getFileList()):this.$Message.error(t.msg)},cancel:function(){this.$emit("changeCancel")},changImage:function(t,e,i){var a=this,s=0;t.isSelect?(t.isSelect=!1,this.checkPicList.map((function(e,i){e.att_id==t.att_id&&(s=i)})),this.checkPicList.splice(s,1)):(t.isSelect=!0,this.checkPicList.push(t)),this.ids=[],this.checkPicList.map((function(t,e){a.ids.push(t.att_id)})),this.pictrueList.map((function(t,e){t.isSelect?a.checkPicList.filter((function(e,i){t.att_id==e.att_id&&(t.num=i+1)})):t.num=0}))},checkPics:function(){if("单选"===this.isChoice){if(this.checkPicList.length>1)return this.$Message.warning("最多只能选一张图片");this.$emit("getPic",this.checkPicList[0])}else{var t=this.$route.query.maxLength;if(void 0!=t&&this.checkPicList.length>Number(t))return this.$Message.warning("最多只能选"+t+"张图片");this.$emit("getPicD",this.checkPicList)}},editName:function(t){var e=t.real_name.split("."),i=void 0==e[1]?[]:e[1];e[0].length,i.length;t.editName=t.real_name},bindTxt:function(t){var e=this;""==t.real_name&&this.$Message.error("请填写内容"),f(t.att_id,{real_name:t.real_name}).then((function(i){e.editName(t),t.isEdit=!1,e.$Message.success(i.msg)})).catch((function(t){e.$Message.error(t.msg)}))}}},L=C,P=(i("13f2"),i("2877")),S=Object(P["a"])(L,a,s,!1,null,"0f3f5024",null);e["a"]=S.exports}}]);