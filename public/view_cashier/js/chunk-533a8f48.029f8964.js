(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-533a8f48"],{"18a1":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content"},[a("div",{staticClass:"goodsCard acea-row"},[a("div",{staticClass:"conter"},[a("div",{staticClass:"cart-wrap"},[a("div",{staticClass:"cart"},[a("Input",{attrs:{search:"","enter-button":"搜索",placeholder:"搜索用户/店员手机号或ID",size:"large"},on:{"on-search":t.searchData},model:{value:t.lodgeFrom.keyword,callback:function(e){t.$set(t.lodgeFrom,"keyword",e)},expression:"lodgeFrom.keyword"}})],1),a("div",{staticClass:"pending-user-wrap"},[t.tableHang.length?a("div",{staticClass:"pending-user",on:{scroll:t.addPage}},t._l(t.tableHang,(function(e,s){return a("div",{key:s,staticClass:"list",class:t.selIndex===s?"bor":"",on:{click:function(a){return t.selectUser(s,e)}}},[a("div",{staticClass:"item row-between"},[a("div",{staticClass:"left_content acea-row"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:e.avatar?e.avatar:t.defaultAvatar,alt:"头像"}})]),a("div",{staticClass:"user"},[a("div",{staticClass:"name"},[t._v(t._s(e.nickname||"游客"))]),a("div",{staticClass:"order-price"},[t._v("\n                        订单金额：\n                        "),a("span",{staticClass:"price-num"},[t._v("￥"+t._s(e.price))])])])]),a("div",{staticClass:"right_content"},[a("div",{staticClass:"time"},[t._v(t._s(e._add_time))]),a("div",{staticClass:"acea-row row-right"},[a("span",{staticClass:"tidan",attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),t.billHang(e,s)}}},[t._v("提单")]),a("span",{staticClass:"shanchu",attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),t.hangDel(e,s)}}},[t._v("删除")])])])])])})),0):a("div",{staticClass:"no-order"},[a("img",{attrs:{src:s("66c9"),alt:""}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无挂单")])])]),a("div",{staticClass:"footer"},[a("Button",{on:{click:t.openSettle}},[t._v("结账￥"+t._s(t.userData.price))])],1)]),t.tableHang.length?a("div",{staticClass:"btn-group-vertical"},[a("Button",{on:{click:t.integralTap}},[t._v("积分")]),a("Button",{on:{click:t.changePrice}},[t._v("改价")]),a("Button",{on:{click:t.remarks}},[t._v("备注")])],1):t._e()]),a("div",{staticClass:"goods-wrapper"},[t.cartList.length?a("div",{staticClass:"goods"},[a("div",{staticClass:"sel-user"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.userData.avatar||s("653d"),alt:"头像"}})]),a("div",{staticClass:"item-right"},[a("div",{staticClass:"user"},[a("div",[t._v(t._s(t.userData.nickname||"游客"))])]),t.userData.uid?a("div",{staticClass:"money"},[a("div",[a("span",{staticClass:"pr20"},[t._v(t._s(t.userInfo.phone||"暂无手机号"))]),t._v("余额 "),a("span",{staticClass:"num"},[t._v(t._s(t.userInfo.now_money||0))])]),a("div",[t._v("\n                  积分 "),a("span",{staticClass:"num"},[t._v(t._s(t.userInfo.integral||0))])])]):t._e()])]),a("div",{staticClass:"cart-num"},[a("div",{staticClass:"cart-num-left"},[a("span",[t._v("共")]),a("span",{staticClass:"num"},[t._v(t._s(t.cartSum))]),a("span",[t._v("件商品")])]),a("div",[a("span",{staticClass:"text"},[t._v("实付：")]),a("span",{staticClass:"money"},[a("span",{staticClass:"rmb"},[t._v("¥")]),t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])])]),a("div",{staticClass:"goods-list"},[a("goodsList",{attrs:{cartList:t.cartList}}),a("div",{staticClass:"discount-con"},[t.priceInfo.firstOrderPrice>0?a("div",{staticClass:"item acea-row"},[a("div",[t._v("首单优惠：")]),a("div",[t._v("￥"+t._s(t.priceInfo.firstOrderPrice||0))])]):t._e(),a("div",{staticClass:"item acea-row"},[a("div",[t._v("会员优惠金额：")]),a("div",[t._v("￥"+t._s(t.priceInfo.vipPrice||0))])]),a("div",{staticClass:"item acea-row"},[a("div",[t._v("优惠券金额：")]),a("div",[t._v("￥"+t._s(t.priceInfo.couponPrice||0))])]),a("div",{staticClass:"item acea-row"},[a("div",[t._v("积分抵扣：")]),a("div",[t._v("￥"+t._s(t.priceInfo.deductionPrice||0))])]),t._l(t.priceInfo.promotionsDetail,(function(e,s){return a("div",{key:s,staticClass:"item acea-row"},[a("div",[t._v(t._s(e.title)+"：")]),a("div",[t._v(t._s(e.promotions_price||0))])])}))],2)],1)]):a("div",{staticClass:"no-order"},[a("img",{attrs:{src:s("0493"),alt:""}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无挂单记录")])])])]),a("footer",[a("div",{staticClass:"footer"},[t.cartList.length?a("div",{staticClass:"pay acea-row row-between-wrapper"},[a("div",{staticClass:"bnt",on:{click:function(e){return t.payPrice("cash")}}},[t._v("现金收款")]),a("div",{staticClass:"bnt",on:{click:function(e){return t.payPrice("")}}},[t._v("微信/支付宝")]),t.userInfo.uid&&t.userInfo.now_money>=(t.priceInfo.payPrice||0)?a("div",{staticClass:"bnt on",on:{click:function(e){return t.payPrice("yue")}}},[t._v("\n            余额收款\n          ")]):a("div",{staticClass:"bnt on bntUid"},[t._v("余额收款")])]):a("div",{staticClass:"pay noCart acea-row row-between-wrapper"},[a("div",{staticClass:"bnt"},[t._v("现金收款")]),a("div",{staticClass:"bnt"},[t._v("微信/支付宝")]),a("div",{staticClass:"bnt on"},[t._v("余额收款")])])]),a("div",{staticClass:"right"},[t.cartList.length?a("div",{staticClass:"rightCon"},[a("div",{staticClass:"item",class:t.integral?"on":"",on:{click:t.integralTap}},[t._v("\n            积分\n          ")]),a("div",{staticClass:"item",on:{click:t.changePrice}},[t._v("改价")]),a("div",{staticClass:"item",on:{click:t.remarks}},[t._v("备注")])]):a("div",{staticClass:"noCart"},[a("div",{staticClass:"item"},[t._v("积分")]),a("div",{staticClass:"item"},[t._v("改价")])])])]),a("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"用户列表","mask-closable":!1,width:"900"},model:{value:t.modalUser,callback:function(e){t.modalUser=e},expression:"modalUser"}},[t.modalUser?a("userList",{ref:"users",on:{getUserId:t.getUserId}}):t._e()],1),a("recharge",{ref:"recharge",attrs:{userInfo:t.userInfo},on:{getSuccess:t.getSuccess}}),t.userInfo&&t.cartList.length?a("couponList",{ref:"coupon",attrs:{uid:t.userInfo.uid,cartList:t.cartList},on:{getCouponId:t.getCouponId}}):t._e(),a("storeList",{ref:"store",attrs:{uid:t.userInfo?t.userInfo.uid:0},on:{getStoreId:t.getStoreId,getUserInfo:t.getUserInfo}}),a("Modal",{attrs:{title:"备注","class-name":"remarks-modal"},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("Input",{attrs:{rows:5,maxlength:"200",placeholder:"订单备注","show-word-limit":"",type:"textarea"},model:{value:t.createOrder.remarks,callback:function(e){t.$set(t.createOrder,"remarks",e)},expression:"createOrder.remarks"}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary",size:"large",long:""},on:{click:t.onSubmit}},[t._v("提交")])],1)],1),a("Modal",{staticClass:"modalPay",attrs:{"footer-hide":"",width:"430px"},on:{"on-cancel":t.modalPayCancel},model:{value:t.modalPay,callback:function(e){t.modalPay=e},expression:"modalPay"}},[a("div",{staticClass:"payPage"},[a("div",{staticClass:"header acea-row row-center-wrapper"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:s("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")])]),a("div",{staticClass:"money"},[t._v("\n          ¥"),a("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])]),a("Input",{ref:"focusNum",staticStyle:{"margin-top":"16px"},attrs:{size:"large",type:"url",placeholder:"请点击输入框聚焦扫码或输入编码号"},on:{input:t.inputSaoMa},model:{value:t.payNum,callback:function(e){t.payNum=e},expression:"payNum"}}),a("div",{staticClass:"process"},[a("div",{staticClass:"pictrue"},["yue"==t.createOrder.pay_type?a("img",{attrs:{src:s("fbde")}}):a("img",{attrs:{src:s("1a3d")}})]),a("div",{staticClass:"list acea-row row-between-wrapper"},[a("div",{staticClass:"item one"},[a("div",{staticClass:"name"},[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"出示付款码":"扫描收银码")+"\n              ")]),a("div",[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"用户打开个人中心":"引导用户扫描")+"\n              ")])]),a("div",{staticClass:"item two"},[a("div",{staticClass:"name"},[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"扫描付款码":"完成支付")+"\n              ")]),a("div",[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"扫码枪":"用户线上支付")+"\n              ")])]),a("div",{staticClass:"item three"},[a("div",{staticClass:"name"},[t._v("确认收款")]),a("div",[t._v("收银台点击确认")])])])])],1)]),a("Modal",{staticClass:"cash",attrs:{"footer-hide":"",width:"770px"},on:{"on-cancel":t.cancel},model:{value:t.modalCash,callback:function(e){t.modalCash=e},expression:"modalCash"}},[a("div",{staticClass:"cashPage acea-row"},[a("div",{staticClass:"left"},[a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:s("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")]),a("div",{staticClass:"money"},[t._v("\n            ¥"),a("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])])]),a("div",{staticClass:"right"},[a("div",{staticClass:"rightCon"},[a("div",{staticClass:"top acea-row row-between-wrapper"},[a("div",[t._v("实际收款(元)")]),a("div",{staticClass:"num"},[t._v(t._s(t.collection))])]),a("div",{staticClass:"center acea-row row-between-wrapper"},[a("div",[t._v("需找零(元)")]),this.$computes.Sub(t.collection,t.priceInfo.payPrice?t.priceInfo.payPrice:0)>0?a("div",{staticClass:"num"},[t._v("\n                "+t._s(this.$computes.Sub(t.collection,t.priceInfo.payPrice?t.priceInfo.payPrice:0))+"\n              ")]):a("div",{staticClass:"num"},[t._v("0")])]),a("div",{staticClass:"bottom acea-row"},[t._l(t.numList,(function(e,s){return a("div",{key:s,staticClass:"item acea-row row-center-wrapper",class:"."==e?"spot":"",on:{click:function(s){return t.numTap(e)}}},[t._v("\n                "+t._s(e)+"\n              ")])})),a("div",{staticClass:"item acea-row row-center-wrapper",on:{click:t.delNum}},[a("Icon",{attrs:{type:"ios-backspace"}})],1)],2)]),a("Button",{attrs:{type:"primary"},on:{click:t.cashBnt}},[t._v("确认")])],1)])]),a("settleDrawer",{attrs:{list:t.payList,type:t.payTape,money:t.priceInfo.payPrice,collection:t.collection,verify:t.yueVerify},on:{payPrice:t.payPrice,numTap:t.numTap,delNum:t.delNum,cashBnt:t.cashBnt},model:{value:t.settleVisible,callback:function(e){t.settleVisible=e},expression:"settleVisible"}}),a("changePrice",{ref:"changePrice",on:{submitSuccess:t.submitSuccess}})],1)},i=[],r=s("bff0"),c=s("5671"),n=s("e449"),o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("Modal",{attrs:{scrollable:"","footer-hide":"",closable:"",title:"商品规格","mask-closable":!1,width:"600"},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[s("div",{staticClass:"productAttr"},[s("div",{staticClass:"acea-row"},[s("div",{staticClass:"pictrue"},[s("img",{attrs:{src:t.attr.productSelect.image}})]),s("div",{staticClass:"text"},[s("div",{staticClass:"name line1"},[t._v(t._s(t.attr.productSelect.store_name))]),s("div",{staticClass:"info"},[t._v("库存 "+t._s(t.attr.productSelect.stock))]),s("div",{staticClass:"money"},[t._v("¥"),s("span",{staticClass:"num"},[t._v(t._s(t.attr.productSelect.price))])]),s("div",{staticClass:"attr"},t._l(t.attr.productAttr,(function(e,a){return s("div",{key:a,staticClass:"list"},[s("div",{staticClass:"title"},[t._v(t._s(e.attr_name))]),s("div",{staticClass:"listn acea-row"},t._l(e.attr_value,(function(i,r){return s("div",{key:r,staticClass:"item acea-row row-center-wrapper",class:e.index===i.attr?"on":"",on:{click:function(e){return t.tapAttr(a,r)}}},[t._v(t._s(i.attr))])})),0)])})),0)])]),s("button",{staticClass:"bnt acea-row row-center-wrapper",attrs:{type:"primary",disabled:t.disabled},on:{click:t.goCat}},[t._v("确定")])])])],1)},u=[],d={name:"productAttr",props:{attr:{type:Object,default:function(){}},isCart:{type:Number,value:0},disabled:{type:Boolean,value:!1}},data:function(){return{modals:!1}},created:function(){},methods:{goCat:function(){this.$emit("goCat",this.isCart)},tapAttr:function(t,e){var s=this;s.$emit("attrVal",{indexw:t,indexn:e}),this.$set(this.attr.productAttr[t],"index",this.attr.productAttr[t].attr_values[e]);var a=s.getCheckedValue().join(",");s.$emit("ChangeAttr",a)},getCheckedValue:function(){for(var t=this.attr.productAttr,e=[],s=0;s<t.length;s++)for(var a=0;a<t[s].attr_values.length;a++)t[s].index===t[s].attr_values[a]&&e.push(t[s].attr_values[a]);return e}}},l=d,h=(s("cf67"),s("2877")),f=Object(h["a"])(l,o,u,!1,null,"c3cc2208",null),p=f.exports,g=s("b89c"),v=s("f500"),m=(s("ed08"),s("9568")),_=s("16b2"),C=s("f8b7"),y=s("c24f"),I=s("d708");function b(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function w(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?b(s,!0).forEach((function(e){O(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):b(s).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function O(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var k={name:"index",components:{userList:r["default"],storeList:c["a"],productAttr:p,couponList:n["a"],recharge:g["a"],goodsList:m["a"],settleDrawer:_["a"],changePrice:v["a"]},data:function(){var t=this;return{loading:!1,totalHang:0,tableHang:[],activeHangon:-1,hangData:[],lodgeFrom:{keyword:"",page:1,limit:10},currentid:"",columns:[{title:"选择",key:"chose",width:60,align:"center",render:function(e,s){var a=s.row.id,i=!1;i=t.currentid===a;var r=t;return e("div",[e("Radio",{props:{value:i},on:{"on-change":function(){r.currentid=a,r.activeHangon=s.index;var e={uid:s.row.uid},i=s.row.tourist_uid;s.row.uid?t.userInfoData(e):t.setUp(i)}}})])}},{title:"用户",slot:"nickname",minWidth:70},{title:"订单金额",key:"price",minWidth:70},{title:"时间",key:"_add_time",minWidth:70},{title:"操作",slot:"action",minWidth:100,align:"center"}],checkOut:0,modalUser:!1,cashBntLoading:!1,flag:!0,goodFrom:{store_name:"",field_key:"",cate_id:"",page:1,limit:30,uid:0,staff_id:0},total:0,goodData:[],cateData:[],currentCate:0,currentTab:"2",codeNum:"",payNum:"",userInfo:{},storeInfos:{},storeList:[],attr:{productAttr:[],productSelect:{}},storeInfo:{},productValue:[],attrValue:"",productId:0,cartList:[],isCart:0,cartInfo:{cart_id:0,product_id:0,unique:""},modal:!1,fapi:{},rule:[{type:"input",field:"remarks",title:"备注",props:{type:"textarea",maxlength:100,"show-word-limit":!0}}],integral:!1,coupon:!1,couponId:0,modalPay:!1,cartSum:0,priceInfo:{},createOrder:{remarks:"",change_price:0,cart_id:[],userCode:"",is_price:0,auth_code:"",new:0,cart_info:[]},modalCash:!1,numList:["7","8","9","4","5","6","1","2","3","0","."],collectionArray:[],collection:0,isOrderCreate:0,discount:!1,payTape:"",orderId:"",clientHeight:0,cartHeight:0,goodsHeight:0,invalidList:[],defaultcalc:!1,orderSystem:{loadingMsg:null,timer:null},disabled:!1,unchangedPrice:0,selIndex:0,userData:{},defaultAvatar:s("586c"),settleVisible:!1,payList:[{label:"微信/支付宝",value:"",status:!0,verify:!0},{label:"现金收款",value:"cash",status:!0,verify:!0},{label:"余额收款",value:"yue",status:!0,verify:!0}],yueVerify:!1,orderCartInfo:[]}},created:function(){this.userInfo=JSON.parse(window.localStorage.getItem("cashierUser"))},mounted:function(){},methods:{selectUser:function(t,e){this.createOrder.remarks="",this.selIndex=t,this.userData=e,this.hangDataTap(t,e)},getSuccess:function(t){var e=this.$computes.Add(this.userInfo.now_money,t);this.userInfo.now_money=e;var s=window.localStorage;s.setItem("cashierUser",JSON.stringify(this.userInfo))},clear:function(){this.priceInfo.couponPrice=0,this.priceInfo.payPrice=0,this.priceInfo.deductionPrice=0,this.priceInfo.firstOrderPrice=0,this.priceInfo.totalPrice=0,this.priceInfo.vipPrice=0,this.cartList=[],this.cartSum=0,this.collection=0,this.collectionArray=[],this.createOrder.change_price=0,this.createOrder.remarks="",this.coupon=!1,this.couponId=0,this.integral=!1,this.createOrder.is_price=0},cancel:function(){this.collection=0,this.collectionArray=[]},hangDel:function(t,e){var s=this;this.$Modal.confirm({title:"删除该挂单",content:"<p>确定要删除该挂单吗？</p><p>删除该挂单后将无法恢复，请谨慎操作！</p>",onOk:function(){Object(C["t"])({id:t.id}).then((function(t){if(s.tableHang.splice(e,1),s.totalHang=s.totalHang-1,s.$Message.success(t.msg),s.tableHang.length){var a=e-1>0?e:0;s.selectUser(a,s.tableHang[a])}else s.selIndex=0,s.userData={},s.cartList=[],s.priceInfo={}})).catch((function(t){s.$Message.error(t.msg)}))},onCancel:function(){}})},hangDataTap:function(t,e){this.activeHangon=t,this.checkOut=0;var s=e.tourist_uid,a={uid:e.uid};e.uid?this.userInfoData(a):this.setUp(s)},hangList:function(){var t=this;this.loading=!0;var e=this.storeInfos.id;Object(C["A"])(e,this.lodgeFrom).then((function(e){t.loading=!1,t.tableHang=t.tableHang.concat(e.data.list),t.totalHang=e.data.count,1===t.lodgeFrom.page&&t.tableHang.length&&t.selectUser(0,t.tableHang[0])})).catch((function(e){t.loading=!1,t.$Message.error(e.msg)}))},searchData:function(){this.lodgeFrom.page=1,this.tableHang=[],this.hangList()},addPage:function(t){(!t||t.target.scrollHeight-t.target.scrollTop-t.target.clientHeight<=0&&this.tableHang.length<this.totalHang)&&(this.lodgeFrom.page++,this.hangList())},pageHangChange:function(t){this.lodgeFrom.page=t,this.hangList()},billHang:function(t,e){this.$router.push({path:"".concat(I["a"].roterPre,"/cashier/index"),query:{uid:t.uid,staff_id:t.staff_id,tourist_uid:t.tourist_uid,index:e}})},hangDataList:function(){var t=this,e=this.storeInfos.id;Object(C["z"])(e,e).then((function(e){t.hangData=e.data,t.defaultSel()})).catch((function(e){t.$Message.error(e.msg)}))},lodgeTap:function(){var t=this,e={cart_ids:this.createOrder.cart_id,uid:this.userInfo.uid||0,tourist_uid:this.userInfo.touristId||"",staff_id:this.storeInfos.id,price:this.priceInfo.payPrice||0};Object(C["T"])(e).then((function(e){t.activeHangon=-1,t.$Message.success(e.msg),t.hangDataList(),t.hangList(),t.setUp()})).catch((function(e){t.$Message.error(e.msg)}))},storeSearch:function(){this.lodgeFrom.page=1,this.hangList()},defaultSel:function(t){var e=this,s=this.userInfo.uid,a=this.userInfo.touristId;if(s){var i=0;this.hangData.forEach((function(t,a){t.uid==s&&(i=1,e.activeHangon=a)})),i||(this.activeHangon=-1)}else a&&this.hangData.forEach((function(t,s){t.tourist_uid==a&&(e.activeHangon=s)}))},navTab:function(t){this.checkOut=t,1==t?(this.currentid="",this.activeHangon=-1,this.clear(),this.lodgeFrom.page=1,this.hangList()):(this.getCartList(),this.defaultSel())},rechargeBnt:function(){this.$refs.recharge.modal=!0},discountCon:function(){this.discount=!0},cashBnt:function(){var t=this;this.cashBntLoading||(this.cashBntLoading=!0,this.isOrderCreate?this.getCashierPay("cash"):this.orderCreate(),setTimeout((function(){t.cashBntLoading=!1}),1e3))},delNum:function(){this.collectionArray.pop(),this.collection=this.collectionArray.length?this.collectionArray.join(""):0},numTap:function(t){!1===this.defaultcalc&&(this.collection="",this.defaultcalc=!0);var e=String(this.collection).indexOf(".")+1,s=String(this.collection).length-e;console.log(e,s),(0===e||s<2)&&(this.collectionArray.join("")<=9999999&&this.collectionArray.push(t),this.collection=this.collectionArray.join("")>99999999?99999999:this.collectionArray.join(""))},checkOrderTime:function(t){var e=this,s=1,a=this.orderSystem.timer=setInterval((function(){e.confirmOrder(a,t),s++,s>=60&&(clearInterval(a),e.isOrderCreate=1,e.$Message.warning("支付失败"))}),1e3)},confirmOrder:function(t,e){var s=this,a={order_id:this.orderId};Object(y["c"])(3,a).then((function(a){if(1==a.data.status){e(),clearInterval(t),s.isOrderCreate=0,s.$Message.success("支付成功"),s.modalPay=!1,s.settleVisible=!1,s.changePoints();var i=window.localStorage;i.setItem("cashierUser",JSON.stringify(s.userInfo)),s.clear()}})).catch((function(t){e(),s.$Message.error(t.msg)}))},payPrice:function(t){if(this.payTape=t,""==t||"yue"==t){this.createOrder.userCode="",this.createOrder.auth_code="",this.payNum="";var e=this;this.$nextTick((function(){document.onkeydown=function(t){13==t.which&&(e.payNum&&(e.createOrder.userCode=e.payNum,e.createOrder.auth_code=e.payNum,e.confirm()),e.codeNum&&e.codeInfo({bar_code:e.codeNum}))}}))}else"cash"==t&&(this.collection=this.priceInfo.payPrice?this.priceInfo.payPrice:0,this.keyboard());if(this.createOrder.integral=this.integral,this.createOrder.coupon=this.coupon,this.createOrder.coupon_id=this.couponId,this.coupon&&!this.couponId)return this.$Message.error("请选择有效优惠券");this.createOrder.pay_type=t,this.createOrder.staff_id=this.storeInfos.id},confirm:function(){if("yue"==this.payTape){if(!this.createOrder.userCode)return this.$Message.error("请扫描个人中心二维码");this.isOrderCreate?this.getCashierPay("yue"):this.orderCreate()}else if(""==this.payTape){if(!this.createOrder.auth_code)return this.$Message.error("请扫描您的付款码");this.isOrderCreate?this.getCashierPay(""):this.orderCreate()}},modalPayCancel:function(){this.$Message.destroy(),this.orderSystem.timer&&(clearInterval(this.orderSystem.timer),this.orderSystem.timer=null)},getCashierPay:function(t){var e=this,s={payType:t,userCode:this.payNum,auth_code:this.payNum};if("cash"==t&&parseFloat(this.priceInfo.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");Object(C["p"])(this.orderId,s).then((function(t){if(e.payNum="","SUCCESS"==t.data.status){e.isOrderCreate=0,e.$Message.success("支付成功"),e.modalCash=!1,e.modalPay=!1,e.changePoints();var s=window.localStorage;s.setItem("cashierUser",JSON.stringify(e.userInfo)),e.clear()}else if("PAY_ING"==t.data.status){var a=e.$Message.loading({content:"等待支付中...",duration:0});e.orderSystem.loadingMsg=a,e.orderId=t.data.order_id,e.checkOrderTime(a)}else e.isOrderCreate=1,e.orderId=t.data.order_id,e.$Message.error(t.data.message)})).catch((function(t){e.payNum="",e.$Message.error(t.msg)}))},orderCreate:function(){var t=this;if("cash"==this.payTape&&parseFloat(this.priceInfo.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");this.createOrder.tourist_uid=this.userInfo.touristId,Object(C["l"])(this.userInfo.uid,this.createOrder).then((function(e){var s=window.localStorage;if(t.payNum="","yue"==t.payTape)if(t.settleVisible=!1,t.payNum="",t.createOrder.userCode="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("SUCCESS"==e.data.status){t.isOrderCreate=0,t.$Message.success("支付成功");var a=t.$computes.Sub(t.userInfo.now_money,t.priceInfo.payPrice);t.userInfo.now_money=a,t.changePoints(),s.setItem("cashierUser",JSON.stringify(t.userInfo)),t.clear()}else t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message);if("cash"==t.payTape&&"SUCCESS"==e.data.status&&(t.$Message.success("支付成功"),t.changePoints(),s.setItem("cashierUser",JSON.stringify(t.userInfo)),t.settleVisible=!1,t.clear()),""==t.payTape)if(t.payNum="",t.createOrder.auth_code="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("PAY_ING"==e.data.status){var i=t.$Message.loading({content:"等待支付中...",duration:0});t.orderId=e.data.order_id,t.checkOrderTime(i)}else"SUCCESS"==e.data.status?(t.$Message.success("支付成功"),t.changePoints(),s.setItem("cashierUser",JSON.stringify(t.userInfo)),t.settleVisible=!1,t.clear()):(t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message))})).catch((function(e){t.payNum="",t.$Message.error(e.msg)}))},changePoints:function(){var t=this,e=this.$computes.Sub(this.userInfo.integral,this.priceInfo.usedIntegral);this.userInfo.integral=e,this.hangDataList(),this.tableHang.forEach((function(e,s){e.uid?t.userInfo.uid==e.uid&&t.tableHang.splice(s,1):t.userInfo.touristId==e.tourist_uid&&t.tableHang.splice(s,1)}))},cartCompute:function(){var t=this,e=[];this.cartList.forEach((function(t){t.cart.forEach((function(t){e.push(t.id)}))})),this.createOrder.cart_id=e;var s={integral:this.integral,coupon:this.coupon,coupon_id:this.couponId,cart_id:e};Object(C["j"])(this.userInfo.uid,s).then((function(e){var s=e.data;t.priceInfo=s,t.orderCartInfo=s.cartInfo,t.unchangedPrice=t.priceInfo.payPrice||0;var a=t.priceInfo.cartInfo.map((function(t,e){return t.cart_num}));t.cartSum=a.reduce((function(t,e){return t+e}),0)})).catch((function(e){t.$Message.error(e.msg),t.coupon=!1}))},couponTap:function(){this.$refs.coupon.modals=!0,this.$refs.coupon.currentid=this.couponId||0,this.$refs.coupon.getList()},getCouponId:function(t){this.couponId=t.id,this.coupon=!0,this.$refs.coupon.modals=!1,t.id&&(this.createOrder.is_price=0),this.cartCompute()},closeCoupon:function(){this.coupon=!1,this.couponId=0,this.cartCompute()},integralTap:function(){this.userInfo.uid?(this.integral=!this.integral,this.integral&&(this.createOrder.is_price=0),this.cartCompute()):this.$Message.warning("请先选择用户再使用积分")},changePrice:function(){this.$refs.changePrice.cartInfo=this.orderCartInfo,this.$refs.changePrice.priceInfo=this.priceInfo,this.$refs.changePrice.ordeUpdateInfo(),this.$refs.changePrice.priceModals=!0},submitSuccess:function(t){var e=this.orderCartInfo.map((function(e){var s=t.cartInfo.find((function(t){return t.id===e.id}));return s?w({},e,{truePrice:s.true_price}):e}));this.orderCartInfo=e,this.createOrder.cart_info=t.cartInfo,this.priceInfo.payPrice=t.resultPayPrice,this.$Message.success("改价成功"),this.createOrder.is_price=1,this.createOrder.change_price=t.resultPayPrice,this.getSwithUser({change_price:t.resultPayPrice})},remarks:function(){this.modal=!0},onSubmit:function(){this.modal=!1},del:function(t,e,s,a){var i=this;this.$Modal.confirm({title:"删除该购物车",content:"<p>确定要删除该购物车吗？</p><p>删除该购物车后将无法恢复，请谨慎操作！</p>",onOk:function(){Object(C["e"])(i.userInfo.uid,t).then((function(t){i.$Message.success("删除成功"),e?(i.clear(),i.invalidList=[],i.hangDataList()):a?i.invalidList.splice(s,1):(i.cartList.splice(s,1),i.cartList.length?i.cartCompute():(i.hangDataList(),i.clear()))})).catch((function(t){i.$Message.error(t.msg)}))},onCancel:function(){}})},delAll:function(){var t=[];this.cartList.forEach((function(e){t.push(e.id)})),this.invalidList.forEach((function(e){t.push(e.id)})),this.del({ids:t},1)},delCart:function(t,e,s){var a=[];a.push(t.id),this.del({ids:a},0,e,s)},cartAttr:function(t){this.disabled=!1,this.$refs.attrs.modals=!0,this.isCart=1,this.cartInfo.cart_id=t.id,this.cartInfo.product_id=t.product_id,this.goodsInfo(t.product_id)},joinCart:function(t){var e=this,s=this;if(t){var a=s.productValue[this.attrValue];if(s.attr.productAttr.length&&void 0===a)return this.$Message.warning("产品库存不足，请选择其它")}var i=this.userInfo.uid,r={productId:this.productId,cartNum:1,uniqueId:t&&void 0!==this.attr.productSelect?this.attr.productSelect.unique:"",staff_id:this.storeInfos.id,tourist_uid:this.userInfo.touristId};Object(C["d"])(i,r).then((function(t){e.$refs.attrs.modals=!1,e.$Message.success("添加购物车成功"),e.getCartList(),e.hangDataList(),e.disabled=!0})).catch((function(t){e.$Message.error(t.msg)}))},cartChange:function(t){var e=this,s=t.uid,a={number:t.cart_num,id:t.id};Object(C["g"])(s,a).then((function(t){e.cartCompute()})).catch((function(t){e.$Message.error(t.msg)}))},calculate:function(t,e){var s=this;"reduce"===e&&t.cart_num>1?t.cart_num--:"add"===e&&t.cart_num<t.branch_stock&&t.cart_num++;var a=t.uid,i={number:t.cart_num,id:t.id};Object(C["g"])(a,i).then((function(t){s.cartCompute()})).catch((function(t){s.$Message.error(t.msg)}))},changeCartAttr:function(){var t=this;this.cartInfo.unique=void 0!==this.attr.productSelect?this.attr.productSelect.unique:"",Object(C["s"])(this.cartInfo).then((function(e){t.disabled=!0,t.$Message.success(e.msg),t.$refs.attrs.modals=!1,t.getCartList()})).catch((function(e){t.$Message.error(e.msg)}))},getCartList:function(){var t=this,e=this.userInfo.uid,s=this.storeInfos.id;if(e>=0){var a={tourist_uid:this.userInfo.touristId};Object(C["f"])(e,s,a).then((function(e){t.cartList=e.data.valid,t.invalidList=e.data.invalid,t.cartSum=e.data.count,e.data.valid.length?t.cartCompute():t.clear()})).catch((function(e){t.$Message.error(e.msg)})).finally((function(e){t.cumping=!1}))}else this.$Message.error("请添加或选择用户")},ChangeAttr:function(t){var e=this.productValue[t];e&&e.stock>0?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.unique),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vip_price",e.vip_price),this.$set(this,"attrValue",t)):(this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this.attr.productSelect,"vip_price",this.storeInfo.vip_price),this.$set(this,"attrValue",""))},storeTap:function(){this.$refs.store.modals=!0,this.$refs.store.cancel()},setUp:function(t){var e=(new Date).getTime(),a={avatar:s("586c"),nickname:"游客",uid:0,touristId:t||e};this.userInfo=a;var i=window.localStorage;i.setItem("cashierUser",JSON.stringify(a)),this.getCartList()},changeMenu:function(t){1==t?this.setUser():(this.activeHangon=-1,this.clear(),this.setUp())},setUser:function(){this.modalUser=!0,this.$refs.users.currentid=0},getStoreId:function(t){var e=this;this.clear(),this.storeList.forEach((function(s){s.id==t.id&&(e.goodFrom.staff_id=t.id,e.storeInfos=s,e.getCartList(),e.hangDataList(),e.hangList())}))},getUserInfo:function(t){this.storeInfos=t.users,this.storeList=t.storeList,this.goodFrom.staff_id=t.users.id,this.userInfo?this.getCartList():this.setUp(),this.hangDataList(),this.hangList()},cashierSwitch:function(t){var e=this;Object(C["U"])(t,this.storeInfos.id).then((function(t){console.log("jkjkj",t)})).catch((function(t){e.$Message.error(t)}))},getUserId:function(t){this.clear(),this.modalUser=!1;var e={uid:t.uid},s={uid:this.userInfo.touristId,to_uid:t.uid,is_tourist:1};this.cashierSwitch(s),this.userInfoData(e)},userInfoData:function(t){var e=this;Object(C["r"])(t).then((function(t){e.userInfo=t.data;var s=window.localStorage;s.setItem("cashierUser",JSON.stringify(t.data)),e.getCartList(),e.defaultSel(1)})).catch((function(t){e.$Message.error(t.msg)}))},inputSaoMa:function(t){var e=this,s=t;if(""===s)return!1;clearTimeout(this.endTimeout),this.endTimeout=null,this.endTimeout=setTimeout((function(){e.codeNum===s&&(clearTimeout(e.endTimeout),s&&e.codeInfo({bar_code:s}))}),500)},codeInfo:function(t){var e=this;if(t.uid=this.userInfo?this.userInfo.uid:0,t.staff_id=this.storeInfos.id,t.tourist_uid=this.userInfo.touristId,null==this.userInfo)return this.codeNum="",this.$Message.error("请添加或选择用户");Object(C["i"])(t).then((function(t){e.codeNum="";var s=t.data;if(s.hasOwnProperty("userInfo"))if(e.userInfo)e.$Modal.confirm({title:"切换用户",content:"<p>确定要切换用户吗？</p>",onOk:function(){e.userInfo=t.data.userInfo;var s=window.localStorage;s.setItem("cashierUser",JSON.stringify(t.data.userInfo)),e.getCartList()},onCancel:function(){}});else{e.userInfo=t.data.userInfo;var a=window.localStorage;a.setItem("cashierUser",JSON.stringify(t.data.userInfo))}e.getCartList()})).catch((function(t){e.codeNum="",e.$Message.error(t.msg)}))},keyboard:function(){var t=this;function e(e){t.collectionArray.pop(),t.collection=t.collectionArray.length?t.collectionArray.join(""):0}function s(e){!1===t.defaultcalc&&(t.collection="",t.defaultcalc=!0);var s=String(t.collection).indexOf(".")+1,a=String(t.collection).length-s;console.log(s,a),(0===s||a<2)&&(t.collectionArray.join("")<=9999999&&t.collectionArray.push(e),t.collection=t.collectionArray.join("")>99999999?99999999:t.collectionArray.join(""))}document.onkeydown=function(a){var i=a||window.event,r=i.keyCode;switch(t.modalCash&&(a.stopPropagation(),a.preventDefault()),r){case 96:case 48:s(0);break;case 97:case 49:s(1);break;case 98:case 50:s(2);break;case 99:case 51:s(3);break;case 100:case 52:s(4);break;case 101:case 53:s(5);break;case 102:case 54:s(6);break;case 103:case 55:s(7);break;case 104:case 56:s(8);break;case 105:case 57:s(9);break;case 110:s(".");break;case 190:s(".");break;case 8:e();break}}},openSettle:function(){var t=this;this.payList.forEach((function(e,s){e.status=!0,"yue"!==e.value||2!=t.priceInfo.yue_pay_status&&t.userData.uid||(e.status=!1)}));for(var e=0;e<this.payList.length;e++)if(this.payList[e].status){this.payTape=this.payList[e].value;break}this.yueVerify=!!this.priceInfo.is_cashier_yue_pay_verify,this.settleVisible=!0}}},S=k,P=(s("c5ff"),Object(h["a"])(S,a,i,!1,null,"b5b01964",null));e["default"]=P.exports},2440:function(t,e,s){},9568:function(t,e,s){"use strict";var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"table"},[s("div",{staticClass:"header"},t._l(t.header,(function(e,a){return s("div",{key:a,class:e.class},[t._v("\n      "+t._s(e.name)+"\n    ")])})),0),t._l(t.cartList,(function(e,a){return s("div",{key:a,staticClass:"list"},[t._l(e.promotions,(function(e,a){return s("div",{key:a,staticClass:"activity"},[s("div",{staticClass:"activity-type"},[t._v("\n        "+t._s(e.title)+"\n      ")]),s("div",{staticClass:"desc"},[t._v("\n        "+t._s(e.desc)+"\n      ")])])})),t._l(e.cart,(function(e,a){return s("div",{key:a+"l",staticClass:"item"},[e.is_gift?t._e():s("div",{staticClass:"goods"},[s("div",{staticClass:"img"},[e.productInfo.attrInfo?s("img",{attrs:{src:e.productInfo.attrInfo.image,alt:""}}):s("img",{attrs:{src:e.productInfo.image}})]),s("div",{staticClass:"name line2"},[t._v(t._s(e.productInfo.store_name))])]),e.is_gift?t._e():s("div",{staticClass:"o_price alc"},[t._v(t._s(e.sum_price))]),e.is_gift?t._e():s("div",{staticClass:"num alc"},[t._v(t._s(e.cart_num))]),e.is_gift?s("div",{staticClass:"give-goods"},[s("div",{staticClass:"img"},[e.productInfo.attrInfo?s("img",{attrs:{src:e.productInfo.attrInfo.image,alt:""}}):s("img",{attrs:{src:e.productInfo.image}})]),s("div",{staticClass:"name line1"},[t._v(t._s(e.productInfo.store_name))]),s("div",{staticClass:"give"},[t._v("赠品")]),s("div",{staticClass:"num"},[t._v("x"+t._s(e.cart_num))])]):s("div",{staticClass:"price alc"},[t._v(t._s(e.sum_price*e.cart_num))])])}))],2)}))],2)},i=[],r={name:"index",props:["cartList"],data:function(){return{header:[{name:"商品",class:"goods",width:15},{name:"单价",class:"o_price"},{name:"数量",class:"num",width:15},{name:"金额",class:"price",width:15}]}}},c=r,n=(s("a9f2"),s("2877")),o=Object(n["a"])(c,a,i,!1,null,"ea29067a",null);e["a"]=o.exports},a9f2:function(t,e,s){"use strict";var a=s("b417"),i=s.n(a);i.a},ae67:function(t,e,s){},b417:function(t,e,s){},c5ff:function(t,e,s){"use strict";var a=s("2440"),i=s.n(a);i.a},cf67:function(t,e,s){"use strict";var a=s("ae67"),i=s.n(a);i.a}}]);