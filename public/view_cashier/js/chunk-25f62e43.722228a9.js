(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-25f62e43"],{"170c":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"auxScreen"},[r("div",{staticClass:"header acea-row row-between-wrapper"},[r("div",{staticClass:"left acea-row row-middle"},[r("div",{staticClass:"pictrue"},[t.userInfo.avatar?r("img",{attrs:{src:t.userInfo.avatar}}):r("img",{attrs:{src:n("c7de")}})]),r("div",{staticClass:"name"},[t._v(t._s(t.userInfo.nickname))]),r("div",{staticClass:"phone"},[t._v("手机号："+t._s(t.userInfo.phone||"-"))])]),r("div",{staticClass:"right acea-row row-middle"},[r("div",{staticClass:"item"},[t._v("积分"),r("span",{staticClass:"num"},[t._v(t._s(t.userInfo.integral||"-"))])]),r("div",{staticClass:"item"},[t._v("余额"),r("span",{staticClass:"num"},[t._v(t._s(t.userInfo.now_money||"-"))])])])]),r("div",{staticClass:"selected acea-row row-middle"},[t._v("已选购"),r("span",{staticClass:"num"},[t._v(t._s(t.count))]),t._v("件")]),t.cartList.length?r("div",{staticClass:"list"},t._l(t.cartList,(function(e,n){return r("div",{key:n+"cart",staticClass:"item"},[e.promotions.length?r("div",{staticClass:"activeCon"},t._l(e.promotions,(function(e,n){return r("div",{key:n,staticClass:"activity"},[r("span",{staticClass:"label"},[t._v(t._s(e.title))]),t._v(t._s(e.desc)+"\n                ")])})),0):t._e(),t._l(e.cart,(function(e,n){return r("div",{key:n+"l",staticClass:"picTxt acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"pictrue"},[e.productInfo.attrInfo?r("img",{attrs:{src:e.productInfo.attrInfo.image,alt:""}}):r("img",{attrs:{src:e.productInfo.image}})]),r("div",{staticClass:"text"},[r("div",{staticClass:"name line1"},[e.is_gift?r("span",{staticClass:"giftTxt"},[t._v("赠品")]):t._e(),t._v(t._s(e.productInfo.store_name))]),e.productInfo.attrInfo&&e.productInfo.spec_type?r("div",[t._v(t._s(e.productInfo.attrInfo.suk))]):r("div",[t._v("默认")])])]),r("div",[r("span",{staticClass:"cartNum"},[t._v("x"+t._s(e.cart_num))]),r("span",{staticClass:"money"},[t._v("¥"+t._s(e.sum_price))])])])}))],2)})),0):r("div",{staticClass:"list"},[t._m(0)]),r("footer",{staticClass:"footer acea-row row-middle row-right"},[r("div",[t._v("优惠：-"+t._s(this.$computes.Sub(t.priceInfo.sumPrice||0,t.priceInfo.payPrice||0)||0))]),r("div",{staticClass:"payment acea-row row-middle"},[t._v("实付："),r("span",{staticClass:"label"},[t._v("￥")]),r("span",{staticClass:"num"},[t._v(t._s(t.priceInfo.payPrice?t.priceInfo.payPrice:0))])])])])},o=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"noGoods"},[r("img",{staticClass:"img",attrs:{src:n("e697")}}),r("div",{staticClass:"tip"},[t._v("暂无商品，快去添加吧～")])])}],a=n("c276"),c=n("d708"),i=n("a026");function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function d(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t}var f={},l={},h=function(){function t(e){u(this,t),this.vm=new i["default"],this.ws=null,this.opt=e||{},this.networkStatus=!0,this.reconneMax=100,this.connectLing=!1,f[this.opt.key]=null,l[this.opt.key]=0,this.init(e),this.networkWath(),this.defaultEvenv()}return d(t,[{key:"defaultEvenv",value:function(){this.vm.$on("timeout",this.timeoutEvent.bind(this))}},{key:"timeoutEvent",value:function(){this.reconne()}},{key:"guid",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0,n="x"==t?e:3&e|8;return n.toString(16)}))}},{key:"addHandler",value:function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent?t.attachEvent("on"+e,n):t["on"+e]=n}},{key:"networkWath",value:function(){var t=this;this.addHandler(window,"online",(function(){t.networkStatus=!0,console.log("联网了"),t.ws.close(),t.vm.$on("timeout",t.timeoutEvent)})),this.addHandler(window,"offline",(function(){t.networkStatus=!1,t.socketStatus=!1,t.timer&&clearInterval(t.timer),t.timer=null,t.vm.$off("timeout",t.timeoutEvent),console.log("断网了")}))}},{key:"reconne",value:function(){var t=this;l[this.opt.key]>this.reconneMax?f[this.opt.key]&&(clearInterval(f[this.opt.key]),f[this.opt.key]=null):f[this.opt.key]||this.socketStatus||(f[this.opt.key]=setInterval((function(){t.socketStatus||t.connectLing||(console.log("重新连接"),t.init(t.opt),l[t.opt.key]++)}),2e3))}},{key:"onOpen",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];clearInterval(f[this.opt.key]),f[this.opt.key]=null,this.connectLing=!1,this.opt.open&&this.opt.open(),l[this.opt.key]=0,this.socketStatus=!0,this.ping()}},{key:"init",value:function(t){var e="",n=c["a"].wsSocketUrl;if("nginx"!==sessionStorage.getItem("SERVER_TYPE")&&(n+="/ws"),1==t.key){var r=a["a"].cookies.get("token");r||(r=localStorage.getItem("token")),e=n+"?type=cashier&token="+r}e&&(this.ws=new WebSocket(a["a"].wss(e)),this.ws.onopen=this.onOpen.bind(this),this.ws.onerror=this.onError.bind(this),this.ws.onmessage=this.onMessage.bind(this),this.ws.onclose=this.onClose.bind(this),this.connectLing=!0)}},{key:"ping",value:function(){var t=this;this.timer=setInterval((function(){t.send({type:"ping"})}),1e4)}},{key:"send",value:function(t){var e=this;return this.socketStatus&&0!==this.ws.readyState&&this.networkStatus||this.reconne(),new Promise((function(n,r){try{e.ws.send(JSON.stringify(t)),n({status:!0})}catch(o){r({status:!1,socketStatus:e.socketStatus,networkStatus:e.networkStatus})}}))}},{key:"onMessage",value:function(t){var e=JSON.parse(t.data),n=e.type,r=e.data;this.opt.message&&this.opt.message(t),this.vm.$emit(n,r)}},{key:"onClose",value:function(){this.connectLing=!1,this.timer&&clearInterval(this.timer),this.timer=null,this.opt.close&&this.opt.close(),this.socketStatus=!1,this.reconne()}},{key:"onError",value:function(t){this.connectLing=!1,this.timer&&clearInterval(this.timer),this.timer=null,this.opt.error&&this.opt.error(t),this.socketStatus=!1,this.reconne()}},{key:"$once",value:function(){var t;(t=this.vm).$once.apply(t,arguments)}},{key:"$on",value:function(){var t;(t=this.vm).$on.apply(t,arguments)}},{key:"$off",value:function(){var t;(t=this.vm).$off.apply(t,arguments)}}]),t}(),m=h,p=n("f8b7"),v={data:function(){return{socket:null,userInfo:{},cartList:[],uid:0,count:0,cashierId:0,touristUid:0,priceInfo:{}}},created:function(){var t=this;this.getAuxScreen(),this.socket=new m({key:1}),this.socket.$on("changUser",(function(e){if(e.uid&&(t.uid=e.uid,t.touristUid=0),e.cashier_id&&(t.cashierId=e.cashier_id),console.log(e),e.tourist_uid&&(t.touristUid=e.tourist_uid,t.uid=0,t.$set(t,"userInfo",{})),e.change_price)t.priceInfo.payPrice=e.change_price;else{var n={tourist_uid:t.touristUid,socket:1};t.uid&&t.userInfoData({uid:t.uid,socket:1}),t.getCartList(t.uid,t.cashierId,n)}})),this.socket.$on("changCart",(function(e){var n=e.uid||t.uid,r={tourist_uid:t.touristUid,socket:1};t.getCartList(n,t.cashierId,r)})),this.socket.$on("changCartRemove",(function(e){t.priceInfo={}})),this.socket.$on("changSuccess",(function(e){t.cartList=[],t.priceInfo={},t.userInfo={}})),this.socket.$on("changCompute",(function(e){var n=e.uid||t.uid,r=e.post_data;r.socket=1,t.cartCompute(n,r)}))},mounted:function(){},methods:{getAuxScreen:function(){var t=this;Object(p["a"])().then((function(e){if(t.cashierId=e.data.cashier_id||0,t.uid=e.data.uid||0,t.touristUid=e.data.tourist_uid||0,e.data.tourist?t.uid=0:t.touristUid=0,t.uid){t.userInfoData({uid:t.uid,socket:1});var n={tourist_uid:t.touristUid,socket:1};t.getCartList(t.uid,t.cashierId,n)}})).catch((function(e){t.$Message.error(e)}))},userInfoData:function(t){var e=this;Object(p["r"])(t).then((function(t){e.userInfo=t.data})).catch((function(t){e.$Message.error(t)}))},getCartList:function(t,e,n){var r=this;Object(p["f"])(t,e,n).then((function(e){r.cartList=e.data.valid,r.count=e.data.count;var n={coupon:!1,coupon_id:0,integral:!1,cart_id:[],socket:1};e.data.valid.forEach((function(t){t.cart.forEach((function(t){n.cart_id.push(t.id)}))})),r.cartCompute(t,n)})).catch((function(t){r.$Message.error(t.msg)}))},cartCompute:function(t,e){var n=this;if(!e.cart_id.length)return!1;Object(p["j"])(t,e).then((function(t){n.priceInfo=t.data})).catch((function(t){n.$Message.error(t.msg)}))}}},g=v,_=(n("f8257"),n("2877")),b=Object(_["a"])(g,r,o,!1,null,"357d9fa7",null);e["default"]=b.exports},"9b77":function(t,e,n){},c7de:function(t,e,n){t.exports=n.p+"view_cashier/img/ren.c7bc0d99.png"},e697:function(t,e,n){t.exports=n.p+"view_cashier/img/no-cart.cd93531f.png"},f8257:function(t,e,n){"use strict";var r=n("9b77"),o=n.n(r);o.a},f8b7:function(t,e,n){"use strict";n.d(e,"q",(function(){return o})),n.d(e,"h",(function(){return a})),n.d(e,"r",(function(){return c})),n.d(e,"i",(function(){return i})),n.d(e,"d",(function(){return u})),n.d(e,"m",(function(){return s})),n.d(e,"n",(function(){return d})),n.d(e,"f",(function(){return f})),n.d(e,"g",(function(){return l})),n.d(e,"s",(function(){return h})),n.d(e,"e",(function(){return m})),n.d(e,"Y",(function(){return p})),n.d(e,"j",(function(){return v})),n.d(e,"l",(function(){return g})),n.d(e,"p",(function(){return _})),n.d(e,"y",(function(){return b})),n.d(e,"Z",(function(){return k})),n.d(e,"O",(function(){return O})),n.d(e,"N",(function(){return j})),n.d(e,"Q",(function(){return w})),n.d(e,"fb",(function(){return y})),n.d(e,"bb",(function(){return I})),n.d(e,"ab",(function(){return x})),n.d(e,"G",(function(){return C})),n.d(e,"db",(function(){return S})),n.d(e,"ib",(function(){return E})),n.d(e,"X",(function(){return $})),n.d(e,"V",(function(){return L})),n.d(e,"k",(function(){return U})),n.d(e,"jb",(function(){return M})),n.d(e,"cb",(function(){return P})),n.d(e,"U",(function(){return T})),n.d(e,"T",(function(){return D})),n.d(e,"A",(function(){return J})),n.d(e,"z",(function(){return H})),n.d(e,"o",(function(){return N})),n.d(e,"C",(function(){return R})),n.d(e,"B",(function(){return W})),n.d(e,"D",(function(){return A})),n.d(e,"F",(function(){return G})),n.d(e,"L",(function(){return V})),n.d(e,"E",(function(){return Y})),n.d(e,"eb",(function(){return q})),n.d(e,"t",(function(){return z})),n.d(e,"v",(function(){return B})),n.d(e,"a",(function(){return F})),n.d(e,"hb",(function(){return K})),n.d(e,"x",(function(){return Q})),n.d(e,"R",(function(){return X})),n.d(e,"I",(function(){return Z})),n.d(e,"K",(function(){return tt})),n.d(e,"w",(function(){return et})),n.d(e,"b",(function(){return nt})),n.d(e,"u",(function(){return rt})),n.d(e,"gb",(function(){return ot})),n.d(e,"J",(function(){return at})),n.d(e,"S",(function(){return ct})),n.d(e,"P",(function(){return it})),n.d(e,"M",(function(){return ut})),n.d(e,"H",(function(){return st})),n.d(e,"W",(function(){return dt})),n.d(e,"c",(function(){return ft}));var r=n("b6bd");function o(t){return Object(r["a"])({url:"product/get_list",method:"get",params:t})}function a(t){return Object(r["a"])({url:"product/get_one_category",method:"get",params:t})}function c(t){return Object(r["a"])({url:"user/user_Info",method:"post",data:t})}function i(t){return Object(r["a"])({url:"order/cashier/code",method:"post",data:t})}function u(t,e){return Object(r["a"])({url:"cart/set_cart/".concat(t),method:"post",data:e})}function s(t,e){return Object(r["a"])({url:"product/get_info/".concat(t,"/").concat(e),method:"get"})}function d(t,e){return Object(r["a"])({url:"product/get_attr/".concat(t,"/").concat(e),method:"get"})}function f(t,e,n){return Object(r["a"])({url:"cart/get_cart/".concat(t,"/").concat(e),method:"get",params:n})}function l(t,e){return Object(r["a"])({url:"cart/set_cart_num/".concat(t),method:"put",data:e})}function h(t){return Object(r["a"])({url:"cart/change_cart",method:"put",data:t})}function m(t,e){return Object(r["a"])({url:"cart/del_cart/".concat(t),method:"DELETE",data:e})}function p(t){return Object(r["a"])({url:"promotions/count/".concat(t),method:"get"})}function v(t,e){return Object(r["a"])({url:"order/compute/".concat(t),method:"post",data:e})}function g(t,e){return Object(r["a"])({url:"/order/create/".concat(t),method:"post",data:e})}function _(t,e){return Object(r["a"])({url:"order/pay/".concat(t),method:"post",data:e})}function b(t){return Object(r["a"])({url:"order/express_list?status="+t,method:"get"})}function k(t){return Object(r["a"])({url:"/order/delivery/".concat(t.id),method:"put",data:t.datas})}function O(t){return Object(r["a"])({url:"/order/express/temp",method:"get",params:t})}function j(){return Object(r["a"])({url:"/order/delivery_list",method:"get"})}function w(){return Object(r["a"])({url:"/order/sheet_info",method:"get"})}function y(t){return Object(r["a"])({url:"order/split_cart_info/".concat(t),method:"get"})}function I(t){return Object(r["a"])({url:"/order/remark/".concat(t.id),method:"put",data:t.remark})}function x(t){return Object(r["a"])({url:"order/refund/remark/".concat(t.id),method:"put",data:t.remark})}function C(t){return Object(r["a"])({url:"/refund/refund/".concat(t),method:"get"})}function S(t){return Object(r["a"])({url:"order/recharge/remark/".concat(t),method:"get"})}function E(t){return Object(r["a"])({url:"order/vip/remark/".concat(t),method:"get"})}function $(t,e){return Object(r["a"])({url:"order/vip/remark/".concat(t),method:"put",data:e})}function L(t,e){return Object(r["a"])({url:"order/recharge/remark/".concat(t),method:"put",data:e})}function U(t,e){return Object(r["a"])({url:"order/coupon_list/".concat(t),method:"post",data:e})}function M(t){return Object(r["a"])({url:"order/verify_cart_info",method:"get",params:t})}function P(t,e){return Object(r["a"])({url:"order/write_off/".concat(t),method:"put",data:e})}function T(t,e){return Object(r["a"])({url:"user/switch/".concat(e),method:"post",data:t})}function D(t){return Object(r["a"])({url:"order/cashier/hang",method:"post",data:t})}function J(t,e){return Object(r["a"])({url:"order/get_hang_list/".concat(t),method:"get",params:e})}function H(t){return Object(r["a"])({url:"order/get_user_list/".concat(t),method:"get"})}function N(t){return Object(r["a"])({url:"order/cashier/hang",method:"DELETE",params:{id:t}})}function R(t){return Object(r["a"])({url:"order/get_order_list",method:"post",data:t})}function W(t){return Object(r["a"])({url:"order/get_order_Info/".concat(t),method:"get"})}function A(t){return Object(r["a"])({url:"order/get_refund_Info/".concat(t),method:"get"})}function G(t){return Object(r["a"])({url:"order/get_refund_list",method:"get",params:t})}function V(t){return Object(r["a"])({url:"order/get_verify_list",method:"post",data:t})}function Y(t){return Object(r["a"])({url:"order/get_order_status/".concat(t),method:"get"})}function q(t,e){return Object(r["a"])({url:"order/order_refund/".concat(t),method:"put",data:e})}function z(t){return Object(r["a"])({url:"order/del_hang",method:"delete",params:t})}function B(){return Object(r["a"])({url:"erp/config",method:"get"})}function F(){return Object(r["a"])({url:"user/aux_screen",method:"get"})}function K(t){return Object(r["a"])({url:"user/swith_user",method:"post",data:t})}function Q(){return Object(r["a"])({url:"/code/list",method:"get"})}function X(t,e){return Object(r["a"])({url:"/order/write/form/".concat(t),method:"get",params:e})}function Z(t){return Object(r["a"])({url:"/get/table/list",method:"get",params:t})}function tt(t){return Object(r["a"])({url:"/table/uid/all",method:"get",params:t})}function et(t){return Object(r["a"])({url:"/get/cart/list",method:"get",params:t})}function nt(t){return Object(r["a"])({url:"/cancel/table",method:"get",params:t})}function rt(t){return Object(r["a"])({url:"/edit/table/cart",method:"post",data:t})}function ot(t){return Object(r["a"])({url:"/staff/place",method:"get",params:t})}function at(t){return Object(r["a"])({url:"/get/order/info/".concat(t),method:"get"})}function ct(t){return Object(r["a"])({url:"/pay_offline/".concat(t),method:"post"})}function it(t,e){return Object(r["a"])({url:"order/refund/".concat(t),method:"put",data:e})}function ut(t,e){return Object(r["a"])({url:"open/refund/".concat(t),method:"post",data:e})}function st(t){return Object(r["a"])({url:"table/update/info",method:"get",params:t})}function dt(t){return Object(r["a"])({url:"table/update",method:"post",data:t})}function ft(t){return Object(r["a"])({url:"card/benefits/".concat(t),method:"get"})}}}]);