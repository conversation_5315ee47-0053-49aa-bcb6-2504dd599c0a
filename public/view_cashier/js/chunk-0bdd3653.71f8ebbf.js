(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0bdd3653"],{"01ff":function(t,e,r){},"072c":function(t,e,r){},"16f5":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGwAAABsCAYAAAH4YWdbAAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAbKADAAQAAAABAAAAbAAAAADaE/gkAAAL1UlEQVR4Ae1deZAdRRn/Zua9nX27C1ilnIFNOCoKIRQoQYpEKuagYghRSIwIxlLZPxKhhCiGgBBigiBoQdAg11KKihI5LDBcuSpQgSpBjUnMRlNGYIGUGlE0+3b3XTP2N5seZubNPT3Tsy89VbvTx3f/uvv1zOvuJwG5yuWyjvewV2dnpyRFZaLCZZqIeo/NWHDTNHD+Gbbirg2vAaiqrczVR/2/79mIMCMd8QFbWZNGpzZkQEFdL+2wMTb5iASlux40iDCtDw81MWFlEyNqVD728felVyrgtMKVEQuHV15nMLoxGBXkn2twaKXfvclUP2JrXWxGCaXEaa9SpVKZUK/X/2Q1wy8tSdJArMDE8i0WU1PbrKz5HtR+9TObW8522eRTrF5Qvuh8QwtKLy+YBfrf95l5qr7JJ2XimUbd0JVfBPnIo5oYsKDJPLeGK33wSOj89SZDAP5r0mTWWBL6u/stORdNtlqPTChNTt7smCK3cqepUfJNUEVhjkobK4ZRlVD6TJU19WJqhfXu1gus9TTt7O20nN4DPaOKggShQEpLhTvvgcq61r86wlOtmrzyqROh68XtTR+NQQYFKoP2dkPJwIyzTWUd9z9CBgLjQwWMaQOp6bjv52a9VyKw6QeFxinYz7tAZU5hSfLBYUwi3cGbrTL8RHcYkFqWzlN2EQ2npaWFOLS6o6NjSWYNhCjUMsNM13U5M2UIkVDGpKFmGkYmH57O6aRXGAI90/b0efGa5c5pqFnhSAR3ak0D/cD/HGzNWecTbTMFgG8Yy3Ongv7evw2+jt5HYbDnUjcZZpnfZxkS+YaRKkJCqki97tuYNa4g4ZSO3n2VWYWpS1cYPJXbbzbmHlhn/RQvXuzvNTL7KkMCqlDbuwezIHefCPg0pPW/AfTpCMvVJTfgzfcKbiCE3eoBVU6l0jpnOa233n09G7q6x1SEDQQvFI5/2CWoIlpuEPj88/XMKsxHhlkV5J2vZ+rXlpmC5O5xZjpuwtezuEK9+Hw982KKWy6UxY2cjU+E0RaOOBkySb3HmH4PDg5uIpPIaXGE5JGHTPVVHEBSfabg5XimI2OWTmbao4VjDCIgEGMQxExFtCxivs9lYUOsDwxAefZ5YckD6YJm1IECCAETxPR9/WF0ZUqT6HMMH+fLF05JxWDlo+dAaXVvbNmJEHNzqu0rXwXlrHNiG0QZG384+CUCLYh4T+SYm662Ly2CtoU9tirllA+bbxWwQjrqGFveRswok6gpWm3we+QOGgysvIVps6B9xR1W0bHSzBwbXn4t1LesN5DQdm2HwcULXQ1CJ/FFBn0h5SQKCoKT3ivPxLHKrTdC7fmnDR2FSedB4/W/gv6vf5o61WUrQT64ykWZTF7tkY8HbfvvjPraxmehvul5kxYTbstpbAQhMokcszahELoMEkSkvn4dDN8S/CItCXpMPqCtTgU1tTjBsMoPm06EWFglPOiYD/c8nHDTKRxzi0qeywRieUbHzbbWRQzXErh5PNrLjDfB5C1w2xC5cEnBqHeIvN4mb4KvMhxDZ8hr7rvIrYc41zVKnesrFAoLVFXFN9sgIVrEqcoodabJbNK1NhPEpkvEqUYrNEGrhwS501t1rtjXqo6xef1mbQZ5SY/64d0rkMIxr8jktVwglldkvOwSiHlFJq/lArG8IuNlV8sixuZNMNnaWb50NoRdJOwV5eKChaBe9U2v6kjlTCbBLF9bdz77CkhdyZ91c9cUWX2fnTvHIrU3H2ImTdFtv6uPTt8q6bDDyVqG5PFONHgMXdMDSb8E9/Ky85mtYDjpRRBQnig0TqeKF803V9AH6A2sHrx8biCNH0EixJyC6y9tBGnMCc7iWHnrBoo4AhIh5lSIfa16H76etF/4LWfh05+1FXY+9gKYG0JtNWwyTB0rTJ3p2hT1gQPQ/o2boDhnnmF159rnQDr6WKg+8Qs2XrhISeQYImHd2qL/Z2Sjj1NPefZk0MsDoC69GTqe3ATSsWOgtvanUL3f/WsDRDLJF+uon8lwP7xiKdQ325c0UOdKdz9EliBNotmm++Blc0B7u98sT+oQFcTEMRSG0yr5hLGgLltl7COiCvBO9xMpkz8JbZd92azX63XQdr9/DoYy8Swo3fOwlTV2mumoqJx+pumE1aLGzj8aWfnEU4w7zVtpMI3b+lhdTBDT+nbA4KIvuNrUfuOtULhgjmsdFuKBG9aZC6ummBwx8shiOqUooJz7CWi8vMV0pNK7BqTOg7N1MhLiSjharzcaNqeQCZcvqTfcYvLHTSRCrHzxdNszWHHufFCvXW7byWY1LKjeSotrHkt3c1qI6XywrD39uKdTaLT2+l6r7b7pxracLcT0s7axc5uv4368UesSfUA7lWFTY9X5nbKj5pMPHhaNxppFMiCwuKz7NuPISzR4xFGYFQ/TppiV0WH0CMfCRClPNAKxPKERxhaBWJgo5YlGIJYnNMLYIhALE6U80QjE8oRGkC1kMeZAqyLWK+PZF0ERGG31eMKdbKx4bhHnCEga8cc4oNpcxY2LnskK9TsJOotbbY3waGtx1F4EiqTvLZVKXyfpKpYbgJGF3GsISFdSQnHPXwRwKDRGDdwfQcC6Jn8mCoucESCgrcZtEgcIYMkX+DilizzzCBDABmQBFvO4piYQsWrV+W9qQeMtWADGG4GI+gVgEQPGm1wAxhuBiPoFYBEDxptcAMYbgYj6BWARA8abXADGG4GI+gVgEQPGm1wAxhuBiPoFYBEDxpuc6YrSpM5YT9JMKosVf3HWXCbL41nZk5se1tiz2zz2lJVzLOTgsma0LS9XbgCTINIvVWcavzzZlhvAMkVgFCvjvnlAe2Mv6PveBtDz28MMfMkvr0rHHQ/yuJO5ws1t0lG5YwXU1j3p6zzuHlYXLwFJbSdnlW+A2gu/8aXPqrI45xKyGXhFVupsergNiUFgoZWFs88FPOkez1a37qy2ecAhE8b2tMziNiSyPHcGgyOVOqAwfRb5NYDdgbM6PDcDG4K2e2cgrVfgee304zYkegXCWa6MP438TGabsUEVD13wutqXf9foibS+8v2VgJterRcOsR0//DFIxxxnFpfnzQR9/z/MfN4T3HpY7fFHoPHm30DbugW0d/e7xoluj8ZKv9/RoMzyRyZA6c4Hbac84e+y1l99ZQQoMmmgV/XRn0D1R7huNvwlk5MB5ClTQRl7EhTnXx6ekSElN8CcPtQ3PAPDq653FofK42dc+/WrQDr8iFD0JhHZ54uHcgTpbb/pNijMvNBk45ngNulwOo0B6XxqCznhQ3FWBeaVcSdFBwulEl3y+FMBim3uOkg92pQXsNDI3PQwGjH8MRr8URq8og6JVAa9J+XHs0bwzJE8XbmbdGh/3mXGp77xOaP1S6oK9d//1ix3JvCsk+JnFgAcZh8SZXLuCb2kDx0NbYuW0OzIvVGH+ssvAh4S43ZZbXGr51GWnx5Wq8LgFZ8DfPMR9Srd9gPbDDESP3nDUr5khu2sGCs/vtnoeGit97BpJc4gzR2wxrbXAKfg2ltvNrlbmPEpaF9+u1GOdENXX9FE41eQdEi0yjYO1SIH/vidDmalTyvNbUgM8+CsvfPWyDNSmwraX/rSikEoudigrA2G14Mztx4WBrBQkQwgUqZMA6V7LNSeesw4EDCAPHQ1L8C49bAwkZFPHg/qt74DEulh9Q3roPrwA2HYbDSNrZuBzalKNrHcMtyew+TjuwOdRsDwhDu5exwokyYH0mdFEMb2tGzhNiSiQ/h6qvbEL0F7pz8t/5jKlcd0Q3He57m9lkJnuALGNJqHiDBuQ+IhEl/mbgrAmIc0XYECsHTjy1y6AIx5SNMVKABLN77MpQvAmIc0XYECsHTjy1y6AIx5SNMVKABLN77MpQvAmIc0XYECsHTjy1y6AIx5SNMViIDx/So3Xf9aTXqfVKlUJjQajR3kaDfR23IMLzkrUVMU5QxZVdVd5EzZEinYnGN7D2nTEBvECLEyD2mmETnY43pIj7uAEHaLAzBpZLK5k5gPkJj3k/t60qN6ESSr5v8DoZjkVuvRIc0AAAAASUVORK5CYII="},"1dd0":function(t,e,r){"use strict";var a=r("7437"),s=r.n(a);s.a},"1e0a":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"order-wrapper"},[a("div",{directives:[{name:"show",rawName:"v-show",value:1==t.active,expression:"active == 1"}],staticClass:"order"},[a("div",{staticClass:"left"},[a("div",{staticClass:"left-top"},[a("div",{staticClass:"title"},[a("div",{staticClass:"item",class:{active:1==t.active},on:{click:function(e){t.active=1}}},[t._v("\n              订单列表\n            ")]),a("div",{staticClass:"item",class:{active:2==t.active},on:{click:function(e){t.active=2}}},[t._v("\n              桌码管理\n            ")])]),1==t.active?a("div",{staticClass:"sx",on:{click:function(e){t.filterModal=!t.filterModal}}},[t._v("\n            "+t._s(t.filterModal?"关闭":"筛选")+"\n            "),t.filterModal?t._e():a("Icon",{staticClass:"ios-funnel-outline",attrs:{color:"#666",type:"ios-funnel-outline"}})],1):t._e()]),a("div",{staticClass:"order-box"},[t.filterModal?t._e():a("div",{staticClass:"search"},[a("Input",{attrs:{search:"","enter-button":"搜索",size:"large",placeholder:"搜索订单流水号、订单编号"},on:{"on-search":t.search},model:{value:t.orderData.keyword,callback:function(e){t.$set(t.orderData,"keyword",e)},expression:"orderData.keyword"}})],1),t.orderListData.length?a("orderList",{directives:[{name:"show",rawName:"v-show",value:!t.filterModal,expression:"!filterModal"}],attrs:{orderType:"table",total:t.count,orderData:t.orderListData},on:{addPage:t.addPage,selectOrder:t.selectOrder}}):t.orderListData.length||t.filterModal?t._e():a("div",{staticClass:"no-order"},[a("img",{attrs:{src:r("66c9"),alt:""}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无订单")])]),a("filter-modal",{directives:[{name:"show",rawName:"v-show",value:t.filterModal,expression:"filterModal"}],attrs:{"order-type":"table"},on:{search:t.searchList}})],1)]),a("div",{staticClass:"order-data"},[a("div",{staticClass:"header"},[t._l(t.tabs,(function(e,r){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.selectOrderData.oid||!r,expression:"selectOrderData.oid || !index"}],key:r,staticClass:"item",class:t.sle===r?"sel":r===t.sle-1?"neighbor-left":r===t.sle+1?"neighbor-right":"def",on:{click:function(e){return t.tabClick(r)}}},[a("div",{staticClass:"item-wrap"},[t._v("\n              "+t._s(e)+"\n            ")])])})),a("div",{staticClass:"box",class:2===t.sle?"neighbor-right":""})],2),a("div",{staticClass:"content",class:{"border-radius":t.sle}},[0===t.sle&&t.selectOrderData.id?a("userOrder",{staticClass:"orders",attrs:{canSend:t.canSend,selectData:t.selectOrderData},on:{remarks:t.remarks,rechargeBnt:t.rechargeBnt,userChange:t.userChange,calculate:t.calculate,discountCon:t.discountCon,delGoods:t.delGoods}}):t._e(),1===t.sle&&t.selectOrderData.oid&&t.selectOrderData.orderId.id?a("orderDetails",{staticClass:"orders",attrs:{orderDatalist:t.orderInfoData}}):t._e(),2===t.sle&&t.selectOrderData.oid&&t.selectOrderData.orderId.id?a("orderRecord",{attrs:{id:t.selectOrderData.oid}}):t.selectOrderData.id?t._e():a("div",{staticClass:"no-order"},[a("img",{attrs:{src:r("0493"),alt:""}}),a("span",{staticClass:"trip"},[t._v("噢～目前暂无挂单记录")])])],1),t.selectOrderData.id&&(2!=t.selectOrderData.status||t.selectOrderData.orderId&&!t.selectOrderData.orderId.is_del)?a("div",{staticClass:"footer"},[t.staff_id?a("Button",{on:{click:t.storeTap}},[t._v(t._s(t.storeInfos.staff_name))]):t._e(),t.selectOrderData.orderId&&t.selectOrderData.orderId.paid?a("div",[a("Button",{staticClass:"btn grey",on:{click:t.remarks}},[t._v("订单备注")]),a("Button",{staticClass:"btn grey",on:{click:t.point}},[t._v("小票打印")]),!t.selectOrderData.orderId.refund_status&&t.selectOrderData.orderId.paid&&t.selectOrderData.orderId.pay_price>0?a("Button",{staticClass:"btn blue",on:{click:t.getRefundData}},[t._v("立即退款")]):t._e()],1):3==t.selectOrderData.status||t.selectOrderData.orderId&&t.selectOrderData.orderId.paid?t._e():a("div",[a("Button",{staticClass:"btn red",on:{click:t.cancelTable}},[t._v("取消")]),t.selectOrderData.orderId&&"offline"==t.selectOrderData.orderId.pay_type?a("Button",{staticClass:"btn blue",on:{click:t.payOffline}},[t._v("立即支付")]):t.selectOrderData.orderId&&!t.selectOrderData.orderId.paid?[t.selectOrderData.orderId.pay_type?t._e():a("Button",{class:["btn",t.integral?"light":"grey"],on:{click:t.integralTap}},[t._v("积分")]),t.selectOrderData.orderId.pay_type?t._e():a("Button",{staticClass:"btn grey",on:{click:t.changePrice}},[t._v("改价")]),a("Button",{staticClass:"btn grey",on:{click:t.staffPlace}},[t._v("打单")]),a("Button",{staticClass:"btn",on:{click:function(e){return t.payPrice("cash")}}},[t._v("现金收款")]),a("Button",{staticClass:"btn",on:{click:function(e){return t.payPrice("")}}},[t._v("微信/支付宝")]),a("Button",{staticClass:"btn blue",on:{click:function(e){return t.payPrice("yue")}}},[t._v("余额收款")])]:t._e()],2)],1):t._e()]),a("order-remark",{ref:"remarks",attrs:{orderId:t.selectOrderData.orderId?t.selectOrderData.orderId.id:0},on:{submitFail:t.submitFail}}),a("orderSend",{ref:"send",attrs:{orderId:t.orderId,status:t.status,pay_type:t.pay_type},on:{submitFail:t.send}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:2==t.active,expression:"active == 2"}],staticClass:"table-code"},[a("div",{staticClass:"left-top"},[a("div",{staticClass:"title"},[a("div",{staticClass:"item",class:{active:1==t.active},on:{click:function(e){t.active=1}}},[t._v("\n            订单列表\n          ")]),a("div",{staticClass:"item",class:{active:2==t.active},on:{click:function(e){t.active=2}}},[t._v("\n            桌码管理\n          ")])]),1==t.active?a("div",{staticClass:"sx",on:{click:function(e){t.filterModal=!t.filterModal}}},[t._v("\n          "+t._s(t.filterModal?"关闭":"筛选")+"\n          "),t.filterModal?t._e():a("Icon",{staticClass:"ios-funnel-outline",attrs:{color:"#666",type:"ios-funnel-outline"}})],1):t._e()]),a("div",{staticClass:"section-wrapper"},t._l(t.codeList,(function(e){return a("div",{key:e.id,staticClass:"section"},[a("div",{staticClass:"head"},[t._v(t._s(e.name)+"（桌码分类）")]),a("div",{staticClass:"body"},[a("ul",{staticClass:"list"},t._l(e.tableQrcode,(function(e){return a("li",{key:e.id,staticClass:"item",class:{active:e.is_use}},[a("div",{staticClass:"code"},[t._v(t._s(e.table_number))]),e.is_use?[a("div",[t._v("\n                  "+t._s(e.eat_number)+"人就餐（"+t._s(e.seat_num)+"人桌）\n                ")]),a("div",{staticClass:"time"},[t._v(t._s(e.order_time)+" 下单")])]:a("div",[t._v("空桌（"+t._s(e.seat_num)+"人桌）")])],2)})),0)])])})),0)]),a("Modal",{attrs:{"footer-hide":"",title:"备注"},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[a("Input",{staticStyle:{width:"100%"},attrs:{rows:5,maxlength:"200",placeholder:"订单备注","show-word-limit":"",type:"textarea"}}),a("Button",{staticClass:"mt20",attrs:{long:"",type:"primary"}},[t._v("提交")])],1),a("storeList",{ref:"store",attrs:{storeInfo:t.storeInfos},on:{getStoreId:t.getStoreId,getUserInfo:t.getUserInfo}}),a("recharge",{ref:"recharge",attrs:{userInfo:t.userInfo},on:{getSuccess:t.getSuccess}}),a("Modal",{staticClass:"modalPay",attrs:{"footer-hide":"",width:"430px"},on:{"on-cancel":t.modalPayCancel},model:{value:t.modalPay,callback:function(e){t.modalPay=e},expression:"modalPay"}},[a("div",{staticClass:"payPage"},[a("div",{staticClass:"header acea-row row-center-wrapper"},[a("div",{staticClass:"picture"},[a("img",{attrs:{src:r("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")])]),a("div",{staticClass:"money"},[t._v("\n          ¥"),a("span",{staticClass:"num"},[t._v(t._s(t.selectOrderData.payPrice?t.selectOrderData.payPrice:0))])]),a("Input",{ref:"focusNum",staticStyle:{"margin-top":"16px"},attrs:{placeholder:"请点击输入框聚焦扫码或输入编码号",size:"large",type:"url"},on:{input:t.inputSaoMa},model:{value:t.payNum,callback:function(e){t.payNum=e},expression:"payNum"}}),a("div",{staticClass:"process"},[a("div",{staticClass:"picture"},[a("img",{attrs:{src:r("fbde")}})]),a("div",{staticClass:"list acea-row row-between-wrapper"},[a("div",{staticClass:"item one"},[a("div",{staticClass:"name"},[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"出示付款码":"打开付款码")+"\n              ")]),a("div",[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"用户打开个人中心":"微信/支付宝付款码")+"\n              ")])]),a("div",{staticClass:"item two"},[a("div",{staticClass:"name"},[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"扫描付款码":"贴合付款盒子")+"\n              ")]),a("div",[t._v("\n                "+t._s("yue"==t.createOrder.pay_type?"扫码枪":"等待完成支付")+"\n              ")])]),a("div",{staticClass:"item three"},[a("div",{staticClass:"name"},[t._v("确认收款")]),a("div",[t._v("收银台确认")])])])])],1)]),a("Modal",{staticClass:"cash",attrs:{"footer-hide":"",width:"770px"},on:{"on-cancel":t.cancel},model:{value:t.modalCash,callback:function(e){t.modalCash=e},expression:"modalCash"}},[a("div",{staticClass:"cashPage acea-row"},[a("div",{staticClass:"left"},[a("div",{staticClass:"picture"},[a("img",{attrs:{src:r("ad18")}})]),a("div",{staticClass:"text"},[t._v("应收金额(元)")]),a("div",{staticClass:"money"},[t._v("\n            ¥"),a("span",{staticClass:"num"},[t._v(t._s(t.selectOrderData.payPrice||0))])])]),a("div",{staticClass:"right"},[a("div",{staticClass:"rightCon"},[a("div",{staticClass:"top acea-row row-between-wrapper"},[a("div",[t._v("实际收款(元)")]),a("div",{staticClass:"num"},[t._v(t._s(t.collection))])]),a("div",{staticClass:"center acea-row row-between-wrapper"},[a("div",[t._v("需找零(元)")]),this.$computes.Sub(t.collection,t.selectOrderData.payPrice?t.selectOrderData.payPrice:0)>0?a("div",{staticClass:"num"},[t._v("\n                "+t._s(this.$computes.Sub(t.collection,t.selectOrderData.payPrice?t.selectOrderData.payPrice:0))+"\n              ")]):a("div",{staticClass:"num"},[t._v("0")])]),a("div",{staticClass:"bottom acea-row"},[t._l(t.numList,(function(e,r){return a("div",{key:r,staticClass:"item acea-row row-center-wrapper",class:"."==e?"spot":"",on:{click:function(r){return t.numTap(e)}}},[t._v("\n                "+t._s(e)+"\n              ")])})),a("div",{staticClass:"item acea-row row-center-wrapper",on:{click:t.delNum}},[a("Icon",{attrs:{type:"ios-backspace"}})],1)],2)]),a("Button",{attrs:{type:"primary"},on:{click:t.cashBnt}},[t._v("确认")])],1)])]),a("Modal",{attrs:{"class-name":"vertical-center-modal","footer-hide":"",title:"优惠明细",width:"400"},model:{value:t.discount,callback:function(e){t.discount=e},expression:"discount"}},[a("div",{staticClass:"discountCon"},[a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("订单原价")]),a("div",[t._v("￥"+t._s(t.selectOrderData.sumPrice||t.selectOrderData.orderId&&t.selectOrderData.orderId.total_price||0))])]),a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("会员优惠金额：")]),a("div",[t._v("￥"+t._s(t.selectOrderData.vipPrice||t.selectOrderData.orderId&&t.selectOrderData.orderId.vip_true_price||0))])]),a("div",{staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v("积分抵扣：")]),a("div",[t._v("￥"+t._s(t.selectOrderData.usedIntegral||t.selectOrderData.orderId&&t.selectOrderData.orderId.deduction_price||0))])]),t._l(t.selectOrderData.promotionsDetail,(function(e,r){return a("div",{key:r,staticClass:"item acea-row row-between-wrapper"},[a("div",[t._v(t._s(e.title)+"：")]),a("div",[t._v("￥"+t._s(e.promotions_price||0))])])}))],2)]),a("Modal",{attrs:{title:"退款处理","class-name":"refund-modal"},model:{value:t.refundVisible,callback:function(e){t.refundVisible=e},expression:"refundVisible"}},[a("Form",{attrs:{model:t.refundData,"label-width":90}},[a("FormItem",{attrs:{label:"退款单号："}},[a("Input",{attrs:{disabled:""},model:{value:t.refundData.order_id,callback:function(e){t.$set(t.refundData,"order_id",e)},expression:"refundData.order_id"}})],1),a("FormItem",{attrs:{label:"退款金额：",required:""}},[a("InputNumber",{attrs:{max:9999999999},on:{"on-change":t.numberChange},model:{value:t.refundData.refund_price,callback:function(e){t.$set(t.refundData,"refund_price",e)},expression:"refundData.refund_price"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary",size:"large",long:""},on:{click:t.orderRefund}},[t._v("提交")])],1)],1),a("changePrice",{ref:"changePrice",attrs:{type:1},on:{submitSuccess:t.submitSuccess}})],1)},s=[],i=r("f500"),o=r("b89c"),n=r("5671"),c=r("d487"),d=r("9568"),l=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"order-user"},[r("div",{staticClass:"sel-user-group"},[t._l(t.selectData.userList,(function(e){return[!t.selectData.oid||t.selectData.orderId&&t.selectData.orderId.uid==e.uid?r("div",{key:e.id,staticClass:"sel-user",on:{click:function(r){return t.userChange(e)}}},[!t.selectData.oid&&t.selectData.userList.length>1?r("i",{class:["iconfont",e.uid==t.selectData.uid?"iconxuanzhong6":"icondayuanjiao"]}):t._e(),r("div",{staticClass:"avatar"},[r("img",{attrs:{src:e.userInfo.avatar||t.defaultAvatar,alt:"头像"}})]),r("div",{staticClass:"item-right"},[r("div",{staticClass:"user"},[r("div",[t._v(t._s(e.userInfo.nickname||"-"))])]),r("div",{staticClass:"money"},[r("div",[e.userInfo.phone?r("span",{staticClass:"pr20"},[t._v(t._s(e.userInfo.phone))]):t._e(),t._v("余额\n                "),r("span",{staticClass:"num"},[t._v(t._s(e.userInfo.now_money||0)),r("a",{staticClass:"btn",on:{click:function(r){return t.rechargeBnt(e.userInfo)}}},[t._v("充值")])])]),r("div",[t._v("\n                积分 "),r("span",{staticClass:"num"},[t._v(t._s(e.userInfo.integral||0))])])])])]):t._e()]}))],2),r("div",{staticClass:"cart-num"},[r("div",{staticClass:"cart-num-left"},[r("span",[t._v("共")]),r("span",{staticClass:"num"},[t._v(t._s(t.total_num))]),r("span",[t._v("件商品")])]),r("div",{staticClass:"reduce"},[t._v("\n        优惠："+t._s(t.$computes.Sub(t.selectData.sumPrice||t.selectData.orderId&&t.selectData.orderId.total_price||0,t.selectData.payPrice||t.selectData.orderId&&t.selectData.orderId.pay_price||0)||0)),r("a",{staticClass:"btn",on:{click:t.discountCon}},[t._v("明细")])]),r("div",{staticClass:"actual"},[t._v("\n        实付："),r("span",{staticClass:"money"},[t._v("¥"+t._s(t.selectData.payPrice||t.selectData.orderId&&t.selectData.orderId.pay_price||0))])])]),r("div",{staticClass:"goods-list"},[r("Table",{attrs:{columns:t.columns,data:t.tableList,loading:t.loading,"no-data-text":"暂无数据","highlight-row":"","no-filtered-data-text":"暂无筛选结果"},scopedSlots:t._u([{key:"info",fn:function(e){var a=e.row;return[r("div",{staticClass:"tabBox"},[r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"tabBox_img"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:a.productInfo.attrInfo?a.productInfo.attrInfo.image:a.productInfo.image,expression:"\n                  row.productInfo.attrInfo\n                    ? row.productInfo.attrInfo.image\n                    : row.productInfo.image\n                "}]})]),r("span",{staticClass:"tabBox_tit line2"},[a.is_gift?r("span",{staticClass:"is-gift"},[t._v("赠品")]):t._e(),t._v("\n              "+t._s(a.productInfo.store_name)+"\n              "),a.productInfo.attrInfo?r("span",[t._v("\n                |\n                "+t._s(a.productInfo.attrInfo?a.productInfo.attrInfo.suk:"")+"\n              ")]):t._e()])])]}},{key:"sum_true_price",fn:function(e){var a=e.row;return[r("div",{staticClass:"tabBox"},[t._v(t._s(a.sum_price*a.cart_num||0))])]}},{key:"action",fn:function(e){var a=e.row;return[r("Button",{staticClass:"sub",attrs:{disabled:1==a.cart_num,shape:"circle"},on:{click:function(e){return t.calculate(a,0)}}},[r("Icon",{attrs:{custom:"iconfont iconjian",size:"12"}})],1),r("InputNumber",{attrs:{min:1,editable:!1},model:{value:a.cart_num,callback:function(e){t.$set(a,"cart_num",e)},expression:"row.cart_num"}}),r("Button",{staticClass:"add",attrs:{disabled:a.cart_num==a.trueStock,shape:"circle"},on:{click:function(e){return t.calculate(a,1)}}},[r("Icon",{attrs:{custom:"iconfont iconjia",size:"12"}})],1),r("a",{staticClass:"delete ml30",on:{click:function(e){return t.delte(a,"删除商品",t.index)}}},[t._v("删除")])]}}])})],1)])},u=[];function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(r,!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var h=[{title:"商品信息",slot:"info",minWidth:200},{title:"单价",key:"sum_price",width:150},{title:"数量",key:"cart_num",width:150},{title:"总金额",slot:"sum_true_price",width:150},{title:"操作",slot:"action",width:180}],p={name:"userOrder",props:["selectData"],data:function(){return{columns:[],tableList:[],loading:!1,defaultAvatar:r("653d"),give_integral_img:r("29ba"),give_coupon_img:r("16f5")}},computed:{total_num:function(){return this.tableList.reduce((function(t,e){var r=e.cart_num;return t+r}),0)}},watch:{"selectData.orderId":{handler:function(t){var e=this,r="action";t?(t.id&&(r="sum_true_price"),t._info&&(this.tableList=[],Array.isArray(t._info)?t._info.forEach((function(t){e.tableList.push(t.cart[0])})):Object.values(t._info).forEach((function(t){e.tableList.push(t.cart_info)})))):this.tableList=[],this.columns=h.filter((function(t){return!t.slot||"info"==t.slot||t.slot==r}))},immediate:!0}},methods:{delte:function(t,e,r){var a=this,s={title:e,num:r,url:"del/table/cart",method:"DELETE",ids:{productId:t.product_id,uniqueId:t.product_attr_unique,tableId:t.collate_code_id}};this.$modalSure(s).then((function(t){a.$Message.success(t.msg),a.$emit("delGoods")})).catch((function(t){a.$Message.error(t.msg)}))},rechargeBnt:function(t){this.$emit("rechargeBnt",t)},getRefundData:function(){this.$emit("getRefundData")},remarks:function(){this.$emit("remarks")},point:function(){var t=this;this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(this.selectData.id),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){t.$Message.success(e.msg)})).catch((function(e){t.$Message.error(e.msg)}))},orderSend:function(){this.$emit("orderSend")},userChange:function(t){this.$emit("userChange",t.uid)},calculate:function(t,e){this.$emit("calculate",m({},t,{isAdd:e}))},discountCon:function(){this.$emit("discountCon")}}},_=p,g=(r("f830"),r("2877")),y=Object(g["a"])(_,l,u,!1,null,"2a69b6fc",null),I=y.exports,b=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"detail"},[r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("用户信息")]),t.orderDatalist.userInfo?r("ul",{staticClass:"list"},[r("li",{staticClass:"item"},[r("div",[t._v("用户昵称：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.uid?t.orderDatalist.userInfo.nickname:"游客")+"\n          ")])]),r("li",{staticClass:"item"},[r("div",[t._v("绑定电话：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.userInfo.phone||"暂无"))])])]):t._e()]),6==t.orderDatalist.orderInfo.product_type?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("预约信息")]),r("ul",{staticClass:"list"},[r("li",{staticClass:"item"},[r("div",[t._v("服务类型：")]),r("div",{staticClass:"value"},[t._v(t._s(3==t.orderDatalist.orderInfo.reservation_type?"上门服务":"到店服务"))])]),r("li",{staticClass:"item"},[r("div",[t._v("预约模式：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time_id>0?"购买时预约":"先买后约"))])]),t.orderDatalist.orderInfo.reservation_time?r("li",{staticClass:"item"},[r("div",[t._v("预约日期：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_time))])]):t._e(),t.orderDatalist.orderInfo.reservation_show_time?r("li",{staticClass:"item"},[r("div",[t._v("预约时段：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.reservation_show_time))])]):t._e(),r("li",{staticClass:"item"},[r("div",[t._v("预约人：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.real_name))])]),r("li",{staticClass:"item"},[r("div",[t._v("预约电话：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_phone))])])]),t.orderDatalist.orderInfo.user_address.trim()?r("div",{staticClass:"item"},[r("div",[t._v("预约地址：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.user_address))])]):t._e()]):t._e(),r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("订单信息")]),t.orderDatalist.orderInfo?r("ul",{staticClass:"list"},[r("li",{staticClass:"item"},[r("div",[t._v("订单编号：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.order_id))])]),r("li",{staticClass:"item"},[r("div",[t._v("创建时间：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._add_time))])]),r("li",{staticClass:"item"},[r("div",[t._v("商品总数：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.total_num))])]),r("li",{staticClass:"item"},[r("div",[t._v("商品总价：")]),r("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.total_price))])]),t.orderDatalist.orderInfo.first_order_price>0?r("li",{staticClass:"item"},[r("div",[t._v("首单优惠：")]),r("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.first_order_price))])]):t._e(),r("li",{staticClass:"item"},[r("div",[t._v("优惠券金额：")]),r("div",{staticClass:"value"},[t._v("￥"+t._s(t.orderDatalist.orderInfo.coupon_price))])]),r("li",{staticClass:"item"},[r("div",[t._v("积分抵扣：")]),r("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.deduction_price||0)+"\n          ")])]),parseFloat(t.orderDatalist.orderInfo.use_integral)?r("li",{staticClass:"item"},[r("div",[t._v("使用积分：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(parseFloat(t.orderDatalist.orderInfo.use_integral))+"\n          ")])]):t._e(),r("li",{staticClass:"item"},[r("div",[t._v("会员商品优惠：")]),r("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.vip_true_price||0)+"\n          ")])]),0!=t.orderDatalist.orderInfo.first_order_price?r("li",{staticClass:"item"},[r("div",[t._v("新人首单优惠：")]),r("div",{staticClass:"value"},[t._v("\n            ￥"+t._s(t.orderDatalist.orderInfo.first_order_price)+"\n          ")])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?r("li",{staticClass:"item"},[r("div",[t._v("门店名称：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._store_name))])]):t._e(),2===t.orderDatalist.orderInfo.shipping_type&&0===t.orderDatalist.orderInfo.refund_status&&1===t.orderDatalist.orderInfo.paid?r("li",{staticClass:"item"},[r("div",[t._v("核销码：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.verify_code))])]):t._e(),r("li",{staticClass:"item"},[r("div",[t._v("推广人：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.userInfo.spread_name)+"/ID:"+t._s(t.orderDatalist.userInfo.spread_uid)+"\n          ")])]),r("li",{staticClass:"item"},[r("div",[t._v("支付时间：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo._pay_time))])]),r("li",{staticClass:"item"},[r("div",[t._v("支付方式：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo._status._payType)+"\n          ")])]),t.orderDatalist.orderInfo.store_order_sn?r("li",{staticClass:"item"},[r("div",[t._v("原订单号：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.store_order_sn))])]):t._e()]):t._e()]),t.orderDatalist.orderInfo&&t.orderDatalist.orderInfo.promotions_detail.length?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("活动信息")]),r("ul",{staticClass:"list"},t._l(t.orderDatalist.orderInfo.promotions_detail,(function(e,a){return r("li",{key:a,staticClass:"item"},[r("div",[t._v(t._s(e.title)+"：")]),r("div",{staticClass:"value"},[t._v("￥"+t._s(e.promotions_price))])])})),0)]):t._e(),t.isShow||6==t.orderDatalist.orderInfo.product_type&&t.orderDatalist.orderInfo.reservation_time_id>0||6!=t.orderDatalist.orderInfo.product_type?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("自定义留言")]),t._l(t.orderDatalist.orderInfo.custom_form,(function(e,a){return r("div",{key:a},[6==t.orderDatalist.orderInfo.product_type&&e.length?r("div",{staticClass:"item fs-14 mb10"},[t._v(t._s(t.orderDatalist.orderInfo.custom_form_title)+t._s(a+1))]):t._e(),r("ul",{staticClass:"list"},t._l(e,(function(e,a){return r("li",{directives:[{name:"show",rawName:"v-show",value:e.value&&-1==["uploadPicture","dateranges"].indexOf(e.name)||e.value.length&&-1!=["uploadPicture","dateranges"].indexOf(e.name),expression:"(item.value && ['uploadPicture','dateranges'].indexOf(item.name) == -1) || (item.value.length && ['uploadPicture','dateranges'].indexOf(item.name) != -1)"}],key:a,staticClass:"item"},[r("div",{staticClass:"txtVal"},[t._v(t._s(e.titleConfig.value)+"：")]),"dateranges"===e.name?r("div",{staticClass:"value"},[t._v(t._s(e.value[0]+"/"+e.value[1]))]):"uploadPicture"===e.name?r("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"value"},t._l(e.value,(function(t,e){return r("div",{key:e,staticClass:"image"},[r("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t,expression:"img"}]})])})),0):r("div",{staticClass:"value"},[t._v(t._s(e.value||"-"))])])})),0)])}))],2):t._e(),t.orderDatalist.orderInfo&&t.orderDatalist.orderInfo.mark?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("买家留言")]),r("ul",{staticClass:"list"},[r("li",{staticClass:"item mark"},[r("div",[t._v("备注：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.mark||"-"))])])])]):t._e(),t.orderDatalist.orderInfo&&t.orderDatalist.orderInfo.remark?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("订单备注")]),r("ul",{staticClass:"list"},[r("li",{staticClass:"item mark"},[r("div",[t._v("备注：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.remark||"-"))])])])]):t._e(),t.orderDatalist.orderInfo&&"express"===t.orderDatalist.orderInfo.delivery_type?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("物流信息")]),r("ul",{staticClass:"list"},[r("li",{staticClass:"item"},[r("div",[t._v("快递公司：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),r("li",{staticClass:"item"},[r("div",[t._v("快递单号：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_id)+"\n          ")])])])]):t._e(),t.orderDatalist.orderInfo&&"send"===t.orderDatalist.orderInfo.delivery_type?r("div",{staticClass:"section"},[r("div",{staticClass:"title"},[t._v("配送信息")]),r("ul",{staticClass:"list"},[r("li",{staticClass:"item"},[r("div",[t._v("送货人姓名：")]),r("div",{staticClass:"value"},[t._v("\n            "+t._s(t.orderDatalist.orderInfo.delivery_name||"-")+"\n          ")])]),r("li",{staticClass:"item"},[r("div",[t._v("送货人电话：")]),r("div",{staticClass:"value"},[t._v(t._s(t.orderDatalist.orderInfo.delivery_id))])])])]):t._e()])},C=[],D={name:"orderDetails",props:{orderDatalist:{type:Object}},data:function(){return{isShow:0}},watch:{orderDatalist:function(t){var e=this;t.orderInfo&&t.orderInfo.custom_form&&t.orderInfo.custom_form.length&&t.orderInfo.custom_form.forEach((function(t){t.length&&t.forEach((function(t){if(t.value)return e.isShow=1}))}))}},methods:{}},O=D,w=(r("3a44"),Object(g["a"])(O,b,C,!1,null,"7ac4f34e",null)),A=w.exports,k=r("6dc2"),x=r("22f89"),S=r("a464"),P=r("a123"),M=(r("94b5"),r("f8b7"));function L(t){return j(t)||N(t)||T()}function T(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function N(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function j(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(r,!0).forEach((function(e){E(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function E(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var G={components:{changePrice:i["a"],recharge:o["a"],storeList:n["a"],orderList:c["a"],goodsList:d["a"],userOrder:I,orderDetails:A,orderRemark:x["a"],orderRecord:k["a"],filterModal:S["a"],orderSend:P["a"]},data:function(){return{orderId:0,orderListData:[],tabs:["商品信息","订单详情","订单记录"],sle:0,filterModal:!1,userFrom:{keyword:"",page:1,limit:9},orderData:{type:"10",status:"",time:"",staff_id:"",serial_number:"",page:1,limit:10},selectOrderData:{},orderInfoData:{},count:0,status:0,pay_type:"",canSend:!0,tengxun_map_key:"",open_erp:null,active:1,modal:!1,userInfo:{},storeInfos:{},storeList:[],goodFrom:{store_name:"",field_key:"all",cate_id:"",page:1,limit:12,uid:0,staff_id:0},codeList:[],uid:0,staff_id:0,cart_ids:"",table_id:0,payNum:"",createOrder:{remarks:"",change_price:0,cart_id:[],userCode:"",is_price:0,auth_code:""},modalPay:!1,priceInfo:{},orderSystem:{loadingMsg:null,timer:null},modalCash:!1,collectionArray:[],collection:0,numList:["7","8","9","4","5","6","1","2","3","0","."],integral:!1,discount:!1,isOrderCreate:0,codeNum:"",refundVisible:!1,refundData:{order_id:0,refund_price:0}}},computed:{isCashierCartList:function(){var t=this.uid,e=this.staff_id,r=this.cart_ids,a=this.selectOrderData.oid;return{uid:t,staff_id:e,cart_ids:r,oid:a}}},watch:{count:function(t){t?this.table_id=this.orderListData[0].id:this.selectOrderData={}},table_id:function(t){var e=this.orderListData.find((function(e){var r=e.id;return r==t}));this.selectOrderData={},this.uid=0,e&&(this.selectOrderData=F({},e),this.getTableUidAll())},uid:function(t){t&&1==this.selectOrderData.status?this.getCartList():this.cart_ids=""},cart_ids:function(t){t?this.cashierCompute():this.unchangedPrice=0},integral:function(){this.cashierCompute()},isCashierCartList:function(t,e){var r=t.uid,a=t.staff_id,s=t.cart_ids,i=t.oid,o=!0;for(var n in t)if(Object.hasOwnProperty.call(t,n)&&t[n]!=e[n]){o=!1;break}o||r&&a&&s&&!i&&this.cashierCartList()},"selectOrderData.oid":function(t){t&&this.getTableOrderInfo()},active:function(t){2==t&&this.getCodeList(),this.timer&&clearTimeout(this.timer)}},created:function(){this.getOrderList(),this.getErpConfig()},methods:{numberChange:function(t){var e=this;this.$nextTick((function(){var r=Number(t).toString(),a=r.indexOf(".");-1===a||2>=r.length-a-1?e.refundData.refund_price=Number(r):e.refundData.refund_price=Number(Number(r).toFixed(2))}))},jsToJava:function(){try{window.Jsbridge.invoke("openCacheBox",JSON.stringify({"p1-key":"p1-value"}),this.myFunction())}catch(t){}},myFunction:function(){},payOffline:function(){var t=this;Object(M["S"])(this.selectOrderData.oid).then((function(){t.$Message.success("支付成功"),t.selectOrderData.orderId.paid=1;var e=!0,r=!1,a=void 0;try{for(var s,i=t.orderListData[Symbol.iterator]();!(e=(s=i.next()).done);e=!0){var o=s.value;if(o.id==t.selectOrderData.id){o.orderId.paid=1;break}}}catch(n){r=!0,a=n}finally{try{e||null==i.return||i.return()}finally{if(r)throw a}}})).catch((function(e){t.$Message.error(e.msg)}))},getTableOrderInfo:function(){var t=this;Object(M["J"])(this.selectOrderData.oid).then((function(e){t.selectOrderData=F({},t.selectOrderData,{orderId:e.data,is_cashier_yue_pay_verify:e.data.is_cashier_yue_pay_verify}),t.refundData.order_id=e.data.order_id,t.refundData.refund_price=+e.data.pay_price}))},confirm:function(){if("yue"==this.payType){if(!this.createOrder.userCode&&this.selectOrderData.is_cashier_yue_pay_verify)return this.$Message.error("请扫描个人中心二维码");this.isOrderCreate?this.getCashierPay("yue"):this.orderCreate()}else if(""==this.payType){if(!this.createOrder.auth_code)return this.$Message.error("请扫描您的付款码");this.isOrderCreate?this.getCashierPay(""):this.orderCreate()}},getCashierPay:function(t){var e=this,r={payType:t,userCode:this.payNum,auth_code:this.payNum};if("cash"==t&&parseFloat(this.selectOrderData.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");Object(M["p"])(this.selectOrderData.orderId.order_id,r).then((function(r){if(e.payNum="","SUCCESS"==r.data.status){e.isOrderCreate=0,e.$Message.success("支付成功"),e.modalCash=!1,e.modalPay=!1,e.selectOrderData.orderId.paid=1;var a=!0,s=!1,i=void 0;try{for(var o,n=e.orderListData[Symbol.iterator]();!(a=(o=n.next()).done);a=!0){var c=o.value;if(c.id==e.selectOrderData.id){c.orderId.paid=1;break}}}catch(l){s=!0,i=l}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}"cash"==t&&e.jsToJava()}else if("PAY_ING"==r.data.status){var d=e.$Message.loading({content:"等待支付中...",duration:0});e.orderSystem.loadingMsg=d,e.orderId=r.data.order_id,e.checkOrderTime(d)}else e.isOrderCreate=1,e.orderId=r.data.order_id,e.$Message.error(r.data.message)})).catch((function(t){e.payNum="",e.$Message.error(t.msg)}))},orderCreate:function(){var t=this;if("cash"==this.payType&&parseFloat(this.selectOrderData.payPrice)>parseFloat(this.collection))return this.$Message.error("您付款金额不足");this.createOrder.new=1,this.createOrder.collate_code_id=this.table_id,this.createOrder.cart_id=this.cart_ids.split(","),Object(M["l"])(this.uid,this.createOrder).then((function(e){window.localStorage;if(t.payNum="","yue"==t.payType)if(t.modalPay=!1,t.payNum="",t.createOrder.userCode="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("SUCCESS"==e.data.status){t.isOrderCreate=0,t.$Message.success("支付成功"),t.payTypeModal=!1,t.modalPay=!1,t.selectOrderData=F({},t.selectOrderData,{oid:e.data.oid});var r=!0,a=!1,s=void 0;try{for(var i,o=t.orderListData[Symbol.iterator]();!(r=(i=o.next()).done);r=!0){var n=i.value;if(n.id==t.selectOrderData.id){n.status=2,n.oid=e.data.oid,"[object Object]"!=Object.prototype.toString.call(n.orderId)&&(n.orderId={}),n.orderId=F({},n.orderId,{paid:1});break}}}catch(b){a=!0,s=b}finally{try{r||null==o.return||o.return()}finally{if(a)throw s}}}else t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message);if("cash"==t.payType&&"SUCCESS"==e.data.status){t.$Message.success("支付成功"),t.selectOrderData=F({},t.selectOrderData,{oid:e.data.oid});var c=!0,d=!1,l=void 0;try{for(var u,f=t.orderListData[Symbol.iterator]();!(c=(u=f.next()).done);c=!0){var m=u.value;if(m.id==t.selectOrderData.id){m.status=2,m.oid=e.data.oid,"[object Object]"!=Object.prototype.toString.call(m.orderId)&&(m.orderId={}),m.orderId=F({},m.orderId,{paid:1});break}}}catch(b){d=!0,l=b}finally{try{c||null==f.return||f.return()}finally{if(d)throw l}}t.modalCash=!1,t.payTypeModal=!1,t.jsToJava()}if(""==t.payType)if(t.payNum="",t.createOrder.auth_code="","ORDER_CREATE"==e.data.status)t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.success(e.data.message);else if("PAY_ING"==e.data.status){var v=t.$Message.loading({content:"等待支付中...",duration:0});t.orderId=e.data.order_id,t.checkOrderTime(v)}else if("SUCCESS"==e.data.status){t.$Message.success("支付成功"),t.selectOrderData=F({},t.selectOrderData,{oid:e.data.oid});var h=!0,p=!1,_=void 0;try{for(var g,y=t.orderListData[Symbol.iterator]();!(h=(g=y.next()).done);h=!0){var I=g.value;if(I.id==t.selectOrderData.id){I.status=2,I.oid=e.data.oid,"[object Object]"!=Object.prototype.toString.call(I.orderId)&&(I.orderId={}),I.orderId=F({},I.orderId,{paid:1});break}}}catch(b){p=!0,_=b}finally{try{h||null==y.return||y.return()}finally{if(p)throw _}}t.modalPay=!1,t.clear()}else t.isOrderCreate=1,t.orderId=e.data.order_id,t.$Message.error(e.data.message)})).catch((function(e){t.payNum="",t.$Message.error(e.msg)}))},numTap:function(t){!1===this.defaultcalc&&(this.collection="",this.defaultcalc=!0);var e=String(this.collection).indexOf(".")+1,r=String(this.collection).length-e;(0===e||r<2)&&(this.collectionArray.join("")<=9999999&&this.collectionArray.push(t),this.collection=this.collectionArray.join("")>99999999?99999999:this.collectionArray.join(""))},getSwithUser:function(t){var e=this;Object(M["hb"])(t).then((function(t){})).catch((function(t){e.$Message.error(t.msg)}))},discountCon:function(){this.discount=!0},staffPlace:function(){var t=this;Object(M["gb"])({tableId:this.table_id}).then((function(t){})).catch((function(e){t.$Message.error(e.msg)}))},calculate:function(t){var e=this;Object(M["u"])({productId:t.product_id,cartNum:1,uniqueId:t.product_attr_unique||0,tableId:this.table_id,isAdd:t.isAdd}).then((function(t){e.getCartList()})).catch((function(t){e.$Message.error(t.msg)}))},delGoods:function(){this.getCartList()},cancelTable:function(){var t=this;this.$Modal.confirm({title:"取消该桌码订单",content:"<p>确定要取消该桌码订单吗？</p><p>取消该桌码订单后将无法恢复，请谨慎操作！</p>",onOk:function(){Object(M["b"])({tableId:t.selectOrderData.id,qrcode_id:t.selectOrderData.qrcode_id}).then((function(e){t.$Message.success("取消该桌码订单成功");for(var r=0;r<t.orderListData.length;r++)if(t.orderListData[r].id==t.selectOrderData.id){t.orderListData[r+1]?t.table_id=t.orderListData[r+1].id:t.orderListData[r-1]?t.table_id=t.orderListData[r-1].id:t.table_id=0,t.orderListData.splice(r,1);break}}))}})},cancelPrice:function(){},changePrice:function(){var t={table_id:this.table_id,uid:this.uid};this.$refs.changePrice.ordeTableUpdateInfo(t),this.$refs.changePrice.priceModals=!0,this.$refs.changePrice.tableInfo=t},submitSuccess:function(t){this.selectOrderData.payPrice=t,this.priceInfo.payPrice=t,this.createOrder.is_price=1,this.createOrder.change_price=t,this.getSwithUser({change_price:t}),this.cancelPrice()},cartCompute:function(){var t=this;if(this.cartList.length){var e=[];this.cartList.forEach((function(t){t.cart.forEach((function(t){e.push(t.id)}))})),this.createOrder.cart_id=e;var r={integral:this.integral,coupon:this.coupon,coupon_id:this.couponId,cart_id:e};Object(M["j"])(this.userInfo.uid,r).then((function(e){t.priceInfo=e.data,t.unchangedPrice=t.priceInfo.payPrice||0})).catch((function(e){t.$Message.error(e.msg),t.coupon=!1}))}else this.priceInfo={}},integralTap:function(){this.selectOrderData.uid?(this.integral=!this.integral,this.integral&&(this.createOrder.is_price=0)):this.$Message.warning("请先选择用户再使用积分")},delNum:function(){this.collectionArray.pop(),this.collection=this.collectionArray.length?this.collectionArray.join(""):0},cashBnt:function(){var t=this;this.cashBntLoading||(this.cashBntLoading=!0,this.isOrderCreate?this.getCashierPay("cash"):this.orderCreate(),setTimeout((function(){t.cashBntLoading=!1}),1e3))},cancel:function(){this.collection=0,this.collectionArray=[]},keyboard:function(){var t=this;function e(e){t.collectionArray.pop(),t.collection=t.collectionArray.length?t.collectionArray.join(""):0}function r(e){!1===t.defaultcalc&&(t.collection="",t.defaultcalc=!0);var r=String(t.collection).indexOf(".")+1,a=String(t.collection).length-r;(0===r||a<2)&&(t.collectionArray.join("")<=9999999&&t.collectionArray.push(e),t.collection=t.collectionArray.join("")>99999999?99999999:t.collectionArray.join(""))}document.onkeydown=function(a){var s=a||window.event,i=s.keyCode;switch(t.modalCash&&(a.stopPropagation(),a.preventDefault()),i){case 96:case 48:r(0);break;case 97:case 49:r(1);break;case 98:case 50:r(2);break;case 99:case 51:r(3);break;case 100:case 52:r(4);break;case 101:case 53:r(5);break;case 102:case 54:r(6);break;case 103:case 55:r(7);break;case 104:case 56:r(8);break;case 105:case 57:r(9);break;case 110:r(".");break;case 190:r(".");break;case 8:e();break}}},inputSaoMa:function(t){var e=this,r=t;if(""===r)return!1;clearTimeout(this.endTimeout),this.endTimeout=null,this.endTimeout=setTimeout((function(){e.codeNum===r&&(clearTimeout(e.endTimeout),r&&e.codeInfo({bar_code:r}))}),500)},modalPayCancel:function(){this.$Message.destroy(),this.orderSystem.timer&&(clearInterval(this.orderSystem.timer),this.orderSystem.timer=null)},codeInfo:function(t){var e=this;t.uid=this.uid||0,t.staff_id=this.storeInfos.id||0,Object(M["i"])(t).then((function(t){e.codeNum="";var r=t.data;if(r.hasOwnProperty("userInfo"))if(e.userInfo)e.$Modal.confirm({title:"切换用户",content:"<p>确定要切换用户吗？</p>",onOk:function(){e.userInfo=t.data.userInfo;var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(t.data.userInfo)),e.getCartList()},onCancel:function(){}});else{e.userInfo=t.data.userInfo;var a=window.localStorage;a.setItem("cashierUser",JSON.stringify(t.data.userInfo))}e.goodList(),e.getCartList()})).catch((function(t){e.codeNum="",e.$Message.error(t.msg)}))},payPrice:function(t){var e=this;if(this.payType=t,this.selectOrderData.oid&&(this.isOrderCreate=!0,this.selectOrderData.payPrice=Number(this.selectOrderData.pay_price)||0),""==t||"yue"==t)if(this.createOrder.userCode="",this.createOrder.auth_code="",this.payNum="",""==t||this.selectOrderData.is_cashier_yue_pay_verify){this.modalPay=!0;var r=this;this.$nextTick((function(){e.$refs.focusNum.focus(),document.onkeydown=function(t){13==t.which&&(r.payNum&&(r.createOrder.userCode=r.payNum,r.createOrder.auth_code=r.payNum,r.confirm()),r.codeNum&&r.codeInfo({bar_code:r.codeNum}))}}))}else this.confirm();else"cash"==t&&(this.modalCash=!0,this.selectOrderData.oid&&(this.isOrderCreate=!0,this.selectOrderData.payPrice=Number(this.selectOrderData.pay_price)||0),this.collection=this.selectOrderData.payPrice||0,this.keyboard());if(this.createOrder.integral=this.integral,this.createOrder.coupon=this.coupon,this.createOrder.coupon_id=this.couponId,this.coupon&&!this.couponId)return this.$Message.error("请选择有效优惠券");this.createOrder.pay_type=t,this.createOrder.staff_id=this.storeInfos.id},userChange:function(t){this.uid=t,this.selectOrderData=F({},this.selectOrderData,{uid:t})},cashierCompute:function(){var t=this;Object(M["j"])(this.uid,{new:1,coupon_id:0,coupon:!1,integral:this.integral,cart_id:this.cart_ids.split(",")}).then((function(e){t.selectOrderData=F({},t.selectOrderData,{},e.data),t.unchangedPrice=t.selectOrderData.payPrice}))},cashierCartList:function(){var t=this;Object(M["f"])(this.uid,this.staff_id,{new:1,cart_ids:this.cart_ids}).then((function(e){var r=e.data.valid,a=[];r.length&&(a=r),t.selectOrderData=F({},t.selectOrderData,{orderId:{_info:a}})}))},getCartList:function(){var t=this;Object(M["w"])({table_id:this.table_id,uid:this.uid}).then((function(e){t.cart_ids=e.msg}))},getTableUidAll:function(){var t=this;Object(M["K"])({table_id:this.table_id}).then((function(e){var r=e.data;r.length&&(t.uid=r[0].uid),t.selectOrderData=F({},t.selectOrderData,{userList:r,uid:t.uid})}))},getCodeList:function(){var t=this;Object(M["x"])().then((function(e){t.codeList=e.data.filter((function(t){return t.tableQrcode.length})),t.timer=setTimeout(t.getCodeList,6e4)}))},rechargeBnt:function(t){this.userInfo=t,this.$refs.recharge.modal=!0},getSuccess:function(t){var e=this.$computes.Add(this.userInfo.now_money,t);this.userInfo.now_money=e;var r=window.localStorage;r.setItem("cashierUser",JSON.stringify(this.userInfo))},storeTap:function(){this.$refs.store.modals=!0,this.$refs.store.cancel()},getUserInfo:function(t){this.staff_id=t.users.id,this.storeInfos=t.users,this.storeList=t.storeList,this.goodFrom.staff_id=t.users.id,sessionStorage.setItem("staffInfo",JSON.stringify(t.users))},getStoreId:function(t){var e=this;this.storeList.forEach((function(r){r.id==t.id&&(e.storeInfos=r,e.staff_id=r.id)}))},orderSend:function(){var t=this;this.$store.commit("store/order/setSplitOrder",this.selectOrderData.total_num),this.$refs.send.modals=!0,this.orderId=this.selectOrderData.id,this.status=this.selectOrderData._status,this.pay_type=this.selectOrderData.pay_type,this.$refs.send.getList(),this.$refs.send.getDeliveryList(),this.$nextTick((function(e){t.$refs.send.getCartInfo(t.selectOrderData._status,t.selectOrderData.id)}))},addPage:function(){this.orderListData.length<this.count&&this.orderData.page++,this.getOrderList()},searchList:function(t){var e="";switch(this.filterModal=!1,t.status){case 0:e=1;break;case 3:e=2;break;case 4:e=3;break}this.orderData=F({},this.orderData,{},t,{status:e}),this.orderData.type="",this.sle=0,this.search()},point:function(){var t=this;this.delfromData={title:"立即打印订单",info:"您确认打印此订单吗?",url:"/order/print/".concat(this.selectOrderData.oid),method:"get",ids:""},this.$modalSure(this.delfromData).then((function(e){t.$Message.success(e.msg)})).catch((function(e){t.$Message.error(e.msg)}))},search:function(){this.orderData.page=1,this.orderListData=[],this.getOrderList()},remarks:function(){this.$refs.remarks.modals=!0,this.$refs.remarks.formValidate.remark=this.selectOrderData.orderId.remark},send:function(){this.canSend=!1,this.search()},submitFail:function(){},getRefundData:function(){var t=this;2===this.selectOrderData.refund_type?(this.delfromData={title:"是否立即退货",url:"/refund/agree/".concat(this.selectOrderData.id),method:"get"},this.$modalSure(this.delfromData).then((function(e){t.$Message.success(e.msg)})).catch((function(e){t.$Message.error(e.msg)}))):this.refundVisible=!0},getOrderInfo:function(t){var e=this;t&&Object(M["B"])(t).then((function(t){e.orderInfoData=t.data})).catch((function(t){e.$Message.error(t.msg)}))},selectOrder:function(t){this.sle=0,this.table_id=t.id},tabClick:function(t){switch(t){case 1:this.getOrderInfo(this.selectOrderData.oid)}this.sle=t},getOrderList:function(){var t=this;Object(M["I"])(this.orderData).then((function(e){t.count=e.data.count;var r=e.data.data.map((function(t){var e=t.qrcode,r=e.category,a=e.table_number,s=[];return Array.isArray(t.cartList)||Object.keys(t.cartList).forEach((function(e){s.push({cart_info:t.cartList[e]})})),t.color="#f5222d",t.pink_name="".concat(r.name).concat(a),t._infoData=s,t.pay_price=t.sum_price,t.total_num=t.cart_num,t}));t.orderListData=[].concat(L(t.orderListData),L(r))})).catch((function(e){t.$Message.error(e.msg)}))},getErpConfig:function(){var t=this;Object(M["v"])().then((function(e){t.open_erp=e.data.open_erp,t.tengxun_map_key=e.data.tengxun_map_key})).catch((function(e){t.$Message.error(e.msg)}))},orderRefund:function(){var t=this;Object(M["P"])(this.selectOrderData.oid,this.refundData).then((function(e){t.refundVisible=!1,t.$Message.success(e.msg);var r=t.orderListData.findIndex((function(e){return e.id==t.table_id}));t.orderListData[r].orderId.refund_status=2,t.selectOrderData=t.orderListData[r]})).catch((function(e){t.$Message.error(e.msg)}))}}},z=G,Q=(r("ae8b"),Object(g["a"])(z,a,s,!1,null,"83f72efe",null));e["default"]=Q.exports},"29ba":function(t,e){t.exports="data:image/png;base64,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"},"3a44":function(t,e,r){"use strict";var a=r("a32d"),s=r.n(a);s.a},7437:function(t,e,r){},a123:function(t,e,r){"use strict";var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Modal",{staticClass:"order_box",attrs:{scrollable:"",title:"订单发送货",closable:!1,width:"1000"},on:{"on-visible-change":t.changeModal},model:{value:t.modals,callback:function(e){t.modals=e},expression:"modals"}},[t.modals?r("Form",{ref:"formItem",attrs:{model:t.formItem,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[r("FormItem",{attrs:{label:"选择类型："}},[r("RadioGroup",{on:{"on-change":t.changeRadio},model:{value:t.formItem.type,callback:function(e){t.$set(t.formItem,"type",e)},expression:"formItem.type"}},[t.productType?t._e():r("Radio",{attrs:{label:"1"}},[t._v("发货")]),t.productType?t._e():r("Radio",{attrs:{label:"2"}},[t._v("送货")]),r("Radio",{attrs:{label:"3"}},[t._v("无需配送")])],1)],1),1==t.formItem.type?r("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.export_open,expression:"export_open"}],attrs:{label:"发货类型："}},[r("RadioGroup",{on:{"on-change":t.changeExpress},model:{value:t.formItem.express_record_type,callback:function(e){t.$set(t.formItem,"express_record_type",e)},expression:"formItem.express_record_type"}},[r("Radio",{attrs:{label:"1"}},[t._v("手动填写")]),r("Radio",{attrs:{label:"2"}},[t._v("电子面单打印")])],1)],1):t._e(),r("div",[1==t.formItem.type?r("FormItem",{attrs:{label:"快递公司："}},[r("Select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择快递公司"},on:{"on-change":t.expressChange},model:{value:t.formItem.delivery_name,callback:function(e){t.$set(t.formItem,"delivery_name",e)},expression:"formItem.delivery_name"}},t._l(t.express,(function(e,a){return r("Option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.value)+"\n          ")])})),1)],1):t._e(),"1"===t.formItem.express_record_type&&1==t.formItem.type?r("FormItem",{attrs:{label:"快递单号："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:t.formItem.delivery_id,callback:function(e){t.$set(t.formItem,"delivery_id",e)},expression:"formItem.delivery_id"}}),"顺丰速运"==t.formItem.delivery_name?r("div",{staticClass:"trips"},[r("p",[t._v("顺丰请输入单号 :收件人或寄件人手机号后四位，")]),r("p",[t._v("例如：SF000000000000:3941")])]):t._e()],1):t._e(),"2"===t.formItem.express_record_type?[r("FormItem",{staticClass:"express_temp_id",attrs:{label:"电子面单："}},[r("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择电子面单"},on:{"on-change":t.expressTempChange},model:{value:t.formItem.express_temp_id,callback:function(e){t.$set(t.formItem,"express_temp_id",e)},expression:"formItem.express_temp_id"}},t._l(t.expressTemp,(function(e,a){return r("Option",{key:a,attrs:{value:e.temp_id}},[t._v(t._s(e.title)+"\n            ")])})),1),t.formItem.express_temp_id?r("Button",{attrs:{type:"text"},on:{click:t.preview}},[t._v("预览")]):t._e()],1),r("FormItem",{attrs:{label:"寄件人姓名："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:t.formItem.to_name,callback:function(e){t.$set(t.formItem,"to_name",e)},expression:"formItem.to_name"}})],1),r("FormItem",{attrs:{label:"寄件人电话："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:t.formItem.to_tel,callback:function(e){t.$set(t.formItem,"to_tel",e)},expression:"formItem.to_tel"}})],1),r("FormItem",{attrs:{label:"寄件人地址："}},[r("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:t.formItem.to_addr,callback:function(e){t.$set(t.formItem,"to_addr",e)},expression:"formItem.to_addr"}})],1)]:t._e()],2),r("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.formItem.type,expression:"formItem.type === '2'"}]},[r("FormItem",{attrs:{label:"送货人："}},[r("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择送货人"},on:{"on-change":t.shDeliveryChange},model:{value:t.formItem.sh_delivery,callback:function(e){t.$set(t.formItem,"sh_delivery",e)},expression:"formItem.sh_delivery"}},t._l(t.deliveryList,(function(e,a){return r("Option",{key:a,attrs:{value:e.id}},[t._v("\n            "+t._s(e.wx_name)+"（"+t._s(e.phone)+"）\n          ")])})),1)],1)],1),r("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.formItem.type,expression:"formItem.type === '3'"}]},[r("FormItem",{attrs:{label:"备注："}},[r("Input",{staticStyle:{width:"80%"},attrs:{type:"textarea",autosize:{minRows:2,maxRows:5},placeholder:"备注"},model:{value:t.formItem.fictitious_content,callback:function(e){t.$set(t.formItem,"fictitious_content",e)},expression:"formItem.fictitious_content"}})],1)],1),t.splitOrder>1&&"3"!==t.formItem.type?r("div",[r("FormItem",{attrs:{label:"分单发货："}},[r("i-switch",{attrs:{size:"large",disabled:8===t.orderStatus},on:{"on-change":t.changeSplitStatus},model:{value:t.splitSwitch,callback:function(e){t.splitSwitch=e},expression:"splitSwitch"}},[r("span",{attrs:{slot:"open"},slot:"open"},[t._v("开启")]),r("span",{attrs:{slot:"close"},slot:"close"},[t._v("关闭")])]),r("div",{staticClass:"trips"},[r("p",[t._v("\n            可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n          ")])]),t.splitSwitch&&t.manyFormValidate.length?r("Table",{attrs:{data:t.manyFormValidate,columns:t.header},on:{"on-selection-change":t.selectOne},scopedSlots:t._u([{key:"image",fn:function(e){var a=e.row;e.index;return[r("div",{staticClass:"product-data"},[r("img",{staticClass:"image",attrs:{src:a.cart_info.productInfo.image}}),r("div",{staticClass:"name line2"},[t._v("\n                "+t._s(a.cart_info.productInfo.store_name)+"\n              ")])])]}},{key:"value",fn:function(e){var a=e.row;e.index;return[r("div",[t._v(t._s(a.cart_info.productInfo.attrInfo.suk))])]}},{key:"sellPrice",fn:function(e){var a=e.row;e.index;return[r("div",[t._v(t._s(a.cart_info.productInfo.attrInfo?a.cart_info.productInfo.attrInfo.price:a.cart_info.productInfo.price)+"\n            ")])]}},{key:"price",fn:function(e){var a=e.row;e.index;return[r("div",[t._v(t._s(a.cart_info.truePrice))])]}}],null,!1,409803649)}):t._e()],1)],1):t._e()],1):t._e(),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("Button",{on:{click:t.cancel}},[t._v("取消")]),r("Button",{attrs:{type:"primary"},on:{click:t.putSend}},[t._v("提交")])],1),r("div",{directives:[{name:"viewer",rawName:"v-viewer"},{name:"show",rawName:"v-show",value:t.temp,expression:"temp"}],ref:"viewer"},[r("img",{staticStyle:{display:"none"},attrs:{src:t.temp.pic}})])],1)},s=[],i=r("a34a"),o=r.n(i),n=r("2f62"),c=r("f8b7");function d(t,e,r,a,s,i,o){try{var n=t[i](o),c=n.value}catch(d){return void r(d)}n.done?e(c):Promise.resolve(c).then(a,s)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(a,s){var i=t.apply(e,r);function o(t){d(i,a,s,o,n,"next",t)}function n(t){d(i,a,s,o,n,"throw",t)}o(void 0)}))}}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(r,!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var v={name:"orderSend",props:{orderId:Number,status:Number,pay_type:String},data:function(){var t=this;return{productType:0,orderStatus:0,splitSwitch:!0,formItem:{type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""},modals:!1,express:[],expressTemp:[],deliveryList:[],temp:{},export_open:!0,manyFormValidate:[],header:[{type:"selection",width:60,align:"center"},{title:"商品信息",slot:"image",align:"center",width:200},{title:"规格",slot:"value",align:"center",minWidth:120},{title:"商品售价",slot:"sellPrice",align:"center",minWidth:100},{title:"商品优惠价",slot:"price",align:"center",minWidth:100},{title:"总数",key:"cart_num",align:"center",minWidth:80},{title:"待发数量",key:"surplus_num",align:"center",width:180,render:function(e,r){return e("div",[e("InputNumber",{props:{min:1,max:r.row.numShow,value:r.row.surplus_num||1},on:{"on-change":function(e){r.row.surplus_num=e||1,t.manyFormValidate[r.index]=r.row,t.selectData.forEach((function(e,a){e.cart_id===r.row.cart_id&&t.selectData.splice(a,1,r.row)}))}}})])}}],selectData:[]}},computed:f({},Object(n["e"])("store/order",["splitOrder"])),methods:{selectOne:function(t){this.selectData=t},changeModal:function(t){t||this.cancel()},changeSplitStatus:function(t){var e=this;t&&Object(c["fb"])(this.orderId).then((function(t){var r=t.data;r.forEach((function(t){t.numShow=t.surplus_num})),e.manyFormValidate=r}))},changeRadio:function(t){switch(this.$refs.formItem.resetFields(),t){case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.formItem.express_temp_id="",this.formItem.express_record_type="1",this.expressTemp=[];break;case"2":this.formItem.sh_delivery="";break;case"3":this.formItem.fictitious_content="";break;default:break}},changeExpress:function(t){switch(t){case"2":this.formItem.delivery_name="",this.formItem.express_temp_id="",this.expressTemp=[],this.getList(2);break;case"1":this.formItem.delivery_name="",this.formItem.delivery_id="",this.getList(1);break;default:break}},reset:function(){this.formItem={type:"1",express_record_type:"1",delivery_name:"",delivery_id:"",express_temp_id:"",expressTemp:[],to_name:"",to_tel:"",to_addr:"",sh_delivery:"",fictitious_content:""}},getList:function(t){var e=this,r=2===t?1:"";Object(c["y"])(r).then(function(){var t=l(o.a.mark((function t(r){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.express=r.data,e.getSheetInfo();case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},putSend:function(t){var e=this,r={id:this.orderId,datas:this.formItem};if("1"===this.formItem.type&&"2"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.express_temp_id)return this.$Message.error("电子面单不能为空");if(""===this.formItem.to_name)return this.$Message.error("寄件人姓名不能为空");if(""===this.formItem.to_tel)return this.$Message.error("寄件人电话不能为空");if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.formItem.to_tel))return this.$Message.error("请输入正确的手机号码");if(""===this.formItem.to_addr)return this.$Message.error("寄件人地址不能为空")}if("1"===this.formItem.type&&"1"===this.formItem.express_record_type){if(""===this.formItem.delivery_name)return this.$Message.error("快递公司不能为空");if(""===this.formItem.delivery_id)return this.$Message.error("快递单号不能为空")}if("2"===this.formItem.type&&""===this.formItem.sh_delivery)return this.$Message.error("送货人不能为空");this.splitSwitch&&(r.datas.cart_ids=[],this.selectData.forEach((function(t){r.datas.cart_ids.push({cart_id:t.cart_id,cart_num:t.surplus_num})}))),Object(c["Z"])(r).then((function(t){e.modals=!1,e.$Message.success(t.msg),e.$emit("submitFail"),e.reset(),e.splitSwitch=!1})).catch((function(t){e.$Message.error(t.msg)}))},cancel:function(t){this.modals=!1,this.orderStatus=0,this.splitSwitch=!1,this.selectData=[],this.reset()},expressChange:function(t){var e=this,r=this.express.find((function(e){return e.value===t}));void 0!==r&&(this.formItem.delivery_code=r.code,"2"===this.formItem.express_record_type&&(this.expressTemp=[],this.formItem.express_temp_id="",Object(c["O"])({com:this.formItem.delivery_code}).then((function(t){e.expressTemp=t.data,t.data.length||e.$Message.error("请配置你所选快递公司的电子面单")})).catch((function(t){e.$Message.error(t.msg)}))))},getCartInfo:function(t,e){var r=this;this.$set(this,"orderStatus",t),this.$set(this,"splitSwitch",8===t),Object(c["fb"])(this.orderId).then((function(t){var e=t.data;e.forEach((function(t){t.numShow=t.surplus_num})),r.manyFormValidate=e,r.productType=e[0].product_type,3==r.productType&&(r.formItem.type="3",r.formItem.fictitious_content="")}))},getDeliveryList:function(){var t=this;Object(c["N"])().then((function(e){t.deliveryList=e.data.list})).catch((function(e){t.$Message.error(e.msg)}))},getSheetInfo:function(){var t=this;Object(c["Q"])().then((function(e){var r=e.data;for(var a in r)r.hasOwnProperty(a)&&(t.formItem[a]=r[a]);t.export_open=void 0===r.export_open||r.export_open,t.export_open||(t.formItem.express_record_type="1"),t.formItem.to_addr=r.to_add})).catch((function(e){t.$Message.error(e.msg)}))},shDeliveryChange:function(t){if(t){var e=this.deliveryList.find((function(e){return e.id===t}));this.formItem.sh_delivery_name=e.wx_name,this.formItem.sh_delivery_id=e.phone,this.formItem.sh_delivery_uid=e.uid}},expressTempChange:function(t){this.temp=this.expressTemp.find((function(e){return t===e.temp_id})),void 0===this.temp&&(this.temp={})},preview:function(){this.$refs.viewer.$viewer.show()}}},h=v,p=(r("1dd0"),r("2877")),_=Object(p["a"])(h,a,s,!1,null,"cd104c5a",null);e["a"]=_.exports},a32d:function(t,e,r){},ae8b:function(t,e,r){"use strict";var a=r("072c"),s=r.n(a);s.a},f830:function(t,e,r){"use strict";var a=r("01ff"),s=r.n(a);s.a}}]);