(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-71da9a83"],{"08f0":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"order"},[s("div",{staticClass:"left"},[e._m(0),s("div",{staticClass:"order-box"},[s("div",{staticClass:"search"},[s("Input",{attrs:{search:"","enter-button":"搜索",placeholder:"搜索手机号/ID",size:"large"},on:{"on-search":e.search},model:{value:e.userFrom.keyword,callback:function(t){e.$set(e.userFrom,"keyword",t)},expression:"userFrom.keyword"}})],1),e.userList.length?s("userList",{attrs:{userList:e.userList,total:e.total},on:{selectUser:e.selectUser,addPage:e.addPage}}):s("div",{staticClass:"no-order"},[s("img",{attrs:{src:a("66c9"),alt:""}}),s("span",{staticClass:"trip"},[e._v("噢～目前暂无用户")])])],1)]),e.total?s("div",{staticClass:"order-data"},[s("div",{staticClass:"header"},[e._l(e.tabs,(function(t,a){return s("div",{directives:[{name:"show",rawName:"v-show",value:!a||!e.selectUserData.delete_time,expression:"!index || !selectUserData.delete_time"}],key:a,staticClass:"item",class:{sel:e.sle==a,"neighbor-left":e.sle-a==1,"neighbor-right":a-e.sle==1},on:{click:function(t){return e.selectTab(a)}}},[s("div",{staticClass:"item-wrap"},[e._v("\n          "+e._s(t)+"\n        ")])])})),s("div",{staticClass:"box",class:{"neighbor-right":e.selectUserData.delete_time||2==e.sle}})],2),s("div",{staticClass:"content",class:{"border-radius":e.sle}},[0===e.sle?s("div",{staticClass:"detail"},[s("div",{staticClass:"user"},[s("div",[s("img",{staticClass:"img",attrs:{src:e.selectUserInfo.avatar,alt:""}})]),s("div",{staticClass:"user-content"},[s("div",{staticClass:"name-wrap"},[s("div",{staticClass:"name"},[e._v(e._s(e.selectUserInfo.nickname))]),e.selectUserInfo.vip_name?s("div",{staticClass:"tag"},[e._v("\n                "+e._s(e.selectUserInfo.vip_name)+"\n              ")]):e._e(),e.selectUserData.delete_time?s("div",[e._v("（已注销）")]):e._e()]),s("div",{staticClass:"phone-wrap"},[e.selectUserInfo.phone?s("div",{staticClass:"phone"},[e._v("\n                "+e._s(e.selectUserInfo.phone)+"\n              ")]):e._e(),s("div",[e._v("\n                余额"),s("span",{staticClass:"num"},[e._v(e._s(e.selectUserInfo.now_money))])]),s("div",[e._v("\n                积分"),s("span",{staticClass:"num"},[e._v(e._s(e.selectUserInfo.integral))])]),e.selectUserInfo.is_money_level?s("div",[e._v("\n                付费会员到期：\n                "),s("span",{staticClass:"time"},[e._v(e._s(e.selectUserInfo.is_ever_level?"永久会员":e.selectUserInfo.overdue_time||"已过期"))])]):e._e()])])]),s("Tabs",{attrs:{animated:!1},model:{value:e.tabActive,callback:function(t){e.tabActive=t},expression:"tabActive"}},e._l(e.menus,(function(t){return s("TabPane",{key:t.value,attrs:{label:t.name,name:t.value,"data-name":t.component}},[s(t.component,{tag:"component",attrs:{userInfo:e.selectUserInfo,type:t.value}})],1)})),1)],1):e._e(),1===e.sle?s("div",{staticClass:"member"},[s("Form",{attrs:{model:e.rechargeData,"label-width":82}},[s("FormItem",{attrs:{label:"会员时长："}},[e.memberCheck.overdue_time?[s("span",{staticClass:"num"},[e._v(e._s(e.memberCheck.vip_day))]),e._v("天（"+e._s(e.memberCheck.overdue_time)+" 到期）\n            ")]:s("span",{staticClass:"num"},[e._v("永久")])],2),s("FormItem",{staticClass:"merber-radio-group",attrs:{"label-width":0}},[s("RadioGroup",{model:{value:e.rechargeData.merber_id,callback:function(t){e.$set(e.rechargeData,"merber_id",t)},expression:"rechargeData.merber_id"}},e._l(e.memberCard,(function(t){return s("Radio",{key:t.id,attrs:{label:t.id,border:""}},[s("div",[e._v(e._s(t.title))]),s("div",[e._v("\n                  ¥"),s("span",{staticClass:"num"},[e._v(e._s(t.pre_price))])]),s("div",{staticClass:"marking"},[e._v("¥"+e._s(t.price))])])})),1)],1)],1)],1):e._e(),2===e.sle?s("div",{staticClass:"balance"},[s("Form",{attrs:{model:e.rechargeData,"label-width":82}},[s("FormItem",{attrs:{label:"充值方式："}},[s("RadioGroup",{attrs:{type:"button","button-style":"solid"},model:{value:e.rechargeType,callback:function(t){e.rechargeType=t},expression:"rechargeType"}},[s("Radio",{attrs:{label:1}},[e._v("充值套餐")]),s("Radio",{attrs:{label:2}},[e._v("自定义充值")])],1)],1),s("FormItem",{directives:[{name:"show",rawName:"v-show",value:1==e.rechargeType,expression:"rechargeType == 1"}],staticClass:"merber-radio-group",attrs:{"label-width":0}},[s("RadioGroup",{model:{value:e.rechargeData.rechar_id,callback:function(t){e.$set(e.rechargeData,"rechar_id",t)},expression:"rechargeData.rechar_id"}},e._l(e.rechargeList,(function(t){return s("Radio",{key:t.id,attrs:{label:t.id,border:""}},[s("div",{staticClass:"money"},[e._v("\n                  ¥"),s("span",{staticClass:"num"},[e._v(e._s(t.price))])]),s("div",{staticClass:"marking"},[e._v("额外赠送：¥ "+e._s(t.give_money))])])})),1)],1),s("FormItem",{directives:[{name:"show",rawName:"v-show",value:2==e.rechargeType,expression:"rechargeType == 2"}]},[s("InputNumber",{attrs:{min:1,max:9999999,placeholder:"0.00"},model:{value:e.payPrice,callback:function(t){e.payPrice=t},expression:"payPrice"}})],1)],1)],1):e._e()]),e.sle?s("div",{staticClass:"footer"},[s("Button",{on:{click:e.recharge}},[e._v("点击支付")])],1):e._e()]):e._e(),s("Modal",{staticClass:"modalPay",attrs:{"footer-hide":"",width:"450px"},on:{"on-cancel":e.yuePayClear},model:{value:e.modalPay,callback:function(t){e.modalPay=t},expression:"modalPay"}},[s("div",{staticClass:"payPage"},[s("div",{staticClass:"header acea-row row-center-wrapper"},[s("div",{staticClass:"pictrue"},[s("img",{attrs:{src:a("ad18")}})]),s("div",{staticClass:"text"},[e._v("应收金额(元)")])]),s("div",{staticClass:"money"},[e._v("\n        ¥"),s("span",{staticClass:"num"},[e._v(e._s(e.rechargeData.price))])]),s("Input",{ref:"rechargeNum",staticStyle:{"margin-top":"16px"},attrs:{size:"large",type:"url",placeholder:"请点击输入框聚焦扫码或输入编码号"},model:{value:e.rechargeData.auth_code,callback:function(t){e.$set(e.rechargeData,"auth_code",t)},expression:"rechargeData.auth_code"}}),s("div",{staticClass:"process"},[s("div",{staticClass:"pictrue"},[s("img",{attrs:{src:a("1a3d")}})]),s("div",{staticClass:"list acea-row row-between-wrapper"},[s("div",{staticClass:"item one"},[s("div",{staticClass:"name"},[e._v("出示付款码")]),s("div",[e._v("支付宝/微信")])]),s("div",{staticClass:"item two"},[s("div",{staticClass:"name"},[e._v("扫描付款码")]),s("div",[e._v("扫码枪")])]),s("div",{staticClass:"item three"},[s("div",{staticClass:"name"},[e._v("确认收款")]),s("div",[e._v("收银台点击确认")])])])]),s("Button",{staticClass:"bnt",attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确认")])],1)]),s("Modal",{staticClass:"cash",attrs:{"footer-hide":"",width:"770px"},on:{"on-cancel":e.cancel},model:{value:e.modalCash,callback:function(t){e.modalCash=t},expression:"modalCash"}},[s("div",{staticClass:"cashPage acea-row"},[s("div",{staticClass:"left"},[s("div",{staticClass:"picture"},[s("img",{attrs:{src:a("ad18")}})]),s("div",{staticClass:"text"},[e._v("应收金额(元)")]),s("div",{staticClass:"money"},[e._v("\n          ¥"),s("span",{staticClass:"num"},[e._v(e._s(e.rechargeData.price||0))])])]),s("div",{staticClass:"right"},[s("div",{staticClass:"rightCon"},[s("div",{staticClass:"top acea-row row-between-wrapper"},[s("div",[e._v("实际收款(元)")]),s("div",{staticClass:"num"},[e._v(e._s(e.collection))])]),s("div",{staticClass:"center acea-row row-between-wrapper"},[s("div",[e._v("需找零(元)")]),e.$computes.Sub(e.collection,e.rechargeData.price||0)>0?s("div",{staticClass:"num"},[e._v("\n              "+e._s(e.$computes.Sub(e.collection,e.rechargeData.price||0))+"\n            ")]):s("div",{staticClass:"num"},[e._v("0")])]),s("div",{staticClass:"bottom acea-row"},[e._l(e.numList,(function(t,a){return s("div",{key:a,staticClass:"item acea-row row-center-wrapper",class:"."==t?"spot":"",on:{click:function(a){return e.numTap(t)}}},[e._v("\n              "+e._s(t)+"\n            ")])})),s("div",{staticClass:"item acea-row row-center-wrapper",on:{click:e.delNum}},[s("Icon",{attrs:{type:"ios-backspace"}})],1)],2)]),s("Button",{attrs:{type:"primary"},on:{click:e.cashBnt}},[e._v("确认")])],1)])]),s("settleDrawer",{attrs:{list:e.payList,type:e.payType,money:e.rechargeData.price,collection:e.collection},on:{payPrice:e.onPayPrice,numTap:e.numTap,delNum:e.delNum,cashBnt:e.cashBnt},model:{value:e.settleVisible,callback:function(t){e.settleVisible=t},expression:"settleVisible"}})],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"left-top"},[a("div",{staticClass:"title"},[e._v("用户列表")])])}],r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"user-info"},[a("div",{staticClass:"section"},[a("div",{staticClass:"section-head"},[e._v("用户信息")]),a("div",{staticClass:"section-body"},[a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户ID：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.uid))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("真实姓名：")]),a("Input",{staticClass:"value p-0",attrs:{placeholder:"请输入真实姓名"},model:{value:e.userInfo.real_name,callback:function(t){e.$set(e.userInfo,"real_name",t)},expression:"userInfo.real_name"}})],1),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("手机号码：")]),a("Input",{staticClass:"value p-0",attrs:{placeholder:"请输入手机号码"},model:{value:e.userInfo.phone,callback:function(t){e.$set(e.userInfo,"phone",t)},expression:"userInfo.phone"}})],1),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("生日：")]),a("DatePicker",{staticClass:"value p-0",attrs:{transfer:!0,type:"date",placeholder:"选择生日"},on:{"on-change":e.dataTap},model:{value:e.userInfo.birthday,callback:function(t){e.$set(e.userInfo,"birthday",t)},expression:"userInfo.birthday"}})],1),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("性别：")]),a("Select",{staticClass:"value p-0",attrs:{transfer:!0,placeholder:"请选择",clearable:""},model:{value:e.userInfo.sex,callback:function(t){e.$set(e.userInfo,"sex",t)},expression:"userInfo.sex"}},e._l(e.sexList,(function(t,s){return a("Option",{attrs:{value:t.value}},[e._v(e._s(t.label))])})),1)],1),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("身份证号：")]),a("Input",{staticClass:"value p-0",attrs:{placeholder:"请输入身份证号"},model:{value:e.userInfo.card_id,callback:function(t){e.$set(e.userInfo,"card_id",t)},expression:"userInfo.card_id"}})],1),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户地址：")]),a("Input",{staticClass:"value p-0",attrs:{placeholder:"请输入用户地址"},model:{value:e.userInfo.addres,callback:function(t){e.$set(e.userInfo,"addres",t)},expression:"userInfo.addres"}})],1)])]),a("div",{staticClass:"section"},[a("div",{staticClass:"section-head"},[e._v("用户概况")]),a("div",{staticClass:"section-body"},[a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("推广资格：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.spread_open?"启用":"禁用"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户状态：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.status?"开启":"锁定"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户等级：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.vip_name||"-"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户标签：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.label_list||"-"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("用户分组：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.group?e.userInfo.group.group_name:"无"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("推广人：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v(e._s(e.userInfo.spread_uid_nickname||"无"))])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("注册时间：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v("\n            "+e._s(e.$moment(1e3*e.userInfo.add_time).format("YYYY-MM-DD H:mm:ss"))+"\n          ")])]),a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("登录时间：")]),a("div",{staticClass:"value bg-w111-F9F9F9"},[e._v("\n            "+e._s(e.$moment(1e3*e.userInfo.last_time).format("YYYY-MM-DD H:mm:ss"))+"\n          ")])])])]),a("div",{staticClass:"section"},[a("div",{staticClass:"section-head"},[e._v("用户备注")]),a("div",{staticClass:"section-body"},[a("div",{staticClass:"item"},[a("div",{staticClass:"name"},[e._v("备注：")]),a("Input",{staticClass:"value p-0",attrs:{placeholder:"请输入备注信息"},model:{value:e.userInfo.mark,callback:function(t){e.$set(e.userInfo,"mark",t)},expression:"userInfo.mark"}})],1)])]),a("div",{staticClass:"w-120 h-44 rd-30px bg-w111-1890FF text-wlll-FFFFFF fs-18 acea-row row-center-wrapper auto pointer",on:{click:e.userUpdate}},[e._v("保存")])])},n=[],c=a("c24f"),o={name:"userInfo",props:{userInfo:{type:Object,default:function(){return{}}}},data:function(){return{sexList:[{value:1,label:"男"},{value:2,label:"女"},{value:3,label:"保密"}]}},mounted:function(){},methods:{userUpdate:function(){var e=this,t=this.userInfo,a={real_name:t.real_name,phone:t.phone,birthday:t.birthday,sex:t.sex,card_id:t.card_id,addres:t.addres,mark:t.mark};if(t.phone&&!/^1(3|4|5|7|8|9|6)\d{9}$/.test(t.phone))return this.$Message.error("请输入正确的手机号");Object(c["l"])(t.uid,a).then((function(t){e.$Message.success(t.msg)})).catch((function(t){e.$Message.error(t.msg)}))},dataTap:function(e){this.userInfo.birthday=e}}},l=o,u=(a("b594"),a("2877")),d=Object(u["a"])(l,r,n,!1,null,"95fd5f5c",null),h=d.exports,m=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"table-wrapper"},[a("Table",{attrs:{columns:e.$data[e.type].columns,data:e.$data[e.type].data},scopedSlots:e._u([{key:"coupon_price",fn:function(t){var s=t.row;return[1==s.coupon_type?a("span",[e._v(e._s(s.coupon_price)+"元")]):e._e(),2==s.coupon_type?a("span",[e._v(e._s(parseFloat(s.coupon_price)/10)+"折（"+e._s(s.coupon_price.toString().split(".")[0])+"%）")]):e._e()]}},{key:"product",fn:function(t){var s=t.row;return[a("div",{staticClass:"product"},[a("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"image"},[a("img",{directives:[{name:"lazy",rawName:"v-lazy",value:s.image,expression:"row.image"}]})]),a("div",{staticClass:"title"},[e._v(e._s(s.store_name))])])]}}])})],1),a("div",{staticClass:"acea-row row-right page"},[a("Page",{attrs:{total:e.$data[e.type].total,current:e.$data[e.type].page,"page-size":e.limit,"show-total":""},on:{"on-change":e.pageChange}})],1)])},p=[],v={props:{userInfo:{type:Object,default:function(){return{}}},type:{type:String,default:""}},data:function(){return{order:{columns:[{title:"订单ID",key:"order_id",minWidth:160},{title:"收货人",key:"real_name",minWidth:100},{title:"商品数量",key:"total_num",minWidth:90},{title:"商品总价",key:"total_price",minWidth:110},{title:"实付金额",key:"pay_price",minWidth:120},{title:"交易完成时间",key:"pay_time",minWidth:120}],data:[],total:0,page:1},integral:{columns:[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"number",minWidth:120},{title:"变化后积分",key:"balance",minWidth:120},{title:"日期",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],data:[],total:0,page:1},coupon:{columns:[{title:"优惠券名称",key:"coupon_title",minWidth:120},{title:"面值",slot:"coupon_price",minWidth:120},{title:"有效期(天)",key:"coupon_time",minWidth:120},{title:"兑换时间",key:"_add_time",minWidth:120}],data:[],total:0,page:1},balance_change:{columns:[{title:"动作",key:"title",minWidth:120},{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}],data:[],total:0,page:1},visit:{columns:[{title:"商品信息",slot:"product",width:500},{title:"价格",key:"product_price",render:function(e,t){return e("div","¥".concat(t.row.product_price))}},{title:"浏览时间",key:"add_time"}],data:[],total:0,page:1},spread_change:{columns:[{title:"推荐人ID",key:"spread_uid",minWidth:120},{title:"推荐人",key:"nickname",minWidth:120,render:function(e,t){return e("div",[e("img",{style:{borderRadius:"50%",marginRight:"10px",verticalAlign:"middle"},attrs:{with:38,height:38},directives:[{name:"lazy",value:t.row.avatar},{name:"viewer"}]}),e("span",{style:{verticalAlign:"middle"}},t.row.nickname)])}},{title:"变更方式",key:"type",minWidth:120},{title:"变更时间",key:"spread_time",minWidth:120}],data:[],total:0,page:1},limit:10}},watch:{userInfo:{handler:function(e){var t=this;e.uid&&(Object.keys(this.$data).forEach((function(e){t[e].page&&(t[e].page=1)})),this.getUserOneInfo())},immediate:!0}},methods:{getUserOneInfo:function(){var e=this;Object(c["g"])(this.userInfo.uid,{type:this.type,page:this[this.type].page,limit:this.limit}).then((function(t){var a=t.data,s=a.count,i=a.list;e[e.type].data=i,e[e.type].total=s}))},pageChange:function(e){this[this.type].page=e,this.getUserOneInfo()}}},f=v,_=(a("8754"),Object(u["a"])(f,m,p,!1,null,"b3454ae8",null)),g=_.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pending-user",on:{scroll:e.userListApi}},e._l(e.userList,(function(t,s){return a("div",{key:s,staticClass:"list",class:e.selIndex===s?"bor":"",on:{click:function(a){return e.selectUser(t,s)}}},[a("div",{staticClass:"item"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.avatar,alt:"头像"}})]),a("div",{staticClass:"item-right"},[a("div",{staticClass:"user"},[a("div",{staticClass:"inner"},[a("div",{staticClass:"name"},[e._v(e._s(t.nickname||"游客"))]),a("div",[e._v("（ID:"+e._s(t.uid)+"）")]),a("div",{staticClass:"phone"},[e._v("手机号："+e._s(t.phone||"暂无"))])])]),a("div",{staticClass:"money"},[a("div",[e._v("\n            积分："),a("span",{staticClass:"num"},[e._v(e._s(t.integral))])]),a("div",{staticClass:"now-money"},[e._v("\n            余额："),a("span",{staticClass:"num"},[e._v(e._s(t.now_money))])])])])])])})),0)},b=[],C={name:"orderList",props:{userList:{type:Array},total:{type:Number}},data:function(){return{selIndex:0}},methods:{userListApi:function(e){(!e||e.target.scrollHeight-e.target.scrollTop-e.target.clientHeight<=0&&this.userList.length<this.total)&&this.$emit("addPage")},selectUser:function(e,t){this.selIndex=t,this.$emit("selectUser",e)}}},k=C,w=(a("e698"),Object(u["a"])(k,y,b,!1,null,"8ce5b92c",null)),I=w.exports,O=a("9568"),D=a("ea47"),j=a("bd57"),$=a("6dc2"),x=a("16b2"),F=a("b6bd");function P(){return Object(F["a"])({url:"store/recharge_info",method:"get"})}a("94b5");function U(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function T(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?U(a,!0).forEach((function(t){L(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):U(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function L(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var N={components:{userList:I,goodsList:O["a"],userOrder:D["a"],orderDetails:j["a"],orderRecord:$["a"],userInfo:h,userTable:g,settleDrawer:x["a"]},data:function(){return{userList:[],tabs:["会员详情","付费会员","余额充值"],sle:0,rechargeList:[],searchData:{type:0,status:0,time:0,people:0},rechargeData:{uid:0,price:0,rechar_id:0,merber_id:0,pay_type:3,auth_code:""},payPrice:1,payNum:0,total:0,modalPay:!1,userFrom:{keyword:"",page:1,limit:15,field_key:"all"},selectUserData:{},selectUserInfo:{},menus:[{name:"用户信息",value:"info",component:"userInfo"},{name:"消费记录",value:"order",component:"userTable"},{name:"积分明细",value:"integral",component:"userTable"},{name:"持有优惠券",value:"coupon",component:"userTable"},{name:"余额变动",value:"balance_change",component:"userTable"},{name:"浏览足迹",value:"visit",component:"userTable"},{name:"推广人变更记录",value:"spread_change",component:"userTable"}],tabActive:"info",memberCard:[],rechargeType:1,modalCash:!1,collectionArray:[],collection:0,numList:["7","8","9","4","5","6","1","2","3","0","."],isOrderCreate:0,settleVisible:!1,payType:"",payList:[{label:"微信/支付宝",value:"",status:!0},{label:"现金收款",value:"cash",status:!0}]}},computed:{memberCheck:function(){var e=this,t=this.memberCard.find((function(t){return t.id==e.rechargeData.merber_id}));return t||{}},balanceCheck:function(){var e=this,t=this.rechargeList.find((function(t){return t.id==e.rechargeData.rechar_id}));return t||{}}},watch:{"selectUserData.uid":function(e){e&&(this.readUserInfo(),this.getMemberCard())},total:function(e){e?this.getRechargeData():(this.selectUserInfo=this.selectUserData={},this.sle=0,this.rechargeType=1,this.payPrice=1)},payPrice:function(e){var t=this;this.$nextTick((function(){"number"==typeof e&&parseFloat(e)!=parseInt(e)&&e.toString().length-(e.toString().indexOf(".")+1)>2&&(t.payPrice=Number(e.toFixed(2)))}))}},created:function(){this.userListApi()},beforeDestroy:function(){clearInterval(this.timer)},methods:{jsToJava:function(){try{window.Jsbridge.invoke("openCacheBox",JSON.stringify({"p1-key":"p1-value"}),this.myFunction())}catch(e){}},keyboard:function(){var e=this;function t(t){e.collectionArray.pop(),e.collection=e.collectionArray.length?e.collectionArray.join(""):0}function a(t){!1===e.defaultcalc&&(e.collection="",e.defaultcalc=!0);var a=String(e.collection).indexOf(".")+1,s=String(e.collection).length-a;(0===a||s<2)&&(e.collectionArray.join("")<=9999999&&e.collectionArray.push(t),e.collection=e.collectionArray.join("")>99999999?99999999:e.collectionArray.join(""))}document.onkeydown=function(s){var i=s||window.event,r=i.keyCode;switch(e.modalCash&&(s.stopPropagation(),s.preventDefault()),r){case 96:case 48:a(0);break;case 97:case 49:a(1);break;case 98:case 50:a(2);break;case 99:case 51:a(3);break;case 100:case 52:a(4);break;case 101:case 53:a(5);break;case 102:case 54:a(6);break;case 103:case 55:a(7);break;case 104:case 56:a(8);break;case 105:case 57:a(9);break;case 110:a(".");break;case 190:a(".");break;case 8:t();break}}},cashBnt:function(e){var t=this;this.cashBntLoading||(this.cashBntLoading=!0,this.payType?this.rechargeData.pay_type=4:(this.rechargeData.pay_type=3,this.rechargeData.auth_code=e),this.confirm(),setTimeout((function(){t.cashBntLoading=!1}),1e3))},delNum:function(){this.collectionArray.pop(),this.collection=this.collectionArray.length?this.collectionArray.join(""):0},numTap:function(e){!1===this.defaultcalc&&(this.collection="",this.defaultcalc=!0);var t=String(this.collection).indexOf(".")+1,a=String(this.collection).length-t;(0===t||a<2)&&(this.collectionArray.join("")<=9999999&&this.collectionArray.push(e),this.collection=this.collectionArray.join("")>99999999?99999999:this.collectionArray.join(""))},cancel:function(){this.collection=0,this.collectionArray=[]},memberRecharge:function(){var e=this,t=T({},this.rechargeData);if(delete t.rechar_id,4==this.rechargeData.pay_type&&this.rechargeData.price>Number(this.collection))return this.$Message.error("您付款金额不足");Object(c["i"])(t).then((function(t){var a=t.data,s=a.status,i=a.data;switch(s){case"SUCCESS":e.$Message.success("支付成功"),e.settleVisible=!1,e.search(),e.payPrice=1,4==e.rechargeData.pay_type?e.jsToJava():e.rechargeData.auth_code="";break;case"PAY_ING":var r=e.$Message.loading({content:"等待支付中...",duration:0});e.checkOrderTime(i.order_id,r);break;default:e.$Message.warning("支付失败");break}})).catch((function(t){e.$Message.warning(t.msg)}))},getMemberCard:function(){var e=this,t=this.selectUserData,a=t.is_money_level,s=t.overdue_time;Object(c["e"])({is_money_level:a,overdue_time:s}).then((function(t){e.memberCard=t.data}))},readUserInfo:function(){var e=this;Object(c["n"])(this.selectUserData.uid).then((function(t){e.selectUserInfo=t.data.ps_info}))},addPage:function(){this.userList.length<this.total&&this.userFrom.page++,this.userListApi()},search:function(){this.userList=[],this.userFrom.page=1,this.userListApi()},userListApi:function(){var e=this;this.loading=!0,Object(c["u"])(this.userFrom).then((function(t){e.loading=!1,e.total=t.data.count,1===e.userFrom.page&&(e.selectUserData=t.data.list.length?t.data.list[0]:{},e.rechargeData.uid=t.data.list.length?t.data.list[0].uid:""),e.userList=e.userList.concat(t.data.list);var a=window.localStorage.getItem("cashierUser");if(a&&(a=JSON.parse(a),"[object Object]"===Object.prototype.toString.call(a))){var s=!0,i=!1,r=void 0;try{for(var n,c=e.userList[Symbol.iterator]();!(s=(n=c.next()).done);s=!0){var o=n.value;if(a.uid===o.uid){a.now_money=o.now_money,window.localStorage.setItem("cashierUser",JSON.stringify(a));break}}}catch(l){i=!0,r=l}finally{try{s||null==c.return||c.return()}finally{if(i)throw r}}}})).catch((function(t){e.loading=!1,e.$Message.error(t.msg)}))},keyupEvent:function(e){if(e){var t=/^\D*([0-9]\d*\.?\d{0,2})?.*$/,a=e.toString().replace(t,"$1");this.payPrice=Number(a)}else this.payPrice=""},selectTab:function(e){this.sle=e,1==e?this.memberCard.length&&(this.rechargeData.merber_id=this.memberCard[0].id):2==e&&(this.rechargeType=1,this.rechargeList.length&&(this.rechargeData.rechar_id=this.rechargeList[0].id))},clear:function(){this.payPrice=0,this.sel=0},yuePayClear:function(){this.$Message.destroy(),this.timer&&(clearInterval(this.timer),this.timer=null)},getRechargeData:function(){var e=this;P().then((function(t){e.rechargeList=t.data.recharge_quota}))},confirm:function(){var e=this;if(1==this.sle)return this.memberRecharge();var t=T({},this.rechargeData);2==this.rechargeType&&delete t.rechar_id,Object(c["w"])(t).then((function(t){e.payNum="";var a=t.data.status,s=t.data.data.order_id;switch(a){case"SUCCESS":e.$Message.success("支付成功"),e.settleVisible=!1,e.search(),e.payPrice=1,e.$emit("getSuccess",e.totalPrice),4==e.rechargeData.pay_type?e.jsToJava():e.rechargeData.auth_code="";break;case"PAY_ING":var i=e.$Message.loading({content:"等待支付中...",duration:0});e.checkOrderTime(s,i);break;default:e.$Message.warning("支付失败");break}})).catch((function(t){e.payNum="",e.rechargeData.auth_code="",e.$Message.error(t.msg)}))},checkOrderTime:function(e,t){var a=this,s=1,i=this.timer=setInterval((function(){a.checkOrder(e,i,t),s++,s>=60&&(clearInterval(i),t(),a.$Message.success("支付失败"))}),1e3)},checkOrder:function(e,t,a){var s=this;Object(c["c"])(2==this.sle?1:2,{order_id:e}).then((function(e){1==e.data.status&&(a(),s.$Message.success("支付成功"),s.$emit("getSuccess",s.totalPrice),s.settleVisible=!1,s.modalPay=!1,s.modal=!1,clearInterval(t),s.readUserInfo())})).catch((function(e){a(),s.$Message.error(e.msg)}))},selectUser:function(e){this.selectUserData=e,this.rechargeData.uid=e.uid,this.sle=0,this.rechargeData.rechar_id=0,this.payPrice=1},recharge:function(e){if(!this.selectUserData.uid)return this.$Message.error("请先选择会员");if(1==this.sle)this.rechargeData.price=Number(this.memberCheck.pre_price);else if(1==this.rechargeType)this.rechargeData.price=this.balanceCheck.price;else{if(!this.payPrice)return this.$Message.error("请先输入金额");this.rechargeData.price=this.payPrice}this.collection=this.rechargeData.price,this.settleVisible=!0},onPayPrice:function(e){this.payType=e,this.rechargeData.auth_code=""}}},S=N,A=(a("7f74"),Object(u["a"])(S,s,i,!1,null,"5a7c092c",null));t["default"]=A.exports},"16b2":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("Drawer",{ref:"drawer",attrs:{value:e.visible,width:"800","class-name":"settle-drawer"},on:{"on-visible-change":e.visibleChange}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"title"},[e._v("选择支付方式")]),a("div",{staticClass:"btn-group"},[a("ButtonGroup",{attrs:{shape:"circle"}},[e._l(e.list,(function(t){return[t.status?a("Button",{key:t.label,attrs:{type:t.value===e.type?"primary":"default"},on:{click:function(a){return e.typeChange(t.value)}}},[e._v(e._s(t.label))]):e._e()]}))],2)],1)]),a("div",{staticClass:"drawer-body"},[a("div",{staticClass:"receivable"},[a("div",{staticClass:"title"},[a("span",{staticClass:"inner"},[e._v("应收金额(元)")])]),a("div",{staticClass:"money"},[e._v("\n        ￥"),a("span",{staticClass:"number"},[e._v(e._s(e.money))])])]),"cash"===e.type?a("div",{staticClass:"counter"},[a("div",{staticClass:"received"},[e._v(e._s(e.collection))]),a("div",{staticClass:"balance"},[e._v("\n        需找零(元)："),a("span",{staticClass:"money"},[e._v("￥"+e._s(e.money>e.collection?0:this.$computes.Sub(e.collection,e.money||0)))])]),a("div",{staticClass:"keypad"},[a("div",{staticClass:"left"},e._l(e.numList,(function(t){return a("Button",{key:t,on:{click:function(a){return e.numTap(t)}}},[e._v(e._s(t))])})),1),a("div",{staticClass:"right"},[a("Button",{on:{click:e.delNum}},[a("Icon",{attrs:{type:"ios-backspace-outline"}})],1),a("Button",{on:{click:function(t){return e.delNum(-1)}}},[e._v("C")]),a("Button",{staticClass:"enter",on:{click:e.cashBnt}},[e._v("确认")])],1)])]):!e.type||e.verify?a("div",{staticClass:"payment-code"},[a("Input",{key:e.type,ref:"input",attrs:{placeholder:"请点击输入框聚焦扫码或输入编码号"},on:{"on-enter":e.cashBnt},model:{value:e.payNum,callback:function(t){e.payNum="string"===typeof t?t.trim():t},expression:"payNum"}}),a("div",{staticClass:"tips-wrap",class:{balance:"yue"===e.type}})],1):e._e()]),a("div",{staticClass:"drawer-footer"},[a("Button",{on:{click:e.handleCancel}},[e._v("取消收款")]),"yue"!==e.type||e.verify?e._e():a("Button",{attrs:{type:"primary"},on:{click:e.cashBnt}},[e._v("扣除余额")])],1)])},i=[],r={model:{prop:"visible",event:"change"},props:{visible:{type:Boolean,default:!1},money:{type:[Number,String],default:0},collection:{type:[Number,String],default:0},zIndex:{type:[Number,String],default:9999},type:{type:String,default:""},verify:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}}},data:function(){return{numList:["1","2","3","4","5","6","7","8","9","0","00","."],payNum:""}},watch:{type:function(e){var t=this;this.$nextTick((function(){(!e||"yue"===e&&t.verify)&&t.$refs.input.focus()}))},zIndex:function(e){var t=this.$refs.drawer.$el;t.querySelector(".ivu-drawer-mask").style.zIndex=e,t.querySelector(".ivu-drawer-wrap").style.zIndex=e}},methods:{visibleChange:function(e){var t=this;e?this.$nextTick((function(){t.$refs.input.focus()})):this.payNum="",this.$emit("change",e)},typeChange:function(e){this.payNum="",this.$emit("payPrice",e)},numTap:function(e){this.$emit("numTap",e)},delNum:function(e){this.$emit("delNum",e)},cashBnt:function(){!this.payNum&&(""===this.type||"yue"===this.type&&this.verify)||("yue"===this.type&&this.isURL(this.payNum)&&(this.payNum=this.getCodeFromLink(this.payNum)),this.$emit("cashBnt",this.payNum),this.payNum="")},isURL:function(e){var t=/^(http|https):\/\/[^ "]+$/;return t.test(e)},getCodeFromLink:function(e){var t=new URL(e),a=new URLSearchParams(t.search),s=a.get("code");return s},handleCancel:function(){this.$emit("change",!1)}}},n=r,c=(a("3c09"),a("2877")),o=Object(c["a"])(n,s,i,!1,null,"1e92d696",null);t["a"]=o.exports},"1a3d":function(e,t,a){e.exports=a.p+"view_cashier/img/process2.f9f8c6c6.png"},"3c09":function(e,t,a){"use strict";var s=a("f19d"),i=a.n(s);i.a},"785c":function(e,t,a){},"7f74":function(e,t,a){"use strict";var s=a("ae89"),i=a.n(s);i.a},8011:function(e,t,a){},8754:function(e,t,a){"use strict";var s=a("eff9"),i=a.n(s);i.a},ad18:function(e,t,a){e.exports=a.p+"view_cashier/img/gold.67ecfa42.png"},ae89:function(e,t,a){},b594:function(e,t,a){"use strict";var s=a("785c"),i=a.n(s);i.a},c24f:function(e,t,a){"use strict";a.d(t,"z",(function(){return i})),a.d(t,"s",(function(){return r})),a.d(t,"t",(function(){return n})),a.d(t,"a",(function(){return c})),a.d(t,"y",(function(){return o})),a.d(t,"r",(function(){return l})),a.d(t,"u",(function(){return u})),a.d(t,"b",(function(){return d})),a.d(t,"C",(function(){return h})),a.d(t,"f",(function(){return m})),a.d(t,"m",(function(){return p})),a.d(t,"d",(function(){return v})),a.d(t,"h",(function(){return f})),a.d(t,"x",(function(){return _})),a.d(t,"v",(function(){return g})),a.d(t,"B",(function(){return y})),a.d(t,"w",(function(){return b})),a.d(t,"A",(function(){return C})),a.d(t,"o",(function(){return k})),a.d(t,"q",(function(){return w})),a.d(t,"c",(function(){return I})),a.d(t,"p",(function(){return O})),a.d(t,"n",(function(){return D})),a.d(t,"g",(function(){return j})),a.d(t,"e",(function(){return $})),a.d(t,"i",(function(){return x})),a.d(t,"k",(function(){return F})),a.d(t,"j",(function(){return P})),a.d(t,"l",(function(){return U}));var s=a("b6bd");function i(){return Object(s["a"])({url:"user/user_label_cate",method:"get"})}function r(){return Object(s["a"])({url:"user/user_label_cate/create",method:"get"})}function n(e){return Object(s["a"])({url:"user/user_label_cate/".concat(e,"/edit"),method:"get"})}function c(e){return Object(s["a"])({url:"user/user_label",method:"get",params:e})}function o(){return Object(s["a"])({url:"user/user_label/create",method:"get"})}function l(e){return Object(s["a"])({url:"user/user_label/".concat(e,"/edit"),method:"get"})}function u(e){return Object(s["a"])({url:"user/get_list",method:"get",params:e})}function d(e){return Object(s["a"])({url:"user/cashier_list",method:"get",params:e})}function h(e){return Object(s["a"])({url:"user/search",method:"get",params:e})}function m(e){return Object(s["a"])({url:"user/label/".concat(e),method:"get"})}function p(e,t){return Object(s["a"])({url:"user/label/".concat(e),method:"post",data:t})}function v(e){return Object(s["a"])({url:"user/info/".concat(e),method:"get"})}function f(e){return Object(s["a"])({url:"user/record/".concat(e.id),method:"get",params:e.datas})}function _(e){return Object(s["a"])({url:"user/set_label",method:"post",data:e})}function g(){return Object(s["a"])({url:"store/recharge_info",method:"get"})}function y(){return Object(s["a"])({url:"user/member/ship",method:"get"})}function b(e){return Object(s["a"])({url:"store/recharge",method:"post",data:e})}function C(e){return Object(s["a"])({url:"/user/member",method:"post",data:e})}function k(e){return Object(s["a"])({url:"staff/binding/user",method:"post",data:e})}function w(e){return Object(s["a"])({url:"updatePwd",method:"PUT",data:e})}function I(e,t){return Object(s["a"])({url:"check_order_status/".concat(e),method:"post",data:t})}function O(){return Object(s["a"])({url:"user/cashier_info ",method:"get"})}function D(e){return Object(s["a"])({url:"user/read/".concat(e),method:"get"})}function j(e,t){return Object(s["a"])({url:"user/one_info/".concat(e),method:"get",params:t})}function $(e){return Object(s["a"])({url:"user/member_card",method:"get",params:e})}function x(e){return Object(s["a"])({url:"user/mer_recharge",method:"post",data:e})}function F(e){return Object(s["a"])({url:"user/search_user_info",method:"post",data:e})}function P(e){return Object(s["a"])({url:"user/register_user",method:"post",data:e})}function U(e,t){return Object(s["a"])({url:"user/update/".concat(e),method:"post",data:t})}},e698:function(e,t,a){"use strict";var s=a("8011"),i=a.n(s);i.a},eff9:function(e,t,a){},f19d:function(e,t,a){}}]);