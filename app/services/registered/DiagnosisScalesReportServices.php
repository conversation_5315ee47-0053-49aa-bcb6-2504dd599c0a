<?php

namespace app\services\registered;

use app\model\diagnosis\DiagnosisReport;
use app\model\registered\DiagnosisScales;
use app\model\registered\DiagnosisScalesReport;
use app\model\user\User;
use app\services\registered\DiagnosisScalesServices;
use app\services\registered\LiuyiMiddleService;
use app\services\registered\PatientServices;
use crmeb\services\CacheService;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Db;

class DiagnosisScalesReportServices
{
    protected DiagnosisScalesReport $model;

    public function __construct()
    {
        $this->model = new DiagnosisScalesReport();
    }

    /**
     * 量表开始答题获取题目
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception
     */
    public function diagnosisScalesStartQuestion(int $uid, array $data): array
    {
        $liuyiMiddleService = new LiuyiMiddleService();
        $diagnosisScalesServices = new DiagnosisScalesServices();
        $diagnosisScalesInfo = $diagnosisScalesServices->getDiagnosisScalesInfo($data['diagnosis_scales_id']);
        if (empty($diagnosisScalesInfo)) {
            throw new Exception('量表不存在');
        }
        // 是否允许重复答题
        $userHadTest = $this->checkUserHadTest($uid, $data['diagnosis_scales_id']);
        if ($userHadTest && $diagnosisScalesInfo['is_repeated'] == 0) {
            throw new Exception('您已测试过，不可重复测试');
        }
        Db::startTrans();
        try {
            $age = $this->getAgeByIdCardNo($data['id_card_no']);
            // 保存提交数据
            $reportTemp = [
                'uid' => $uid,
                'name' => $data['name'],
                'gender' => $data['gender'],
                'id_card_no' => $data['id_card_no'],
                'diagnosis_scales_id' => $data['diagnosis_scales_id'],
                'age' => $age,
                'start_time' => time()
            ];
            $saveReportTemp = $this->model->save($reportTemp);
            $scalesReportId = $saveReportTemp ? $this->model->id : 0;
            // 保存主表
            $mainReportId = 0;
            $reportUuid = '';
            $allQuestions = [];
            $mainDiagnosisReportModel = new DiagnosisReport();
            if ($saveReportTemp) {
                $mainReport = [
                    'uid' => $uid,
                    'project_name' => '心身健康量表测评',
                    'report_type' => 2,
                    'diagnosis_scales_id' => $data['diagnosis_scales_id'],
                    'status' => 1
                ];
                $mainRes = $mainDiagnosisReportModel->save($mainReport);
                if (!$mainRes) {
                    throw new \Exception('保存数据错误，稍后再试');
                }
                $mainReportId = $mainDiagnosisReportModel->id;
                // 保存主表记录 ID
                $this->model->where('id', $scalesReportId)->update(['diagnosis_report_id' => $mainReportId]);
            } else {
                throw new \Exception('保存量表数据失败');
            }
            // 获取题目列表
            $scalesQuestions = $liuyiMiddleService->getScalesQuestionsList($diagnosisScalesInfo['dimension']);
            if (!empty($scalesQuestions['data'])) {
                $reportUuid = $scalesQuestions['data']['report_uuid'] ?? '';
                if ($reportUuid == '') {
                    throw new \Exception('获取量表数据失败');
                }
                $this->model->where('id', $scalesReportId)->update(['report_uuid' => $reportUuid]);
                $allQuestions = array_merge(
                    $scalesQuestions['data']['physiology']['questions'],
                    $scalesQuestions['data']['psychology']['questions'],
                    $scalesQuestions['data']['society']['questions']
                );
            }
            $diagnosisScalesServices->addUserTestNum($data['diagnosis_scales_id']);
            $mainDiagnosisReportModel->where('id', $mainReportId)->update(['flow_sn' => $reportUuid]);
            Db::commit();
            return [
                'diagnosis_report_id' => $mainReportId,
                'diagnosis_scales_report_id' => $scalesReportId,
                'report_uuid' => $reportUuid,
                'questions' => $allQuestions,
            ];
        } catch (\Exception $e) {
            Db::rollback();
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 用户是否已测试过该量表
     * @param int $uid
     * @param int $diagnosisScalesId
     * @return bool
     * @throws DbException
     */
    public function checkUserHadTest(int $uid, int $diagnosisScalesId): bool
    {
        $where = [
            'uid' => $uid,
            'diagnosis_scales_id' => $diagnosisScalesId,
        ];
        $count = $this->model->where($where)->whereNotNull('finished_time')->count();
        return $count > 0;
    }

    /**
     * 检测用户是否有未完成的测试
     * @param int $uid
     * @return DiagnosisScalesReport|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkBreakTestReport(int $uid)
    {
        return $this->model->where('uid', $uid)->order('id desc')->find();
    }

    /**
     * Calculates age from Chinese ID card number
     *
     * @param string $idCardNo 18-digit ID card number containing birth date (YYYYMMDD)
     * @return int Age in years
     * @throws \InvalidArgumentException If ID format is invalid
     */
    public function getAgeByIdCardNo(string $idCardNo): int
    {
        // Extract birth date from ID card number (assuming format: YYYYMMDD)
        $birthDate = \DateTime::createFromFormat(
            'Ymd',
            substr($idCardNo, 6, 8)
        );

        if (!$birthDate) {
            throw new \InvalidArgumentException('Invalid ID card number format');
        }

        $today = new \DateTime();

        return $today->diff($birthDate)->y;
    }

    /**
     * Handles answer submission for diagnosis scales
     *
     * @param int $uid User ID
     * @param array $data {
     * @return array
     * @throws Exception
     * @var string $report_uuid Report unique identifier
     * @var int $serial_num Question serial number
     * @var string $key Answer key
     * @var int $is_finish Whether submission is complete (1 = yes)
     * @var string $question Question text
     * @var string $label Answer label
     * }
     */
    public function clickAnswerItems(int $uid, array $data): array
    {
        // Validate required fields
        $required = ['report_uuid', 'serial_num', 'key', 'is_finish', 'question', 'label'];
        if (count(array_intersect($required, array_keys($data))) !== count($required)) {
            throw new Exception('Missing required fields in submission data');
        }

        // Extract and validate report
        $reportUuid = $data['report_uuid'];
        $tempReport = $this->model->where('report_uuid', $reportUuid)->find();
        if (!$tempReport) {
            throw new Exception('提交数据异常，稍后再试');
        }
        $totalQuestionNum = (new DiagnosisScales())->getTotalQuestionNum($tempReport->diagnosis_scales_id);

        // Prepare answer data
        $answerData = [
            'key' => $data['key'],
            'question' => $data['question'],
            'label' => $data['label'],
            'serial_num' => $data['serial_num'],
        ];

        // Store in Redis
        $redisAnswerKey = "diagnosis_scales_report_{$reportUuid}";
        $redis = CacheService::redisHandler();

        $hasAnswerList = $redis->has($redisAnswerKey)
            ? json_decode($redis->get($redisAnswerKey), true)
            : [];

        $hasAnswerList[$data['serial_num']] = $answerData;
        $redis->set($redisAnswerKey, json_encode($hasAnswerList));

        $nowSerialNum = ($data['serial_num'] + 1 >= $totalQuestionNum) ? $totalQuestionNum : $data['serial_num'] + 1;
        // Handle final submission
        if ($data['is_finish'] == 1) {
            if (empty($hasAnswerList)) {
                throw new Exception('提交数据错误');
            }

            // Format answers for submission
            $answers = array_map(function ($key, $value) {
                return [
                    'serial_num' => $key,
                    'key' => $value['key'],
                    'question' => $value['question'],
                    'label' => $value['label']
                ];
            }, array_keys($hasAnswerList), $hasAnswerList);
            // Get user openid and prepare submission data
            $openid = (new PatientServices())->getUserOpenIdByUid($uid);
            $postData = [
                'reportUuid' => $reportUuid,
                'gender' => $tempReport->gender,
                'age' => $tempReport->age,
                'answer' => json_encode($answers),
                'openid' => $openid,
            ];

            // Submit to middle service
            $res = (new LiuyiMiddleService())->scalesReportTask($postData);

            if ($res['code'] != 200) {
                throw new Exception($res['message']);
            }

            (new DiagnosisScalesServices())->addFinishNum($tempReport->diagnosis_scales_id);

            $tempReport->answer = json_encode($answers);
            $tempReport->end_time = time();
            $tempReport->save();
            (new DiagnosisReport())->where('flow_sn', $reportUuid)->update(['status' => 2, 'updated_at' => time()]);

            $redis->del($redisAnswerKey);

            return ['success' => true, 'task' => $res['data'], 'next' => false, 'report_uuid' => $reportUuid, 'serial_num' => $nowSerialNum, 'total_num' => $totalQuestionNum];
        }

        return ['success' => true, 'task' => [], 'next' => true, 'report_uuid' => $reportUuid, 'serial_num' => $nowSerialNum, 'total_num' => $totalQuestionNum];
    }

    public function reportTest()
    {
//        $reportUuid = 'e1153a8e-6eb5-42a4-8764-dde267348dab';
//        $redisAnswerKey = "diagnosis_scales_report_{$reportUuid}";
//        $redis = CacheService::redisHandler();
//        print_r($redis->get($redisAnswerKey));exit;
//        $answerList = json_decode($redis->get($redisAnswerKey), true);
//        unset($answerList[39]);
        $string = <<<EOF
{"1":{"key":"01q7r8s9t0","question":"\u60a8\u7684\u98df\u6b32\u600e\u4e48\u6837\uff1f","label":"\u975e\u5e38\u597d","serial_num":1},"2":{"key":"02y5z6a7b8","question":"\u60a8\u7684\u7761\u7720\u600e\u4e48\u6837\uff1f","label":"\u6bd4\u8f83\u5dee","serial_num":2},"3":{"key":"03w9x0y1z2","question":"\u60a8\u5bf9\u81ea\u5df1\u7684\u5934\u53d1\u751f\u957f\u60c5\u51b5\u6ee1\u610f\u5417\uff1f(\u5982\u5934\u53d1\u65e9\u767d\u3001\u67af\u9ec4\u6216\u8131\u53d1\u7b49\u60c5\u51b5)","label":"\u4e00\u822c","serial_num":3},"4":{"key":"04m5n6o7p8","question":"\u60a8\u611f\u5230\u53e3\u82e6\u6216\u53e3\u5e72\u5417\uff1f","label":"\u5f88\u5c11","serial_num":4},"5":{"key":"05k9l0m1n2","question":"\u60a8\u6709\u80c3\u80a0\u4e0d\u9002\u5417\uff1f(\u5982\u53cd\u9178\u3001\u6696\u6c14\u3001\u6076\u5fc3\u3001\u8179\u75db\u3001\u8179\u80c0\u3001\u8179\u6e7e\u3001\u4fbf\u79d8\u7b49)","label":"\u6709\u65f6\u6709","serial_num":5},"6":{"key":"06e9f0g1h2","question":"\u60a8\u7684\u5c0f\u4fbf\u6709\u5f02\u5e38\u5417\uff1f(\u5982\u5c3f\u9ec4\u3001\u5c3f\u75db\u3001\u5c3f\u5c11\u3001\u5c3f\u9891\u3001\u591c\u5c3f\u591a\u7b49)","label":"\u6709\u65f6\u6709","serial_num":6},"7":{"key":"07y9z0a1b2","question":"\u60a8\u6709\u5934\u90e8\u4e0d\u9002\u5417\uff1f(\u5982\u5934\u75db\u6216\u5934\u80c0)","label":"\u6709\u65f6\u6709","serial_num":7},"8":{"key":"08w3x4y5z6","question":"\u60a8\u6709\u773c\u775b\u4e0d\u9002\u5417\uff1f(\u5982\u9178\u80c0\u3001\u5e72\u6e7f)","label":"\u7ecf\u5e38\u6709","serial_num":8},"9":{"key":"09u7v8w9x0","question":"\u60a8\u7684\u542c\u89c9\u7cfb\u7edf\u6709\u5f02\u5e38\u5417\uff1f(\u5982\u8033\u9e23\u3001\u542c\u529b\u4e0b\u964d)","label":"\u4e00\u76f4\u6709","serial_num":9},"10":{"key":"10n3o4p5q6","question":"\u60a8\u5f2f\u8170\u3001\u5c48\u819d\u6709\u56f0\u96be\u5417\uff1f","label":"\u6bd4\u8f83\u56f0\u96be","serial_num":10},"11":{"key":"11d9e0f1g2","question":"\u6b63\u5e38\u722c3\u81f35\u5c42\u697c\uff0c\u60a8\u6709\u56f0\u96be\u5417\uff1f","label":"\u6709\u70b9\u56f0\u96be","serial_num":11},"12":{"key":"12b3c4d5e6","question":"\u60a8\u6b65\u884c1500\u7c73\u6709\u56f0\u96be\u5417\uff1f","label":"\u6bd4\u8f83\u56f0\u96be","serial_num":12},"13":{"key":"13z7a8b9c0","question":"\u6b63\u5e38\u4f11\u606f\u540e\u60a8\u7684\u75b2\u52b3\u80fd\u5f97\u5230\u7f13\u89e3\u5417\uff1f","label":"\u5b8c\u5168\u53ef\u4ee5","serial_num":13},"14":{"key":"14t7u8v9w0","question":"\u60a8\u6709\u5145\u6c9b\u7cbe\u529b\u5e94\u4ed8\u65e5\u5e38\u751f\u6d3b\u3001\u5de5\u4f5c\u548c\u5b66\u4e60\u5417\uff1f","label":"\u5b8c\u5168\u6709","serial_num":14},"15":{"key":"15j3k4l5m6","question":"\u60a8\u8ba4\u4e3a\u81ea\u5df1\u7684\u751f\u7406\uff08\u8eaf\u4f53\uff09\u5065\u5eb7\u5904\u4e8e\u4ec0\u4e48\u72b6\u6001\uff1f","label":"\u91cd\u5ea6\u4e9a\u5065\u5eb7","serial_num":15},"16":{"key":"16h7i8j9k0","question":"\u60a8\u5bf9\u81ea\u5df1\u6709\u4fe1\u5fc3\u5417\uff1f","label":"\u5f88\u6709\u4fe1\u5fc3","serial_num":16},"17":{"key":"17x3y4z5a6","question":"\u60a8\u5bf9\u76ee\u524d\u7684\u751f\u6d3b\u72b6\u51b5\u6ee1\u610f\u5417\uff1f","label":"\u6bd4\u8f83\u6ee1\u610f","serial_num":17},"18":{"key":"18v7w8x9y0","question":"\u60a8\u5bf9\u672a\u6765\u4e50\u89c2\u5417\uff1f","label":"\u975e\u5e38\u4e50\u89c2","serial_num":18},"19":{"key":"19w7x8y9z0","question":"\u60a8\u6709\u5e78\u798f\u7684\u611f\u89c9\u5417\uff1f","label":"\u4e00\u76f4\u6709","serial_num":19},"20":{"key":"20q7r8s9t0","question":"\u60a8\u611f\u5230\u7cbe\u795e\u7d27\u5f20\u5417\uff1f","label":"\u603b\u662f","serial_num":20},"21":{"key":"21k7l8m9n0","question":"\u60a8\u611f\u5230\u5fc3\u60c5\u4e0d\u597d\u3001\u60c5\u7eea\u4f4e\u843d\u5417\uff1f","label":"\u4e00\u76f4\u6709","serial_num":21},"22":{"key":"22e7f8g9h0","question":"\u60a8\u611f\u5230\u4e0d\u5b89\u5168\u5417\uff1f","label":"\u603b\u662f","serial_num":22},"23":{"key":"23y7z8a9b0","question":"\u60a8\u4f1a\u6beb\u65e0\u7406\u7531\u5730\u611f\u5230\u5bb3\u6015\u5417\uff1f","label":"\u603b\u662f\u8fd9\u6837","serial_num":23},"24":{"key":"24o3p4q5r6","question":"\u60a8\u89c9\u5f97\u5b64\u72ec\u5417\uff1f","label":"\u6bd4\u8f83\u5b64\u72ec","serial_num":24},"25":{"key":"25m7n8o9p0","question":"\u60a8\u654f\u611f\u591a\u7591\u5417\uff1f","label":"\u603b\u662f","serial_num":25},"26":{"key":"26g7h8i9j0","question":"\u60a8\u7684\u8bb0\u5fc6\u529b\u600e\u4e48\u6837\uff1f","label":"\u975e\u5e38\u597d","serial_num":26},"27":{"key":"27a7b8c9d0","question":"\u60a8\u601d\u8003\u95ee\u9898\u6216\u5904\u7406\u95ee\u9898\u7684\u80fd\u529b\u600e\u4e48\u6837\uff1f","label":"\u975e\u5e38\u597d","serial_num":27},"28":{"key":"28u7v8w9x0","question":"\u60a8\u8ba4\u4e3a\u81ea\u5df1\u7684\u5fc3\u7406\u5065\u5eb7(\u5982\u60c5\u7eea\u3001\u8ba4\u77e5\u80fd\u529b\u7b49)\u5904\u4e8e\u4ec0\u4e48\u72b6\u6001\uff1f","label":"\u4e0d\u5065\u5eb7\uff08\u75be\u75c5\uff09","serial_num":28},"29":{"key":"29o7p8q9r0","question":"\u5bf9\u4e8e\u5728\u751f\u6d3b\u3001\u5de5\u4f5c\u548c\u5b66\u4e60\u4e2d\u53d1\u751f\u5728\u81ea\u5df1\u8eab\u4e0a\u7684\u4e0d\u6109\u5feb\u4e8b\u60c5\uff0c\u60a8\u80fd\u59a5\u5584\u5730\u5904\u7406\u597d\u5417\uff1f","label":"\u5b8c\u5168\u53ef\u4ee5","serial_num":29},"30":{"key":"30i7j8k9l0","question":"\u60a8\u5bf9\u81ea\u5df1\u5728\u793e\u4f1a\u4e2d\u7684\u4eba\u9645\u5173\u7cfb\u6ee1\u610f\u5417\uff1f","label":"\u975e\u5e38\u6ee1\u610f","serial_num":30},"31":{"key":"31c7d8e9f0","question":"\u60a8\u5bf9\u81ea\u5df1\u5728\u751f\u6d3b\u3001\u5de5\u4f5c\u548c\u5b66\u4e60\u4e2d\u7684\u8868\u73b0\u6ee1\u610f\u5417\uff1f","label":"\u975e\u5e38\u6ee1\u610f","serial_num":31},"32":{"key":"32w7x8y9z0","question":"\u60a8\u80fd\u591f\u8f83\u5feb\u5730\u9002\u5e94\u65b0\u7684\u751f\u6d3b\u3001\u5de5\u4f5c\u548c\u5b66\u4e60\u73af\u5883\u5417\uff1f","label":"\u5b8c\u5168\u53ef\u4ee5","serial_num":32},"33":{"key":"33q7r8s9t0","question":"\u60a8\u548c\u4eb2\u670b\u597d\u53cb\u7ecf\u5e38\u4fdd\u6301\u8054\u7cfb(\u5982\u4e92\u76f8\u63a2\u671b\u3001\u7535\u8bdd\u95ee\u5019\u3001\u901a\u4fe1\u7b49)\u5417\uff1f","label":"\u4e00\u76f4\u8054\u7cfb","serial_num":33},"34":{"key":"34k7l8m9n0","question":"\u60a8\u6709\u53ef\u4ee5\u4e0e\u60a8\u5206\u4eab\u5feb\u4e50\u548c\u5fe7\u4f24\u7684\u670b\u53cb\u5417\uff1f","label":"\u975e\u5e38\u591a(\u4e94\u4e2a\u4ee5\u4e0a)","serial_num":34},"35":{"key":"35e7f8g9h0","question":"\u4e0e\u60a8\u5173\u7cfb\u5bc6\u5207\u7684\u540c\u4e8b\u3001\u540c\u5b66\u3001\u90bb\u5c45\u3001\u4eb2\u621a\u6216\u670b\u53cb\u591a\u5417\uff1f","label":"\u975e\u5e38\u591a(\u4e94\u4e2a\u4ee5\u4e0a)","serial_num":35},"36":{"key":"36y7z8a9b0","question":"\u9700\u8981\u5e2e\u52a9\u65f6\uff0c\u5bb6\u4eba\u3001\u540c\u4e8b\u6216\u670b\u53cb\u4f1a\u7ed9\u4e88\u60a8\u7269\u8d28\u6216\u60c5\u611f\u4e0a\u7684\u652f\u6301\u6216\u5e2e\u52a9\u5417\uff1f","label":"\u603b\u662f\u8fd9\u6837","serial_num":36},"37":{"key":"37s7t8u9v0","question":"\u9047\u5230\u56f0\u96be\u65f6\uff0c\u60a8\u4f1a\u4e3b\u52a8\u5bfb\u6c42\u4ed6\u4eba\u7684\u652f\u6301\u548c\u5e2e\u52a9\u5417\uff1f","label":"\u603b\u662f\u8fd9\u6837","serial_num":37},"38":{"key":"38x1y2z3a4","question":"\u60a8\u8ba4\u4e3a\u81ea\u5df1\u7684\u793e\u4f1a\u5065\u5eb7(\u5982\u4eba\u9645\u5173\u7cfb\u3001\u793e\u4f1a\u4ea4\u5f80\u7b49\u65b9\u9762)\u5904\u4e8e\u4ec0\u4e48\u72b6\u6001\uff1f","label":"\u5065\u5eb7","serial_num":38},"39":{"key":"39v5w6x7y8","question":"\u60a8\u8ba4\u4e3a\u81ea\u5df1\u7684\u603b\u4f53\u5065\u5eb7(\u5305\u62ec\u8eaf\u4f53\u3001\u5fc3\u7406\u3001\u793e\u4f1a\u5065\u5eb7\u4e09\u65b9\u9762)\u5904\u4e8e\u4ec0\u4e48\u72b6\u6001\uff1f","label":"\u8f7b\u5ea6\u4e9a\u5065\u5eb7","serial_num":"39"}}
EOF;
        $hasAnswerList = json_decode($string, true);
        $answers = array_map(function ($key, $value) {
            return [
                'serial_num' => $key,
                'key' => $value['key'],
                'question' => $value['question'],
                'label' => $value['label']
            ];
        }, array_keys($hasAnswerList), $hasAnswerList);
        $answerJson = json_encode($answers);
//        print_r($answerJson);exit;
        return [];
    }

    /**
     * test
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception
     */
    public function createTestTask(int $uid, string $reportUuid): array
    {
        $tempAnswer = <<<EOF
[{"serial_num":1,"key":"01q7r8s9t0","question":"您的食欲怎么样？","label":"非常好"},{"serial_num":2,"key":"02g3h4i5j6","question":"您的睡眠怎么样？","label":"比较好"},{"serial_num":3,"key":"03a3b4c5d6","question":"您对自己的头发生长情况满意吗？(如头发早白、枯黄或脱发等情况)","label":"比较满意"},{"serial_num":4,"key":"04q9r0s1t2","question":"您感到口苦或口干吗？","label":"有时"},{"serial_num":5,"key":"05c1d2e3f4","question":"您有胃肠不适吗？(如反酸、暖气、恶心、腹痛、腹胀、腹湾、便秘等)","label":"根本没有"},{"serial_num":6,"key":"06a5b6c7d8","question":"您的小便有异常吗？(如尿黄、尿痛、尿少、尿频、夜尿多等)","label":"很少有"},{"serial_num":7,"key":"07u5v6w7x8","question":"您有头部不适吗？(如头痛或头胀)","label":"很少有"},{"serial_num":8,"key":"08o5p6q7r8","question":"您有眼睛不适吗？(如酸胀、干湿)","label":"很少有"},{"serial_num":9,"key":"09e1f2g3h4","question":"您的听觉系统有异常吗？(如耳鸣、听力下降)","label":"根本没有"},{"serial_num":10,"key":"10f5g6h7i8","question":"您弯腰、屈膝有困难吗？","label":"比较轻松"},{"serial_num":11,"key":"11z5a6b7c8","question":"正常爬3至5层楼，您有困难吗？","label":"比较轻松"},{"serial_num":12,"key":"12p1q2r3s4","question":"您步行1500米有困难吗？","label":"没有困难"},{"serial_num":13,"key":"13z7a8b9c0","question":"正常休息后您的疲劳能得到缓解吗？","label":"完全可以"},{"serial_num":14,"key":"14p3q4r5s6","question":"您有充沛精力应付日常生活、工作和学习吗？","label":"多数有"},{"serial_num":15,"key":"15b5c6d7e8","question":"您认为自己的生理（躯体）健康处于什么状态？","label":"轻度亚健康"},{"serial_num":16,"key":"16h7i8j9k0","question":"您对自己有信心吗？","label":"很有信心"},{"serial_num":17,"key":"17x3y4z5a6","question":"您对目前的生活状况满意吗？","label":"比较满意"},{"serial_num":18,"key":"18v7w8x9y0","question":"您对未来乐观吗？","label":"非常乐观"},{"serial_num":19,"key":"19w7x8y9z0","question":"您有幸福的感觉吗？","label":"一直有"},{"serial_num":20,"key":"20i9j0k1l2","question":"您感到精神紧张吗？","label":"有时"},{"serial_num":21,"key":"21c9d0e1f2","question":"您感到心情不好、情绪低落吗？","label":"有时有"},{"serial_num":22,"key":"22s5t6u7v8","question":"您感到不安全吗？","label":"很少"},{"serial_num":23,"key":"23m5n6o7p8","question":"您会毫无理由地感到害怕吗？","label":"很少会"},{"serial_num":24,"key":"24g5h6i7j8","question":"您觉得孤独吗？","label":"较不孤独"},{"serial_num":25,"key":"25w1x2y3z4","question":"您敏感多疑吗？","label":"从不"},{"serial_num":26,"key":"26c3d4e5f6","question":"您的记忆力怎么样？","label":"比较好"},{"serial_num":27,"key":"27w3x4y5z6","question":"您思考问题或处理问题的能力怎么样？","label":"比较好"},{"serial_num":28,"key":"28e1f2g3h4","question":"您认为自己的心理健康(如情绪、认知能力等)处于什么状态？","label":"健康"},{"serial_num":29,"key":"29o7p8q9r0","question":"对于在生活、工作和学习中发生在自己身上的不愉快事情，您能妥善地处理好吗？","label":"完全可以"},{"serial_num":30,"key":"30e3f4g5h6","question":"您对自己在社会中的人际关系满意吗？","label":"比较满意"},{"serial_num":31,"key":"31y3z4a5b6","question":"您对自己在生活、工作和学习中的表现满意吗？","label":"比较满意"},{"serial_num":32,"key":"32w7x8y9z0","question":"您能够较快地适应新的生活、工作和学习环境吗？","label":"完全可以"},{"serial_num":33,"key":"33i9j0k1l2","question":"您和亲朋好友经常保持联系(如互相探望、电话问候、通信等)吗？","label":"有时联系"},{"serial_num":34,"key":"34c9d0e1f2","question":"您有可以与您分享快乐和忧伤的朋友吗？","label":"一般"},{"serial_num":35,"key":"35w9x0y1z2","question":"与您关系密切的同事、同学、邻居、亲戚或朋友多吗？","label":"一般"},{"serial_num":36,"key":"36u3v4w5x6","question":"需要帮助时，家人、同事或朋友会给予您物质或情感上的支持或帮助吗？","label":"多数会"},{"serial_num":37,"key":"37k9l0m1n2","question":"遇到困难时，您会主动寻求他人的支持和帮助吗？","label":"有时会"},{"serial_num":38,"key":"38x1y2z3a4","question":"您认为自己的社会健康(如人际关系、社会交往等方面)处于什么状态？","label":"健康"},{"serial_num":39,"key":"39v5w6x7y8","question":"您认为自己的总体健康(包括躯体、心理、社会健康三方面)处于什么状态？","label":"轻度亚健康"}]
EOF;
//        $tempAnswerArr = json_decode($tempAnswer, true);
        $tempReport = $this->model->where('report_uuid', $reportUuid)->find();
        $openid = (new PatientServices())->getUserOpenIdByUid($uid);
        $postData = [
            'reportUuid' => $reportUuid,
            'gender' => $tempReport->gender,
            'age' => $tempReport->age,
            'answer' => $tempAnswer,
            'openid' => $openid,
        ];
        $res = (new LiuyiMiddleService())->scalesReportTask($postData);
        return ['success' => true, 'task' => $res['data'], 'next' => false, 'report_uuid' => $reportUuid];
    }


    /**
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception
     */
    public function timerListenReportStatus(): bool
    {
        $runningReports = $this->model->where('running_status', 'running')->select();
        $currentTime = time();
        $timeOutIds = [];

        if ($runningReports->isEmpty()) {
            return true;
        }

        foreach ($runningReports as $report) {
            // 检查是否超时（10分钟）
            if ($currentTime - $report->finished_time > 600) {
                $timeOutIds[] = $report->id;
                continue;
            }

            // 获取报告状态
            $this->getDiagnosisScalesReportStatus($report->report_uuid);
        }

        if (!empty($timeOutIds)) {
            $this->model->whereIn('id', $timeOutIds)->update(['running_status' => 'fail']);
        }

        return true;
    }


    /**
     * 获取报告状态
     * @param string $reportUuid
     * @return mixed
     * @throws Exception
     */
    public function getDiagnosisScalesReportStatus(string $reportUuid)
    {
        try {
            $statusInfo = (new LiuyiMiddleService())->getScalesReportStatus($reportUuid);
        } catch (Exception $e) {
            throw new Exception('获取报告状态失败' . $e->getMessage());
        }
        if ($statusInfo['code'] == 200) {
            // 更新任务数据
            if (isset($statusInfo['data'])) {
                $reportInfo = $this->model->where('report_uuid', $reportUuid)->find();
                if (!$reportInfo) {
                    throw new Exception('报告不存在');
                }
                if ($statusInfo['data']['process'] > 0 && $statusInfo['data']['process'] < 100) {
                    $reportInfo->running_status = 'running';
                    $reportInfo->save();
                }
                if (in_array($statusInfo['data']['status'], ['fail', 'finished'])) {
                    Db::startTrans();
                    try {
                        // 完成时间
                        $reportInfo->finished_time = time();
                        // 得分
                        $reportInfo->physiology_score = $statusInfo['data']['physiology_score'];
                        $reportInfo->psychology_score = $statusInfo['data']['psychology_score'];
                        $reportInfo->society_score = $statusInfo['data']['society_score'];
                        $reportInfo->score = $statusInfo['data']['synthesis_score'];
                        // 诊断
                        $reportInfo->physiology_diagnosis = $this->exchangeDiagnosisText($statusInfo['data']['physiology_diagnosis'])['text'];
                        $reportInfo->psychology_diagnosis = $this->exchangeDiagnosisText($statusInfo['data']['psychology_diagnosis'])['text'];
                        $reportInfo->society_diagnosis = $this->exchangeDiagnosisText($statusInfo['data']['society_diagnosis'])['text'];
                        $reportInfo->physique_name = $this->exchangeDiagnosisText($statusInfo['data']['synthesis_diagnosis'])['text'];
                        // 诊断层级
                        $reportInfo->physiology_level = $this->exchangeDiagnosisText($statusInfo['data']['physiology_diagnosis'])['level'];
                        $reportInfo->psychology_level = $this->exchangeDiagnosisText($statusInfo['data']['psychology_diagnosis'])['level'];
                        $reportInfo->society_level = $this->exchangeDiagnosisText($statusInfo['data']['society_diagnosis'])['level'];
                        $reportInfo->diagnosis_level = $this->exchangeDiagnosisText($statusInfo['data']['synthesis_diagnosis'])['level'];
                        // 报告
                        $reportInfo->physiology_report = $statusInfo['data']['physiology_report'];
                        $reportInfo->psychology_report = $statusInfo['data']['psychology_report'];
                        $reportInfo->society_report = $statusInfo['data']['society_report'];
                        $reportInfo->result = $statusInfo['data']['synthesis_report'];
                        // 状态
                        $reportInfo->running_status = $statusInfo['data']['status'] == 'fail' ? 'fail' : 'success';
                        $reportInfo->save();
                        // 更新生产了报告的人数
                        if ($statusInfo['data']['status'] == 'finished') {
                            (new DiagnosisScalesServices())->addRealNum($reportInfo->diagnosis_scales_id);
                        }
                        // 更新主表
                        $mainReportId = $reportInfo->diagnosis_report_id;
                        $mainDiagnosisReportModel = new DiagnosisReport();
                        $mainDiagnosisReportModel->where('id', $mainReportId)->update([
                            'result' => $this->exchangeDiagnosisText($statusInfo['data']['synthesis_diagnosis'])['text'],
                            'score' => $statusInfo['data']['synthesis_score'],
                            'updated_at' => time(),
                            'status' => $statusInfo['data']['status'] == 'fail' ? 4 : 3,
                        ]);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        throw new Exception($e->getMessage());
                    }
                }
            }
        } else {
            throw new Exception($statusInfo['message']);
        }
        return [
            'report_uuid' => $reportUuid,
            'status' => $statusInfo['data']['status'] ?? 'init',
            'process' => $statusInfo['data']['process'] ?? '0',
            'status_memo' => $statusInfo['data']['status_memo'] ?? '任务初始化',
            'task_id' => $statusInfo['data']['id'] ?? 0,
        ];
    }

    private function exchangeDiagnosisText(string $originDiagnosis): array
    {
        $originTextList = ['疾病', '重度亚健康', '中度亚健康', '轻度亚健康', '健康'];
        $newTextList = ['很差', '较差', '一般', '良好', '优秀'];
        $originIndex = array_search($originDiagnosis, $originTextList);
        return ['level' => $originIndex + 1, 'text' => $newTextList[$originIndex]];
    }

    /**
     * 获取报告列表
     * @param int $uid
     * @param array $data
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDiagnosisScalesReportList(int $uid, array $data): array
    {
        $type = $data['type'] ?? 0; // 0:全部 1:中医舌诊面诊 2:量表筛查
        $where = ['uid' => $uid];
        if ($type != 0) {
            $where['report_type'] = $type;
        }
        $diagnosisScalesModel = new DiagnosisScales();
//        $diagnosisScalesReportModel = new DiagnosisScalesReport();
        $mainListFields = 'id,uid,project_name,report_type,result,score,flow_sn,status,created_at,diagnosis_scales_id';
        $mainList = (new DiagnosisReport())->where($where)->field($mainListFields)->select()
            ->each(function ($item) use ($diagnosisScalesModel) {
                $item->cover_img = $diagnosisScalesModel->where('id', $item->diagnosis_scales_id)->value('cover_img');
            });
        return $mainList ? $mainList->toArray() : [];
    }

    /**
     * 重新生成报告
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws Exception
     */
    public function reCreateReport(int $diagnosisReportId): array
    {
        // 验证报告数据是否存在
        $reportInfo = (new DiagnosisReport())->where('id', $diagnosisReportId)->find();
        if (!$reportInfo) {
            throw new Exception('报告不存在');
        }
        if ($reportInfo->status != 4) {
            throw new Exception('报告状态错误，不能重新生成报告');
        }
        if ($reportInfo->report_type == 1) {
            // 重新生成中医舌诊面诊报告
            return ['report_uuid' => '', 'status' => 'init', 'msg' => '舌诊报告暂不支持'];
        }
        if ($reportInfo->report_type == 2) {
            Db::startTrans();
            try {
                // 重新生成量表筛查报告
                $tempReport = (new DiagnosisScalesReport())->where('diagnosis_report_id', $diagnosisReportId)->find();
                if ($tempReport->answers) {
                    $answerArr = json_decode($tempReport->answers, true);
                    // 如果提数不对，置为失败，异常提示
                    $questionNum = (new DiagnosisScales())->getTotalQuestionNum($tempReport->diagnosis_scales_id);
                    if (count($answerArr) != $questionNum) {
                        $tempReport->running_status = 'fail';
                        $tempReport->save();
                        throw new Exception('答题数量不对，不能重新生成报告');
                    }
                } else {
                    $tempReport->running_status = 'fail';
                    $tempReport->save();
                    throw new Exception('没有答题数据，不能重新生成报告');
                }

                $openid = (new PatientServices())->getUserOpenIdByUid($tempReport->uid);
                $postData = [
                    'reportUuid' => $tempReport->report_uuid,
                    'gender' => $tempReport->gender,
                    'age' => $tempReport->age,
                    'answer' => $tempReport->answers,
                    'openid' => $openid,
                ];

                // Submit to middle service
                $res = (new LiuyiMiddleService())->scalesReportTask($postData);

                if ($res['code'] != 200) {
                    throw new Exception($res['message']);
                }
                $tempReport->last_re_time = time();
                $tempReport->running_status = 'running';
                $tempReport->save();

                $reportInfo->status = 2;
                $reportInfo->save();

                Db::commit();
                return ['report_uuid' => $tempReport->report_uuid, 'status' => 'running'];
            } catch (\Exception|Exception $e) {
                Db::rollback();
                throw new Exception($e->getMessage());
            }
        }
        return ['report_uuid' => '', 'status' => 'init', 'msg' => '报告类型错误'];
    }


    /**
     * 报告详情
     * @param int $id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function getDiagnosisReportInfo(int $id): array
    {
        $mainReportInfo = (new DiagnosisReport())->where('id', $id)->find();
        if (!$mainReportInfo) {
            throw new Exception('报告不存在');
        }
        $reportType = $mainReportInfo->report_type;
        $result = [];
        if ($reportType == 2) {
            $fields = 'id,uid,name,gender,age,report_uuid,can_show,start_time,end_time,finished_time,physique_name,score,
            result,diagnosis_level,physiology_diagnosis,physiology_score,physiology_report,physiology_level,psychology_diagnosis,
            psychology_score,psychology_level,psychology_report,society_diagnosis,society_score,society_level,society_report,
            diagnosis_report_id,diagnosis_scales_id,running_status';
            $tempReportInfo = $this->model->where('diagnosis_report_id', $id)->field($fields)->find();
            if (!$tempReportInfo) {
                throw new Exception('报告不存在');
            }
            if ($tempReportInfo->running_status == 'fail') {
                throw new Exception('报告生成失败,稍后再试');
            }
            if ($tempReportInfo->running_status == 'running') {
                throw new Exception('报告生成中，请稍后再试');
            }
            if ($tempReportInfo->running_status == 'init') {
                throw new Exception('报告初始化中，请完善答题再生成报告');
            }
            if ($tempReportInfo->can_show == 0) {
                throw new Exception('暂不能查看报告，请联系客服');
            }
            // 获取报告名称
            $diagnosisScalesModel = new DiagnosisScales();
            $title = $diagnosisScalesModel->getTitle($tempReportInfo->diagnosis_scales_id);
            // 获取用户头像
            $avatar = (new User())->where('uid', $tempReportInfo->uid)->value('avatar');
            // 获取报告说明
            $reportIntro = $diagnosisScalesModel->getReportIntro($tempReportInfo->diagnosis_scales_id);
            // 格式化
            $retext = $this->getSummaryText($tempReportInfo->result);
            $result = [
                'id' => $tempReportInfo->id,
                'name' => $tempReportInfo->name,
                'gender' => $tempReportInfo->gender,
                'age' => $tempReportInfo->age,
                'report_uuid' => $tempReportInfo->report_uuid,
                'title' => $title,
                'avatar' => $avatar,
                'finished_time_text' => date('Y-m-d', $tempReportInfo->finished_time),
                'report_intro' => $reportIntro ? htmlspecialchars_decode($reportIntro) : '',
                'answer_time_len' => $tempReportInfo->answer_time_len,
                'result_1' => [
                    'diagnosis_name' => '心身总体健康',
                    'score' => $tempReportInfo->score,
                    'physique_name' => $tempReportInfo->physique_name,
                    'level' => $tempReportInfo->diagnosis_level,
                    'report' => $tempReportInfo->result,
                    'summary_text' => $this->getSummaryText($tempReportInfo->result),
                    'advice_text' => $this->getAdviceText($tempReportInfo->result),
                ],
                'result_2' => [
                    'diagnosis_name' => '身体健康情况',
                    'score' => $tempReportInfo->physiology_score,
                    'physique_name' => $tempReportInfo->physiology_diagnosis,
                    'level' => $tempReportInfo->physiology_level,
                    'report' => $tempReportInfo->physiology_report,
                    'summary_text' => $this->getSummaryText($tempReportInfo->physiology_report),
                    'advice_text' => $this->getAdviceText($tempReportInfo->physiology_report),
                ],
                'result_3' => [
                    'diagnosis_name' => '心理健康情况',
                    'score' => $tempReportInfo->psychology_score,
                    'physique_name' => $tempReportInfo->psychology_diagnosis,
                    'level' => $tempReportInfo->psychology_level,
                    'report' => $tempReportInfo->psychology_report,
                    'summary_text' => $this->getSummaryText($tempReportInfo->psychology_report),
                    'advice_text' => $this->getAdviceText($tempReportInfo->psychology_report),
                ],
                'result_4' => [
                    'diagnosis_name' => '社会健康情况',
                    'score' => $tempReportInfo->society_score,
                    'physique_name' => $tempReportInfo->society_diagnosis,
                    'level' => $tempReportInfo->society_level,
                    'report' => $tempReportInfo->society_report,
                    'summary_text' => $this->getSummaryText($tempReportInfo->society_report),
                    'advice_text' => $this->getAdviceText($tempReportInfo->society_report),
                ],
            ];
        }
        return $result;
    }

    private function getSummaryText(string $reportText): string
    {
        $reportText = $this->clearTig($reportText);

        // 查找"健康分析"的位置
        $startPos = strpos($reportText, '健康分析');
        if ($startPos === false) {
            return '';
        }

        // 跳过"健康分析"标题，查找实际内容开始位置
        $startPos = $startPos + mb_strlen('健康分析');

        // 查找"健康建议："的位置
        $endPos = strpos($reportText, '健康建议：');
        if ($endPos === false) {
            // 如果没有找到"健康建议："，返回从"健康分析"到结尾的内容
            return trim(substr($reportText, $startPos));
        }

        // 提取"健康分析"到"健康建议："之间的内容
        $length = $endPos - $startPos;
        return trim(substr($reportText, $startPos, $length));
    }

    private function getAdviceText(string $reportText): string
    {
        $reportText = $this->clearTig($reportText);

        // 查找"健康建议："的位置
        $startPos = strpos($reportText, '健康建议：');
        if ($startPos === false) {
            return '';
        }

        // 跳过"健康建议："标题，查找实际内容开始位置
        $startPos = $startPos + mb_strlen('健康建议：');

        // 返回从"健康建议："到结尾的内容
        return trim(substr($reportText, $startPos));
    }

    /**
     * 获取报告展示权限
     * @param int $uid
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDiagnosisReportShowCheck(int $uid): array
    {
        // 获取当前用户在此之前是否已经有了查看报告的权限
        $tempReport = $this->model->where('uid', $uid)->where('can_show', 1)->find();
        if ($tempReport) {
            return ['type' => 1, 'tip' => '报告列表'];
        }
        return ['type' => 0, 'tip' => '添加客服'];
    }

    private function clearTig($markdown)
    {
        // 移除标题
        $markdown = preg_replace('/^#+\s+/m', '', $markdown);

        // 移除列表标记
        $markdown = preg_replace('/^[\*\-+]\s+/m', '', $markdown);
        $markdown = preg_replace('/^\d+\.\s+/m', '', $markdown);

        // 移除链接和图片
        $markdown = preg_replace('/!?\[(.*?)\]\(.*?\)/', '$1', $markdown);

        // 移除粗体和斜体
        $markdown = preg_replace('/\*\*(.*?)\*\*/', '$1', $markdown);
        $markdown = preg_replace('/\*(.*?)\*/', '$1', $markdown);
        $markdown = preg_replace('/__(.*?)__/', '$1', $markdown);
        $markdown = preg_replace('/_(.*?)_/', '$1', $markdown);
        return $markdown;
    }
}
