-- 微信v3支付
INSERT INTO `eb_system_config` (`id`, `is_store`, `menu_name`, `type`, `input_type`, `config_tab_id`, `parameter`, `upload_type`, `required`, `width`, `high`, `value`, `info`, `desc`, `sort`, `status`) VALUES
(null, 0, 'v3_pay_public_key', 'text', 'input', 4, '', 1, '', 100, 0, '\"\"', 'v3支付公钥', 'v3支付公钥，新版本使用公钥请填写', 0, 1),
(null, 0, 'v3_pay_public_pem', 'upload', 'input', 4, '', 3, '', 0, 0, '\"\"', 'v3支付公钥证书', 'v3支付公钥证书，使用新版本支付公钥上传此证书', 0, 1);

INSERT INTO `eb_system_config`  VALUES
    (null, 0, 'pay_weixin_scene_id', 'text', 'number', 4, '', 1, '', 100, 0, '', '转账场景ID', '微信v3支付商家转账的转账场景ID,用于抽奖红包和佣金提现', 0, 1);

-- 增加业绩设置
INSERT INTO `eb_system_config_tab` (`id`, `is_store` ,`pid`, `title`, `eng_title`, `status`, `info`, `icon`, `type`, `sort`) VALUES
    (null, 0, 0, '业绩设置', 'performance', '1', '0', '', '0', '0');

-- 增加线下支付配置
SELECT `id` as tabId FROM `eb_system_config_tab` WHERE `eng_title`='performance' LIMIT 1;
INSERT INTO `eb_system_config` (`id`, `is_store`, `menu_name`, `type`, `input_type`, `config_tab_id`, `parameter`, `upload_type`, `required`, `width`, `high`, `value`, `info`, `desc`, `sort`, `status`) VALUES
(NULL, 0, 'binding_scene', 'checkbox', '', @tabId, '1=>下单\n2=>访问店铺页面\n3=>添加企业微信\n4=>扫门店推广码', 1, '', 0, 0, '[1]', '绑定场景', '绑定场景', 0, 1),
(NULL, 0, 'period_of_validity', 'radio', '', @tabId, '1=>永久\n2=>固定期限\n3=>临时', 1, '', 0, 0, '\"1\"', '关系有效期', '永久:绑定后不可更换；固定期限：期限内不可更换，超出期限绑定关系自动解绑。临时：每一次扫码都会重新绑定关系', 0, 1),
(NULL, 0, 'period_validity_day', 'text', 'input', @tabId, '', 0, '', 100, 0, '30', '固定期限天数', '固定期限天数', 0, 1);

UPDATE `eb_system_config` set `info` = '专属导购对应门店' where `menu_name` = 'belong_store_unbind_status';


-- 商品
ALTER TABLE `eb_store_product` CHANGE `product_type` `product_type` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品, 4: 次卡商品，5:卡项商品，6:预约商品';

ALTER TABLE `eb_store_product` ADD `is_brokerage` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否参与分佣1 参与 0 不参与' AFTER `is_sold`;
ALTER TABLE `eb_store_product` ADD `brokerage_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '返佣类型 1 固定优惠类型,2 返佣比例' AFTER `is_brokerage`;
ALTER TABLE `eb_store_product` ADD  `level_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '等级会员价格,1:系统默认,2:自定义' AFTER `brokerage_type`;
ALTER TABLE `eb_store_product` ADD `share_content` varchar(500)  NOT NULL DEFAULT '' COMMENT '分享文案'  AFTER `level_type`;
ALTER TABLE `eb_store_product` ADD `default_sku` varchar(255)  NOT NULL DEFAULT '' COMMENT '默认规格'  AFTER `share_content`;
ALTER TABLE `eb_store_product` ADD `presale_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '预售结束后状态 1:上架,0 下架'  AFTER `presale_day`;
-- 预约商品
ALTER TABLE `eb_store_product` ADD `reservation_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '预约类型1：到店服务+上门服务，2：到店服务，3：上门服务';
ALTER TABLE `eb_store_product` ADD `reservation_timing_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '预约时机1：购买时预约+售后预约，2：购买时预约，3：售后预约';
ALTER TABLE `eb_store_product` ADD `sale_time_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '销售日期1：每天，2:每周，3：自定义时间';
ALTER TABLE `eb_store_product` ADD `sale_time_week` varchar (100) NOT NULL DEFAULT '1' COMMENT '销售日期每周设置';
ALTER TABLE `eb_store_product` ADD `sale_time_start` int (11) NOT NULL DEFAULT '0' COMMENT '销售日期自定义开始时间';
ALTER TABLE `eb_store_product` ADD `sale_time_end` int (11) NOT NULL DEFAULT '0' COMMENT '销售日期自定义结束时间';
ALTER TABLE `eb_store_product` ADD `show_reservation_days_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '显示可预约日期类型1：全部展示，2：自定义展示时间';
ALTER TABLE `eb_store_product` ADD `show_reservation_days` int(10) NOT NULL DEFAULT '0' COMMENT '显示多少天内可预约日期（天）';
ALTER TABLE `eb_store_product` ADD `is_advance` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否需要提前预约0：无需提前1：需要';
ALTER TABLE `eb_store_product` ADD `advance_time` int(10) NOT NULL DEFAULT ' 0' COMMENT '提前多少小时预约（小时）';
ALTER TABLE `eb_store_product` ADD `is_cancel_reservation` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否可以取消预约0：不允许1：可以';
ALTER TABLE `eb_store_product` ADD `cancel_reservation_time` int(10) NOT NULL DEFAULT ' 0' COMMENT '服务开始前多少小时允许取消（小时）';
ALTER TABLE `eb_store_product` ADD `reservation_time_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '预约时段类型1：自动划分，2：自定义';
ALTER TABLE `eb_store_product` ADD `customize_time_period` LONGTEXT NULL DEFAULT NULL COMMENT '自定义时间段划分数据';
ALTER TABLE `eb_store_product` ADD `reservation_time_start` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '预约时段自定义开始时间';
ALTER TABLE `eb_store_product` ADD `reservation_time_end` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '预约时段自定义结束时间';
ALTER TABLE `eb_store_product` ADD `reservation_time_interval` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '预约时段自动类型：时间间隔（分钟）';
ALTER TABLE `eb_store_product` ADD `is_show_stock` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '是否展示库存';
ALTER TABLE `eb_store_product` ADD `card_cover` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '卡片封面 1:图片，2:颜色';
ALTER TABLE `eb_store_product` ADD `card_cover_image` varchar(256) NOT NULL DEFAULT '' COMMENT '卡片封面图片';
ALTER TABLE `eb_store_product` ADD `card_cover_color` varchar(32) NOT NULL DEFAULT '' COMMENT '卡片封面颜色';


--
-- 表的结构 `eb_store_product_reservation_time`
--
CREATE TABLE IF NOT EXISTS `eb_store_product_reservation_time` (
    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_unique` varchar(20) NOT NULL DEFAULT '' COMMENT '商品sku',
    `show_time` varchar(255) NOT NULL DEFAULT '' COMMENT '展示的时间段',
    `start` varchar(20) NOT NULL DEFAULT '' COMMENT '时间段开始时间',
    `end` varchar(20) NOT NULL DEFAULT '' COMMENT '时间段结束时间',
    `service_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '服务费金额',
    `stock` int(10) NOT NULL DEFAULT '1' COMMENT '时间段内库存',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约商品时段划分库存表';

--
-- 表的结构 `eb_store_reservation_order`
--
CREATE TABLE IF NOT EXISTS `eb_store_reservation_order` (
    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `order_id` varchar(50) NOT NULL DEFAULT '' COMMENT '预约单单号',
    `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户UID',
    `oid` int(11) NOT NULL DEFAULT '0' COMMENT '订单表ID',
    `cart_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单商品ID',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku` varchar(255) NOT NULL DEFAULT '' COMMENT '商品sku',
    `sku_unique` varchar(20) NOT NULL DEFAULT '' COMMENT '商品sku唯一值',
    `store_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店ID',
    `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店店员ID',
    `service_staff_id` int(10) NOT NULL DEFAULT '0' COMMENT '服务人员ID',
    `service_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '服务费金额',
    `verify_code` varchar(125) NOT NULL DEFAULT '' COMMENT '核销码',
    `reservation_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '预约类型2：到店服务，3：上门服务',
    `reservation_time` int(10) NOT NULL DEFAULT '0' COMMENT '预约日期时间',
    `reservation_time_id` int(10) NOT NULL DEFAULT '0' COMMENT '预约时间段ID',
    `reservation_start` varchar(20) NOT NULL DEFAULT '' COMMENT '预约时间段开始时间',
    `reservation_end` varchar(20) NOT NULL DEFAULT '' COMMENT '预约时间段结束时间',
    `reservation_name` varchar(50) NOT NULL DEFAULT '' COMMENT '预约人昵称',
    `reservation_phone` varchar(50) NOT NULL DEFAULT '' COMMENT '预约人电话',
    `reservation_address` varchar(500) NOT NULL DEFAULT '' COMMENT '预约人地址',
    `custom_form_title` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '自定义补充信息标题',
    `reservation_info` longtext NULL DEFAULT NULL COMMENT '预约补充信息',
    `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户备注',
    `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '管理员备注',
    `status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '预约单状态-1：已取消0：待服务1：服务中2：已完成',
    `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '用户是否删除',
    `is_system_del` tinyint(1) NULL DEFAULT 0 COMMENT '后台是否删除',
    `reservation_create_time` int(10) NOT NULL DEFAULT '0' COMMENT '预约确定时间',
    `service_time` int(10) NOT NULL DEFAULT '0' COMMENT '服务时间',
    `service_end_time` int(10) NOT NULL DEFAULT '0' COMMENT '服务结束时间',
    `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约订单表';


-- 商品规格
ALTER TABLE `eb_store_product_attr_value`  ADD `level_price` varchar(1000)  NOT NULL DEFAULT '' COMMENT '等级价格' AFTER `write_end`;
ALTER TABLE `eb_store_product_attr_value`  ADD `is_default_select` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认规格' AFTER `level_price`;
ALTER TABLE `eb_store_product_attr_value`  ADD  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示' AFTER `is_default_select`;

-- 订单记录
ALTER TABLE `eb_store_order_status` ADD `id` INT(10) NOT NULL AUTO_INCREMENT FIRST, ADD PRIMARY KEY (`id`);
ALTER TABLE `eb_store_order_status` ADD `change_manager_id` INT(10) NOT NULL DEFAULT '0' COMMENT '操作人ID' AFTER `change_message`;
ALTER TABLE `eb_store_order_status` ADD `change_manager_type` VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '操作人类型' AFTER `change_manager_id`;
ALTER TABLE `eb_store_order_status` ADD `change_manager` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '操作人昵称' AFTER `change_manager_type`;


ALTER TABLE `eb_user_belong_store` CHANGE `group` `group` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '记录类型1:绑定,2:换绑,3:解绑';
ALTER TABLE `eb_user_belong_store` CHANGE `type` `type` varchar (100) NOT NULL DEFAULT 'order' COMMENT '归属绑定事件类型admin：管理员操作，order：门店下单，svip：开通会员卡，spread：绑定推广人，visit：访问店铺页面，we_com：添加企业微信，scan_code：扫门店推广码，expire：有效期到期解绑，shop：专属店员对应门店';
ALTER TABLE `eb_user_belong_store` ADD `is_store` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否门店记录' AFTER `type`;

-- 店员流水
-- 表的结构 `eb_staff_flowing_water`
--
CREATE TABLE `eb_staff_flowing_water` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店ID',
 `staff_id` INT(10) NOT NULL DEFAULT '0' COMMENT '店员ID',
 `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
 `order_id` varchar(20) NOT NULL DEFAULT '' COMMENT '交易单号',
 `link_id` varchar(50) NOT NULL DEFAULT '' COMMENT '关联订单',
 `pm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = 支出 1 = 获得',
 `number` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '明细',
 `type` varchar(50) NOT NULL DEFAULT '' COMMENT '交易类型(1:支付订单,2:充值订单,3:会员订单,4:退款订单,5:充值退款,6:核销业绩)',
 `pay_type` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式',
 `pay_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
 `total_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
 `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '平台备注',
 `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
 `trade_time` int(11) NOT NULL DEFAULT '0' COMMENT '交易时间',
 `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店员流水表';

ALTER TABLE `eb_user` ADD `salesman_time` int(11) NOT NULL DEFAULT '0' COMMENT '绑定时间' AFTER `salesman_id`;

-- 订单表类型
ALTER TABLE `eb_store_order` CHANGE `type` `type` SMALLINT NOT NULL DEFAULT '0' COMMENT '类型 0:普通、1：秒杀、2:砍价、3:拼团、4:积分、5:套餐、6:预售、7:新人礼、8:抽奖、9:拼单、10:桌码、11:卡项、12:预约';

ALTER TABLE `eb_store_order` ADD `service_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '服务费金额' AFTER `change_price`;

-- 预约信息
ALTER TABLE `eb_store_order` ADD `reservation_type` TINYINT(1) NOT NULL DEFAULT '2' COMMENT '预约类型2：到店服务，3：上门服务';
ALTER TABLE `eb_store_order` ADD `reservation_time` int(10) NOT NULL DEFAULT '0' COMMENT '预约日期时间';
ALTER TABLE `eb_store_order` ADD `reservation_time_id` int(10) NOT NULL DEFAULT '0' COMMENT '预约sku时段划分ID';
ALTER TABLE `eb_store_order` ADD `reservation_show_time` varchar(50) NOT NULL DEFAULT '' COMMENT '预约时段展示';
ALTER TABLE `eb_store_order` ADD `service_staff_id` int(10) NOT NULL DEFAULT '0' COMMENT '预约单服务人员ID';
ALTER TABLE `eb_store_order` ADD `reservation_status` TINYINT(1) NOT NULL DEFAULT '-1' COMMENT '预约单状态-1：待预约0：待服务1：服务中2：已完成';

-- 系统表单标题
ALTER TABLE `eb_store_order` ADD `custom_form_title` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '自定义表单模版标题' AFTER `virtual_info`;

-- 订单
ALTER TABLE `eb_store_order` ADD `is_user_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '用户再次删除' AFTER `is_del`;

-- 售后订单
ALTER TABLE `eb_store_order_refund` ADD `is_system_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '管理员删除' AFTER `is_del`;

-- 订单商品表增加商品结算数据
ALTER TABLE `eb_store_order_cart_info` ADD `total_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品总价' AFTER `cart_num`;
ALTER TABLE `eb_store_order_cart_info` ADD `pay_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际支付金额' AFTER `total_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `pay_postage` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '支付邮费' AFTER `pay_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `member_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户等级、svip折扣金额' AFTER `pay_postage`;
ALTER TABLE `eb_store_order_cart_info` ADD `deduction_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '抵扣金额' AFTER `member_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `coupon_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '优惠券金额' AFTER `deduction_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `promotions_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '优惠活动优惠金额' AFTER `coupon_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `first_order_price` DECIMAL(8,2) NOT NULL DEFAULT '0.00' COMMENT '首单优惠金额' AFTER `promotions_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `change_price` DECIMAL(8,2) NOT NULL DEFAULT '0.00' COMMENT '改价优惠金额' AFTER `first_order_price`;
ALTER TABLE `eb_store_order_cart_info` ADD `service_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '服务费金额' AFTER `change_price`;

-- 订单商品增加预约信息
ALTER TABLE `eb_store_order_cart_info` ADD `reservation_type` TINYINT(1) NOT NULL DEFAULT '2' COMMENT '预约类型2：到店服务，3：上门服务' AFTER `writeoff_time`;
ALTER TABLE `eb_store_order_cart_info` ADD `reservation_time` int(10) NOT NULL DEFAULT '0' COMMENT '预约日期时间' AFTER `reservation_type`;
ALTER TABLE `eb_store_order_cart_info` ADD `reservation_time_id` int(10) NOT NULL DEFAULT '0' COMMENT '预约sku时段划分ID' AFTER `reservation_time`;

-- 订单购物详情表
ALTER TABLE `eb_store_order_cart_info` ADD `is_card` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否卡项关联商品1:是0:否' AFTER `is_gift`;

-- 核销记录表
ALTER TABLE `eb_store_order_writeoff` ADD `reservation_oid` INT(10) NOT NULL DEFAULT '0' COMMENT '预约单ID' AFTER `oid`;

-- 订单表类型
ALTER TABLE `eb_store_order_writeoff` CHANGE `product_type` `product_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品类型0:普通商品，1：卡密，2：优惠券，3：虚拟商品,4：次卡商品，5:卡项商品，6:预约商品';

-- 门店资金流水表
ALTER TABLE `eb_store_finance_flow` CHANGE `type` `type` varchar(50) NOT NULL DEFAULT '' COMMENT '交易类型(1:支付订单,2,门店订单,3,订单手续费,4:退款订单,5:充值返点,6:付费会员返点,7:充值订单,8:付费订单,9:收银订单,10:核销订单,11:分配订单,12:配送订单,13:同城配送订单)';



ALTER TABLE `eb_system_store_staff` ADD `work_member_id` int(11) NOT NULL DEFAULT '0' COMMENT '企业微信员工id' AFTER `customer_url`;
ALTER TABLE `eb_system_store_staff` ADD `work_member_code` varchar(255) NOT NULL DEFAULT '' COMMENT '员工个人二维码' AFTER `work_member_id`;

INSERT INTO `eb_system_timer` (`id`, `name`, `mark`, `type`, `title`, `is_open`, `cycle`, `last_execution_time`, `update_execution_time`, `is_del`, `add_time`) VALUES
(NULL, '店员企业微信绑定处理', 'shop_assistant_enterprise_we_chat_binding', 2, '店员企业微信绑定', 1, '1/0', 0, 0, 0, 1733477393),
(NULL, '临期解绑店员处理', 'untamed_shop_assistant_time', 2, '用户临期解绑店员', 1, '1/0', 0, 0, 0, 1733477423),
(NULL, '预售商品结束状态', 'presale_product_status', 1, '预售商品结束状态', 1, '5', 0, 0, 0, 1733477423);

--
-- 表的结构 `eb_store_card_related`
--
CREATE TABLE `eb_store_card_related` (
    `id` int(12) NOT NULL AUTO_INCREMENT,
    `card_product_id` int(10) NOT NULL DEFAULT '0' COMMENT '卡项商品ID',
    `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联商品ID',
    `product_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品类型',
    `product_attr_unique` varchar(20) NOT NULL DEFAULT '' COMMENT '商品属性',
    `cost` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '成本价',
    `price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品价格',
    `write_times` smallint(5) NOT NULL DEFAULT '0' COMMENT '可核销次数',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
    `add_time` int(12) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡项关联商品';

--
-- 表的结构 `eb_user_card_holder`
--
CREATE TABLE `eb_user_card_holder` (
    `id` int(12) NOT NULL AUTO_INCREMENT,
    `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `oid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
    `card_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '卡名称',
    `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店ID',
    `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联商品ID',
    `product_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品类型',
    `verify_code` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
    `write_valid` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '核销时效：1、永久； 2、购买后几天；3、固定',
    `write_days` INT(10) NOT NULL DEFAULT '0' COMMENT '购买后：N天有效',
    `write_start` INT(10) NOT NULL DEFAULT '0' COMMENT '核销开始时间：0不限制',
    `write_end` INT(10) NOT NULL DEFAULT '0' COMMENT '核销结束时间：0不限制',
    `write_times` INT(10) NOT NULL DEFAULT '0' COMMENT '核销总次数',
    `write_surplus_times` INT(10) NOT NULL DEFAULT '0' COMMENT '核销剩余次数',
    `is_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    `add_time` int(12) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡包列表';


INSERT INTO `eb_system_group_data` (`id`, `gid`, `value`, `add_time`, `sort`, `status`) VALUES
(null, 54, '{\"name\":{\"type\":\"input\",\"value\":\"\\u6211\\u7684\\u5361\\u5305\"},\"pic\":{\"type\":\"upload\",\"value\":\"\\/uploads\\/system\\/0a73f202109230908134790.png\"},\"url\":{\"type\":\"input\",\"value\":\"\\/pages\\/users\\/user_card_list\\/index\"},\"type\":{\"type\":\"radio\",\"value\":\"1\"}}', 1634803318, 0, 1);


-- 用户提现表
ALTER TABLE `eb_user_extract`
    ADD `order_id` varchar(32)  NOT NULL DEFAULT '' COMMENT '订单号'  AFTER `id`,
    ADD `user_name` varchar(64)  NOT NULL DEFAULT '' COMMENT '真实姓名'  AFTER `qrcode_url`,
    ADD `withdraw_fee` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '提现费率%' AFTER `extract_fee`,
    ADD `wechat_state` varchar(32)  NOT NULL DEFAULT '' COMMENT 'v3转账状态码'  AFTER `user_name`,
    ADD `package_info` varchar(1000)  NOT NULL DEFAULT '' COMMENT 'v3转账支付收款页的package'  AFTER `wechat_state`,
    ADD `fail_reason` varchar(32) NOT NULL DEFAULT '' COMMENT 'v3转账失败原因'  AFTER `package_info`,
    ADD `transfer_bill_no` varchar(256)  NOT NULL DEFAULT '' COMMENT 'v3微信转账单号' AFTER `fail_reason`,
    ADD `channel_type` varchar(10)  NOT NULL DEFAULT '' COMMENT '提现渠道(小程序:routine;公众号:h5;app)' AFTER `transfer_bill_no`;

ALTER TABLE `eb_luck_lottery_record`
    ADD `order_id` varchar(32)  NOT NULL DEFAULT '' COMMENT '订单号'  AFTER `add_time`,
    ADD `wechat_state` varchar(32)  NOT NULL DEFAULT '' COMMENT 'v3转账状态码'  AFTER `order_id`,
    ADD `package_info` varchar(1000)  NOT NULL DEFAULT '' COMMENT 'v3转账支付收款页的package'  AFTER `wechat_state`,
    ADD `fail_reason` varchar(32) NOT NULL DEFAULT '' COMMENT 'v3转账失败原因'  AFTER `package_info`,
    ADD `transfer_bill_no` varchar(256)  NOT NULL DEFAULT '' COMMENT 'v3微信转账单号' AFTER `fail_reason`,
    ADD `channel_type` varchar(10)  NOT NULL DEFAULT '' COMMENT '提现渠道(小程序:routine;公众号:h5;app)' AFTER `transfer_bill_no`;


INSERT INTO `eb_system_notification`  VALUES (null, 'revenue_received', '收益到账通知', '收益到账给用户通知', 0, 0, 1, 1, 0, 0, '收益到账通知', '', 0, '0', '0', '', '', '', '', '', 1, 0);

INSERT INTO `eb_template_message` VALUES (null, '41', 0, '1493', '收益到账通知', '', '收益金额{{amount3.DATA}}\n温馨提醒{{thing4.DATA}}\n时间{{time9.DATA}}', '', '', '1739763519', 1);
INSERT INTO `eb_template_message` VALUES (null, '41', 1, '54531', '提现结果通知', '', '金额{{amount2.DATA}}\n提现结果{{const5.DATA}}\n提现时间{{time3.DATA}}', '', '', '1739763519', 1);

-- 角色创建时间
ALTER TABLE `eb_system_role` ADD `add_time` INT(11) NOT NULL DEFAULT '0' COMMENT '添加时间' AFTER `status`;

UPDATE `eb_system_menus` set `menu_name` = '服务业绩' where `unique_auth` = 'store-staff-statistics';


ALTER TABLE `eb_system_form_data` ADD `product_id` INT(10) NOT NULL DEFAULT '0' COMMENT '关联商品ID' AFTER `system_form_id`;

-- 个人中心链接
INSERT INTO `eb_page_link` (`id`, `cate_id`, `type`, `name`, `url`, `param`, `example`, `status`, `sort`, `add_time`) VALUES
    (null, 5, 3, '我的预约', '/pages/goods/reservation_list/index', ' ', '/pages/goods/reservation_list/index', 1, 0, 1626837579),
    (null, 5, 3, '我的卡包', '/pages/users/user_card_list/index', ' ', '/pages/users/user_card_list/index', 1, 0, 1626837579);


